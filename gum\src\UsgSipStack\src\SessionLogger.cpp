#include "SessionLogger.hpp"

namespace usg {

CSessionLogger::CSessionLogger( pj_pool_t *pool )
: m_pool( pool )
{
    pj_mutex_create( m_pool, 0, PJ_MUTEX_DEFAULT, &m_rm );
    pj_mutex_create( m_pool, 0, PJ_MUTEX_DEFAULT, &m_tm );
}

CSessionLogger::~CSessionLogger()
{
    pj_mutex_destroy( m_rm );
    pj_mutex_destroy( m_tm );

    for ( std::map<std::string, pjsip_rx_data *>::iterator it = m_rx.begin(); it != m_rx.end(); ++it )
    {
        pjsip_rx_data_free_cloned( (*it).second );
    }
    for ( std::map<std::string, pjsip_tx_data *>::iterator it = m_tx.begin(); it != m_tx.end(); ++it )
    {
        pjsip_tx_data_dec_ref( (*it).second );
    }
}

void CSessionLogger::insertRx( const std::string &sid, pjsip_rx_data *data )
{
    if ( !data ) return ;

    pj_mutex_lock( m_rm );
    m_rx[sid] = data;
    pj_mutex_unlock( m_rm );
}

void CSessionLogger::insertTx( const std::string &sid, pjsip_tx_data *data )
{
    if ( !data ) return ;

    pj_mutex_lock( m_tm );
    m_tx[sid] = data;
    pj_mutex_unlock( m_tm );
}

bool CSessionLogger::removeRx( const std::string &sid, pjsip_rx_data **data )
{
    bool result = false;

    pj_mutex_lock( m_rm );

    std::map<std::string, pjsip_rx_data *>::iterator it = m_rx.find( sid );
    if ( it != m_rx.end() )
    {
        result = true;
        if ( data ) 
            *data = (*it).second;
        else
            pjsip_rx_data_free_cloned( (*it).second );

        m_rx.erase( it );
    }

    pj_mutex_unlock( m_rm );

    return result;
}

bool CSessionLogger::removeTx( const std::string &sid, pjsip_tx_data **data )
{
    bool result = false;

    pj_mutex_lock( m_tm );

    std::map<std::string, pjsip_tx_data *>::iterator it = m_tx.find( sid );
    if ( it != m_tx.end() )
    {
        result = true;
        if ( data ) 
            *data = (*it).second;
        else
            pjsip_tx_data_dec_ref( (*it).second );
        m_tx.erase( it );
    }

    pj_mutex_unlock( m_tm );

    return result;
}

bool CSessionLogger::pickupRx( const std::string &sid, pjsip_rx_data *&data )
{
    bool result = false;
    pj_mutex_lock( m_rm );

    std::map<std::string, pjsip_rx_data *>::iterator it = m_rx.find( sid );
    if ( it != m_rx.end() )
    {
        result = true;
        data = (*it).second;
    }
    pj_mutex_unlock( m_rm );

    return result;
}

bool CSessionLogger::pickupTx( const std::string &sid, pjsip_tx_data *&data )
{
    bool result = false;
    pj_mutex_lock( m_tm );

    std::map<std::string, pjsip_tx_data *>::iterator it = m_tx.find( sid );
    if ( it != m_tx.end() )
    {
        result = true;
        data = (*it).second;
    }
    pj_mutex_unlock( m_tm );

    return result;
}

}
