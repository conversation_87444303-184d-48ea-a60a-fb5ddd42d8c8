/**
 * Project FVM
 */
#pragma once

#include "data/video_source_info.h"
#include "platform/front_platform.h"
#include "stream/output/stream_output.h"
#include "stream/input/stream_input.h"

/**
 * @brief: 视频流创建接口
 */
namespace fvm::stream
{
    /**
     * 创建取流器
     * @param VideoSourceInfo 视频资源信息
     * @return StreamInputPtr
     */
    StreamInputPtr createStreamInput(VideoSourceInfoPtr videoInfo);

    /**
     * 创建发流器
     * @param outputType 输出类型
     * @param port 端口
     * @param baseUrl 地址
     * @return StreamOutputPtr
     */
    StreamOutputPtr createStreamOutput(StreamOuputType outputType, int port= 20000, std::string baseUrl= "127.0.0.1");

    /**
     * 获取资源的视频平台
     * @param VideoSourceInfo 视频资源信息
     * @return FrontPlatformPtr
     */
    FrontPlatformPtr getFrontPlatform(int platformID );

    /**
     * 更新远端平台信息
     * @param platformID
     */
    void updatePlatform( int platformID );
}