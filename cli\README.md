# 命令行交互界面(command line interface)
&emsp;&emsp;该模块主要用于在终端进行命令输入，并解析命令。将输入区固定在终端底部。

## 目录
* [目录结构](#目录结构)
* [接口概览](#接口概览)
* [快速示例](#快速示例)
* [终端操作](#终端操作)


## 目录结构
<pre>
.
├── include        对外头文件
├── src            主要功能实现
│   └── utils      控制台通用接口
└── test           测试代码
</pre>

## 接口概览
&emsp;主要包括3个接口: 添加用户命令,开启,停止
~~~c++
    // 处理终端命令输入，命令处理类
class CommandProcess {
...
public:
// 添加一个用户命令，将用户函数与命令名绑定
void addCommand(const std::string& funcName, const consoleFunction& func);
// 开启终端处理
void start();
// 退出终端处理
void stop();
};
~~~
## 快速示例
```c++
// 通过终端命令执行的函数
void test_int(int a, int b){
cout << "test_int a+b = " << a+b << endl;
}

class TestClass{
public:
int c = 1;
void fun(int a, int b) const{
cout << "TestClass::fun a+b = " << a+b  << " c = " << c << endl;
}
};

// 创建终端处理实例 
cli::CommandProcess cmdProcess;

// 添加终端命令，绑定命令对应函数
cmdProcess.addCommand("testint", [](const cli::consoleParamList& paramList) {
CHECK_PARAM_NUM(paramList,2);                      // 检查参数个数
int a = GET_PARAM_INT(paramList[0].c_str());       // 将用户输入的参数转为对应类型参数
int b = GET_PARAM_INT(paramList[1].c_str();
test_int(a,b);
}
TestClass testClass;
cmdProcess.addCommand("testclass", [](const cli::consoleParamList& paramList) {
CHECK_PARAM_NUM(paramList,2);
int a = GET_PARAM_INT(paramList[0].c_str());
int b = GET_PARAM_INT(paramList[1].c_str();
testClass.fun(a, b);;
}

// 开启终端
cmdProcess.start();

```
## 终端操作
&emsp;默认情况下，终端是处于正常日志输出模式，进入命令行交互接口步骤如下： <br>
- *命令输入与执行：*
<pre>
1. 在终端上输入符号 > 后按Enter键进入输入模式
2. 开始输入命令
3. 按Enter执行命令(执行完后继续步骤2,3执行其他命令)
</pre>
- *退出输入模式：*
<pre>
1. 在输入模式下输入exit退出输入模式
</pre>


