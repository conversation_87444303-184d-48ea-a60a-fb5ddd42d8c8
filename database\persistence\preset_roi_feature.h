/**
 * @addtogroup odbDatabaseGroup
 * @brief 预置位对应的感兴趣区信息
 * @{
 */
#pragma once

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>
#include <utility>

namespace db
{
/**
 * @brief  iva多进程配置信息 对应数据库aimonitorV3的表wn_process_config
 */
#pragma db object table("wn_preset_roi_feature")

    class PresetRoiFeature
    {
    public:

        PresetRoiFeature(unsigned long roiId_, std::string area_, std::string beginTime_, std::string endTime_)
                : roiId(roiId_), area(std::move(area_)), beginTime(std::move(beginTime_)), endTime(std::move(endTime_)){}

        unsigned long getId() const
        {
            return id;
        }

        unsigned long getRoiId() const
        {
            return roiId;
        }

        const std::string& getArea() const
        {
            return area;
        }

        void setArea(std::string area_)
        {
            area = std::move(area_);
        }

        void setRoiId(unsigned long id)
        {
            this->roiId = id;
        }

        const std::string& getBeginTime() const
        {
            return beginTime;
        }

        void setBeginTime(std::string time)
        {
            this->beginTime = std::move(time);
        }

        const std::string& getEndTime() const
        {
            return endTime;
        }

        void setEndTime(std::string time)
        {
            this->endTime = std::move(time);
        }


    private:

        friend class odb::access;
        PresetRoiFeature() {}

    private:

#pragma db id auto
        unsigned long id;                   //!< 表ID

#pragma db column("roi_id")
        unsigned long roiId;                //!< 特征提取区域所在的感兴趣区Id

#pragma db column("area")       type("VARCHAR(255)")
        std::string area;                   //!< 特征提取区域

#pragma db column("begin_time") type("VARCHAR(255)")
        std::string beginTime;              //!< 特征提取时间起始时间

#pragma db column("end_time")   type("VARCHAR(255)")
        std::string endTime;                //!< 特征提取时间结束时间

    };

/**
 * @}
 */
}
