#ifndef RESMANAGERTESTCASE_HPP_
#define RESMANAGERTESTCASE_HPP_

#include <cppunit/extensions/HelperMacros.h>

namespace usg
{

class CResManagerTestCase : public CPPUNIT_NS::TestFixture
{
    CPPUNIT_TEST_SUITE( CResManagerTestCase );

    CPPUNIT_TEST( test_onReceiveCatalog );
    CPPUNIT_TEST( test_getResInfo );


    CPPUNIT_TEST_SUITE_END();

public:
    CResManagerTestCase();
    ~CResManagerTestCase();

public:
    virtual void setUp();
    virtual void tearDown();

public:
    void test_onReceiveCatalog();
    void test_getResInfo();
};

}

#endif