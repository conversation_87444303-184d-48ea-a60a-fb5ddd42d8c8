#include "UsgManager/src/CfgConfigFile.hpp"

namespace usg
{

    CCfgConfigFile::CCfgConfigFile()
    {

    }

    CCfgConfigFile::~CCfgConfigFile()
    {

    }

    bool CCfgConfigFile::readFile( const std::string &fileName, std::vector<std::string>& lines )
    {
        std::ifstream ifs;
        if ( !openFile( fileName, ifs ) )
        {
            return false;
        }
        std::string strLine;
        while( getline(ifs,strLine) )
        {
            strLine = trim( strLine );
            if ( strLine.empty())
                continue;
            if (strLine[0] == '#')
                continue;
            lines.push_back( strLine );
        }
        ifs.close();
        return true;
    }

    bool CCfgConfigFile::writeFile( const std::string &fileName, const std::vector<std::string>& lines )
    {
        std::locale loc = std::locale::global(std::locale(""));
        std::ofstream of( fileName.c_str() );
        uint16_t size = lines.size();
        for ( uint16_t i = 0; i < size; ++i )
        {
            of << lines[i] << std::endl;
        }
        std::locale::global(loc);//恢复全局locale
        of.close();
        return true;
    }

    std::string& CCfgConfigFile::trim( std::string &s )
    {
#ifdef __GNUC__
        std::string::size_type pos = s.find_last_of('\r');
        if( pos != std::string::npos )
            s.erase( pos );
#endif
        if (s.empty())
        {
            return s;
        }

        //此函数在使用split进行分割时会再次调用，这样每个单词如果有空格就会被去掉
        s.erase(0,s.find_first_not_of(" "));
        s.erase(s.find_last_not_of(" ") + 1);

        return s;
    }

    bool CCfgConfigFile::split( const std::string &s, std::vector<std::string>& vtr, char delim /*= ',' */ )
    {
        vtr.clear();
        std::stringstream ss(s);
        for (;;)
        {
            std::string token;
            getline( ss, token, delim );
            if ( ss.fail() )  break;
            token = trim( token );
            vtr.push_back( token );
        }
        return true;
    }

    bool CCfgConfigFile::openFile( const std::string &fileName, std::ifstream& ifs )
    {
        std::locale loc = std::locale::global(std::locale("")); //要打开的文件路径含中文，设置全局locale为本地环境
        ifs.open( fileName.c_str() );
        std::locale::global(loc);//恢复全局locale
        if ( !ifs.is_open() )
            return false;
        return true;
    }
}
