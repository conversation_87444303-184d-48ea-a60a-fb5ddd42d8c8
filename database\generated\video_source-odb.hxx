// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#ifndef VIDEO_SOURCE_ODB_HXX
#define VIDEO_SOURCE_ODB_HXX

#include <odb/version.hxx>


#include <odb/pre.hxx>

#include "video_source.h"

#include <memory>
#include <cstddef>
#include <utility>

#include <odb/core.hxx>
#include <odb/traits.hxx>
#include <odb/callback.hxx>
#include <odb/wrapper-traits.hxx>
#include <odb/pointer-traits.hxx>
#include <odb/container-traits.hxx>
#include <odb/no-op-cache-traits.hxx>
#include <odb/result.hxx>
#include <odb/simple-object-result.hxx>

#include <odb/details/unused.hxx>
#include <odb/details/shared-ptr.hxx>

namespace odb
{
  // VideoSource
  //
  template <>
  struct class_traits< ::db::VideoSource >
  {
    static const class_kind kind = class_object;
  };

  template <>
  class access::object_traits< ::db::VideoSource >
  {
    public:
    typedef ::db::VideoSource object_type;
    typedef ::db::VideoSource* pointer_type;
    typedef odb::pointer_traits<pointer_type> pointer_traits;

    static const bool polymorphic = false;

    typedef long unsigned int id_type;

    static const bool auto_id = true;

    static const bool abstract = false;

    static id_type
    id (const object_type&);

    typedef
    no_op_pointer_cache_traits<pointer_type>
    pointer_cache_traits;

    typedef
    no_op_reference_cache_traits<object_type>
    reference_cache_traits;

    static void
    callback (database&, object_type&, callback_event);

    static void
    callback (database&, const object_type&, callback_event);
  };
}

#include <odb/details/buffer.hxx>

#include <odb/mysql/version.hxx>
#include <odb/mysql/forward.hxx>
#include <odb/mysql/binding.hxx>
#include <odb/mysql/mysql-types.hxx>
#include <odb/mysql/query.hxx>

namespace odb
{
  // VideoSource
  //
  template <typename A>
  struct query_columns< ::db::VideoSource, id_mysql, A >
  {
    // id
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    id_type_;

    static const id_type_ id;

    // name
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    name_type_;

    static const name_type_ name;

    // address
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    address_type_;

    static const address_type_ address;

    // videoServerId
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    videoServerId_type_;

    static const videoServerId_type_ videoServerId;

    // location
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    location_type_;

    static const location_type_ location;

    // hasPtz
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        bool,
        mysql::id_long >::query_type,
      mysql::id_long >
    hasPtz_type_;

    static const hasPtz_type_ hasPtz;

    // isDetectable
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    isDetectable_type_;

    static const isDetectable_type_ isDetectable;

    // detectPointId
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_long >::query_type,
      mysql::id_long >
    detectPointId_type_;

    static const detectPointId_type_ detectPointId;

    // ptzControl
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    ptzControl_type_;

    static const ptzControl_type_ ptzControl;

    // videoStatus
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    videoStatus_type_;

    static const videoStatus_type_ videoStatus;

    // roadId
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_long >::query_type,
      mysql::id_long >
    roadId_type_;

    static const roadId_type_ roadId;

    // isEnable
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        bool,
        mysql::id_long >::query_type,
      mysql::id_long >
    isEnable_type_;

    static const isEnable_type_ isEnable;

    // isChange
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        bool,
        mysql::id_long >::query_type,
      mysql::id_long >
    isChange_type_;

    static const isChange_type_ isChange;
  
    // videoType
    //
    typedef
        mysql::query_column<
        mysql::value_traits<
        int,
        mysql::id_long >::query_type,
        mysql::id_long >
        videoType_type_;

    static const videoType_type_ videoType;

    // domePreset
    //
    typedef
        mysql::query_column<
        mysql::value_traits<
        long unsigned int,
        mysql::id_long >::query_type,
        mysql::id_long >
        domePreset_type_;

    static const domePreset_type_ domePreset;

    // groupId
    //
    typedef
        mysql::query_column<
        mysql::value_traits<
        long unsigned int,
        mysql::id_long >::query_type,
        mysql::id_long >
        groupId_type_;

    static const groupId_type_ groupId;

    // groupUUID
    //
    typedef
        mysql::query_column<
        mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
        mysql::id_string >
        groupUUID_type_;

    static const groupUUID_type_ groupUUID;
  };

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::id_type_
  query_columns< ::db::VideoSource, id_mysql, A >::
  id (A::table_name, "`id`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::name_type_
  query_columns< ::db::VideoSource, id_mysql, A >::
  name (A::table_name, "`name`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::address_type_
  query_columns< ::db::VideoSource, id_mysql, A >::
  address (A::table_name, "`address`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::videoServerId_type_
  query_columns< ::db::VideoSource, id_mysql, A >::
  videoServerId (A::table_name, "`access_front_end_id`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::location_type_
  query_columns< ::db::VideoSource, id_mysql, A >::
  location (A::table_name, "`location`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::hasPtz_type_
  query_columns< ::db::VideoSource, id_mysql, A >::
  hasPtz (A::table_name, "`has_ptz`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::isDetectable_type_
  query_columns< ::db::VideoSource, id_mysql, A >::
  isDetectable (A::table_name, "`is_detectable`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::detectPointId_type_
  query_columns< ::db::VideoSource, id_mysql, A >::
  detectPointId (A::table_name, "`detect_point_id`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::ptzControl_type_
  query_columns< ::db::VideoSource, id_mysql, A >::
  ptzControl (A::table_name, "`ptz_control`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::videoStatus_type_
  query_columns< ::db::VideoSource, id_mysql, A >::
  videoStatus (A::table_name, "`video_status`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::roadId_type_
  query_columns< ::db::VideoSource, id_mysql, A >::
  roadId (A::table_name, "`road_id`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::isEnable_type_
  query_columns< ::db::VideoSource, id_mysql, A >::
  isEnable (A::table_name, "`is_enable`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::isChange_type_
  query_columns< ::db::VideoSource, id_mysql, A >::
  isChange (A::table_name, "`is_change`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::videoType_type_
      query_columns< ::db::VideoSource, id_mysql, A >::
      videoType(A::table_name, "`video_type`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::domePreset_type_
      query_columns< ::db::VideoSource, id_mysql, A >::
      domePreset(A::table_name, "`dome_preset`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::groupId_type_
      query_columns< ::db::VideoSource, id_mysql, A >::
      groupId(A::table_name, "`group_id`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoSource, id_mysql, A >::groupUUID_type_
      query_columns< ::db::VideoSource, id_mysql, A >::
      groupUUID(A::table_name, "`group_uuid`", 0);

  template <typename A>
  struct pointer_query_columns< ::db::VideoSource, id_mysql, A >:
    query_columns< ::db::VideoSource, id_mysql, A >
  {
  };

  template <>
  class access::object_traits_impl< ::db::VideoSource, id_mysql >:
    public access::object_traits< ::db::VideoSource >
  {
    public:
    struct id_image_type
    {
      unsigned long long id_value;
      my_bool id_null;

      std::size_t version;
    };

    struct image_type
    {
      // id
      //
      unsigned long long id_value;
      my_bool id_null;

      // name
      //
      details::buffer name_value;
      unsigned long name_size;
      my_bool name_null;

      // address
      //
      details::buffer address_value;
      unsigned long address_size;
      my_bool address_null;

      // videoServerId
      //
      unsigned long long videoServerId_value;
      my_bool videoServerId_null;

      // location
      //
      details::buffer location_value;
      unsigned long location_size;
      my_bool location_null;

      // hasPtz
      //
      int hasPtz_value;
      my_bool hasPtz_null;

      // isDetectable
      //
      int isDetectable_value;
      my_bool isDetectable_null;

      // detectPointId
      //
      int detectPointId_value;
      my_bool detectPointId_null;

      // ptzControl
      //
      details::buffer ptzControl_value;
      unsigned long ptzControl_size;
      my_bool ptzControl_null;

      // videoStatus
      //
      int videoStatus_value;
      my_bool videoStatus_null;

      // roadId
      //
      int roadId_value;
      my_bool roadId_null;

      // isEnable
      //
      int isEnable_value;
      my_bool isEnable_null;

      // isChange
      //
      int isChange_value;
      my_bool isChange_null;

      // videoType
      //
      int videoType_value;
      my_bool videoType_null;

      // domePreset
      //
      int domePreset_value;
      my_bool domePreset_null;

      // groupId
      //
      int groupId_value;
      my_bool groupId_null;

      // groupUUID
      //
      details::buffer groupUUID_value;
      unsigned long groupUUID_size;
      my_bool groupUUID_null;

      std::size_t version;
    };

    struct extra_statement_cache_type;

    using object_traits<object_type>::id;

    static id_type
    id (const id_image_type&);

    static id_type
    id (const image_type&);

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&,
          mysql::statement_kind);

    static void
    bind (MYSQL_BIND*, id_image_type&);

    static bool
    init (image_type&,
          const object_type&,
          mysql::statement_kind);

    static void
    init (object_type&,
          const image_type&,
          database*);

    static void
    init (id_image_type&, const id_type&);

    typedef mysql::object_statements<object_type> statements_type;

    typedef mysql::query_base query_base_type;

    static const std::size_t column_count = 17UL;
    static const std::size_t id_column_count = 1UL;
    static const std::size_t inverse_column_count = 0UL;
    static const std::size_t readonly_column_count = 1UL;
    static const std::size_t managed_optimistic_column_count = 0UL;

    static const std::size_t separate_load_column_count = 0UL;
    static const std::size_t separate_update_column_count = 0UL;

    static const bool versioned = false;

    static const char persist_statement[];
    static const char find_statement[];
    static const char update_statement[];
    static const char erase_statement[];
    static const char query_statement[];
    static const char erase_query_statement[];

    static const char table_name[];

    static void
    persist (database&, object_type&);

    static pointer_type
    find (database&, const id_type&);

    static bool
    find (database&, const id_type&, object_type&);

    static bool
    reload (database&, object_type&);

    static void
    update (database&, const object_type&);

    static void
    erase (database&, const id_type&);

    static void
    erase (database&, const object_type&);

    static result<object_type>
    query (database&, const query_base_type&);

    static unsigned long long
    erase_query (database&, const query_base_type&);

    public:
    static bool
    find_ (statements_type&,
           const id_type*);

    static void
    load_ (statements_type&,
           object_type&,
           bool reload);
  };

  template <>
  class access::object_traits_impl< ::db::VideoSource, id_common >:
    public access::object_traits_impl< ::db::VideoSource, id_mysql >
  {
  };

  // VideoSource
  //
}

#include "video_source-odb.ixx"

#include <odb/post.hxx>

#endif // VIDEO_SOURCE_ODB_HXX
