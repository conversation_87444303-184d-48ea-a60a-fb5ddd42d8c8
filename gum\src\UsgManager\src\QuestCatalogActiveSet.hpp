#ifndef QUESTCATALOGACTIVESET_HPP_
#define QUESTCATALOGACTIVESET_HPP_

#include <map>
#include <string>

#include <boost/noncopyable.hpp>
#include <boost/thread/mutex.hpp>
#include <boost/thread/condition.hpp>
#include <boost/shared_ptr.hpp>


#include "../include/UsgManagerCfg.hpp"

namespace usg {

    class CQuestCatalogActive;

    class USGMANAGER_PRIVATE CQuestCatalogActiveSet : public boost::noncopyable
    {
        friend class CQuestCatalogActive;

        CQuestCatalogActiveSet();
    public:
        virtual ~CQuestCatalogActiveSet();

    public:
        static bool init();
        static bool fini();
        static CQuestCatalogActiveSet* get();

    public:
        ///< sip查询返回错误
        bool removeActive( const std::string &sid, const std::string &devid );
        ///< 本地，请求查询后调用
        bool creatActive( const std::string &sid, const std::string &devid, int waitSec );
        ///< 本地，系统推出时调用
        bool cleanActives();

    private:
//	void pushCatalogInfos( const std::string &sid, const std::string &devid );
        ///< 检查线程调用，超时时调用
        bool cutCalalog( const std::string &sid, const std::string &devid );

    private:
        static CQuestCatalogActiveSet* m_catalogActiveSet;

    private:
        std::map< std::pair< std::string, std::string >, CQuestCatalogActive* > m_atives;
        boost::mutex m_mutex;
    };

    class USGMANAGER_PRIVATE CQuestCatalogActive
    {
    public:
        CQuestCatalogActive( const std::string &sid, const std::string devid, int time );
        virtual ~CQuestCatalogActive();

    public:
        bool wait();
        bool quit();
        bool clean();

    private:
        void control();
        void reQuestCalalog();

    private:
        boost::shared_ptr< boost::thread > m_waitThread;
        std::string m_sid;
        std::string m_devid;
        int m_time;
        bool m_isWait;
        bool m_isClean;
        boost::mutex m_mutex;
        boost::condition m_cond;
    };

}

#endif
