#ifndef DDCPDOSESSION_HPP_
#define DDCPDOSESSION_HPP_

#include <map>
#include <vector>
#include <string>

#include <pjlib.h>
#include <pjsip.h>

#include "../include/UsgSipStackItf.hpp"

namespace usg {

class CDdcpDoSession : public IDdcpDoSession
{
public:
    CDdcpDoSession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from );
    CDdcpDoSession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from,const std::string& xmlType );
    virtual ~CDdcpDoSession();

public:
    CDdcpDoSession *setHandler( ICtxHandler *handler );

public:
    virtual bool ddcpDo( std::string &sid, const std::string &sipUri, const std::string &result );
	virtual bool ddcpDo( std::string &sid, const std::string &callid, const std::string &sipUri, const std::string &result );
    virtual bool answer( pjsip_rx_data *rdata, int status, const std::string &result );
    virtual bool ddcpDoByCallId( 
        const pjsip_uri *target,
        const pjsip_from_hdr *from,
        const pjsip_to_hdr *to,
        const pjsip_cid_hdr *call_id,       
        int cseq,
        const std::string &result );
    virtual bool ddcpDoByCallId( 
        const pjsip_uri *target,
        const pjsip_from_hdr *from,
        const pjsip_to_hdr *to,
        const pjsip_cid_hdr *call_id,   
		const pjsip_contact_hdr *contact,
        int cseq,
        const std::string &xmlType,
        const std::string &result );

	virtual bool setSipUrl( const std::string catalogSipUrl );

public:
    bool onDdcpDo( pjsip_rx_data *rdata );
    bool onAnswer( pjsip_rx_data *rdata );

private:
    std::string createSid( pjsip_cid_hdr *cid, pjsip_cseq_hdr *cseq );
	bool ddcpDo( std::string &sid, const std::string &sipUri, const std::string &result, int cseqid );
	bool ddcpDo( std::string &sid, const std::string &sipUri, const std::string &result, int cseqid, const std::string callid );
	bool answerRtsp( pjsip_rx_data *rdata, int status, const std::string &result );

private:
    pj_pool_t      *m_pool;
    pjsip_endpoint *m_endPoint;

private:
    ICtxHandler *m_handler;
    const std::string m_from;
    const std::string m_xmlType;
	std::string m_catalogSipUrl;

private:
	std::map< std::string, std::vector< std::string > > m_queryCatalog;
};

}

#endif // DDCPDOSESSION_HPP_
