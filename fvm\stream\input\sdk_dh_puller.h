/**
 * Project FVM
 */
#pragma once

#include "stream_input.h"
#include "platform_puller.h"
#include "stream/element/circular_queue.h"

/**
 * @brief: 大华SDK取流
 */
namespace fvm::stream
{
    class SDKDHPuller : public PlatformPuller
    {
    public:

        SDKDHPuller(FrontPlatformPtr platform) : PlatformPuller(platform){};

        /**
         * @brief 获取错误信息
         */
        std::string getLastError(void) override;

    private:
        void initOptions() override;

        /**
         * @brief 打开视频流、分析视频格式
         */
        bool open() override;

        /**
         * @brief 读取每帧数据
         */
        bool read();

        /**
         * @brief 关闭视频源，释放内存
         */
        void close() override;

        /**
         * @brief 处理线程：访问视频源、搬运视频流、打开视频流、读取每帧数据
         */
        void process() override;

         /**
         * @brief 控制云台 
         * @param[in] action:  云台动作 1：向左 2：向右 3：向上 4：向下 5：变倍短 6：变倍长 7：聚焦近 8：聚焦远 9：光圈小 10：光圈大 11：灯光关 12：灯光开
         * @param[in] step:  步长  主要用于控制云台转动方向的操作，1-8为步长值，1为最小步长，8为最大步长
         */
        bool controlPtz(int action, int step);

    private:

        /**
         * @brief 接收SDK实时流数据，SDK接口回调中调用
         */
        void receiveRealData(uint8_t* buf, int buf_size);

        /**
         * 调用相机预置位
         */
        bool callCameraPreset(int presetId) override;

        /**
         * 输入流是否能调用预置位
         */
        //bool isPtzCapable() override;

    public:
        bool getPtzPosition(double& x, double& y, double& z, const std::optional<int>& actPresetId) override;

        bool isPtzCapable() override;

    private:
        uint8_t* ioContextBuffer = nullptr;        //!< 视频数据缓冲BUFF
        AVIOContext* pb = nullptr;                 //!< 视频数据缓冲IO
        CCircularQueue streamQueue;                //!< SDK流数据缓存
        int packetErrorCount = 0;                  //!< av_read_frame返回类似-2147379531错误码次数
    };
}
