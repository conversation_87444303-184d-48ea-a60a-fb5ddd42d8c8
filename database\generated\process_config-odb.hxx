// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#ifndef PROCESS_CONFIG_ODB_HXX
#define PROCESS_CONFIG_ODB_HXX

#include <odb/version.hxx>


#include <odb/pre.hxx>

#include "process_config.h"

#include <memory>
#include <cstddef>
#include <utility>

#include <odb/core.hxx>
#include <odb/traits.hxx>
#include <odb/callback.hxx>
#include <odb/wrapper-traits.hxx>
#include <odb/pointer-traits.hxx>
#include <odb/container-traits.hxx>
#include <odb/no-op-cache-traits.hxx>
#include <odb/result.hxx>
#include <odb/simple-object-result.hxx>

#include <odb/details/unused.hxx>
#include <odb/details/shared-ptr.hxx>

namespace odb
{
  // ProcessConfig
  //
  template <>
  struct class_traits< ::db::ProcessConfig >
  {
    static const class_kind kind = class_object;
  };

  template <>
  class access::object_traits< ::db::ProcessConfig >
  {
    public:
    typedef ::db::ProcessConfig object_type;
    typedef ::db::ProcessConfig* pointer_type;
    typedef odb::pointer_traits<pointer_type> pointer_traits;

    static const bool polymorphic = false;

    typedef long unsigned int id_type;

    static const bool auto_id = true;

    static const bool abstract = false;

    static id_type
    id (const object_type&);

    typedef
    no_op_pointer_cache_traits<pointer_type>
    pointer_cache_traits;

    typedef
    no_op_reference_cache_traits<object_type>
    reference_cache_traits;

    static void
    callback (database&, object_type&, callback_event);

    static void
    callback (database&, const object_type&, callback_event);
  };
}

#include <odb/details/buffer.hxx>

#include <odb/mysql/version.hxx>
#include <odb/mysql/forward.hxx>
#include <odb/mysql/binding.hxx>
#include <odb/mysql/mysql-types.hxx>
#include <odb/mysql/query.hxx>

namespace odb
{
  // ProcessConfig
  //
  template <typename A>
  struct query_columns< ::db::ProcessConfig, id_mysql, A >
  {
    // id
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    id_type_;

    static const id_type_ id;

    // processId
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    processId_type_;

    static const processId_type_ processId;

    // processPort
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    processPort_type_;

    static const processPort_type_ processPort;

    // startChannel
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    startChannel_type_;

    static const startChannel_type_ startChannel;

    // endChannel
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    endChannel_type_;

    static const endChannel_type_ endChannel;

    // networkCard
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    networkCard_type_;

    static const networkCard_type_ networkCard;

    // gpuCard
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    gpuCard_type_;

    static const gpuCard_type_ gpuCard;

    // fvmPort
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    fvmPort_type_;

    static const fvmPort_type_ fvmPort;

    // streamType
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    streamType_type_;

    static const streamType_type_ streamType;

    // fvmStatus
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    fvmStatus_type_;

    static const fvmStatus_type_ fvmStatus;

    // fvmUpdateTime
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    fvmUpdateTime_type_;

    static const fvmUpdateTime_type_ fvmUpdateTime;

    // ivaStatus
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    ivaStatus_type_;

    static const ivaStatus_type_ ivaStatus;

    // ivaUpdateTime
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    ivaUpdateTime_type_;

    static const ivaUpdateTime_type_ ivaUpdateTime;
  };

  template <typename A>
  const typename query_columns< ::db::ProcessConfig, id_mysql, A >::id_type_
  query_columns< ::db::ProcessConfig, id_mysql, A >::
  id (A::table_name, "`id`", 0);

  template <typename A>
  const typename query_columns< ::db::ProcessConfig, id_mysql, A >::processId_type_
  query_columns< ::db::ProcessConfig, id_mysql, A >::
  processId (A::table_name, "`process_id`", 0);

  template <typename A>
  const typename query_columns< ::db::ProcessConfig, id_mysql, A >::processPort_type_
  query_columns< ::db::ProcessConfig, id_mysql, A >::
  processPort (A::table_name, "`process_port`", 0);

  template <typename A>
  const typename query_columns< ::db::ProcessConfig, id_mysql, A >::startChannel_type_
  query_columns< ::db::ProcessConfig, id_mysql, A >::
  startChannel (A::table_name, "`start_channel`", 0);

  template <typename A>
  const typename query_columns< ::db::ProcessConfig, id_mysql, A >::endChannel_type_
  query_columns< ::db::ProcessConfig, id_mysql, A >::
  endChannel (A::table_name, "`end_channel`", 0);

  template <typename A>
  const typename query_columns< ::db::ProcessConfig, id_mysql, A >::networkCard_type_
  query_columns< ::db::ProcessConfig, id_mysql, A >::
  networkCard (A::table_name, "`network_card`", 0);

  template <typename A>
  const typename query_columns< ::db::ProcessConfig, id_mysql, A >::gpuCard_type_
  query_columns< ::db::ProcessConfig, id_mysql, A >::
  gpuCard (A::table_name, "`gpu_card`", 0);

  template <typename A>
  const typename query_columns< ::db::ProcessConfig, id_mysql, A >::fvmPort_type_
  query_columns< ::db::ProcessConfig, id_mysql, A >::
  fvmPort (A::table_name, "`fvm_port`", 0);

  template <typename A>
  const typename query_columns< ::db::ProcessConfig, id_mysql, A >::streamType_type_
  query_columns< ::db::ProcessConfig, id_mysql, A >::
  streamType (A::table_name, "`streamType`", 0);

  template <typename A>
  const typename query_columns< ::db::ProcessConfig, id_mysql, A >::fvmStatus_type_
  query_columns< ::db::ProcessConfig, id_mysql, A >::
  fvmStatus (A::table_name, "`fvm_status`", 0);

  template <typename A>
  const typename query_columns< ::db::ProcessConfig, id_mysql, A >::fvmUpdateTime_type_
  query_columns< ::db::ProcessConfig, id_mysql, A >::
  fvmUpdateTime (A::table_name, "`fvm_update_time`", 0);

  template <typename A>
  const typename query_columns< ::db::ProcessConfig, id_mysql, A >::ivaStatus_type_
  query_columns< ::db::ProcessConfig, id_mysql, A >::
  ivaStatus (A::table_name, "`iva_status`", 0);

  template <typename A>
  const typename query_columns< ::db::ProcessConfig, id_mysql, A >::ivaUpdateTime_type_
  query_columns< ::db::ProcessConfig, id_mysql, A >::
  ivaUpdateTime (A::table_name, "`iva_update_time`", 0);

  template <typename A>
  struct pointer_query_columns< ::db::ProcessConfig, id_mysql, A >:
    query_columns< ::db::ProcessConfig, id_mysql, A >
  {
  };

  template <>
  class access::object_traits_impl< ::db::ProcessConfig, id_mysql >:
    public access::object_traits< ::db::ProcessConfig >
  {
    public:
    struct id_image_type
    {
      unsigned long long id_value;
      my_bool id_null;

      std::size_t version;
    };

    struct image_type
    {
      // id
      //
      unsigned long long id_value;
      my_bool id_null;

      // processId
      //
      unsigned long long processId_value;
      my_bool processId_null;

      // processPort
      //
      int processPort_value;
      my_bool processPort_null;

      // startChannel
      //
      int startChannel_value;
      my_bool startChannel_null;

      // endChannel
      //
      int endChannel_value;
      my_bool endChannel_null;

      // networkCard
      //
      details::buffer networkCard_value;
      unsigned long networkCard_size;
      my_bool networkCard_null;

      // gpuCard
      //
      int gpuCard_value;
      my_bool gpuCard_null;

      // fvmPort
      //
      int fvmPort_value;
      my_bool fvmPort_null;

      // streamType
      //
      int streamType_value;
      my_bool streamType_null;

      // fvmStatus
      //
      int fvmStatus_value;
      my_bool fvmStatus_null;

      // fvmUpdateTime
      //
      details::buffer fvmUpdateTime_value;
      unsigned long fvmUpdateTime_size;
      my_bool fvmUpdateTime_null;

      // ivaStatus
      //
      int ivaStatus_value;
      my_bool ivaStatus_null;

      // ivaUpdateTime
      //
      details::buffer ivaUpdateTime_value;
      unsigned long ivaUpdateTime_size;
      my_bool ivaUpdateTime_null;

      std::size_t version;
    };

    struct extra_statement_cache_type;

    using object_traits<object_type>::id;

    static id_type
    id (const id_image_type&);

    static id_type
    id (const image_type&);

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&,
          mysql::statement_kind);

    static void
    bind (MYSQL_BIND*, id_image_type&);

    static bool
    init (image_type&,
          const object_type&,
          mysql::statement_kind);

    static void
    init (object_type&,
          const image_type&,
          database*);

    static void
    init (id_image_type&, const id_type&);

    typedef mysql::object_statements<object_type> statements_type;

    typedef mysql::query_base query_base_type;

    static const std::size_t column_count = 13UL;
    static const std::size_t id_column_count = 1UL;
    static const std::size_t inverse_column_count = 0UL;
    static const std::size_t readonly_column_count = 0UL;
    static const std::size_t managed_optimistic_column_count = 0UL;

    static const std::size_t separate_load_column_count = 0UL;
    static const std::size_t separate_update_column_count = 0UL;

    static const bool versioned = false;

    static const char persist_statement[];
    static const char find_statement[];
    static const char update_statement[];
    static const char erase_statement[];
    static const char query_statement[];
    static const char erase_query_statement[];

    static const char table_name[];

    static void
    persist (database&, object_type&);

    static pointer_type
    find (database&, const id_type&);

    static bool
    find (database&, const id_type&, object_type&);

    static bool
    reload (database&, object_type&);

    static void
    update (database&, const object_type&);

    static void
    erase (database&, const id_type&);

    static void
    erase (database&, const object_type&);

    static result<object_type>
    query (database&, const query_base_type&);

    static unsigned long long
    erase_query (database&, const query_base_type&);

    public:
    static bool
    find_ (statements_type&,
           const id_type*);

    static void
    load_ (statements_type&,
           object_type&,
           bool reload);
  };

  template <>
  class access::object_traits_impl< ::db::ProcessConfig, id_common >:
    public access::object_traits_impl< ::db::ProcessConfig, id_mysql >
  {
  };

  // ProcessConfig
  //
}

#include "process_config-odb.ixx"

#include <odb/post.hxx>

#endif // PROCESS_CONFIG_ODB_HXX
