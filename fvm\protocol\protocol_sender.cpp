/**
 * Project FVM
 */

#include "protocol_sender.h"
#include "data/data_manager.h"
#include "protocol_manager.h"
#include "util/protocol_util.h"
#include "util/json_util.h"
#include "ailog.h"

namespace fvm::protocol
{
    using namespace network;
    using namespace network::util;
    using std::optional;

    static bool configDetectParamPresets(const VideoSourceInfoPtr& videoSrcInfoPtr, std::vector<Preset>& presets);

    bool sendAlgorithmParam(const AlgorithmParam& data, int processID)
    {
        auto msg  = network::generateProtocol<AlgorithmParam>(data, ProtocolType::UDP_ALGORITHM_PARAM, network::SessionType::FVM, "vtParam");
        if ( network::sendDataTo(network::SessionType::IVA, msg, processID) )
        {
            ai::LogInfo << "To IVA " << processID << " " << msg;
            return true;
        }
        return false;
    }

    bool sendBasicChannelConfig(const VecChannelBasicConf& data, int processID)
    {
        return PROTOCOL_MANAGER.sendToIVA(network::ProtocolType::UDP_BASIC_CONFIG, data, processID);
    }

    /**
     * @brief 组AlgorithmParam数据包，并发送给iva
     * @param[in]  processId: 进程号，nullopt表示所有进程
     */
    void sendAlgorithmParam(const optional<int> &processId)
    {
        data::AlgoParamsVec AlgoParamsData = DATA_MANAGER.getAlgoParams();
        int oldPlan = 0;
        AlgorithmParam algorithmParam;
        AlgorithmParamItem paramItem;
        for (const auto &data : AlgoParamsData) {
            if (oldPlan != (int)data.planId){
                if (oldPlan > 0) {
                    paramItem.planId = oldPlan;
                    algorithmParam.vtParam.emplace_back(paramItem);
                    paramItem.param.clear();
                }
                oldPlan = (int) data.planId;
            }
            Param paramValue(data.paramKey,data.paramValue);
            paramItem.param.emplace_back(paramValue);
        }

        if (oldPlan > 0){
            paramItem.planId = oldPlan;
            algorithmParam.vtParam.emplace_back(paramItem);
        }

        if (!algorithmParam.vtParam.empty())
        {
            //! processId为nullopt时发送给所有进程，否则只发送给指定进程,进程表为空时，表示是单进程
            auto processConfigs = DATA_MANAGER.getProcessConfigs(processId);
            if (processConfigs.empty())
            {
                bool ret = sendAlgorithmParam(algorithmParam, DEFAULT_CLIENT_ID);
                if (!ret)
                    ai::LogError << "sendAlgorithmParam failed ret = " << (int)ret;
            }else
            {
                for (const auto& process : processConfigs){
                    bool ret = sendAlgorithmParam(algorithmParam, (int)process.getProcessId());
                    if (!ret)
                        ai::LogError << "sendAlgorithmParam failed ret = " << (int)ret;
                }
            }
        }
    }

    /**
     * @brief 组SystemConfig数据包，并发送给iva
     * @param[in]  processId: 进程号，nullopt表示所有进程
     */
    void sendSystemConfig(const optional<int> &processId)
    {
        SystemConfig systemConfig;
        systemConfig.localIP = DATA_MANAGER.getLocalIP();
        systemConfig.platIP = DATA_MANAGER.getPlatformIP();
        auto eventTypeData = DATA_MANAGER.getEventTypes();
        for (const auto &data : eventTypeData) {
            EventType type;
            type.eventType = (int) data.getId();
            type.remark= data.getEvtTypeRemark();
            systemConfig.eventType.emplace_back(type);
        }

        auto checkAreaTypeData = DATA_MANAGER.getCheckAreaType();
        for (const auto &data : checkAreaTypeData) {
            CheckAreaType type;
            type.id = (int) data.getId();
            type.remark = data.getRemark();
            systemConfig.checkAreaType.emplace_back(type);
        }

        systemConfig.isShowId = (short) stoi(DATA_MANAGER.getParamProgData("ShowTrackId", "1"));
        systemConfig.offsetRetunTime = stoul(DATA_MANAGER.getParamProgData("WaitDeviation", "3")) * 60;
        systemConfig.roiAlgLevel = stoul(DATA_MANAGER.getParamProgData("roi_alg_level", "0"));
        systemConfig.laneAlgLevel = stoul(DATA_MANAGER.getParamProgData("lane_alg_level", "0"));
        systemConfig.checkAreaAlgLevel = stoul(DATA_MANAGER.getParamProgData("checkarea_alg_level", "0"));
        systemConfig.ivaPreRecord = DATA_MANAGER.getParamProgData(data::IVA_PRE_RECORD, 2);
        systemConfig.ivaRecordTime = DATA_MANAGER.getParamProgData(data::IVA_RECORD_TIME, 5);
        auto evtRecordType = DATA_MANAGER.getParamProgData(data::EVT_RECORD_TYPE, 0);
        if (evtRecordType == static_cast<int>(data::EvtRecordType::FVM))
        {
            systemConfig.ivaPreRecord = 0;
            systemConfig.ivaRecordTime = 0;
        }

        //! processId为nullopt时发送给所有进程，否则只发送给指定进程
        auto processConfigs = DATA_MANAGER.getProcessConfigs(processId);
        if (processConfigs.empty()){
            auto ret = PROTOCOL_MANAGER.sendToIVA(network::ProtocolType::UDP_SYSTEM_CONFIG, systemConfig);
            if (!ret)
                ai::LogError << "sendSystemConfig failed ret = " << (int)ret;
        }else{
            for (const auto& process : processConfigs){
                auto ret = PROTOCOL_MANAGER.sendToIVA(network::ProtocolType::UDP_SYSTEM_CONFIG, systemConfig, (int)process.getProcessId());
                if (!ret)
                    ai::LogError << "sendSystemConfig failed ret = " << (int)ret;
            }
        }
    }

    /**
     * @brief 判断当前通道是否是iva检测偏移。
     * @note  iva检测偏移条件：1、通道所有预置位都没有position字段 2、web配置云台可控
     */
    bool isIvaCheckOffset(const VideoSourceInfoPtr& videoSourceInfoPtr)
    {
        if (!videoSourceInfoPtr || !videoSourceInfoPtr->videoSourcePtr)
            return false;

        //! 视频所有预置位的position都没有值
        for (const auto& [presetId,presetInfo] : videoSourceInfoPtr->presetInfosMap)
        {
            if (!presetInfo.presetPtr)
                return false;
           if (!presetInfo.presetPtr->getPosition().empty())
               return false;
        }
        //! 云台可控
        if (videoSourceInfoPtr->videoSourcePtr->getHasPtz())
            return true;

        return false;
    }

    /**
     * @brief 组对应个进程下所有通道的BasicConfig数据包
     * @param[in]  processId: 进程号，nullopt表示所有进程
     */
    bool sendProcessBasicConfigs(const optional<int> &processId)
    {
        std::map<int, VecChannelBasicConf> basicConfigs;    //!< key: processId
        auto channelSources = DATA_MANAGER.getProcessChannelsSource(processId);
        for (auto& [channelId, videoSourceInfo] : channelSources) 
		{
            auto currChannelProcessId = DATA_MANAGER.getChannelsProcessId(channelId);
            if (processId.has_value())
            {
                if (currChannelProcessId != processId.value())
                    continue;
            }

            if (nullptr == videoSourceInfo) {
                ai::LogError << "get videoSourceInfo failed ! processId:" << processId.value_or(-1);
                return false;
            }
            if (nullptr == videoSourceInfo->videoSourcePtr) {
                ai::LogError << "get videoSource failed ! processId:" << processId.value_or(-1);
                return false;
            }
            ChannelBasicConf basicConf;
            basicConf.channelId = (int) videoSourceInfo->channelId;
            basicConf.status = SINGLE;  // ？

            SingleInfo preset;
            preset.videoId = (int) videoSourceInfo->videoSourcePtr->getId();  //!< 一个通道默认只有一个video
            preset.hasPtz = isIvaCheckOffset(videoSourceInfo);
            preset.presetId.clear();
            for (const auto &presetInfo : videoSourceInfo->presetInfosMap) {
                preset.presetId.emplace_back(presetInfo.first);
            }
            basicConf.videoInfo.emplace_back(preset);
            if (basicConfigs.end() != basicConfigs.find(currChannelProcessId)) {
                basicConfigs[currChannelProcessId].vtChannelBasicConf.emplace_back(basicConf);
            } else {
                VecChannelBasicConf vecChannelBasicConf;
                vecChannelBasicConf.vtChannelBasicConf.emplace_back(basicConf);
                basicConfigs.emplace(std::make_pair(currChannelProcessId, vecChannelBasicConf));
            }
        }

        for (const auto& basicConf : basicConfigs){
            auto ret = sendBasicChannelConfig(basicConf.second, basicConf.first);
            if (!ret)
                ai::LogError << "sendBasicChannelConfig failed ret = " << (int)ret;
        }

        return true;
    }

    /**
      * @brief 组ChannelDetect数据包，并发送到对应的进程
      * @param[in] channelId: 通道号
      */
    void sendChannelDetectParam(int channelId)
    {
        auto channelSources = DATA_MANAGER.getChannelSource(channelId);
        if (!channelSources.has_value()){
            ai::LogError << "get videoSourceInfo failed ";
            return;
        }
        auto channelSourcesPtr = channelSources.value();
        if (nullptr == channelSourcesPtr->videoSourcePtr){
            ai::LogError << "get videoSource failed ";
            return;
        }

        ChannelDetectParam channelDetectParam;
        channelDetectParam.channelId = channelId;
        channelDetectParam.videoId = (int) channelSourcesPtr->videoSourcePtr->getId();
        auto processId = DATA_MANAGER.getChannelsProcessId(channelId);

        if (!configDetectParamPresets(channelSourcesPtr, channelDetectParam.presets))
            return;

        auto msg = network::generateProtocol<ChannelDetectParam>(channelDetectParam, 
            network::ProtocolType::UDP_CHANNEL_DETECT, network::SessionType::FVM);  //"ChannelDetectParam"
        auto returnVal = network::sendDataTo(network::SessionType::IVA, msg, processId);
        
        if (!returnVal && network::lastErrorCode())
        {
            ai::LogInfo << "IVA msg sent failed! protocol: " << "ChannelDetectParam "
                << " process: " << processId
                << " code: " << network::lastErrorCode() << " err: " << network::lastErrorMsg();
        }
        else if ( returnVal )
        {
            ai::LogInfo << "To IVA " << processId << " " << msg;
        }
    }

    /**
     * @brief 组DetectParam presets字段数据包
     * @param[in]  videoSrcInfoPtr: 视频资源信息
     * @param[out] presets 返回组好的presets字段数据包
     */
    static bool configDetectParamPresets(const VideoSourceInfoPtr& videoSrcInfoPtr, std::vector<Preset>& presets)
    {
        for (const auto& presetData : videoSrcInfoPtr->presetInfosMap){
            if (nullptr == presetData.second.presetPtr){
                ai::LogError << "get preset failed ";
                return false;
            }
            Preset preset;
            preset.presetId = presetData.first;
            preset.detectParam.detectType = presetData.second.presetPtr->getDetectType();
            if (presetData.second.presetPtr->getCheckArea().empty())
                continue;

            std::vector<Point> checkAreaPoints = deserializePoints(presetData.second.presetPtr->getCheckArea());
            preset.detectParam.area = transPtsToRect(checkAreaPoints);

            for (const auto& offsetAreaData : presetData.second.offsetAreasMap)
            {
                auto offsetAreaPtr = offsetAreaData.second;
                if (nullptr == offsetAreaPtr){
                    ai::LogError << "get offsetArea failed ";
                    return false;
                }
                preset.detectParam.deviation = deserializePoints(offsetAreaPtr->getOffsetArea());
                break;        //!< 一个预置位默认只包含一个偏移检测区
            }
            preset.detectParam.paramPlanId = (int)presetData.second.presetPtr->getParamPlanId();
            preset.detectParam.param = deserializeParamList(presetData.second.presetPtr->getParam());

            for (const auto& [roiId, roiInfo] : presetData.second.roiInfosMap)
            {
                auto& roiPtr = roiInfo.roiPtr;
                if (nullptr == roiPtr){
                    ai::LogError << "get roi failed ";
                    return false;
                }
                if(roiPtr->getCheckArea().empty()) //!< 检测区为空，就不发送配置消息
                    continue;
                ROINode roiNode;
                roiNode.eventProperty = std::stoi(roiPtr->getEventProperty());
                roiNode.paramPlanId = (int)roiPtr->getParamPlanId();
                roiNode.param = deserializeParamList(roiPtr->getParam());
                roiNode.labelProperty = std::stoi(roiPtr->getLabelProperty());
                roiNode.area = deserializePoints(roiPtr->getCheckArea());
                roiNode.direction = deserializePoints(roiPtr->getDirection());
                roiNode.countLine = deserializePoints(roiPtr->getCountLine());
                roiNode.keyLength = roiPtr->getSpeedLineLen();
                auto p1 = deserializePoints(roiPtr->getSpeedPoint1());
                auto p2 = deserializePoints(roiPtr->getSpeedPoint2());
                if(!p1.empty())
                    roiNode.keyPoint.emplace_back(p1.front());
                if(!p2.empty())
                    roiNode.keyPoint.emplace_back(p2.front());

                roiNode.keyRect = deserializePoints(roiPtr->getSpeedArea(), 4);
                roiNode.roiId = roiId;
                roiNode.logicId = (int)roiPtr->getLogicId();

                for (const auto& laneData : roiInfo.lanesMap){
                    auto lanePtr = laneData.second;
                    if (nullptr == lanePtr){
                        ai::LogError << "get lane failed ";
                        return false;
                    }
                    if (lanePtr->getLaneArea().empty())  //!< 车道配置区为空，就不发送配置消息
                        continue;

                    Lane lane;
                    lane.area = deserializePoints(lanePtr->getLaneArea());
                    lane.direction = deserializePoints(lanePtr->getDirection());
                    lane.laneType = lanePtr->getLaneType();
                    lane.eventProperty = std::stoi(lanePtr->getEventProperty());
                    lane.paramPlanId = (int)lanePtr->getParamPlanId();
                    lane.param = deserializeParamList(lanePtr->getParam());
                    lane.labelProperty = std::stoi(lanePtr->getLabelProperty());
                    lane.laneId = laneData.first;
                    lane.maxSpeed = lanePtr->getMaxSpeed();
                    lane.minSpeed = lanePtr->getMinSpeed();
                    roiNode.lane.emplace_back(lane);
                }

                for (const auto& checkAreaData : roiInfo.checkAreasMap){
                    auto checkAreaPtr = checkAreaData.second;
                    if (nullptr == checkAreaPtr){
                        ai::LogError << "get check area failed ";
                        return false;
                    }
                    if (checkAreaPtr->getCheckArea().empty())  //!< 子区域为空，就不发送配置消息
                        continue;
                    CheckAreaItemNode checkArea;
                    checkArea.area = deserializePoints(checkAreaPtr->getCheckArea());
                    checkArea.eventProperty = std::stoi(checkAreaPtr->getEventProperty());
                    checkArea.areaTypeId = checkAreaPtr->getAreaType();
                    checkArea.areaId = checkAreaData.first;
                    checkArea.paramPlanId = (int)checkAreaPtr->getParamPlanId();
                    checkArea.param = deserializeParamList(checkAreaPtr->getParam());
                    checkArea.labelProperty = std::stoi(checkAreaPtr->getLabelProperty());
                    roiNode.checkArea.emplace_back(checkArea);
                }

                if (roiInfo.featureArea)
                {
                    roiNode.featureArea.beginTime = roiInfo.featureArea->getBeginTime();
                    roiNode.featureArea.endTime = roiInfo.featureArea->getEndTime();
                    roiNode.featureArea.area = deserializePoints(roiInfo.featureArea->getArea());
                }

                preset.detectParam.roi.emplace_back(roiNode);
            }

            if (presetData.second.presetLaneLinePtr)
            {
                auto laneLines = presetData.second.presetLaneLinePtr->getLaneLines();
                for (const auto& line : laneLines)
                {
                    if (!line.empty())
                        preset.detectParam.laneLine.emplace_back(deserializePoints(line));
                }
            }
            presets.emplace_back(preset);
        }
        return true;
    }
}
