
#pragma once
#include <thread>
#include <memory>
#include <boost/asio.hpp>
#include <boost/asio/deadline_timer.hpp>
//#include <boost/system.hpp>
#include <boost/shared_ptr.hpp>
#include <boost/weak_ptr.hpp>
#include <boost/thread.hpp>
#include <boost/enable_shared_from_this.hpp>
#include "stream/element/circular_queue.h"
#include "ailog.h"

namespace fvm::stream
{
    using IOContext = boost::asio::io_context;
    using TCP = boost::asio::ip::tcp;
    using UDP = boost::asio::ip::udp;
    using BoostError = boost::system::error_code;
    using Boostbuffer = boost::asio::mutable_buffer;
    using Timeduration = boost::posix_time::time_duration;
    using Steadytimer = boost::asio::steady_timer;

    constexpr int TCPHEADER_LEN = 2;
    constexpr int BUF_SIZE = 1024 * 32;

    //回调函数指针
    using RecvCallback = std::function<void(uint8_t*, int)>;

    //客户端基类
    struct SocketClient
    {
        using Ptr = std::shared_ptr<SocketClient>;
        virtual ~SocketClient() = default;
    };

    //服务端基类
    struct SocketServer
    {
        using Ptr = std::shared_ptr<SocketServer>;
        virtual ~SocketServer(void) = default;
        virtual bool start(RecvCallback pCb, int uPort) = 0;
        virtual void stop(void) = 0;
        virtual bool isConnect(void)
        {
            return true;
        }

        //轮询调度
        void run_io_service(bool loop = false)
        {
            if (loop)
                io_service.run();
            else
                io_service.run_one();
        }
    public:
        RecvCallback pCallback = nullptr;       //回调指针
        IOContext io_service;
        std::atomic_bool bStatus = false;       //服务器状态
        std::thread thd;
    };
 
    //通道资源管理
    struct Channel
    {
        void join(SocketClient::Ptr subscriber)
        {
            subscribers_.insert(subscriber);
        }

        void leave(SocketClient::Ptr subscriber)
        {
            subscribers_.erase(subscriber);
        }
    private:
        std::set<SocketClient::Ptr> subscribers_;
    };

    //tcp客户端
    class TcpSession : public SocketClient, public std::enable_shared_from_this<TcpSession>
    {
    public:
        using Ptr = std::shared_ptr<TcpSession>;
    public:
        //传入socket
        TcpSession(TCP::socket socket, Channel& ch, RecvCallback fb);

        ~TcpSession(void);

        //打开sock读
        void start(void);

        //关闭sock
        void stop(void);

        //查询停止状态
        bool stopped(void) const;

        std::string getLocalIpPort(void);

        std::string getRemoteIpPort(void);
    private:
        //读头获得数据长
        void do_read_header(void);

        //读全部buff
        void do_read_body(size_t bodyLen);

        //检测超时断线
        void check_deadline(Steadytimer& deadline);
    private:
        std::string localIpPort, remoteIpPort;
        TCP::socket mSocket;
        Channel& channel;
        Steadytimer deadTimer{ mSocket.get_executor() };
        uint8_t recvData[BUF_SIZE];
        RecvCallback fRecvCallback;         //回调
        std::atomic_bool bStatus = false;
    };

    //tcp服务器
    class TcpServer :public SocketServer
    {
    public:
        using Ptr = std::shared_ptr<TcpServer>;
    public:
        TcpServer(void);

        ~TcpServer(void);

        //启动服务器
        bool start(RecvCallback pCb, int uPort);

        //停止服务器
        void stop(void);

        //连接状态
        bool isConnect(void);
    private:
        //异步轮巡一次 待测试
        void poll_io_service(void)
        {
            io_service.poll_one();
        }

        //打开异步接受
        void do_accept(void);
    private:
        TcpSession::Ptr curr_session = nullptr;
        TCP::acceptor tcpAccept;
        Channel channel;
    };

    //udp服务器
    class UdpServer :public SocketServer
    {
    public:
        using Ptr = std::shared_ptr<UdpServer>;
    public:
        UdpServer(void);

        ~UdpServer(void);

        //启动服务器
        bool start(RecvCallback pCb, int uPort);

        //停止服务器
        void stop(void);

    private:
        //打开异步数据接受
        void do_receive(void);
    private:
        UDP::socket mSocket;
        UDP::endpoint senderPoint;
        uint8_t recvData[BUF_SIZE];
    };

}