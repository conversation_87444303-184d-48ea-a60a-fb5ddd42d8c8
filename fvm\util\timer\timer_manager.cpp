
#include "timer_manager.h"
#include "boost/date_time/posix_time/posix_time.hpp"
#include "ailog.h"

namespace timer
{
    /**
     * @brief                   添加时间点为TimePoint的定时器
     * @param[in] func:         用户注册的定时器回调函数
     * @param[in] when:         指定的定时时间点，到该时间后调用func
     * @param[in] duration:     当指定repeatTimes时，duration就是when距离下一次的时间点间隔
     * @param[in] repeatTimes： 定时器重复次数 -1:循环执行. 0: 只执行一次 >0:重复n次
     * @return                  返回定时器上下文参数指针，通过该指针可以取消正当前定时器
     */
    TimerPtr TimerManager::addTimer(const TimePoint& when, const TimerFunc& func, const Duration& duration, int32_t repeatTimes)
    {
        auto now = std::chrono::system_clock::now();
        if ( when < now )
        {
            std::time_t t = std::chrono::system_clock::to_time_t(when);
            ai::LogWarn << "when is earlier than now:" << std::ctime(&t);
            return nullptr;
        }

        auto timerPtr = std::make_shared<TimerContext>(service);
        auto weakTimePtr = std::weak_ptr<TimerContext>(timerPtr);
        bool loop = (repeatTimes == -1);
        timerPtr->repeatTimes = repeatTimes;
        timerPtr->when = when;
        {
            std::lock_guard lk(timersLock);
            timers.emplace_back(timerPtr);
        }

        timerPtr->funcWrapper = [=](const boost::system::error_code &error) {
                if (error){
                    ai::LogError << "Timer execution failed,error code:" << error;
                    return;
                }else if (!weakTimePtr.expired()){
                    auto ptr = weakTimePtr.lock();
                    if ((loop) || (ptr->repeatTimes > 0))
                    {
                        ptr->when += duration;
                        auto now = std::chrono::system_clock::now();
                        if ( ptr->when > now )
                        {
                            ptr->systemTimer.expires_at(ptr->when);
                            ptr->systemTimer.async_wait(ptr->funcWrapper);
                        }
                    }
                    func(ptr);
                    ptr->repeatTimes--;
                }
        };

        timerPtr->systemTimer.expires_at(when);
        timerPtr->systemTimer.async_wait(timerPtr->funcWrapper);
        return timerPtr;

    }

    /**
     * @brief                   添加时间点为Ptime的定时器
     * @param[in] when:         指定的定时时间点，到该时间后调用func
     * @param[in] func:         用户注册的定时器回调函数
     * @param[in] duration:     当指定repeatTimes时，duration就是when距离下一次的时间点间隔
     * @param[in] repeatTimes： 定时器重复次数 -1:循环执行. 0: 只执行一次 >0:重复n次
     * @return                  返回定时器上下文参数指针，通过该指针可以取消正当前定时器
     */
    TimerPtr TimerManager::addTimer(const Ptime& when, const TimerFunc& func, const Duration& duration, int32_t repeatTimes)
    {
        //! ptimeWhen是系统本地时间，to_time_t转换成time_t时，会把时间转成UTC-0时区时间，所以要减去两者差值
        auto ptimeWhen = when - UTCInterval;
        std::time_t time = boost::posix_time::to_time_t(ptimeWhen);
        std::chrono::system_clock::time_point timePoint = std::chrono::system_clock::from_time_t(time);
        ai::LogInfo << "addTimer,time point:" << std::ctime(&time);

        return addTimer(timePoint, func, duration, repeatTimes);
    }

    /**
     * @brief                   添加时间间隔为duration的定时器
     * @param[in] func:         用户注册的定时器回调函数
     * @param[in] when:         到达指定时间间隔后调用func
     * @param[in] repeatTimes： 定时器重复次数 -1:循环执行. 0: 只执行一次 >0:重复n次
     * @return                  返回定时器上下文参数指针，通过该指针可以取消正当前定时器
     */
    TimerPtr TimerManager::addTimer(const Duration& duration, const TimerFunc& func, int32_t repeatTimes)
    {
        auto timerPtr = std::make_shared<TimerContext>(service);
        auto weakTimePtr = std::weak_ptr<TimerContext>(timerPtr);
        bool loop = (repeatTimes == -1);
        timerPtr->repeatTimes = repeatTimes;
        {
            std::lock_guard lk(timersLock);
            timers.emplace_back(timerPtr);
        }
        timerPtr->funcWrapper = [=](const boost::system::error_code &error) {
            if (error && error.value() != 125) { // 125= cancel
                ai::LogError << "Timer execution failed,error code:" << error;
                return;
            }else if (!weakTimePtr.expired()){
                auto ptr = weakTimePtr.lock();
                if ((loop) || (ptr->repeatTimes > 0))
                {
                    ptr->systemTimer.expires_from_now(duration);
                    ptr->systemTimer.async_wait(ptr->funcWrapper);
                }
                func(ptr);
                ptr->repeatTimes--;
            }
        };

        timerPtr->systemTimer.expires_from_now(duration);
        timerPtr->systemTimer.async_wait(timerPtr->funcWrapper);
        return timerPtr;
    }

    /**
     * @brief                   删除取消指定定时器任务
     * @param[in]timer:         要取消的定时器任务指针(该指针是调用addTimer时返回的)
     */
    void TimerManager::removeTimer(TimerPtr& timer)
    {
        if (!timer)
            return;

        timer->systemTimer.cancel();
        std::lock_guard lk(timersLock);
        timers.remove(timer);
    }

    /**
     * @brief                   启动定时器，执行io_service run
     */
    void TimerManager::start()
    {
        if (!running)
        {
            threadTimer = std::thread([&] {
                running = true;
                ai::LogInfo << "start timer manger thread";
                auto guard(boost::asio::make_work_guard(service));
                this->service.run();
                ai::LogInfo << "exit timer manger thread";
                running = false;
            });
        }

    }

    /**
     * @brief                   停止定时器，执行io_service stop
     */
    void TimerManager::stop()
    {
        if (running)
        {
            this->service.stop();
            running = false;
        }

        if (threadTimer.joinable())
            threadTimer.join();

    };


}
