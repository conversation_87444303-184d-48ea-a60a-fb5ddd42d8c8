
/* MD5DEEP - md5.h
 *
 * By <PERSON>
 *
 * This is a work of the US Government. In accordance with 17 USC 105,
 * copyright protection is not available for any work of the US Government.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * <PERSON>RCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 *
 */

/* $Id$ */

#ifndef __MD5_H
#define __MD5_H

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <errno.h>
#include <fcntl.h>
#include <string.h>

#include <sys/ioctl.h>
#include <linux/hdreg.h>
#include <sys/fcntl.h>

#include <netdb.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#include <linux/if.h>   
#include <linux/if_ether.h>
#include<time.h>

#include <stdio.h>
#include <stdlib.h>
#include <ctype.h>
#include <string>
#include <ifaddrs.h>
#include <map>

using namespace std;


//#include "common.h"
// -------------------------------------------------------------- 
// After this is the algorithm itself. You shouldn't change these

//__BEGIN_DECLS

//#define uint32_t unsigned int

typedef struct {
  uint32_t buf[4];
  uint32_t bits[2];
  unsigned char in[64];
} context_md5_t;

// This is needed to make RSAREF happy on some MS-DOS compilers 
typedef context_md5_t MD5_CTX;

void MD5Init(context_md5_t *ctx);
void MD5Update(context_md5_t *context, const unsigned char *buf, size_t len);
void MD5Final(unsigned char digest[16], context_md5_t *context);
void MD5Transform(uint32_t buf[4], uint32_t const in[16]);

void hash_init_md5(void * ctx);
void hash_update_md5(void *ctx, const unsigned char *buf, size_t len);
void hash_final_md5(void *ctx, unsigned char *digest);

//__END_DECLS



std::map<std::string, std::string> procValue(const char* cmd, char chSplit);

int getdiskid(char *id, size_t max);


int getcpuid(char *id, size_t max);

std::string getNetMac(const std::string& szDevName);



int getlocalmac(char *id, size_t max);


std::string datetimeTostring(time_t t);


time_t stringToDatetime(std::string str);
bool  stringToDatetime(std::string str, time_t& t);

//16进制字符串化
std::string hextostring(unsigned char *digest, int len);


time_t stringToTime_t(std::string str);



std::string  getdeviceinfo( bool bShowOld = false );


void genmd5(std::string str, unsigned char *digest);


int parse(std::string strSrc, std::string &md5, time_t &begin, time_t &end);


#endif /* ifndef __MD5_H */
