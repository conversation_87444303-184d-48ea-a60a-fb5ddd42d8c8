cmake_minimum_required(VERSION 3.5.2)


## 项目信息
project("test_cli")

## 输出目录
set(EXECUTABLE_OUTPUT_PATH   ../out/bin)

## 编译选项
set(CMAKE_VERBOSE_MAKEFILE ON)  
add_compile_options(-std=c++17 -fPIC -fstack-protector-all -Wno-deprecated-declarations)

## 依赖路径
include_directories(/usr/include/cdk/)
link_directories("/usr/lib/x86_64-linux-gnu/")
include_directories(../include)

## 目标生成
add_subdirectory(../  cli_binary_dir)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR} MAIN_SRC)

add_executable(test_cli ${MAIN_SRC})

## 目标依赖
target_link_libraries(test_cli pthread ai_cli)


