#include "UsgManager/src/ReceiveSipData.hpp"

namespace usg
{

    CReciveSipData::CReciveSipData()
            :m_is200Ok( false ),m_bSubscribeResult(false),m_broadcastResult(false)
    {

    }

    CReciveSipData::~CReciveSipData()
    {
        m_confirm.notify_one();
    }


    bool CReciveSipData::waitForRecive()
    {
        /** 等到收到回应，10秒超时 */
        boost::mutex::scoped_lock lock( m_mutex );
        if( m_is200Ok ) return true;
        return m_confirm.timed_wait( lock, boost::get_system_time()+boost::posix_time::millisec(10 * 1000) );
    }

    bool CReciveSipData::onReceiveRealMediaResponse( const std::string &sid, void* out )
    {
        m_is200Ok = ( out != NULL );
        SRealMediaResponse* tmp = (SRealMediaResponse*)out;
        if ( !tmp )
        {
            m_is200Ok = false;
        }
        else
        {
            m_streamRate = tmp->bitRate;
        }
        m_confirm.notify_one();
        return true;
    }

    bool CReciveSipData::onReceiveHistoryListResponse( const std::string &sid, void* out )
    {
        boost::mutex::scoped_lock lock( m_mutex );
        m_is200Ok = ( out != NULL );
        SHistoryListResponse* tmp = (SHistoryListResponse*)out;
        if ( !tmp )
        {
            m_is200Ok = false;
        }
        else
        {
            uint16_t size = tmp->files.size();
            for ( uint16_t i = 0; i < size; ++i )
            {
                TimePeriodUnit unit = std::make_pair( tmp->files[i].startTime,tmp->files[i].endTime );
                std::pair< std::string, TimePeriodUnit > tmpPair = std::make_pair( tmp->files[i].fileName, unit );
                m_sHistoryListResponse.push_back( tmpPair );
            }

            if (tmp->allItemSize > m_sHistoryListResponse.size())
            {
                return true;
            }

            m_is200Ok = true;
        }
        m_confirm.notify_one();
        return true;
    }


    bool CReciveSipData::onReceiveHistoryMediaResponse( const std::string &sid, void* out )
    {
        m_is200Ok = ( out != NULL );
        SHistoryMediaResponse* tmp = (SHistoryMediaResponse*)out;
        if ( !tmp )
        {
            m_is200Ok = false;
        }
        else
        {
            m_sHistoryMediaResponse = tmp->playUrl;
        }
        m_confirm.notify_one();
        return true;
    }

    bool CReciveSipData::onReceivePresetListResponse( const std::string &sid, void* out )
    {
        m_is200Ok = ( out != NULL );
        SPresetListResponse* tmp = (SPresetListResponse*)out;
        if ( !tmp )
        {
            m_is200Ok = false;
        }
        else
        {
            uint16_t size = tmp->presets.size();
            for ( uint16_t i = 0; i < size; ++i )
            {
                uint8_t presetId = (uint8_t)tmp->presets[i].presetId;
                m_sPresetListResponse[presetId] = tmp->presets[i].presetName;
            }
        }
        m_confirm.notify_one();
        return true;
    }

    bool CReciveSipData::onReceiveDeviceCatalogResponse( const std::string &sid, void* out )
    {
        m_is200Ok = ( out != NULL );
        SCatalog* tmp = (SCatalog*)out;
        if ( !tmp )
        {
            m_is200Ok = false;
            m_confirm.notify_one();
        }
        else
        {
            for (int i = 0; i < ( int )tmp->subItems.size(); i++)
            {
                m_catalog.subItems.push_back(tmp->subItems[i]);
            }

            m_catalog.num = tmp->num;
            m_catalog.endflg = (m_catalog.subItems.size() == m_catalog.num);
            tmp->endflg = m_catalog.endflg;

            if (m_catalog.endflg)
            {
                m_confirm.notify_one();
            }
        }
        return true;
    }

    bool CReciveSipData::onReceiveDeviceInfoResponse( const std::string &sid, void* out )
    {
        m_is200Ok = ( out != NULL );
        SDeviceInfoResponse* tmp = (SDeviceInfoResponse*)out;
        if ( !tmp )
        {
            m_is200Ok = false;
        }
        else
        {
            m_info = *tmp;
        }
        m_confirm.notify_one();
        return true;
    }

    bool CReciveSipData::onReceiveDeviceStatusResponse( const std::string &sid, void* out )
    {
        m_is200Ok = ( out != NULL );
        SDeviceStatusResponse* tmp = (SDeviceStatusResponse*)out;
        if ( !tmp )
        {
            m_is200Ok = false;
        }
        else
        {
            m_status = *tmp;
        }
        m_confirm.notify_one();
        return true;
    }

    bool CReciveSipData::onReceiveSubscribeResponse( const std::string &sid, void* out )
    {
        m_is200Ok = ( out != NULL );
        SubscribeCommandResponse* tmp = (SubscribeCommandResponse*)out;
        if ( !tmp )
        {
            m_is200Ok = false;
        }
        else
        {
            m_bSubscribeResult = tmp->isOk;
        }
        m_confirm.notify_one();
        return true;
    }

    bool CReciveSipData::onReceiveBroadcastResponse( const std::string &sid, void* out )
    {
        m_is200Ok = ( out != NULL );
        SBroadcastResponse* tmp = (SBroadcastResponse*)out;
        if ( !tmp )
        {
            m_is200Ok = false;
        }
        else
        {
            m_broadcastResult = tmp->isOk;
        }
        m_confirm.notify_one();
        return true;
    }

    bool CReciveSipData::getHistoryListResponse( std::vector< std::pair< std::string, TimePeriodUnit >  > &info )
    {
        if ( m_is200Ok )
        {
            info = m_sHistoryListResponse;
            return true;
        }
        return false;
    }

    bool CReciveSipData::getPresetListResponse( std::map< uint8_t, std::string > &infos  )
    {
        if ( m_is200Ok )
        {
            infos = m_sPresetListResponse;
            return true;
        }
        return false;
    }

    bool CReciveSipData::getRealMediaResponse( uint16_t& streamRate )
    {
        if ( m_is200Ok )
        {
            streamRate = m_streamRate;
            return true;
        }
        return false;
    }

    bool CReciveSipData::getHistoryMediaResponse( std::string& vodUrl  )
    {
        if ( m_is200Ok )
        {
            vodUrl = m_sHistoryMediaResponse;
            return true;
        }

        return false;
    }

    bool CReciveSipData::getDeviceCatalogResponse(SCatalog& catalog)
    {
        if ( m_is200Ok )
        {
            catalog = m_catalog;
            return true;
        }
        return false;
    }

    bool CReciveSipData::getDeviceInfoResponse(SDeviceInfoResponse& info)
    {
        if ( m_is200Ok )
        {
            info = m_info;
            return true;
        }
        return false;
    }

    bool CReciveSipData::getDeviceStatusResponse(SDeviceStatusResponse &status)
    {
        if ( m_is200Ok )
        {
            status = m_status;
            return true;
        }
        return false;
    }
    bool CReciveSipData::getSubscribeResponse(bool &result)
    {
        if ( m_is200Ok )
        {
            result = m_bSubscribeResult;
            return true;
        }
        return false;
    }

    bool CReciveSipData::getBroadcastResponse(bool &result)
    {
        if ( m_is200Ok )
        {
            result = m_broadcastResult;
            return true;
        }
        return false;
    }

}

