/**
 * Project FVM
 */
#include "sdk_mpc_platform.h"
#include "data/config_param_info.h"
#include "boost/locale.hpp"
#include "boost/uuid/uuid_io.hpp"
#include "boost/uuid/uuid_generators.hpp"
#include "ace/INET_Addr.h"

 /**
  * SDKMPCPlatform implementation
  */
namespace fvm::platform {

    using namespace data;
    void SDKMPCPlatform::init(data::VideoServerPtr serverPtr) {
        std::call_once(initflag , [this, serverPtr] {
            FrontPlatform::init(serverPtr);
            int mpcType = DATA_MANAGER.getParamProgData(MPC_STREAM_TYPE, 0);
            streamType = mpcType == 1 ? StreamType::Sub : StreamType::Main;
            DATA_MANAGER.updateVideoServerInfo(serverPtr->getId(), 0);
            });
    }

    mpc::nsdk::SResId uuid2resId(const std::string addr)
    {
        boost::uuids::uuid uid = boost::uuids::string_generator()(addr);
        mpc::nsdk::SResId rid;
        memset(&rid, 0, sizeof(mpc::nsdk::SResId));
        if (uid.size() != sizeof(mpc::nsdk::SResId))
            return rid;
        memcpy(&rid, uid.data, sizeof(mpc::nsdk::SResId));
        return rid;
    }

    boost::uuids::uuid resId2uuid(const mpc::nsdk::SResId& rid)
    {
        boost::uuids::uuid uid;
        memset(uid.data, 0, uid.size());
        if (uid.size() != sizeof(mpc::nsdk::SResId))
            return uid;
        memcpy(uid.data, &rid, sizeof(mpc::nsdk::SResId));
        return uid;
    }

    std::string getResKey(mpc::nsdk::SResId resId)
    {
        boost::uuids::uuid resUuid = resId2uuid(resId);
        std::string szKey = boost::uuids::to_string(resUuid);
        if (szKey.find('-') != std::string::npos)
            boost::algorithm::erase_all(szKey, "-");

        return szKey;
    }

    std::string convert2Utf8(const std::string& input, const std::string& fromEncoding) {
        try {
            boost::locale::generator gen;
            std::locale loc = gen("en_US.UTF-8");
            std::locale::global(loc);
            return boost::locale::conv::to_utf<char>(input, fromEncoding);
        }
        catch (const std::exception& e) {
            throw std::runtime_error(std::string("Conversion failed: ") + e.what());
        }
    }

    bool SDKMPCPlatform::login()
    {
        std::unique_lock<std::mutex> lck(mtxLogin);
        logining = true;
        if (!this->isOnline)
        {
            int passed = std::chrono::duration_cast<std::chrono::milliseconds>(steady_clock::now() - lastLoginTime).count();
            if (passed < 3000)
            {
                logining = false;
                return false;
            }
        }

        lastLoginTime = steady_clock::now();
        if (!mgrSession)
        {
            ai::LogInfo << "MPC create session: " << serverPtr->getIp();
            std::string username = serverPtr->getUserName();
            std::string passwd = serverPtr->getPassword();

            ACE_INET_Addr addr(u_short(0), serverPtr->getIp().c_str());
            mgrSession = mpc::nsdk::Factory::createManageSession(addr.get_ip_address(), serverPtr->getPort(), username.c_str(), strlen(username.c_str()), passwd.c_str(), strlen(passwd.c_str()));
            if (!mgrSession->init())
            {
                mgrSession->release();
                mgrSession = NULL;
                ai::LogWarn << "MPC " << serverPtr->getIp() << " init failed! ";
                logining = false;
                return false;
            }
            ai::LogInfo << "MPC " << serverPtr->getIp() << " init success! ";
        }
        if (mgrSession && !this->isOnline)
        {
            this->isOnline = mgrSession->login();
            mgrSession->subscribe(&platNotify);
            //mgrSession->subscribeMediaStatusChange(&resourceNotify);
            logining = false;
            DATA_MANAGER.updateVideoServerInfo(serverPtr->getId(), this->isOnline);
            if (this->isOnline)
            {
                ai::LogInfo << "MPC " << serverPtr->getIp() << " LOGIN ok! ";
            }
            else
            {
                ai::LogWarn << "MPC " << serverPtr->getIp() << " LOGIN failed! ";
                return false;
            }

        }
        cvLogin.notify_all();
        updateResource();
        return this->isOnline;
    }

    void SDKMPCPlatform::setOnline(bool status)
    {
        ai::LogInfo << "MPC set online: " << status;
        this->isOnline = status;
        DATA_MANAGER.updateVideoServerInfo(serverPtr->getId(), this->isOnline);
    }

    bool SDKMPCPlatform::startPlay(const std::string& addr, RecvCallback pCallback) {
        {
            std::unique_lock<std::mutex> lck(mtxStartPlay);
            if (!this->isOnline)
            {
                if (!logining)
                {
                    login();
                }
                else
                {
                    int passed = std::chrono::duration_cast<std::chrono::milliseconds>(steady_clock::now() - lastLoginTime).count();
                    if (passed > 2000)
                    {
                        login();
                    }
                    else
                    {
                        cvLogin.wait_for(lck, std::chrono::milliseconds(1000), [this] { return !logining; });
                    }
                }
            }
        }

        if (!this->isOnline)
        {
            return false;
        }

        if (livingStreams.find(addr) == livingStreams.end())
        {
            bool isSub = streamType == StreamType::Sub;
            auto resId = uuid2resId(addr);
            auto livingStream = mgrSession->createLivingStream(resId, isSub);
            if (!livingStream)
                return false;

            livingStreams[addr] = livingStream;
            livingStream->setHandler(new MPCStreamHandler(pCallback));
        }

        auto livingStream = livingStreams[addr];
        if (!livingStream->okey())
        {
            livingStream->init();
        }
        return livingStream->okey();
    }

    void SDKMPCPlatform::stopPlay(const std::string& addr) {
        if (livingStreams.find(addr) != livingStreams.end())
        {
            auto livingStream = livingStreams[addr];
            livingStream->fini();
            livingStream->release();
            livingStream = nullptr;
            livingStreams.erase(addr);
        }
    }

    bool SDKMPCPlatform::callPreset(VideoSourceInfoPtr videoSourceInfo, int preset)
    {
        if (!mgrSession) return false;

        auto addr = videoSourceInfo->videoSourcePtr->getAddress();
        ai::LogInfo << "MPC " << serverPtr->getIp() << " " << addr << " callPreset:" << preset;
        auto resId = uuid2resId(addr);
        mpc::nsdk::IPtzController* ptzCtrl = mgrSession->createPtzController(resId);
        if (!ptzCtrl)  return false;

        // SDK <= 4.1的 只有10个预置位列表   暂时iActPreset - 1 改成 iActPreset - 3
        int iActPreset = preset >= 10 ? preset - 3 : preset - 1;
        ptzCtrl->preset(iActPreset);
        ptzCtrl->release();
        return true;
    }

    bool SDKMPCPlatform::savePreset(VideoSourceInfoPtr videoSourceInfo, int preset)
    {
        if (!mgrSession) return false;

        auto addr = videoSourceInfo->videoSourcePtr->getAddress();
        ai::LogInfo << "MPC " << serverPtr->getIp() << " " << addr << " savePreset:" << preset;
        auto resId = uuid2resId(addr);
        mpc::nsdk::IPtzController* ptzCtrl = mgrSession->createPtzController(resId);
        if (!ptzCtrl)  return false;

        // SDK <= 4.1的 只有10个预置位列表   暂时iActPreset - 1 改成 iActPreset - 3
        int iActPreset = preset >= 10 ? preset - 3 : preset - 1;
        ptzCtrl->delPreset(iActPreset);
        ptzCtrl->addPreset("ai_event", strlen("ai_event"), iActPreset);
        ptzCtrl->release();
        return true;
    }

    bool SDKMPCPlatform::focusRect(VideoSourceInfoPtr videoSourceInfo, int xTop, int yTop, int xBottom, int yBottom)
    {
        if (!mgrSession) return false;

        auto addr = videoSourceInfo->videoSourcePtr->getAddress();
        ai::LogInfo << "MPC " << serverPtr->getIp() << " " << addr << " focusRect: " << xTop << " " << yTop << " " << xBottom << " " << yBottom;
        auto resId = uuid2resId(addr);
        mpc::nsdk::IPtzController* ptzCtrl = mgrSession->createPtzController(resId);
        if (!ptzCtrl)  return false;
        ptzCtrl->exec3DLocation(xTop, yTop, xBottom, yBottom);
        ptzCtrl->release();
        return true;
    }

    mpc::nsdk::IPtzController::EPtzMove convertAsgMoveToMpcMove(EPtzCommand cmd)
    {
        switch (cmd)
        {
        case EPTZCOMMAND_STOP:
        {
            return mpc::nsdk::IPtzController::MOVE_STOP;
            break;
        }
        case EPTZCOMMAND_UP:
        {
            return mpc::nsdk::IPtzController::MOVE_UP;
            break;
        }
        case EPTZCOMMAND_DOWN:
        {
            return mpc::nsdk::IPtzController::MOVE_DOWN;
            break;
        }
        case EPTZCOMMAND_LEFT:
        {
            return mpc::nsdk::IPtzController::MOVE_LEFT;
            break;
        }
        case EPTZCOMMAND_RIGHT:
        {
            return mpc::nsdk::IPtzController::MOVE_RIGHT;
            break;
        }
        default:
            return mpc::nsdk::IPtzController::MOVE_STOP;
        }
        return mpc::nsdk::IPtzController::MOVE_STOP;
    }

    mpc::nsdk::IPtzController::EPtzSpeed convertAsgSpeedToMpcSpeed(uint8_t step)
    {
        if (step >= 7)
        {
            return mpc::nsdk::IPtzController::SPEED_FASTER;
        }
        if (step >= 6)
        {
            return mpc::nsdk::IPtzController::SPEED_FAST;
        }
        if (step >= 4)
        {
            return mpc::nsdk::IPtzController::SPEED_NORMAL;
        }
        if (step >= 2)
        {
            return mpc::nsdk::IPtzController::SPEED_SLOW;
        }

        return mpc::nsdk::IPtzController::SPEED_SLOWER;
    }

    mpc::nsdk::IPtzController::EPtzFocus convertAsgFocusToMpcFocus(EPtzCommand cmd)
    {
        switch (cmd)
        {
        case EPTZCOMMAND_FOCUSSTOP:
        {
            return mpc::nsdk::IPtzController::FOCUS_STOP;
            break;
        }
        case EPTZCOMMAND_FOCUSNEAR:
        {
            return mpc::nsdk::IPtzController::FOCUS_NEAR;
            break;
        }
        case EPTZCOMMAND_FOCUSFAR:
        {
            return mpc::nsdk::IPtzController::FOCUS_FAR;
            break;
        }
        default:
            return mpc::nsdk::IPtzController::FOCUS_STOP;
        }
        return mpc::nsdk::IPtzController::FOCUS_STOP;
    }

    mpc::nsdk::IPtzController::EPtzZoom convertAsgZoomToMpcZoom(EPtzCommand cmd)
    {
        switch (cmd)
        {
        case EPTZCOMMAND_ZOOMIN:
        {
            return mpc::nsdk::IPtzController::ZOOM_IN;
            break;
        }
        case EPTZCOMMAND_ZOOMOUT:
        {
            return mpc::nsdk::IPtzController::ZOOM_OUT;
            break;
        }
        default:
            return mpc::nsdk::IPtzController::ZOOM_STOP;
        }

        return mpc::nsdk::IPtzController::ZOOM_STOP;
    }

    bool SDKMPCPlatform::controlPtz(VideoSourceInfoPtr videoSourceInfo, network::EPtzCommand cmd, int step)
    {
        if (!mgrSession) return false;

        auto addr = videoSourceInfo->videoSourcePtr->getAddress();
        printf("MPC %s %s control ptz: %d %d \n", serverPtr->getIp().c_str(), addr.c_str(), cmd, step);

        auto resId = uuid2resId(addr);
        mpc::nsdk::IPtzController* ptzCtrl = mgrSession->createPtzController(resId);
        if (!ptzCtrl)  return false;

        switch (cmd)
        {
        case EPTZCOMMAND_UP:
        case EPTZCOMMAND_DOWN:
        case EPTZCOMMAND_LEFT:
        case EPTZCOMMAND_RIGHT:
        case EPTZCOMMAND_STOP:
        {
            mpc::nsdk::IPtzController::EPtzMove mpcOper = convertAsgMoveToMpcMove(cmd);
            mpc::nsdk::IPtzController::EPtzSpeed mpcSpeed = convertAsgSpeedToMpcSpeed(step);
            ptzCtrl->move(mpcOper, mpcSpeed);
            break;
        }
        case EPTZCOMMAND_FOCUSNEAR:
        case EPTZCOMMAND_FOCUSFAR:
        case EPTZCOMMAND_FOCUSSTOP:
        {
            mpc::nsdk::IPtzController::EPtzFocus oper = convertAsgFocusToMpcFocus(cmd);
            ptzCtrl->focus(oper);
            break;
        }
        case EPTZCOMMAND_ZOOMIN:
        case EPTZCOMMAND_ZOOMOUT:
        {
            mpc::nsdk::IPtzController::EPtzZoom oper = convertAsgZoomToMpcZoom(cmd);
            ptzCtrl->zoom(oper);
            break;
        }
        default:
            break;
        }

        ptzCtrl->release();
        return true;
    }

    const char HEX[16] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b','c', 'd', 'e', 'f' };
    static std::string bytesToHexString(const uint8_t* input, size_t length)
    {
        std::string str;
        str.reserve(length << 1);
        for (size_t i = 0; i < length; ++i) {
            int t = input[i];
            int a = t / 16;
            int b = t % 16;
            str.append(1, HEX[a]);
            str.append(1, HEX[b]);
        }
        return str;
    }

    void SDKMPCPlatform::updateResource()
    {
        if (mgrSession == NULL)
            return;

        size_t size = 0;
        uint8_t digest[mpc::nsdk::MAX_DIGEST_SIZE] = { 0 };
        mgrSession->getMediaDigest(digest, mpc::nsdk::MAX_DIGEST_SIZE, size);
        std::string szNewInfo = bytesToHexString(digest, size);
        if (szNewInfo == lastPlatInfo)
            return;

        int passed = std::chrono::duration_cast<std::chrono::seconds>(steady_clock::now() - lastResUpdateTime).count();
        if (passed < 5)
        {
            return;
        }

        ai::LogInfo << "MPC start updateResource: " << this->serverPtr->getIp();
        mpc::nsdk::IMediaIterator* srcItor = mgrSession->createMediaIterator();
        if (!srcItor)
        {
            ai::LogError << "MPC createMediaIterator failed: " << this->serverPtr->getIp();
            if (mgrSession)
            {
                //mgrSession->subscribeMediaStatusChange(0);
                mgrSession->subscribe(0);
                mgrSession->logout();
            }
            this->isOnline = false;
            return;
        }

        lastResUpdateTime = steady_clock::now();
        bool hasNew = getResource(srcItor);
        srcItor->release();
        srcItor = NULL;

        if (hasNew)
        {
            DATA_MANAGER.updateVideoSourcesInfo(false, nullopt, serverPtr->getId());
        }
        lastPlatInfo = szNewInfo;
    }

    bool SDKMPCPlatform::getResource(mpc::nsdk::IMediaIterator* it)
    {	
        bool hasNew = false;
        mpc::nsdk::IMediaNode* self = 0;
        mpc::nsdk::IMediaIterator* curr = 0;
        mpc::nsdk::IMediaIterator* next = 0;
        mpc::nsdk::IMediaIterator* down = 0;
        mpc::nsdk::IMediaRes* resPtr = 0;
        size_t strSize = 0;
        curr = it;
        do
        {
            self = curr->self();
            next = curr->next();
            down = curr->down();

            if (self->isRes())
            {
                resPtr = dynamic_cast<mpc::nsdk::IMediaRes*> (self);
                std::string resName = convert2Utf8(self->label(strSize), "GBK");
                
                std::string szKey = getResKey(resPtr->msid());
                bool bStatus = resPtr->isOnline();

                auto  spRes = DATA_MANAGER.queryVideoSource(serverPtr->getId(), szKey);
                if (spRes.has_value())
                {
                    auto oldName = spRes.value()->videoSourcePtr->getName();
                    if (oldName != resName)
                    {
                        ai::LogInfo << "MPC res name changed from " << oldName << " to " << resName;
                        DATA_MANAGER.updateVideoSourceName(serverPtr->getId(), szKey, resName);
                    }
                }
                else /*if ( bStatus )*/ 
                {
                    char* codeBuf[512] = { 0 };
                    size_t retValueLen = 0;
                    resPtr->getPropertyByName((char*)"编码", 4, (char*)codeBuf, 512, retValueLen);
                    std::string szCode((char*)codeBuf);

                    if (DATA_MANAGER.queryAccessFrontDetectable(serverPtr->getId()))
                    {
                        ai::LogInfo << "MPC new res: " << resName;
                        DATA_MANAGER.insertVideoSource(serverPtr->getId(), resName, szKey, szCode);
                    }
                    hasNew = true;
                }
            }

            if (down)
            {
                auto dirty = getResource(down);
                if (!hasNew)
                    hasNew = dirty;
            }

            if (next)
            {
                curr = next;
            }

        } while (next);

        return hasNew;
    }

    void MPCSessionNotify::terminate(mpc::nsdk::IManageSession* session)
    {
        plat->setOnline(false);
    }
}