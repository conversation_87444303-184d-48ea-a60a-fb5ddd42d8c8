#include <string.h>
#include <iostream>
#include <thread>
#include "ailog.h"
#include <boost/thread/thread.hpp>
#include <boost/thread/barrier.hpp>

using namespace ai;
void testFunc1()
{
    LogWarn << " test " << 1;
    LogTrace << "this is trace 1";
    LogError << "this is Error 1";
}

void testFunc2()
{
    LogInfo << " test " << 2;
    LogFatal << "this is Fatal 2";
    LogError << "this is Error 2";
}

enum
{
    LOG_RECORDS_TO_WRITE = 10000,
    THREAD_COUNT = 10
};

void thread_fun(int id, boost::barrier& bar)
{
    // Wait until all threads are created
    bar.wait();

    // Now, do some logging
    for (unsigned int i = 0; i < LOG_RECORDS_TO_WRITE; ++i)
    {
        LogError << "Log " << id << " record " << i;
        if ( i % 1000 == 0 )
            sleep(1);
    }
}

int main( int argc, char* argv[] )
{
    //首先初始化日志文件名和参数
    std::string szProgram = "test";
    InitLog(szProgram, "./config/");

    LogInfo << " This is main";

    //boost::thread td = boost::thread( boost::bind(&testFunc2 ) );
    std::thread td = std::thread([&]()
                            {
                                testFunc2();
                            });
    td.detach();

    testFunc1();

    std::vector<int> some_list;
    int total = 0;
    for (int i=0;i<5;++i) some_list.push_back(i);
    std::for_each(begin(some_list), end(some_list), [&total](int x)
    {
        LogInfo << "test lambda";
        total += x;
    });

    // Create logging threads
    boost::barrier bar(THREAD_COUNT);
    boost::thread_group threads;
    for (unsigned int i = 0; i < THREAD_COUNT; ++i)
        threads.create_thread(boost::bind(&thread_fun, i+1, boost::ref(bar)));


    while ( true )
    {
        char ch = getchar();
        if ( ch == 'q')
            break;
        ::sleep(1);
    }
    // Wait until all action ends
    threads.join_all();

    FiniLog();
    return 0;
};