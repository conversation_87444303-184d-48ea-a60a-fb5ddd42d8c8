/**
 * Project FVM
 */
#ifdef SDK_DH_SUPPORTED
#include "sdk_dh_puller.h"
#include "ailog.h"
#include "platform/sdk_dh_platform.h"

namespace fvm::stream
{
    using namespace platform;
    using namespace data;

    using std::chrono::steady_clock;
    using std::chrono::duration_cast;
    using std::chrono::milliseconds;

    constexpr int BUF_SIZE = 1024 * 32;                   //!< AVIOContext 缓存大小
    constexpr int STREAM_QUEUE_SIZE = 2 * 1024 * 1024;    //!< 帧缓存环形队列大小
    constexpr int UNKNOWN_READ_PACKET_ERROR = -200000000; //!< 大华相机流异常错误码  eg：-2147379531

    void SDKDHPuller::initOptions()
    {
        av_dict_set(&options, "fflags", "nobuffer", 0);
        av_dict_set(&options, "max_analyze_duration", "10", 0);
        av_dict_set(&options, "max_delay", "1000", 0);
        av_dict_set(&options, "stimeout", "1000000", 0);
        av_dict_set(&options, "probesize", "4096", 0);
    }

    /**
     * @brief 打开视频流、分析视频格式
     */
    bool SDKDHPuller::open()
    {
        if (!videoSourceInfo || !videoSourceInfo->videoSourcePtr)
            return false;

        int videoId = (int)videoSourceInfo->videoSourcePtr->getId();
        if (!streamQueue.init(videoId, STREAM_QUEUE_SIZE))
        {
            ai::LogError << address << " init stream queue is failed";
            return false;
        }
        printInfo(str(boost::format("Start request dh sdk %d %s") % videoId % videoSourceInfo->videoSourcePtr->getAddress()));

        sourceAddr = videoSourceInfo->videoSourcePtr->getAddress();
        SDKDHPlatformPtr DHPlatformPtr = static_pointer_cast<SDKDHPlatform>(frontPlatform);
        bool bRet = DHPlatformPtr->startPlay(sourceAddr, [&](uint8_t* buff, int buffSize)
            {
                receiveRealData(buff, buffSize);
            });
        if (!bRet)
        {
            ai::LogError << "play dh :" << sourceAddr << " failed ";
            return false;
        }

        //ffmpeg分析流
        ioContextBuffer = (uint8_t*)av_malloc(sizeof(uint8_t) * BUF_SIZE);
        if (!ioContextBuffer)
        {
            ai::LogError << this->address << " av_malloc ctx buffer failed";
            return false;
        }

        auto onReadData = [](void* pUser, uint8_t* buf, int bufSize)->int
        {
            auto* DHPullerPtr = (SDKDHPuller*)pUser;
            if (!DHPullerPtr)
                return -1;
            auto startTime = steady_clock::now();
            do
            {
                auto readLen = DHPullerPtr->streamQueue.popdata(buf, bufSize);
                if (readLen > 0)
                    return readLen;
                else if (readLen < 0)       //未工作
                    return -1;
                else                        //空
                {
                    auto passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
                    if (passed > 5000)
                    {
                        ai::LogWarn << DHPullerPtr->sourceAddr << " Read No Data for " << passed << "ms";
                        return -1;
                    }
                }
            } while (DHPullerPtr->jobIsRunning());
            return 0;
        };

        pb = avio_alloc_context(ioContextBuffer, BUF_SIZE, 0, this, onReadData, nullptr, nullptr);
        if (pb == nullptr)  //分配空间失败
        {
            if (ioContextBuffer)
                av_freep(&ioContextBuffer);
            ai::LogError << this->address << "avio_alloc_context failed";
            return false;
        }

        //等待数据包
        auto startTime = steady_clock::now();
        while (jobIsRunning())
        {
            if (streamQueue.getsize() > 100000)
                break;
            int passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
            if (passed > 15000) {
                ai::LogError << this->address << " wait source stream failed " << passed << "ms";
                return false;
            }
            boost::this_fiber::sleep_for(std::chrono::milliseconds(10));
        }

        int ret = 0;
        const AVInputFormat* piFmt = nullptr;
        if ((ret = av_probe_input_buffer(pb, &piFmt, "", nullptr, 0, 0)) < 0)
        {
            if (ioContextBuffer)
                av_freep(&ioContextBuffer);
            setLastError(str(boost::format("av_probe_input_buffer %s FAILED: %s") % streamUrl() % getErrorString(ret)), ret);
            return false;
        }

        initOptions();
        FFMPEGElement::createContext();
        if(!formatCtx)
        {
            return false;
        }
        formatCtx->pb = pb;

        if ((ret = avformat_open_input(&formatCtx, "", piFmt, &options)) < 0)
        {
            setLastError(str(boost::format("avformat_open_input %s FAILED: %s") % streamUrl() % getErrorString(ret)), ret);
            return false;
        }

        if ((ret = avformat_find_stream_info(formatCtx, nullptr)) < 0)
        {
            setLastError(str(boost::format("avformat_find_stream_info %s FAILED: %s") % streamUrl() % getErrorString(ret)), ret);
            return false;
        }

        //查找视频
        const AVCodec* avCodec = nullptr;
        auto streamIndex = av_find_best_stream(formatCtx, AVMEDIA_TYPE_VIDEO, -1, -1, &(avCodec), 0);
        if (streamIndex < 0)
        {
            ai::LogError << streamUrl() << " av_find_best_stream is err";
            return false;
        }

        AVStream* avStream = formatCtx->streams[streamIndex];
        videoIndex = streamIndex;
        auto timebase = avStream->time_base;
        auto codecpar = avStream->codecpar;
        auto frameRate = av_guess_frame_rate(formatCtx, avStream, nullptr);
        duration = (int64_t)((double)AV_TIME_BASE / av_q2d(frameRate)) / 1000;

        if (duration == 0 || duration > 100)
        {
            duration = 40;
            frameRate.den = 1;
            frameRate.num = 25;
        }
        auto codecInfo = std::make_shared<CodecInfo>(timebase, frameRate, codecpar);
        this->onStreamCodecInfoRetrieved(codecInfo);

        isOpened = true;
        return true;
    }

    /**
     * @brief 读取每帧数据
     */
    bool SDKDHPuller::read()
    {
        // 连接成功 开始收包
        int ret = 0;
        AVPacket* pkt = av_packet_alloc();
        resetContextTimer();

        DO_IN_THREAD(ret = av_read_frame(formatCtx, pkt);)
        auto passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
        if(passed > 1000)
            ai::LogWarn << this->address<< " av_read_frame "<<  passed << "ms";

        // 收包错误处理
        if (ret < 0)
        {
            av_packet_unref(pkt);
            av_packet_free(&pkt);
            if ( !jobIsRunning() )
                return false;

            if (ret == AVERROR_EOF || !formatCtx->pb || avio_feof(formatCtx->pb) || formatCtx->pb->error || packetErrorCount > 5)
            {
                close();
                onStreamInputLost(); //通知web流状态
            }
            if (ret < UNKNOWN_READ_PACKET_ERROR)
                packetErrorCount++;

            setLastError(str(boost::format("av_read_frame %s FAILED: %s") % address % getErrorString(ret)), ret);
            boost::this_fiber::sleep_for(std::chrono::milliseconds(10));
            return false;
        }
        if (pkt->stream_index != videoIndex)
        {
            av_packet_unref(pkt);
            av_packet_free(&pkt);
            return false;
        }
        auto packet = std::make_shared<PacketData>(pkt);
        this->onStreamDataReceived(packet);
        frameIndex++;
        return true;
    }

    /**
     * @brief 关闭视频源，释放内存
     */
    void SDKDHPuller::close()
    {
        //关闭大华
        SDKDHPlatformPtr DHPlatformPtr = static_pointer_cast<SDKDHPlatform>(frontPlatform);
        DHPlatformPtr->stopPlay(sourceAddr);
        if (pb)
        {
            if (pb->buffer)
                av_freep(&pb->buffer);
            avio_context_free(&pb);
        }
        streamQueue.reset(true);
        StreamInput::close();
        streamQueue.fini();
        packetErrorCount = 0;
    }

    /**
     * @brief 访问视频源、搬运视频流、打开视频流、读取每帧数据
     */
    void SDKDHPuller::process()
    {
        // 初始化参数
        boost::posix_time::ptime tmBegin, tmNow;
        frameIndex = 0;
        while (jobIsRunning())
        {
            if (!isOpened) // 尝试连接
            {
                auto startTime = steady_clock::now();
                DO_IN_THREAD(this->open();)
                frameIndex = 0;
                tmBegin = boost::posix_time::microsec_clock::local_time();
                auto passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
                if (passed > 1000 && isOpened)
                    std::cerr << this->address<< " openContext "<<  passed << "ms" << std::endl;
            }

            if (!isOpened) // 连接失败 等待5s后尝试
            {
                close();
                WAIT_FOR_SECONDS(5)
                continue;
            }

            if (!read())
                continue;

            tmNow = boost::posix_time::microsec_clock::local_time();
            int actTime = (int)(tmNow - tmBegin).total_milliseconds();
            int needTime = frameIndex * duration;
            int waitTime = max(needTime - actTime - 1, 0);
            if (waitTime > 0)
                boost::this_fiber::sleep_for(std::chrono::milliseconds(waitTime));
            else
                boost::this_fiber::yield();
        }
        close();
        printInfo(str(boost::format("EXIT %s") % address));
    }


    /**
     * @brief 接收sdk通道实时数据
     */
    void SDKDHPuller::receiveRealData(uint8_t* buff, int buffSize)
    {
        uint8_t* dataBuff = buff;            //环
        int32_t totalRead = 0;
        int32_t ret = 0;
        int emptyCount = 0;
        do
        {
            ret = streamQueue.pushdata(dataBuff, buffSize);
            if (ret == 0)               //没有数据
            {
                emptyCount++;
                if (emptyCount >= 10)
                    return;
            }
            else if (ret < 0)           //跳过
                return;
            else
                emptyCount = 0;
            totalRead += ret;
            dataBuff += ret;
        } while (totalRead < buffSize && jobIsRunning());
    }

    bool SDKDHPuller::callCameraPreset(int presetId)
    {
        auto platform = static_pointer_cast<SDKDHPlatform>(frontPlatform);
        if (!platform->ptzControl(sourceAddr, presetId))
        {
            return onvifPtzInfo->callPreset(presetId);
        }
        return true;
    }

    bool SDKDHPuller::isPtzCapable()
    {
        return onvifPtzInfo->isPtzCapable() || videoSourceInfo->videoSourcePtr->getHasPtz();
    }

    /**
     * @brief                   获取云台坐标
     * @param[out] x,y,z        云台ptz坐标
     * @param[in] actPresetId:  云台真实预置位，nullopt时获取实时坐标，否则获取actPresetId对应的预置位坐标
     */
    bool SDKDHPuller::getPtzPosition(double& x, double& y, double& z, const std::optional<int>& actPresetId)
    {
        if (!onvifPtzInfo || !videoSourceInfo)
            return false;

        if (actPresetId.has_value())
        {
            //! 优先从数据库中获取预置位的基准坐标，如果数据库中没有，尝试从onvif接口获取
            if (!DATA_MANAGER.getPresetPosition(videoSourceInfo->channelId, actPresetId.value(), x,y,z))
            {
                auto platform = static_pointer_cast<SDKDHPlatform>(frontPlatform);
                if(!platform->getPresetPosition(sourceAddr, actPresetId.value(), x, y, z))
                    return onvifPtzInfo->getPresetPosition(actPresetId.value(), x, y, z);
            }
            return true;
        }
        else
        {
            auto platform = static_pointer_cast<SDKDHPlatform>(frontPlatform);
            if(!platform->getPtzPosition(sourceAddr, x, y, z)) 
                return onvifPtzInfo->getPosition(x, y, z);
        }
        return true;
    }

    bool SDKDHPuller::controlPtz(int action, int step)
    {
        auto platform = static_pointer_cast<SDKDHPlatform>(frontPlatform);
        if (!platform->controlPtz(sourceAddr, action, step))
        {
            if (!onvifPtzInfo)
                return false;

            network::EPtzCommand cmd = convertPtzCmd(action);
            onvifPtzInfo->ptzControl(cmd, step, 0);
            return true;
        }
        return true;
    }

    std::string SDKDHPuller::getLastError(void)
    {
        SDKDHPlatformPtr pDHPlatformPtr = static_pointer_cast<SDKDHPlatform>(frontPlatform);
        auto errorStr = pDHPlatformPtr->getLastError();
        if(!errorStr.empty())
            return errorStr;

        return StreamElement::getLastError();
    }
}
#endif



