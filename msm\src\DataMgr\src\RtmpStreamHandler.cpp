#include <iostream>
#include "RtmpStreamHandler.hpp"
#include "MPCStream.hpp"
#include "include/file_info.hpp"
#include "include/debug_info.hpp"

#define LOGD(...) printf(__VA_ARGS__)
#define LOGE(...) printf(__VA_ARGS__)

namespace msm
{
    #define BUF_SIZE        (16 * 1024)         // ffpmeg每次从原始数据缓冲区中取的原始数据大小，单位字节.
    #define DATAQUEUE_SIZE  (2 * 1024 * 1024)   // 原始数据缓冲区的大小，单位字节.

    #define USE_FFMPEG_LOW_VERSION 1
 
    static int32_t readData_cb( void* opaque, uint8_t* buf, int32_t size )
    {
        CRtmpStreamHandler* pThis = ( CRtmpStreamHandler* )opaque;
        if ( pThis )
        {
            return pThis->readData( buf, size );
        }

        return -1;
    }

    static int32_t writeData_cb( void* opaque, uint8_t* buf, int32_t size )
    {
        CRtmpStreamHandler* pThis = ( CRtmpStreamHandler* )opaque;
        if ( pThis )
        {
            return 0;//pThis->writeData( buf, size );
        }

        return -1;
    }

    // 返回0-继续阻塞直到ffmpeg正常工作为止 
    // 返回1-代表ffmpeg结束阻塞，将操纵权交给用户线程
    static int32_t avPullInterrupt_cb(void *param)  
    {  
        CRtmpStreamHandler* pThis = ( CRtmpStreamHandler* )param;
        if ( !pThis || pThis->isPullStreamTimeOut() )
        {
            return 1;
        }

        return 0; 
    }

    static int32_t avPushInterrupt_cb(void *param)  
    {  
        CRtmpStreamHandler* pThis = ( CRtmpStreamHandler* )param;
        if ( !pThis || pThis->isPushStreamTimeOut() )
        {
            return 1;
        }

        return 0; 
    }

    static int checkStreamFindInfo(void* arg)
    {
        CRtmpStreamHandler *pThis = (CRtmpStreamHandler*)arg;
        if (!pThis) return 1;
    
        AVFormatContext* fmt_ctx=pThis->m_fmtCtxIn;
        if (!fmt_ctx) return 1;

        int nb_streams = fmt_ctx->nb_streams;
        for (int index = 0; index < nb_streams; ++index)
        {
            AVStream* stream = fmt_ctx->streams[index];
            #ifdef USE_FFMPEG_LOW_VERSION
            if (stream && stream->codec->codec_type == AVMEDIA_TYPE_VIDEO && stream->nb_decoded_frames > 0)
            {
                fmt_ctx->probesize = 512 * 1024;
                fmt_ctx->max_analyze_duration = 1 * AV_TIME_BASE;
                std::cout<<"videoID: "<<pThis->m_id<<"  nb_decoded_frames : "<<stream->nb_decoded_frames<<std::endl;
            }
            #endif
        }

        return 0;
    }

    CRtmpStreamHandler::CRtmpStreamHandler( uint32_t id )
                      : m_id( id )
					  , m_sendToRtmpServer( true )
                      , m_init( false )
                      , m_quit( false )
                      , m_hasException( false )
                      , m_startTime( 0 )
                      , m_fmtCtxIn( NULL )
                      , m_fmtCtxOut( NULL )   
					  , m_ioCtxIn( NULL )
					  , m_bShowFps( false )
					  , m_frame_count( 0 )
                      , m_reportOk(true)
    {

    }

    CRtmpStreamHandler::~CRtmpStreamHandler()
    {

    }

	bool CRtmpStreamHandler::init()
	{
		return true;
	}

	bool CRtmpStreamHandler::fini()
	{
		stop();
		return true;
	}

    bool CRtmpStreamHandler::play( const std::string &rtmpUrl, CMPCStream *userHandler, const bool sendToRtmpServer )
    {
		LOGD("rtmpUrl %s\n", rtmpUrl.c_str() );
        if ( /*!userHandler || */rtmpUrl.empty() )
        {
            return false;
        }

        if ( !m_streamData.init( DATAQUEUE_SIZE ) )
        {
			LOGD("streamData init fail\n");
            return false;
        }

        {
            boost::mutex::scoped_lock lock( m_mutex );

            if ( m_init )
            {
                LOGD("CRtmpStreamHandler already init. \n");
                return false;
            }

            m_init = true;
        }

        m_quit = false;
        m_userHandler = userHandler;

        getTransformParam( sendToRtmpServer, rtmpUrl );        

        m_thread.reset( new (std::nothrow) boost::thread( boost::bind( &CRtmpStreamHandler::streamHandlerThread, this ) ) );
        if ( !m_thread )
        {
            fini();
			LOGD("streamHandlerThread create failed\n");
            return false;
        }

        m_streamData.reset( false );

        return true;
    }

    bool CRtmpStreamHandler::stop()
    {
        {
            boost::mutex::scoped_lock lock( m_mutex );
            if ( !m_init )
            {
                return false;
            }

            m_init = false;
        }

        m_quit = true;
		m_szStreamInfo = "";

        if ( m_fmtCtxIn && m_fmtCtxIn->pb )
        {
            m_fmtCtxIn->pb->eof_reached = 1;
        }

        m_streamData.reset( true );

        if ( m_thread )
        {
            m_thread->join();
            m_thread.reset();
        }

        m_streamData.fini();
		m_hasException = false;
		m_userHandler = NULL;
        return true;
    }

    bool CRtmpStreamHandler::inputMediaData( uint8_t* data, uint32_t len )
    {
        if ( !m_sendToRtmpServer || m_hasException )
        {
            return false;
        }

        return ( m_streamData.pushdata( data, len ) > 0 );
    }

	std::string CRtmpStreamHandler::showStatus()
	{
		return m_szStreamInfo;
	}

	void CRtmpStreamHandler::showFps()
	{
		m_bShowFps = !m_bShowFps;
		m_frame_count = 0;
		m_tmLastFps = getCurTime();
	}

	void CRtmpStreamHandler::getTransformParam( const bool sendToRtmpServer, const std::string &rtmpUrl )
    {
        m_sendToRtmpServer = sendToRtmpServer;

        if ( m_sendToRtmpServer )
        {
            // 发送ps流到rtmp服务器
            m_inTransParam.muxType     = EMUXTYPE_PS;
            m_inTransParam.formatName  = FORMATNAME_PS_IN;
            m_inTransParam.fileName    = std::string("");

            m_outTransParam.muxType    = EMUXTYPE_FLV;
            m_outTransParam.formatName = FORMATNAME_FLV;
            m_outTransParam.fileName   = rtmpUrl;
            m_outTransParam.needAudio  = true;
        }
        else
        {
            // 从rtmp服务器接收ps流
            m_inTransParam.muxType     = EMUXTYPE_FLV;
            m_inTransParam.formatName  = FORMATNAME_FLV;
            m_inTransParam.fileName    = rtmpUrl;

            m_outTransParam.muxType    = EMUXTYPE_PS;
            m_outTransParam.formatName = FORMATNAME_PS_OUT;
            m_outTransParam.fileName   = std::string("");
            m_outTransParam.needAudio  = false;
        }
    }

    int32_t CRtmpStreamHandler::readData( uint8_t* buf, int32_t size )
    {
        int32_t ret = 0;

        do
        {
            ret = m_streamData.popdata( buf, size ); // 没有读够,则返回-1,以中止循环.
            if ( ret < 0 )
            {
                return -1;
            }
        } while ( ret == 0 && !m_quit );

        if (m_quit)
            return -1;

        return ret;
    }

    void CRtmpStreamHandler::streamHandlerThread()
    {
        bool ret = false;
        if ( initInput() && initOutput() )
        {
            ret = transform();
        }

        finiOutput();
        finiInput();

        if ( !m_quit && !ret )
        {   
            notifyException();
        } 
    }

    void CRtmpStreamHandler::notifyException()
    {
        bool hasException = m_hasException;
        m_hasException = true;

        {
            boost::mutex::scoped_lock lock( m_mutex );
            if ( !m_init )
            {
                return;
            }
        }

        if ( m_userHandler && !hasException )
        {
            m_userHandler->rtmpStreamException();
        }
    }

    bool CRtmpStreamHandler::isPushStreamTimeOut()
    {
        return m_quit;
    }

    bool CRtmpStreamHandler::isPullStreamTimeOut()
    {
        if ( m_quit )
        {
            return true;
        }

        time_t curTime = time( NULL );
        if ( curTime > m_startTime && curTime - m_startTime > STREAM_TIME_OUT )
        {
            m_quit = true;
            LOGD("CRtmpStreamHandler stream time out!!! \n");
            return true;
        }

        return false;
    }

    bool CRtmpStreamHandler::initInput()
    {
        int32_t ret = 0;
        time_t elapsed_time = 0;

        writeLog("initInput being " + int2string(m_id));
        m_reportOk = true;

        do 
        {
            m_fmtCtxIn = avformat_alloc_context();
            if ( !m_fmtCtxIn )
            {
                ret = AVERROR_UNKNOWN;
                writeLog("CRtmpStreamHandler avformat_alloc_context error: " + err2str(ret) + ", videoID:" + int2string(m_id));
                break;
            }

            m_startTime = time( NULL );

            AVInputFormat* inputFmt = NULL;

            if ( m_sendToRtmpServer )
            {
                // 推流
                uint8_t* pBuf = ( uint8_t* )av_mallocz( sizeof(uint8_t) * BUF_SIZE );
                if ( !pBuf )
                {
                    ret = AVERROR_UNKNOWN;  
                    writeLog("CRtmpStreamHandler avio_alloc_context error: " + err2str(ret) + ", videoID:" + int2string(m_id));
                    break;
                }

                m_ioCtxIn = avio_alloc_context( pBuf, BUF_SIZE, 0, this, readData_cb, NULL, NULL );
                if ( !m_ioCtxIn )
                {
                    av_free( pBuf );
                    pBuf = NULL;

                    ret = AVERROR_UNKNOWN;
                    writeLog("CRtmpStreamHandler avio_alloc_context error: " + err2str(ret) + ", videoID:" + int2string(m_id));
                    break;
                }

                ret = av_probe_input_buffer( m_ioCtxIn, &inputFmt, NULL, NULL, 0, 0 );
                if ( ret < 0 )
                {
					if ( pBuf )
					{
						av_free( pBuf );
						pBuf = NULL;
					}

					if ( m_ioCtxIn )
					{
						av_free( m_ioCtxIn );
						m_ioCtxIn = NULL;
					}

                    writeLog("CRtmpStreamHandler av_probe_input_buffer error: " + err2str(ret) + ", videoID:" + int2string(m_id));
                    break;
                }

                m_fmtCtxIn->pb = m_ioCtxIn;
                elapsed_time = time( NULL ) - m_startTime;
                writeLog("CRtmpStreamHandler av_probe_input_buffer elapsed time: " + int2string(elapsed_time));
            }
            else
            {
                // 拉流
                m_fmtCtxIn->interrupt_callback.opaque   = this;             // 流超时检测
                m_fmtCtxIn->interrupt_callback.callback = avPullInterrupt_cb;
            }

            // 推流时,不输入数据会阻塞在avformat_open_input,依靠使用者主动调用fini()来结束处理
            // 拉流时,无法从rtmp服务获取数据会阻塞在avformat_find_stream_info,依靠设置的avPullInterrupt_cb来检测无数据,并通知使用者
            ret = avformat_open_input( &m_fmtCtxIn, m_inTransParam.fileName.empty() ? NULL : m_inTransParam.fileName.c_str(), inputFmt, NULL );
            if ( ret < 0 )
            {
                writeLog("CRtmpStreamHandler avformat_open_input error: " + err2str(ret) + ", videoID:" + int2string(m_id));
                break;
            }

            elapsed_time = time( NULL ) - m_startTime;
            //LOGD("CRtmpStreamHandler avformat_open_input elapsed time:%d second\n", elapsed_time) ;

            //m_fmtCtxIn->probesize = 256 * 1024;
            //m_fmtCtxIn->max_analyze_duration = 10 * AV_TIME_BASE;
			//m_fmtCtxIn->flags |= AVFMT_FLAG_DISCARD_CORRUPT;

            m_fmtCtxIn->video_codec_id = AV_CODEC_ID_H264;
            // m_fmtCtxIn->raw_packet_buffer_remaining_size = 256 * 1024;

            #ifdef USE_FFMPEG_LOW_VERSION
            // AVIOInterruptCB icb = { checkStreamFindInfo, this };
            AVIOInterruptCB icb = { avPullInterrupt_cb, this };
            m_fmtCtxIn->interrupt_callback = icb;
            #endif

            ret = avformat_find_stream_info( m_fmtCtxIn, NULL );
            if ( ret < 0 )
            {
                writeLog("CRtmpStreamHandler avformat_find_stream_info error: " + err2str(ret) + ", videoID:" + int2string(m_id));

                #ifdef USE_FFMPEG_LOW_VERSION
                icb.callback = NULL;
                icb.opaque   = NULL;
                m_fmtCtxIn->interrupt_callback = icb;
                #endif
                break;
            }

            #ifdef USE_FFMPEG_LOW_VERSION
            icb.callback = NULL;
            icb.opaque   = NULL;
            m_fmtCtxIn->interrupt_callback = icb;
            #endif

            elapsed_time = time( NULL ) - m_startTime;
            writeLog("CRtmpStreamHandler avformat_find_stream_info elapsed: " + int2string(elapsed_time) + ", videoID:" + int2string(m_id));

            for ( uint32_t i = 0; i < m_fmtCtxIn->nb_streams; ++i )
            {
                AVStream *in_stream = m_fmtCtxIn->streams[i];
                if ( !in_stream )
                {
                    continue;
                }

                #ifdef USE_FFMPEG_LOW_VERSION
                if ( in_stream->codec->codec_type == AVMEDIA_TYPE_VIDEO )
                #else
                if ( in_stream->codecpar->codec_type == AVMEDIA_TYPE_VIDEO )  
                #endif
                {  
                    writeLog("     avg_frame_rate:" + int2string(av_q2d(in_stream->avg_frame_rate)) + ", r_frame_rate:" + int2string(av_q2d(in_stream->avg_frame_rate)) + ", time_base:" + int2string(av_q2d(in_stream->time_base)));
                    if (in_stream->avg_frame_rate.den == 0 || in_stream->avg_frame_rate.num == 0) {
                         in_stream->avg_frame_rate.den = 1;
                         in_stream->avg_frame_rate.num = 25;
                    }

                    m_inTransParam.videoIndex = i;
					AVRational frameRate = in_stream->avg_frame_rate;
					uint32_t fps = (uint32_t)av_q2d(frameRate);
                    #ifdef USE_FFMPEG_LOW_VERSION
                    m_szStreamInfo = int2string(in_stream->codec->width) + "*" + int2string(in_stream->codec->height) + ",FPS " + int2string(fps) + " ";
                    #else
					m_szStreamInfo = int2string(in_stream->codecpar->width) + "*" + int2string(in_stream->codecpar->height) + ",FPS " + int2string(fps) + " ";
                    #endif
                }  
            }
            
            writeLog("CRtmpStreamHandler stream info " + m_szStreamInfo + ", videoID:" + int2string(m_id));
            av_dump_format( m_fmtCtxIn, 0, m_inTransParam.fileName.c_str(), 0 );

            if ( m_inTransParam.videoIndex == STREAM_INDEX_INVAIL )
            {
                writeLog("CRtmpStreamHandler not find video stream  videoID:" + int2string(m_id));

                ret = AVERROR_STREAM_NOT_FOUND;
                break;
            }

            #ifdef USE_FFMPEG_LOW_VERSION
            ret = url_feof(m_fmtCtxIn->pb);
            #else
            ret = avio_feof( m_fmtCtxIn->pb );
            #endif
            if ( ret < 0 )
            {
                writeLog("CRtmpStreamHandler avio_feof error: " + err2str(ret));
                break;
            }

            elapsed_time = time( NULL ) - m_startTime;
            writeLog("CRtmpStreamHandler initinput total elapsed time: " + int2string(elapsed_time) + ", videoID:" + int2string(m_id));

        } while ( false );

        return (ret < 0) ? false : true;
    }

    bool CRtmpStreamHandler::initOutput()
    {
        int32_t ret = 0;
		time_t start_time = time(NULL);
		time_t elapsed_time = 0;

        writeLog("initOutput being " + int2string(m_id));

        do
        {
            ret = avformat_alloc_output_context2( &m_fmtCtxOut, NULL, m_outTransParam.formatName.c_str(), m_outTransParam.fileName.c_str() );
            if ( ret < 0 )
            {
                writeLog("CRtmpStreamHandler avformat_alloc_output_context2 error: " + err2str(ret) + ", videoID:" + int2string(m_id));
                break;
            }

            AVOutputFormat *outputFmt = m_fmtCtxOut->oformat;
            if ( !outputFmt )
            {
                ret = AVERROR_UNKNOWN;
                writeLog("CRtmpStreamHandler outputFmt is empty error: " + err2str(ret) + ", videoID:" + int2string(m_id));
                break;
            }

            for ( uint32_t i = 0; i < m_fmtCtxIn->nb_streams; ++i )
            {
                AVStream *in_stream = m_fmtCtxIn->streams[i];
                #ifdef USE_FFMPEG_LOW_VERSION
                if ( !in_stream || !in_stream->codec )
                #else
                if ( !in_stream || !in_stream->codecpar )
                #endif
                {
                    continue;
                }

                #ifdef USE_FFMPEG_LOW_VERSION
                if ( /*in_codecpar->codec_type != AVMEDIA_TYPE_AUDIO &&*/
                    in_stream->codec->codec_type != AVMEDIA_TYPE_VIDEO  || in_stream->codec->pix_fmt == -1)
                #else
                AVCodecParameters *in_codecpar = in_stream->codecpar;
                if ( /*in_codecpar->codec_type != AVMEDIA_TYPE_AUDIO &&*/
                    in_codecpar->codec_type != AVMEDIA_TYPE_VIDEO )
                #endif
                {
                    continue;
                }

                AVStream *out_stream = avformat_new_stream( m_fmtCtxOut, NULL );
                if ( !out_stream )
                {
                    ret = AVERROR_UNKNOWN;
                    writeLog("CRtmpStreamHandler avformat_new_stream error: " + err2str(ret) + ", videoID:" + int2string(m_id));
                    break;
                }

                #ifdef USE_FFMPEG_LOW_VERSION
                ret = avcodec_copy_context(out_stream->codec, in_stream->codec);
                #else
                ret = avcodec_parameters_copy( out_stream->codecpar, in_codecpar );
                #endif
                if ( ret < 0 )
                {
                    writeLog("CRtmpStreamHandler avcodec_copy_context error: " + err2str(ret) + ", videoID:" + int2string(m_id));
                    break;
                }

                out_stream->start_time = 0;
                #ifdef USE_FFMPEG_LOW_VERSION
                out_stream->codec->codec_tag = 0;
                #else
                out_stream->codecpar->codec_tag = 0;
                #endif
                out_stream->avg_frame_rate = in_stream->avg_frame_rate;

                out_stream->time_base.den = in_stream->time_base.den;
                out_stream->time_base.num = in_stream->time_base.num;

                #ifdef USE_FFMPEG_LOW_VERSION
                if(out_stream->codec->time_base.den == 0 || out_stream->codec->time_base.num == 0)
                {
                    out_stream->codec->time_base = (AVRational){1,90000};
                }

                if ( in_stream->codec->codec_type == AVMEDIA_TYPE_VIDEO )
                #else
                if ( in_stream->codecpar->codec_type == AVMEDIA_TYPE_VIDEO )
                #endif
                {  
                    m_outTransParam.videoIndex = m_fmtCtxOut->nb_streams - 1;
                }
            }

            if ( ret < 0 )
            {
                break;
            }

            if ( !m_sendToRtmpServer )
            {
                // 拉流
                uint8_t* pBuf = (uint8_t *)av_mallocz( sizeof(uint8_t) * BUF_SIZE );
                if ( !pBuf )
                {
                    ret = AVERROR_UNKNOWN;
                    writeLog("CRtmpStreamHandler av_mallocz error: " + err2str(ret) + ", videoID:" + int2string(m_id));
                    break;
                }

                AVIOContext* ioCtx = avio_alloc_context( pBuf, BUF_SIZE, 1, this, NULL, writeData_cb, NULL );
                if ( !ioCtx )
                {
                    av_free( pBuf );
                    pBuf = NULL;

                    ret = AVERROR_UNKNOWN;
                    writeLog("CRtmpStreamHandler avio_alloc_context error: " + err2str(ret) + ", videoID:" + int2string(m_id));
                    break;
                }

                m_fmtCtxOut->pb = ioCtx;
                m_fmtCtxOut->flags |= AVFMT_FLAG_CUSTOM_IO;
                m_fmtCtxOut->packet_size = 5120;
            }
            else
            {
                // 推流
                if ( !(outputFmt->flags & AVFMT_NOFILE) )
                {  
                    const AVIOInterruptCB int_cb = { avPushInterrupt_cb, this };

                    ret = avio_open2( &m_fmtCtxOut->pb, m_outTransParam.fileName.c_str(), AVIO_FLAG_WRITE, &int_cb, NULL );
                    if ( ret < 0 )
                    {  
                        LOGE("CRtmpStreamHandler avio_open %d  error: %s. \n", m_id, err2str(ret).c_str());
                        break;
                    }  
                }
            }

            av_dump_format( m_fmtCtxOut, 0, m_outTransParam.fileName.c_str(), 1 );
              
            AVDictionary* opts = NULL;
            if ( m_sendToRtmpServer )
            {
                av_dict_set( &opts, "flvflags", "no_duration_filesize", 0 );
            }
            ret = avformat_write_header( m_fmtCtxOut, &opts );
            av_dict_free( &opts );
            if ( ret < 0 )
            {
                writeLog("CRtmpStreamHandler avformat_write_header error: " + err2str(ret) + ", videoID:" + int2string(m_id));
                break;
            }
			elapsed_time = time( NULL ) - start_time;
            writeLog("CRtmpStreamHandler initOutput total elapsed time: " + int2string(elapsed_time) + ", videoID:" + int2string(m_id));


        } while ( false );

        return (ret < 0) ? false : true;
    }

    void CRtmpStreamHandler::finiInput()
    {
        writeLog("finiInput  " + int2string(m_id));
        m_reportOk = false;
        if ( !m_fmtCtxIn ) {
            return;
        }

        #ifdef USE_FFMPEG_LOW_VERSION
        if ((m_fmtCtxIn->flags & AVFMT_FLAG_CUSTOM_IO) && m_fmtCtxIn->pb) {
            if ( m_fmtCtxIn->pb->buffer )
            {
                av_free( m_fmtCtxIn->pb->buffer );
                m_fmtCtxIn->pb->buffer = NULL;
            }
            //avio_context_free( &m_fmtCtxIn->pb );
            av_free(m_fmtCtxIn->pb);
            //avio_closep(&m_fmtCtxIn->pb);
        }
        #else
        if (m_ioCtxIn) {
            av_freep(&m_ioCtxIn->buffer);
            avio_context_free(&m_ioCtxIn);
        }
        #endif

        avformat_close_input(&m_fmtCtxIn);
        m_fmtCtxIn = NULL;
    }

    void CRtmpStreamHandler::finiOutput()
    {
        writeLog("finiOutput  " + int2string(m_id));
        #ifdef USE_FFMPEG_LOW_VERSION
        if ( !m_fmtCtxOut )
        {
            return;
        }

        if ( ( m_fmtCtxOut->flags & AVFMT_FLAG_CUSTOM_IO ) &&  m_fmtCtxOut->pb )
        {
            if ( m_fmtCtxOut->pb->buffer )
            {
                av_free( m_fmtCtxOut->pb->buffer );
                m_fmtCtxOut->pb->buffer = NULL;
            }
            //avio_context_free( &m_fmtCtxOut->pb );
            av_free(m_fmtCtxOut->pb);
        }
        else
        {
            AVOutputFormat* outputFmt = m_fmtCtxOut->oformat;
            if ( outputFmt && !( outputFmt->flags & AVFMT_NOFILE ) && m_fmtCtxOut->pb )
            {
                int32_t ret = avio_closep( &m_fmtCtxOut->pb );
                if ( ret < 0 )
                {
                    LOGE("CRtmpStreamHandler avio_close error: %s. \n", err2str(ret).c_str());
                }
            }
        }

        avformat_free_context( m_fmtCtxOut );
        m_fmtCtxOut = NULL;
        #else
        if ( !m_fmtCtxOut )
        {
            return;
        }

        if ( ( m_fmtCtxOut->flags & AVFMT_FLAG_CUSTOM_IO ) &&  m_fmtCtxOut->pb )
        {
            if ( m_fmtCtxOut->pb->buffer )
            {
                av_freep( &m_fmtCtxOut->pb->buffer );
            }
            avio_context_free( &m_fmtCtxOut->pb );
        }
        else
        {
            AVOutputFormat* outputFmt = m_fmtCtxOut->oformat;
            if ( outputFmt && !( outputFmt->flags & AVFMT_NOFILE ) && m_fmtCtxOut->pb )
            {
                int32_t ret = avio_closep( &m_fmtCtxOut->pb );
				if ( ret < 0 )
				{
					if ( !m_quit  )                
						LOGE("CRtmpStreamHandler avio_close error: %s. \n", err2str(ret).c_str());
					else
						ret = 0;
				}
            }
        }

        avformat_free_context( m_fmtCtxOut );
        m_fmtCtxOut = NULL;
        #endif
    }

    bool CRtmpStreamHandler::transform()
    {
        int32_t ret          = 0;
        int64_t last_pts     = 0;

        AVStream* in_stream  = NULL;
        AVStream* out_stream = NULL;
        int32_t stream_index = STREAM_INDEX_INVAIL;

        writeLog("transform " + int2string(m_id));

        AVPacket pkt;
        av_init_packet(&pkt);
        while ( true )
        {
            ret = av_read_frame(m_fmtCtxIn, &pkt);
            if (ret < 0) 
            {
                #ifdef USE_FFMPEG_LOW_VERSION
                if ( ret == AVERROR_EOF || url_feof( m_fmtCtxIn->pb ) || m_fmtCtxIn->pb->error )
                #else
                if ( ret == AVERROR_EOF || avio_feof( m_fmtCtxIn->pb ) || m_fmtCtxIn->pb->error )
                #endif
                {
                    writeLog("CRtmpStreamHandler av_read_frame error: " + err2str(ret) + ", videoID:" + int2string(m_id));
                    break;
                }
                else
                {
                    writeLog("CRtmpStreamHandler av_read_frame error2: " + err2str(ret) + ", videoID:" + int2string(m_id));
                    av_packet_unref( &pkt );
                    continue;
                }
            }

            m_startTime  = time( NULL );
            stream_index = STREAM_INDEX_INVAIL;

            if ( pkt.stream_index == STREAM_INDEX_INVAIL )
            {
                av_packet_unref(&pkt);
                continue;
            }

            if ( pkt.stream_index == m_inTransParam.videoIndex )
            {
                stream_index = m_outTransParam.videoIndex;
            }

            if ( stream_index == STREAM_INDEX_INVAIL )
            {
                av_packet_unref(&pkt);
                continue;
            }

            //if ( pkt.pts == AV_NOPTS_VALUE )
            {  
                // 内部生成时间戳
                AVRational time_base = m_fmtCtxIn->streams[m_inTransParam.videoIndex]->time_base;
                AVRational frame_rate = m_fmtCtxIn->streams[m_inTransParam.videoIndex]->avg_frame_rate;
                if (frame_rate.den == 0 || frame_rate.num == 0)
                {
                    frame_rate = m_fmtCtxIn->streams[m_inTransParam.videoIndex]->r_frame_rate;
                    if (frame_rate.den == 0 || frame_rate.num == 0)
                    {
                        frame_rate.den = 1;
                        frame_rate.num = 25;
                    }
                }
                if ( time_base.num == 0 || time_base.den == 0 || frame_rate.num == 0 || frame_rate.den == 0 )
                {
                    writeLog("CRtmpStreamHandler time_base error  videoID:" + int2string(m_id));
                    av_packet_unref(&pkt);
                    continue;
                }
                //int64_t calc_duration = (double)( AV_TIME_BASE / av_q2d( frame_rate ) );
                //int64_t duration = (double)( calc_duration ) / (double)( av_q2d( time_base ) * AV_TIME_BASE );
                int64_t duration = (int64_t)( 1 / ( av_q2d( frame_rate ) * av_q2d( time_base ) ) );
                if ( pkt.stream_index == m_inTransParam.videoIndex )
                {
                    pkt.pts = last_pts + duration;
                }
                //else if ( pkt.stream_index == m_inTransParam.audioIndex )
                //{
                //    if ( last_pts == 0 )
                //    {
                //        av_packet_unref( &pkt );
                //        continue;
                //    }
                //    pkt.pts = last_pts + duration / 2;
                //}
                pkt.dts = pkt.pts;  
                pkt.duration = duration;  
            }

            if ( pkt.stream_index == m_inTransParam.videoIndex )
            {
                last_pts = pkt.pts;
            }

            in_stream  = m_fmtCtxIn->streams[pkt.stream_index];
            out_stream = m_fmtCtxOut->streams[stream_index];
            pkt.stream_index = stream_index;

            // copy packet
            pkt.pts = av_rescale_q_rnd( pkt.pts, in_stream->time_base, out_stream->time_base, (AVRounding)( AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX ) );  
            pkt.dts = av_rescale_q_rnd( pkt.dts, in_stream->time_base, out_stream->time_base, (AVRounding)( AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX ) );  
            pkt.duration = av_rescale_q( pkt.duration, in_stream->time_base, out_stream->time_base );  
            pkt.pos = -1;
            
			boost::posix_time::ptime tmNow = getCurTime();
			m_frame_count ++;
			if (m_bShowFps && (tmNow - m_tmLastFps).seconds() >= 10)
			{
				LOGE("******* VIDEO %d FPS %d AT: %s\n", m_id, m_frame_count/10, boost::posix_time::to_simple_string(tmNow).c_str() );
				m_frame_count = 0;
				m_tmLastFps = tmNow;
			}

            ret = av_interleaved_write_frame( m_fmtCtxOut, &pkt );
            if ( ret < 0 )
            {
				if ( !m_quit )
					LOGE("CRtmpStreamHandler. rtmpUrl: %s. av_interleaved_write_frame error: %s. \n", m_outTransParam.fileName.c_str(), err2str(ret).c_str());
				else
					ret = 0;
                break;
            }
            av_packet_unref( &pkt );

			if ( m_userHandler && m_reportOk)
			{
				m_userHandler->rtmpStreamOk();
                m_reportOk = false;
			}
        }

        av_packet_unref( &pkt );
        av_write_trailer( m_fmtCtxOut );

        writeLog("transform end " + int2string(m_id));

        return (ret < 0) ? false : true;
    }
} // namespace msm

