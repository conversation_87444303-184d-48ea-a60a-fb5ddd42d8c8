
#ifndef STRUCTEXCHANGE_CPP_
#define STRUCTEXCHANGE_CPP_

#include "StructExchange.hpp"

namespace gb28181
{

    CStructExchange::CStructExchange()
    {

    }

    bool CStructExchange::realMediaGbtToWtoe( const gb28181::SRealMedia &from, usg::SRealMedia &to )
    {
        //from.resAddr; 没有转换.
        //from.privilege; 没有转换.
        //from.maxBitrate; 没有转换.
        //from.supportFormatTypes 没有转换
        if( /*from.supportFormatTypes.empty() ||*/
                ( from.supportVideoTypes.empty() && from.supportAudioTypes.empty() ) )
            return false;
        //to.supportFormatTypes.clear();
        //{
        //    std::vector< gb28181::EFormatType >::const_iterator it( from.supportFormatTypes.begin() );
        //    std::vector< gb28181::EFormatType >::const_iterator end( from.supportFormatTypes.end() );
        //    for( ; it != end; ++it )
        //    {
        //        msg::EFormatType tmp;
        //        if( transEFormatType( (*it), tmp ) )
        //        {
        //            to.supportFormatTypes.push_back( tmp );
        //        }
        //    }
        //}
        to.supportVideoTypes.clear();
        {
            std::vector< gb28181::EVideoType >::const_iterator it( from.supportVideoTypes.begin() );
            std::vector< gb28181::EVideoType >::const_iterator end( from.supportVideoTypes.end() );
            for( ; it != end; ++it )
            {
                usg::EVideoType tmp;
                if( transEVideoType( (*it), tmp ) )
                {
                    to.supportVideoTypes.push_back( tmp );
                }
            }
        }
        to.supportAudioTypes.clear();
        {
            std::vector< gb28181::EAudioType >::const_iterator it( from.supportAudioTypes.begin() );
            std::vector< gb28181::EAudioType >::const_iterator end( from.supportAudioTypes.end() );
            for( ; it != end; ++it )
            {
                usg::EAudioType tmp;
                if( transEAudioType( (*it), tmp ) )
                {
                    to.supportAudioTypes.push_back( tmp );
                }
            }
        }
        if( to.supportFormatTypes.empty() ||
            ( to.supportVideoTypes.empty() && to.supportAudioTypes.empty() )
                )
            return false;
        if( !transESocketType( from.socketType, to.socketType ) )
            return false;
        to.sockAddr = from.sockAddr;
        to.sockPort = from.sockPort;
        return true;
    }

    bool CStructExchange::realMediaResponseWtoeToGbt( const usg::SRealMediaResponse &from, gb28181::SRealMediaResponse &to )
    {
        // to.resAddr; // wtoe中本身无此值,此值由外界填充.
        if( !transEFormatType( from.formatType, to.formatType ) )
            return false;
        if( !transEVideoType( from.videoType, to.videoType ) )
            return false;
        if( !transEAudioType( from.audioType, to.audioType ) )
            return false;
        to.bitrate = from.bitRate;
        if( !transESocketType( from.socketType, to.socketType ) )
            return false;
        to.sockAddr = from.sockAddr;
        to.sockPort = from.sockPort;
        //to.decoderTag; // wtoe中本身无此值,所以由外界填充.
        return true;
    }

    bool  CStructExchange::realMediaResponseGbtToWtoe( const gb28181::SRealMediaResponse & from, usg::SRealMediaResponse &to )
    {
        transEAudioType(from.audioType,to.audioType);
        //to.audioType = from.audioType;
        to.bitRate = from.bitrate;
        //to.formatType = from.formatType;
        transEVideoType(from.videoType,to.videoType);
        //to.videoType = from.videoType;
        transEFormatType(from.formatType,to.formatType);
        return true;
    }

    bool CStructExchange::historyListGbtToWtoe( const gb28181::SFileList &from, usg::SHistoryList &to )
    {
        //from.resAddr; 没有转换.
        //from.privilege; 没有转换.
        //from.fileType; 没有转换.

        if( !utcString2UtcTime_t( from.beginTime, to.startTime ) )
            return false;
        if( !utcString2UtcTime_t( from.endTime, to.endTime ) )
            return false;

        to.fromIndex = 1;

        return true;
    }

    bool CStructExchange::historyListResponseWtoeToGbt( const usg::SHistoryListResponse &from, gb28181::SFileListResponse &to )
    {
        to.result = !from.isOk;
        to.realFileNum = from.allItemSize;
        to.fileInfolist.clear();

        // to.resAddr // wtoe中本身无此值,所以由外界填充.
        if( from.files.empty() )
        {
            return true;
        }

        to.sendFileNum = from.toIndex - from.fromIndex + 1;
        std::vector< usg::SHistoryListResponse::SItem >::const_iterator it( from.files.begin() );
        std::vector< usg::SHistoryListResponse::SItem >::const_iterator end( from.files.end() );
        for( ; it != end; ++it )
        {
            gb28181::SFileListResponse::SItem tmp;
            tmp.itemBeginTime = utcTime_t2UtcString( (*it).startTime );
            tmp.itemEndTime = utcTime_t2UtcString( (*it).endTime );
            tmp.itemName = tmp.itemBeginTime; // 没有文件名,用开始时间作名字.
            tmp.itemFileSize = 500000; // 没有文件大小,任意给个值.
            to.fileInfolist.push_back( tmp );
        }
        return true;
    }

    bool CStructExchange::historyListResponseGbtToWtoe( const gb28181::SFileListResponse &from, usg::SHistoryListResponse &to )
    {
        if (from.fileInfolist.empty())
        {
            to.allItemSize = 0;
            return false;
        }

        to.isOk = !from.result;
        to.fromIndex = from.realFileNum - from.sendFileNum;
        to.allItemSize = from.realFileNum;
        to.files.clear();
        std::vector<gb28181::SFileListResponse::SItem>::const_iterator it(from.fileInfolist.begin() );
        std::vector<gb28181::SFileListResponse::SItem>::const_iterator end(from.fileInfolist.end() );
        for ( ; it != end; ++it)
        {
            usg::SHistoryListResponse::SItem tmpItem;
            utcString2UtcTime_t((*it).itemBeginTime,tmpItem.startTime);
            utcString2UtcTime_t((*it).itemEndTime,tmpItem.endTime);
            tmpItem.fileName = (*it).itemName;
            to.files.push_back(tmpItem);
        }

        return true;
    }

    bool CStructExchange::historyMediaGbtToWtoe( const gb28181::SHistoryMedia &from, usg::SHistoryMedia &to )
    {
//     from.resAddr;没有转换.
//     from.fileType;没有转换.
//     from.name;没有转换.
//     from.endTime;没有转换.
//     from.maxBitrate;没有转换.
        if( !utcString2UtcTime_t( from.beginTime, to.startTime ) )
            return false;
        if( !utcString2UtcTime_t( from.endTime, to.endTime ) )
            return false;
        return true;
    }

    bool CStructExchange::historyMediaResponseWtoeToGbt( const usg::SHistoryMediaResponse &from, gb28181::SHistoryMediaResponse &to )
    {
        //to.resAddr;// wtoe中本身无此值,所以由外界填充.
        to.result = !from.isOk;
        to.bitrate = 512; // 暂没有值, 预计WTOE全CIF,所以给512;
        to.playUrl = from.playUrl;
        return true;
    }

    bool CStructExchange::historyMediaResponseGbtToWtoe( const gb28181::SHistoryMediaResponse &from, usg::SHistoryMediaResponse &to)
    {
        to.isOk = !from.result;
        to.playUrl = from.playUrl;
        return true;
    }

    bool CStructExchange::ptzCommandGb28181ToWtoe( const gb28181::SPtzCommand &from, std::vector<usg::SPtzCommand> &to )
    {
//     from.resAddr;没有转换.
//     from.privilege;没有转换.
        if ( !transPtzCommandGb28181ToWtoe( from, to ) )
            return false;
        return true;
    }

    bool CStructExchange::ptzCommandResponseWtoeToGbt( const usg::SPtzCommandResponse &from, gb28181::SPtzCommandResponse &to )
    {
        to.result = !from.isOk;
//     to.resAddr; // wtoe中本身无此值,由外界填充.
//     to.command; // wtoe中本身无此值,由外界填充.
//     to.commandParam1; // wtoe中本身无此值,由外界填充.
//     to.commandParam2; // wtoe中本身无此值,由外界填充.
        return true;
    }

    bool CStructExchange::ptzCommandResponseGbtToWtoe( const gb28181::SPtzCommandResponse &from, usg::SPtzCommandResponse &to)
    {
        to.isOk = !from.result;
        return true;
    }

    bool CStructExchange::presetListGbtToWtoe( const gb28181::SPresetList &from, usg::SPresetList &to )
    {
        to.fromIndex = from.fromIndex;
        to.toIndex = from.toIndex;

        return true;
    }

    bool CStructExchange::presetListResponseWtoeToGbt( const usg::SPresetListResponse &from, gb28181::SPresetListResponse &to )
    {
        if (  !from.presets.empty() )
        {
            std::vector<usg::SPresetListResponse::SItem>::const_iterator it( from.presets.begin() );
            std::vector<usg::SPresetListResponse::SItem>::const_iterator end( from.presets.end() );
            gb28181::SPresetListResponse::SItem tmp;
            for ( ; it != end; ++it )
            {
                tmp.itemValue = (*it).presetId;
                tmp. itemDescription= (*it).presetName;
                to.presetInfoList.push_back(tmp);
            }
        }
        to.result = !from.isOk;
        to.fromIndex = from.fromIndex;
        to.toIndex = from.toIndex;
        to.realNum = from.allItemSize;
        return true;
    }

    bool CStructExchange::presetListResponseGbtToWtoe( const gb28181::SPresetListResponse &from, usg::SPresetListResponse &to )
    {
        if ( !from.presetInfoList.empty() )
        {
            std::vector<gb28181::SPresetListResponse::SItem>::const_iterator it( from.presetInfoList.begin() );
            std::vector<gb28181::SPresetListResponse::SItem>::const_iterator end( from.presetInfoList.end() );
            usg::SPresetListResponse::SItem tmp;
            for ( ; it != end; ++it )
            {
                tmp.presetId = (*it).itemValue;
                tmp.presetName = (*it).itemDescription;
                to.presets.push_back(tmp);
            }
        }
        to.isOk = !from.result;
        to.fromIndex = from.fromIndex;
        to.toIndex = from.toIndex;
        to.allItemSize = from.realNum;
        return true;
    }

    bool CStructExchange::deviceCatalogGb28181ToWtoe(const gb28181::SCatalog &from, usg::SCatalog &to)
    {
        uint8_t size_f = from.subList.size();
        uint8_t size_t   = to.subItems.size();

        to.num = from.subNum;
        for (uint8_t i = 0; i < size_f; ++i, ++size_t)
        {
            usg::SCatalog::SItem item;
            item.name = from.subList[i].itemName;
            item.sipResCode = from.subList[i].devid;
            item.operatorType = usg::CATALOG_OPER_ADD;
            item.ip = from.subList[i].itemAddr;
            item.resStatus = true;
            item.isHD = true;
            item.hasPtz = true;

            to.subItems.push_back(item);
        }
        return true;
    }


    bool CStructExchange::deviceInfoResponseWtoeToGb28181( const usg::SDeviceInfoResponse &from, gb28181::SDeviceInfoResponse &to )
    {
        to.deviceType	= from.deviceType;
        to.firmware		= from.firmware;
        to.manufacturer	= from.manufacturer;
        to.maxAlarm		= from.maxAlarm;
        to.maxCamera	= from.maxCamera;
        to.model		= from.model;
        to.resAddr		= from.resAddr;
        return true;
    }

    bool CStructExchange::deviceInfoResponseGb28181ToWtoe( const gb28181::SDeviceInfoResponse &from, usg::SDeviceInfoResponse &to )
    {
        to.deviceType	= from.deviceType;
        to.firmware		= from.firmware;
        to.manufacturer	= from.manufacturer;
        to.maxAlarm		= from.maxAlarm;
        to.maxCamera	= from.maxCamera;
        to.model		= from.model;
        to.resAddr		= from.resAddr;
        return true;
    }

    bool CStructExchange::deviceStatusResponseWtoeToGb28181( const usg::SDeviceStatusResponse &from, gb28181::SDeviceStatusResponse &to )
    {
        to.isEncoder	= from.isEncoder;
        to.isNormal		= from.isNormal;
        to.isOnline		= from.isOnline;
        to.isRecord		= from.isRecord;
        to.resAddr		= from.resAddr;
        to.result		= from.result;
        to.strFaultReason	= from.strFaultReason;

        return true;
    }

    bool CStructExchange::deviceStatusResponseGb28181ToWtoe( const gb28181::SDeviceStatusResponse &from, usg::SDeviceStatusResponse &to )
    {
        to.isEncoder	= from.isEncoder;
        to.isNormal		= from.isNormal;
        to.isOnline		= from.isOnline;
        to.isRecord		= from.isRecord;
        to.resAddr		= from.resAddr;
        to.result		= from.result;
        to.strFaultReason	= from.strFaultReason;

        return true;
    }

    bool CStructExchange::alarmInfoGb28181ToWtoe(const gb28181::SAlarmParam &from, usg::SAlarmParam &to)
    {
        to.resId        = from.resId;
        to.priority     = from.priority;
        to.method       = from.method;
        to.time         = from.time;
        to.description  = from.description;
        to.longitude    = from.longitude;
        to.latitude     = from.latitude;
        return true;
    }


}

#endif
