
#ifndef STRUCTEXCHANGE_HPP_
#define STRUCTEXCHANGE_HPP_

#include <time.h>
#include <boost/date_time/posix_time/posix_time.hpp>
#include <boost/date_time/c_local_time_adjustor.hpp>
#include <algorithm>
#include <functional>

#include "../../UsgManager/include/WtoeStruct.hpp"
#include "../include/Gb28181Struct.hpp"

typedef boost::date_time::c_local_adjustor<boost::posix_time::ptime> local_adj;

namespace gb28181
{

    class CStructExchange
    {
    public:
        CStructExchange();

    public:
        bool realMediaGbtToWtoe( const gb28181::SRealMedia &from, usg::SRealMedia &to );
        bool realMediaResponseWtoeToGbt( const usg::SRealMediaResponse &from, gb28181::SRealMediaResponse &to );

        bool realMediaResponseGbtToWtoe( const gb28181::SRealMediaResponse & from, usg::SRealMediaResponse &to );

        bool historyListGbtToWtoe( const gb28181::SFileList &from, usg::SHistoryList &to );
        bool historyListResponseWtoeToGbt( const usg::SHistoryListResponse &from, gb28181::SFileListResponse &to );

        bool historyListResponseGbtToWtoe( const gb28181::SFileListResponse &from, usg::SHistoryListResponse &to );


        bool historyMediaGbtToWtoe( const gb28181::SHistoryMedia &from, usg::SHistoryMedia &to );
        bool historyMediaResponseWtoeToGbt( const usg::SHistoryMediaResponse &from, gb28181::SHistoryMediaResponse &to );

        bool historyMediaResponseGbtToWtoe( const gb28181::SHistoryMediaResponse &from, usg::SHistoryMediaResponse &to);

        bool ptzCommandGb28181ToWtoe( const gb28181::SPtzCommand &from, std::vector<usg::SPtzCommand> &to );
        bool ptzCommandResponseWtoeToGbt( const usg::SPtzCommandResponse &from, gb28181::SPtzCommandResponse &to );

        bool ptzCommandResponseGbtToWtoe( const gb28181::SPtzCommandResponse &from, usg::SPtzCommandResponse &to);

        bool presetListGbtToWtoe( const gb28181::SPresetList &from, usg::SPresetList &to );
        bool presetListResponseWtoeToGbt( const usg::SPresetListResponse &from, gb28181::SPresetListResponse &to );

        bool presetListResponseGbtToWtoe( const gb28181::SPresetListResponse &from, usg::SPresetListResponse &to);

        bool deviceCatalogGb28181ToWtoe(const gb28181::SCatalog &from, usg::SCatalog &to);
        bool deviceInfoResponseWtoeToGb28181( const usg::SDeviceInfoResponse &from, gb28181::SDeviceInfoResponse &to);
        bool deviceInfoResponseGb28181ToWtoe( const gb28181::SDeviceInfoResponse &from, usg::SDeviceInfoResponse &to );
        bool deviceStatusResponseWtoeToGb28181( const usg::SDeviceStatusResponse &from, gb28181::SDeviceStatusResponse &to);
        bool deviceStatusResponseGb28181ToWtoe( const gb28181::SDeviceStatusResponse &from, usg::SDeviceStatusResponse &to);

        bool alarmInfoGb28181ToWtoe(const gb28181::SAlarmParam &from, usg::SAlarmParam &to);
    };

}

inline bool transEFormatType( const gb28181::EFormatType from, usg::EFormatType &to )
{
    switch ( from )
    {
        case gb28181::EFORMATTYPE_QCIF:
        {
            to = usg::EFORMATTYPE_QVGA;
            return true;
        }
        case gb28181::EFORMATTYPE_CIF:
        {
            to = usg::EFORMATTYPE_CIF;
            return true;
        }
        case gb28181::EFORMATTYPE_2CIF:
        {
            to = usg::EFORMATTYPE_VGA;
            return true;
        }
        case gb28181::EFORMATTYPE_DCIF:
        {
            to = usg::EFORMATTYPE_VGA;
            return true;
        }
        case gb28181::EFORMATTYPE_D1:
        {
            to = usg::EFORMATTYPE_4CIF;
            return true;
        }
        case gb28181::EFORMATTYPE_4CIF:
        {
            to = usg::EFORMATTYPE_4CIF;
            return true;
        }
        case gb28181::EFORMATTYPE_16CIF:
        {
            to = usg::EFORMATTYPE_1080P;
            return true;
        }
        default:
            ;
    }
    return false;
}
inline bool transEFormatType( const usg::EFormatType from, gb28181::EFormatType &to )
{
    switch ( from )
    {
        case usg::EFORMATTYPE_CIF:
        {
            to = gb28181::EFORMATTYPE_CIF;
            return true;
        }
        case usg::EFORMATTYPE_4CIF:
        {
            to = gb28181::EFORMATTYPE_D1;
            return true;
        }
            //case msg::EFORMATTYPE_2CIF:
            //    {
            //        to = gb28181::EFORMATTYPE_2CIF;
            //        return true;
            //    }
        case usg::EFORMATTYPE_1080P:
        {
            to = gb28181::EFORMATTYPE_16CIF;
            return true;
        }
        default:
            ;
    }
    return false;
}
inline bool transEVideoType( const gb28181::EVideoType from, usg::EVideoType &to )
{
    if( from == gb28181::EVIDEOTYPE_H_264 )
    {
        to = usg::EVIDEOTYPE_H_264;
        return true;
    }
    return false;
}
inline bool transEVideoType( const usg::EVideoType from, gb28181::EVideoType &to )
{
    if( from == usg::EVIDEOTYPE_H_264 )
    {
        to = gb28181::EVIDEOTYPE_H_264;
        return true;
    }
    return false;
}
inline bool transEAudioType( const gb28181::EAudioType from, usg::EAudioType &to )
{
    if( from == gb28181::EAUDIOTYPE_G_711 )
    {
        to = usg::EAUDIOTYPE_G_711A;
        return true;
    }
    if( from == gb28181::EAUDIOTYPE_G_722 )
    {
        to = usg::EAUDIOTYPE_G_722;
        return true;
    }
    return false;
}
inline bool transEAudioType( const usg::EAudioType from, gb28181::EAudioType &to )
{
    if( from == usg::EAUDIOTYPE_G_711A )
    {
        to = gb28181::EAUDIOTYPE_G_711;
        return true;
    }
    return false;
}
inline bool transESocketType( const gb28181::ESocketType from, usg::ESocketType &to )
{
    if( from == gb28181::ESOCKETTYPE_UDP )
    {
        to = usg::ESOCKETTYPE_UDP;
        return true;
    }
    else if( from == gb28181::ESOCKETTYPE_TCP_PASSIVE )
    {
        to = usg::ESOCKETTYPE_TCP_PASSIVE;
        return true;
    }
    else if (from == gb28181::ESOCKETTYPE_TCP_ACTIVE)
    {
        to = usg::ESOCKETTYPE_TCP_ACTIVE;
        return true;
    }
    return false;
}
inline bool transESocketType( const usg::ESocketType from, gb28181::ESocketType &to )
{
    if( from == usg::ESOCKETTYPE_UDP )
    {
        to = gb28181::ESOCKETTYPE_UDP;
        return true;
    }
    else if( from == usg::ESOCKETTYPE_TCP_PASSIVE )
    {
        to = gb28181::ESOCKETTYPE_TCP_PASSIVE;
        return true;
    }
    else if (from == usg::ESOCKETTYPE_TCP_ACTIVE)
    {
        to = gb28181::ESOCKETTYPE_TCP_ACTIVE;
        return true;
    }
    return false;
}

//for gb28181
inline bool strTime2uint32Time( const std::string &str, uint32_t &t )
{
    try
    {
        std::string strTemp = str;
        int year;
        int month;
        int day;
        int hour;
        int minute;
        int second;
        sscanf( strTemp.c_str(), "%d-%d-%dT%d:%d:%d", &year, &month, &day, &hour, &minute, &second );
        tm tm_localTime;// 本地时间

        tm_localTime.tm_year = year - 1900;
        tm_localTime.tm_mon = month - 1;
        tm_localTime.tm_mday = day;
        tm_localTime.tm_hour = hour;
        tm_localTime.tm_min = minute;
        tm_localTime.tm_sec = second;
        t = mktime( &tm_localTime );
    }
    catch ( std::exception &ex )
    {
        std::cout << ex.what() << std::endl;
        return false;
    }
    catch ( ... )
    {
        std::cout << " time format error" << std::endl;
        return false;
    }
    return true;
}

// ISO时间串形如:20020131T235959,或 20020131T100001.123456789(后面9位是微秒,如果为0就没有)
inline bool utcString2UtcTime_t( const std::string &str, uint32_t &t )
{
    try
    {
        std::string strTemp = str;
        std::string::iterator new_end = std::remove_if(strTemp.begin(), strTemp.end(), std::bind2nd(std::equal_to<char>(), '-'));
        strTemp.erase(new_end, strTemp.end());

        std::remove_if(strTemp.begin(), strTemp.end(), std::bind2nd(std::equal_to<char>(), ':'));
        strTemp.erase(new_end, strTemp.end());

        boost::posix_time::ptime utcTime = boost::posix_time::from_iso_string( strTemp ); // utc时间
        boost::posix_time::ptime localTime = local_adj::utc_to_local( utcTime ); // 本地时间
        tm tm_localTime = boost::posix_time::to_tm( localTime ); // 本地时间
        t = mktime( &tm_localTime ); // 此时将本地时间变为UTC时间.
    }
    catch ( std::exception &ex )
    {
        std::cout << ex.what() << std::endl;
        return false;
    }
    catch ( ... )
    {
        std::cout << " time format error" << std::endl;
        return false;
    }
    return true;
}
// ISO时间串形如:20020131T235959,或 20020131T100001.123456789(后面9位是微秒,如果为0就没有)
inline std::string utcTime_t2UtcString( const uint32_t t )
{
    // 由于是从time_t来的,所以秒以下的时间(fractional_seconds)是为0的.
    boost::posix_time::ptime pt1 = boost::posix_time::from_time_t( t );
    std::string str = boost::posix_time::to_iso_extended_string( pt1 );
    return str;
}



//将字符串转换为国标类型的控制指令
inline bool transPtzCommandGb28181ToWtoe( const gb28181::SPtzCommand &fromCmnd, std::vector<usg::SPtzCommand> &toCmnd )
{
    usg::SPtzCommand tmp;
    uint8_t byte5 = fromCmnd.commandParam1 & 0x00ff;
    uint8_t byte6 = (fromCmnd.commandParam2 & 0xff00) >> 8;
    uint8_t byte7 = fromCmnd.commandParam2 & 0x00ff;

    uint32_t cmd = fromCmnd.command;

    //停止
    if (cmd == 0x00000000)
    {
        tmp.command = usg::EPTZCOMMAND_STOP;
        toCmnd.push_back(tmp);
        return true;
    }

    //预置位、导航和扫描
    if (cmd & 0x80000000)
    {
        switch (cmd)
        {
            case gb28181::EPTZCOMMAND_PREPOSITIONADD:
            {
                tmp.command = usg::EPTZCOMMAND_ADD;
                tmp.commandParam1 = 0x00;
                tmp.commandParam2 = byte6;
                break;
            }
            case gb28181::EPTZCOMMAND_PREPOSITIONSWITCH:
            {
                tmp.command = usg::EPTZCOMMAND_SWITCH;
                tmp.commandParam1 = 0x00;
                tmp.commandParam2 = byte6;
                break;
            }
            case gb28181::EPTZCOMMAND_PREPOSITIONDEL:
            {
                tmp.command = usg::EPTZCOMMAND_DEL;
                tmp.commandParam1 = 0x00;
                tmp.commandParam2 = byte6;
                break;
            }
            case gb28181::EPTZCOMMAND_CRUISEADD:
            {
                tmp.command = usg::EPTZCOMMAND_CRUISEADD;
                tmp.commandParam1 = byte5;
                tmp.commandParam2 = byte6;
                break;
            }
            case gb28181::EPTZCOMMAND_CRUISEDEL:
            {
                tmp.command = usg::EPTZCOMMAND_CRUISEDEL;
                tmp.commandParam1 = byte5;
                tmp.commandParam2 = byte6;
                break;
            }
            case gb28181::EPTZCOMMAND_CRUISESPEED:
            {
                tmp.command = usg::EPTZCOMMAND_CRUISESPEED;
                tmp.commandParam1 = byte5;
                tmp.commandParam2 = (byte7 << 4) + byte6;
                break;
            }
            case gb28181::EPTZCOMMAND_CRUISESTIME:
            {
                tmp.command = usg::EPTZCOMMAND_CRUISESTIME;
                tmp.commandParam1 = byte5;
                tmp.commandParam2 = (byte7 << 4) + byte6;
                break;
            }
            case gb28181::EPTZCOMMAND_CRUISESSTART:
            {
                tmp.command = usg::EPTZCOMMAND_CRUISESSTART;
                tmp.commandParam1 = byte5;
                tmp.commandParam2 = 0x00;
                break;
            }
            case gb28181::EPTZCOMMAND_SCANSTART:
            {
                tmp.command = usg::EPTZCOMMAND_SCANOPT;
                tmp.commandParam1 = byte5;
                tmp.commandParam2 = 0x00;
                break;
            }
            case gb28181::EPTZCOMMAND_SCANLEFT:
            {
                tmp.command = usg::EPTZCOMMAND_SCANOPT;
                tmp.commandParam1 = byte5;
                tmp.commandParam2 = 0x01;
                break;
            }
            case gb28181::EPTZCOMMAND_SCANRIGHT:
            {
                tmp.command = usg::EPTZCOMMAND_SCANOPT;
                tmp.commandParam1 = byte5;
                tmp.commandParam2 = 0x02;
                break;
            }
            case gb28181::EPTZCOMMAND_SCANTIME:
            {
                tmp.command = usg::EPTZCOMMAND_SCANTIME;
                tmp.commandParam1 = byte5;
                tmp.commandParam2 = (byte7 << 4) + byte6;
                break;
            }
            default:
            {
                std::cout << "ptz chg msg: " << fromCmnd.command << std::endl;
                return false;
            }
        }

        toCmnd.push_back(tmp);
        return true;
    }
    else if (cmd & 0x40000000) //光圈和聚焦
    {
        uint32_t focus = cmd & 0x03000000;
        uint32_t iris = cmd & 0x0c000000;

        if (focus != 0x00)
        {
            focus += 0x40000000;
            switch (focus)
            {
                case gb28181::EPTZCOMMAND_FOCUSNEAR:
                {
                    tmp.command = usg::EPTZCOMMAND_FOCUSNEAR;
                    tmp.commandParam1 = byte5;
                    tmp.commandParam2 = 0x00;
                    break;
                }
                case gb28181::EPTZCOMMAND_FOCUSFAR:
                {
                    tmp.command = usg::EPTZCOMMAND_FOCUSFAR;
                    tmp.commandParam1 = byte5;
                    tmp.commandParam2 = 0x00;
                    break;
                }
                default:
                {
                    std::cout << "ptz chg msg: " << fromCmnd.command << std::endl;
                    return false;
                }
            }

            toCmnd.push_back(tmp);
        }

        if (iris != 0x00)
        {
            iris += 0x40000000;
            switch (iris)
            {
                case gb28181::EPTZCOMMAND_APERTUREWIDE:
                {
                    tmp.command = usg::EPTZCOMMAND_APERTUREWIDE;
                    tmp.commandParam1 = byte6;
                    tmp.commandParam2 = 0x00;
                    break;
                }
                case gb28181::EPTZCOMMAND_APERTURETELE:
                {
                    tmp.command = usg::EPTZCOMMAND_APERTURETELE;
                    tmp.commandParam1 = byte6;
                    tmp.commandParam2 = 0x00;
                    break;
                }
                default:
                {
                    std::cout << "ptz chg msg: " << fromCmnd.command << std::endl;
                    return false;
                }
            }

            toCmnd.push_back(tmp);
        }
        return true;
    }
    else //方向和镜头
    {
        uint32_t zoom = cmd & 0x30000000;
        uint32_t tile = cmd & 0x0c000000;
        uint32_t pan = cmd & 0x03000000;

        if (zoom != 0x00)
        {
            switch( zoom )
            {
                case gb28181::EPTZCOMMAND_ZOOMIN:
                {
                    tmp.command = usg::EPTZCOMMAND_ZOOMIN;
                    tmp.commandParam1 = byte7 >> 4;
                    tmp.commandParam2 = 0x00;
                    break;
                }
                case gb28181::EPTZCOMMAND_ZOOMOUT:
                {
                    tmp.command = usg::EPTZCOMMAND_ZOOMOUT;
                    tmp.commandParam1 = byte7 >> 4;
                    tmp.commandParam2 = 0x00;
                    break;
                }
                default:
                {
                    std::cout << "ptz chg msg: " << fromCmnd.command << std::endl;
                    return false;
                }
            }

            toCmnd.push_back(tmp);
        }

        if (tile != 0x00)
        {
            switch( tile )
            {
                case gb28181::EPTZCOMMAND_UP:
                {
                    tmp.command = usg::EPTZCOMMAND_UP;
                    tmp.commandParam1 = byte6;
                    tmp.commandParam2 = 0x00;
                    break;
                }
                case gb28181::EPTZCOMMAND_DOWN:
                {
                    tmp.command = usg::EPTZCOMMAND_DOWN;
                    tmp.commandParam1 = byte6;
                    tmp.commandParam2 = 0x00;
                    break;
                }
                default:
                {
                    std::cout << "ptz chg msg: " << fromCmnd.command << std::endl;
                    return false;
                }
            }

            toCmnd.push_back(tmp);
        }

        if (pan != 0x00)
        {
            switch( pan )
            {
                case gb28181::EPTZCOMMAND_LEFT:
                {
                    tmp.command = usg::EPTZCOMMAND_LEFT;
                    tmp.commandParam1 = byte5;
                    tmp.commandParam2 = 0x00;
                    break;
                }
                case gb28181::EPTZCOMMAND_RIGHT:
                {
                    tmp.command = usg::EPTZCOMMAND_RIGHT;
                    tmp.commandParam1 = byte5;
                    tmp.commandParam2 = 0x00;
                    break;
                }
                default:
                {
                    std::cout << "ptz chg msg: " << fromCmnd.command << std::endl;
                    return false;
                }
            }

            toCmnd.push_back(tmp);
        }

        return true;
    }

}

inline bool transPtzCommandWtoeToGb28181(const usg::SPtzCommand &from, gb28181::SPtzCommand &to)
{
//command标识字节4，param1的后8位标识字节5，param2的前8位标识字节6，后8位标识字节7
    switch( from.command )
    {
        case usg::EPTZCOMMAND_UP:
        {
            to.command = gb28181::EPTZCOMMAND_UP;
            to.commandParam1 = 0x00;
            to.commandParam2 = uint16_t(from.commandParam1 << 8);
            break;
        }
        case usg::EPTZCOMMAND_DOWN:
        {
            to.command = gb28181::EPTZCOMMAND_DOWN;
            to.commandParam1 = 0x00;
            to.commandParam2 = uint16_t(from.commandParam1 << 8);
            break;
        }
        case usg::EPTZCOMMAND_LEFT:
        {
            to.command = gb28181::EPTZCOMMAND_LEFT;
            to.commandParam1 = (uint16_t)from.commandParam2;
            to.commandParam2 = 0x00;
            break;
        }
        case usg::EPTZCOMMAND_RIGHT:
        {
            to.command = gb28181::EPTZCOMMAND_RIGHT;
            to.commandParam1 = (uint16_t)from.commandParam2;
            to.commandParam2 = 0x00;
            break;
        }
        case usg::EPTZCOMMAND_STOP:
        {
            to.command = gb28181::EPTZCOMMAND_STOP;
            to.commandParam1 = 0x00;
            to.commandParam2 = 0x00;
            break;
        }
        case usg::EPTZCOMMAND_FOCUSNEAR:
        {
            to.command = gb28181::EPTZCOMMAND_FOCUSNEAR;
            to.commandParam1 = (uint16_t)from.commandParam1;
            to.commandParam2 = 0x00;
            break;
        }
        case usg::EPTZCOMMAND_FOCUSFAR:
        {
            to.command = gb28181::EPTZCOMMAND_FOCUSFAR;
            to.commandParam1 = (uint16_t)from.commandParam1;
            to.commandParam2 = 0x00;
            break;
        }
        case usg::EPTZCOMMAND_FOCUSSTOP:
        {
            to.command = gb28181::EPTZCOMMAND_FOCUSSTOP;
            to.commandParam1 = 0x00;
            to.commandParam2 = 0x00;
            break;
        }
        case usg::EPTZCOMMAND_ZOOMIN:
        {
            to.command = gb28181::EPTZCOMMAND_ZOOMIN;
            to.commandParam1 = 0x00;
            to.commandParam2 = uint16_t(from.commandParam1 << 4);
            break;
        }
        case usg::EPTZCOMMAND_ZOOMOUT:
        {
            to.command = gb28181::EPTZCOMMAND_ZOOMOUT;
            to.commandParam1 = 0x00;
            to.commandParam2 = uint16_t(from.commandParam1 << 4);
            break;
        }
        case usg::EPTZCOMMAND_APERTUREWIDE:
        {
            to.command = gb28181::EPTZCOMMAND_APERTUREWIDE;
            to.commandParam1 = 0x00;
            to.commandParam2 = uint16_t(from.commandParam2 << 8);
            break;
        }
        case usg::EPTZCOMMAND_APERTURETELE:
        {
            to.command = gb28181::EPTZCOMMAND_APERTURETELE;
            to.commandParam1 = 0x00;
            to.commandParam2 = uint16_t(from.commandParam2 << 8);
            break;
        }
        case usg::EPTZCOMMAND_ADD:
        {
            to.command = gb28181::EPTZCOMMAND_PREPOSITIONADD;
            to.commandParam1 = 0x00;
            to.commandParam2 = uint16_t(from.commandParam2 << 8);
            break;
        }
        case usg::EPTZCOMMAND_SWITCH:
        {
            to.command = gb28181::EPTZCOMMAND_PREPOSITIONSWITCH;
            to.commandParam1 = 0x00;
            to.commandParam2 = uint16_t(from.commandParam2 << 8);
            break;
        }
        case usg::EPTZCOMMAND_DEL:
        {
            to.command = gb28181::EPTZCOMMAND_PREPOSITIONDEL;
            to.commandParam1 = 0x00;
            to.commandParam2 = uint16_t(from.commandParam2 << 8);
            break;
        }
        case usg::EPTZCOMMAND_CRUISEADD:
        {
            to.command = gb28181::EPTZCOMMAND_CRUISEADD;
            to.commandParam1 = (uint16_t)from.commandParam1;
            to.commandParam2 = uint16_t(from.commandParam2 << 8);
            break;
        }
        case usg::EPTZCOMMAND_CRUISEDEL:
        {
            to.command = gb28181::EPTZCOMMAND_CRUISEDEL;
            to.commandParam1 = (uint16_t)from.commandParam1;
            to.commandParam2 = uint16_t(from.commandParam2 << 8);
            break;
        }
        case usg::EPTZCOMMAND_CRUISESPEED:
        {
            to.command = gb28181::EPTZCOMMAND_CRUISESPEED;
            to.commandParam1 = (uint16_t)from.commandParam1;
            to.commandParam2 = uint16_t(((from.commandParam2 & 0x00ff) << 8) + ((from.commandParam2 & 0x0f00) >> 4));
            break;
        }
        case usg::EPTZCOMMAND_CRUISESTIME:
        {
            to.command = gb28181::EPTZCOMMAND_CRUISESTIME;
            to.commandParam1 = (uint16_t)from.commandParam1;
            to.commandParam2 = uint16_t(((from.commandParam2 & 0x00ff) << 8) + ((from.commandParam2 & 0x0f00) >> 4));
            break;
        }
        case usg::EPTZCOMMAND_CRUISESSTART:
        {
            to.command = gb28181::EPTZCOMMAND_CRUISESSTART;
            to.commandParam1 = (uint16_t)from.commandParam1;
            to.commandParam2 = 0x00;
            break;
        }
        case usg::EPTZCOMMAND_SCANOPT:
        {
            if (from.commandParam2 == 0x00)	{
                to.command = gb28181::EPTZCOMMAND_SCANSTART;
            }
            else if (from.commandParam2 == 0x01) {
                to.command = gb28181::EPTZCOMMAND_SCANLEFT;
            }
            else if (from.commandParam2 == 0x02) {
                to.command = gb28181::EPTZCOMMAND_SCANRIGHT;
            }
            else {
                std::cout << "ptz chg msg: " << from.command << std::endl;
                return false;
            }
            to.commandParam1 = (uint16_t)from.commandParam1;
            to.commandParam2 = (uint16_t)(from.commandParam2 << 8);
            break;
        }
        case usg::EPTZCOMMAND_SCANTIME:
        {
            to.command = gb28181::EPTZCOMMAND_SCANTIME;
            to.commandParam1 = (uint16_t)from.commandParam1;
            to.commandParam2 = uint16_t(((from.commandParam2 & 0x00ff) << 8) + ((from.commandParam2 & 0x0f00) >> 4));
            break;
        }
        default:
        {
            std::cout << "ptz chg msg: " << from.command << " gb28181 no this cmd" << std::endl;
            return false;
        }
    }
    return true;
}

#endif
