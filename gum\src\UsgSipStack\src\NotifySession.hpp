#ifndef NOTIFYSESSION_HPP_
#define NOTIFYSESSION_HPP_

#include <map>
#include <vector>
#include <string>

#include <pjlib.h>
#include <pjsip.h>

#include "../include/UsgSipStackItf.hpp"

namespace usg {

class CNotifySession : public INotifySession
{
public:
    CNotifySession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from );
    CNotifySession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from,const std::string& xmlType );
    virtual ~CNotifySession();

public:
    CNotifySession *setHandler( ICtxHandler *handler );
    void setExpries( uint32_t iExpries );

public:
    virtual bool subscribe( std::string &sid, const std::string &sipUri, const std::string &result );
    virtual bool notify( std::string &sid, const std::string &sipUri, const std::string &result );
	virtual bool notify( const std::string &sid, const std::string &sipUri, const std::vector<std::string> &result );
    virtual bool answer( pjsip_rx_data *rdata, int status, const std::string &result );
	virtual bool setSipUrl( const std::string catalogSipUrl );

    //for gb28181
    virtual bool answer( pjsip_rx_data *rdata, int status, pj_int32_t expires,const std::string &result );

public:
    bool onSubscribe( pjsip_rx_data *rdata );
    bool onNotify( pjsip_rx_data *rdata );
    bool onAnswer( pjsip_rx_data *rdata );

private:
    std::string createSid( pjsip_cid_hdr *cid, pjsip_cseq_hdr *cseq );
	bool notify( std::string &sid, const std::string &sipUri, const std::string &result, const std::string &callid );

private:
    pj_pool_t      *m_pool;
    pjsip_endpoint *m_endPoint;

private:
    ICtxHandler *m_handler;
    const std::string m_from;
    const std::string m_xmlType;
	std::string m_catalogSipUrl;
    uint32_t m_iExpries;

private:
private:
	std::map< std::string, std::vector< std::string > > m_queryCatalog;
};

}

#endif // NOTIFYSESSION_HPP_
