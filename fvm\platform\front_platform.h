/**
 * Project FVM
 */
#pragma once
#include <memory>
#include <list>
#include "data/data_manager.h"
#include "protocol/all.h"
#include "protocol/protocol_manager.h"
/**
 * @brief:前端平台
 */
namespace fvm {
    //! 设备密码登录失败尝试次数
    constexpr auto AUTH_LOGIN_IGNORE_COUNT = 0;
    constexpr auto NET_LOGIN_IGNORE_COUNT = 0;
    constexpr auto LOGIN_IGNORE_COUNT = 0;
    namespace platform {
        enum class StreamType {
            Main = 0,               //主码流
            Sub,                    //子码流
            Third,                  //第三码流
            Fourth,                 //第四码流
        };
        enum class TransferType
        {
            UDP = 1,                //UDP被动
            TCPPASSIVE,             //TCP被动
            TCPACTIVE,              //TCP主动
            RTMP,                   //和二代平台的特定lrg对接，使用rtmp地址，            
        };

        struct EventFocusInfo
        {
            int presetId;			//事件聚焦预置位
            int xTop;				//事件聚焦坐标
            int yTop;
            int xBottom;
            int yBottom;
        };

        class FrontPlatform : public std::enable_shared_from_this<FrontPlatform> {
        public:
            virtual void init(data::VideoServerPtr serverPtr);

            virtual void startPlay(VideoSourceInfoPtr videoSourceInfo, const std::string& destAddr );

            virtual void stopPlay(VideoSourceInfoPtr videoSourceInfo, const std::string& destAddr );

            virtual bool callPreset(VideoSourceInfoPtr videoSourceInfo, int ptzId);
            virtual bool savePreset(VideoSourceInfoPtr videoSourceInfo, int ptzId );

            // （新增）调用预置位 (默认通道)
            virtual bool callPreset(int presetId) { return false; }
            // （新增）3D定位
            virtual bool focusRect(VideoSourceInfoPtr videoSourceInfo, int xTop, int yTop, int xBottom, int yBottom) { return false; }
            // （新增）聚焦事件
            virtual bool focusEvent(VideoSourceInfoPtr videoSourceInfo, EventFocusInfo focusInfo, bool ignoreIfFocusing = false);
            // （新增）执行聚焦事件
            bool doFocusEvent(VideoSourceInfoPtr videoSourceInfo);

            virtual bool controlPtz(VideoSourceInfoPtr videoSourceInfo, network::EPtzCommand cmd, int step);

            virtual void onChanged();

            void onResourceUpdated();

            void onStateChanged();

            //取流的传输方式
            TransferType getTransferType() { return transferType; }

            //取流的传输名称
            std::string getTransferName();

            /**
            * @brief  获取错误信息
            */
            virtual std::string getLastError(void) { return ""; }

            /**
            * @brief  设备登录
            */
            virtual bool login(void) { return false; }

        public:
            data::VideoServerPtr serverPtr;
        
        private:
            network::EPtzCommand getPtzStopCmd(network::EPtzCommand cmd);

        protected:
            std::atomic_bool isOnline = false;
            //信令发送的目的ip和端口
            std::string destIp = "";
            int destPort = 0;
            TransferType transferType = TransferType::UDP;
            //本地监听的端口
            int localPort = 0;

            StreamType streamType = StreamType::Main; //0-主码流，1-子码流，2-第三码流。。。

			// 事件聚焦排队列表
            std::list<EventFocusInfo> eventFocusList;
			// 事件是否在聚焦中
            std::atomic_bool eventFocusing = false;
            std::mutex mutexFocus;
        };
    }
    typedef std::shared_ptr<platform::FrontPlatform> FrontPlatformPtr;
}