#ifndef AI_LOG_H
#define AI_LOG_H

/**
 * @file    ailog.h
 * @brief   写日志接口
 * <AUTHOR>
 * @version 0.0.1
 * @date    01 Nov 2021
 */
#include <string>
#include <iostream>
#include <sstream>
#include <boost/log/trivial.hpp>
#include <boost/log/sources/severity_channel_logger.hpp>


namespace ai
{
    namespace logging = boost::log;
    using namespace logging::trivial;
    namespace src = logging::sources;

    //日志模块的初始化和反初始化
    /**
     * [in] appName: 应用程序名称，生成的日志文件名以该名称开头
     * [in] appPath: 日志配置文件所在目录，不输入则为当前路径
     */
    #define InitLog(appName, ... ) LogMaker::initLog( appName, ## __VA_ARGS__ )
    #define FiniLog() LogMaker::finiLog();
    //设置日志开放等级
    #define SetLogLevel(level) LogMaker::setLogLevel( level );

    #define LogTrace LogMaker( boost::log::trivial::trace)
    #define LogDebug LogMaker( boost::log::trivial::debug, __FILE__, __FUNCTION__, __LINE__)
    #define LogInfo LogMaker( boost::log::trivial::info)
    #define LogWarn LogMaker( boost::log::trivial::warning, __FILE__, __FUNCTION__, __LINE__)
    #define LogError LogMaker( boost::log::trivial::error, __FILE__, __FUNCTION__, __LINE__)
    #define LogFatal LogMaker( boost::log::trivial::fatal, __FILE__, __FUNCTION__, __LINE__)

    class LogMaker
    {
    public:
        LogMaker(severity_level level, const char* szFile="", const char* function="", int line =0);
        virtual ~LogMaker();

        //通用模板
        template<typename T>
        LogMaker& operator<<(const T& data)
        {
            m_message << data;
            return *this;
        }
        /**
         * [in] appName: 应用程序名称,生成的日志文件名以该名称开头
         * [in] logPath: 日志文件目录,不输入则为当前路径
         * [in] maxfiles: 日志文件最大个数 超过清除旧文件
         */
        static bool initLog(const std::string& appName, const std::string& logPath = "./", int maxfiles = 50);

        static void finiLog();
        
        /**
         * 设置日志开放等级 
         * [in] level:  >=trace,debug,info,warning,error其中一个
         */
        static void setLogLevel(severity_level level);
    private:
        std::stringstream m_message;
        src::severity_channel_logger_mt<severity_level, std::string> m_scl;
        severity_level m_level;     //事件等级
        std::string m_file;         //所属文件
        std::string m_function;     //所属函数
        int m_line;                 //行号
    };
} //namespace ai

#endif //AI_LOG_H
