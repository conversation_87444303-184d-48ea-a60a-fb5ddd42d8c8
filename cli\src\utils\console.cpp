#include "console.h"
#include <iostream>

#include <csignal>
#include <vector>
#include <termio.h>
#include <cstdio>
#include <unistd.h>
#include <cstring>
#include <fcntl.h>

namespace cli{


bool hasInput()
{
    struct termios orgTerm = {};
    struct termios newTerm = {};

    tcgetattr(STDIN_FILENO, &orgTerm);
    newTerm = orgTerm;
    newTerm.c_lflag &= ~(ICANON | ECHO);
    tcsetattr(STDIN_FILENO, TCSANOW, &newTerm);

    int fcn = fcntl(STDIN_FILENO, F_GETFL, 0);
    fcntl(STDIN_FILENO, F_SETFL, fcn | O_NONBLOCK);

    int ch = getchar();

    tcsetattr(STDIN_FILENO, TCSANOW, &orgTerm);
    fcntl(STDIN_FILENO, F_SETFL, fcn);

    if(ch != EOF)
    {
        ungetc(ch,stdin);
        return true;
    }
    return false;
}


void moveCursorUp(int lines)
{
    printf("\033[%dA", lines);
    fflush(stdout);
}

void moveCursorDown(int lines)
{
    printf("\033[%dB", lines);
    fflush(stdout);
}

void moveCursorRight(int columns)
{
    printf("\033[%dC", columns);
    fflush(stdout);
}

void moveCursorLeft(int columns)
{
    printf("\033[%dD", columns);
    fflush(stdout);
}

void moveCursorToColumn(int column)
{
    printf("\033[%dG", column);
    fflush(stdout);
}

void moveCursorToPosition(int column, int row)
{
    printf("\033[%d;%dH", row + 1, column);
    fflush(stdout);
}

bool getCursorPosition(int& row, int& column)
{

    printf("\033[6n");
    fflush(stdout);

    if (scanf("\033[%d;%dR", &column, &row) != 2)
        return false;

    return true;
}

bool setCursorEnable(bool enable)
{
    if (enable)
        printf("\033[?25h");
    else
        printf("\033[?25l");
    fflush(stdout);

    return true;
}


bool setCanonEnable(bool enable)
{
    struct termios term = {};

    if (tcgetattr(0, &term) < 0)
        return false;

    if (enable)
        term.c_lflag |= ICANON;
    else
        term.c_lflag &= ~ICANON;

   if (tcsetattr(0, TCSAFLUSH, &term) < 0)
       return false;

   return true;
}

void clearLine()
{
    printf("\033[2K");
    fflush(stdout);
}

void clearLineAfter()
{
    printf("\033[K");
    fflush(stdout);
}

void clearScreen()
{
    printf("\033[2J");
    fflush(stdout);
}

void clearScreenAfter()
{
    printf("\033[0J");
    fflush(stdout);
}

bool setEchoEnable(bool enable)
{
    struct termios term = {};

    if (tcgetattr(0, &term) < 0)
        return false;

    if (enable)
        term.c_lflag |= ECHO;
    else
        term.c_lflag &= ~ECHO;

    if (tcsetattr(0, TCSAFLUSH, &term) < 0)
        return false;

    return true;
}

void backSpace(int n)
{
    for (int i = 0; i < n; ++i)
        printf("\033[1D");      //!< 光标左移1格
    printf("\033[K");           //!< 清空本行光标后的内容

    fflush(stdout);
}



int getChar()
{

    struct termios orgOpts = {};
    struct termios newOpts = {};

    if (tcgetattr(STDIN_FILENO, &orgOpts) < 0)
        return -1;

    memcpy(&newOpts, &orgOpts, sizeof(orgOpts));
    newOpts.c_lflag &= ~(ICANON | ECHO | ECHOE | ECHOK | ECHONL | ECHOPRT | ECHOKE);
    if (tcsetattr(STDIN_FILENO, TCSANOW, &newOpts) < 0)
        return -1;

    int c = getchar();

    if (tcsetattr(STDIN_FILENO, TCSANOW, &orgOpts) < 0)
        return -1;

    return c;
}


}
