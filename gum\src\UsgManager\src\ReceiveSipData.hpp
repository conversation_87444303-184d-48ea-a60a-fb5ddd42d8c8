#ifndef RECEIVESIPDATA_HPP_
#define RECEIVESIPDATA_HPP_

#include <boost/date_time.hpp>
#include <boost/thread/mutex.hpp>
#include <boost/thread/condition.hpp>
#include <boost/function.hpp>
#include <boost/shared_ptr.hpp>

#include "UsgManager/include/UsgManagerCfg.hpp"
#include "UsgManager/include/WtoeStruct.hpp"
#include "UsgManager/include/UsgManagerItf.hpp"

namespace usg
{

class USGMANAGER_PRIVATE CReciveSipData
{
public:
    CReciveSipData();
    ~CReciveSipData();


public:
	bool onReceiveRealMediaResponse( const std::string &sid, void* out );
    bool onReceiveHistoryListResponse( const std::string &sid, void* out );
    bool onReceiveHistoryMediaResponse( const std::string &sid, void* out );
    bool onReceivePresetListResponse( const std::string &sid, void* out );
	bool onReceiveDeviceCatalogResponse( const std::string &sid, void* out );
	bool onReceiveDeviceInfoResponse( const std::string &sid, void* out );
	bool onReceiveDeviceStatusResponse( const std::string &sid, void* out );
    bool onReceiveSubscribeResponse( const std::string &sid, void* out );
    bool onReceiveBroadcastResponse( const std::string &sid, void* out );
    bool waitForRecive();

    bool getRealMediaResponse( uint16_t& streamRate );
    bool getHistoryListResponse( std::vector< std::pair< std::string, TimePeriodUnit >  > &info );
    bool getHistoryMediaResponse( std::string& vodUrl );
    bool getPresetListResponse( std::map< uint8_t, std::string > &infos ); 
	bool getDeviceCatalogResponse(SCatalog &catalog);
	bool getDeviceInfoResponse(SDeviceInfoResponse &info);
	bool getDeviceStatusResponse(SDeviceStatusResponse &status);
    bool getSubscribeResponse(bool &result);
    bool getBroadcastResponse(bool &result);

private:
    boost::condition m_confirm;
    boost::mutex m_mutex;
    uint16_t m_streamRate;
    std::vector< std::pair< std::string, TimePeriodUnit >  >  m_sHistoryListResponse;
    std::string m_sHistoryMediaResponse;
    std::map< uint8_t, std::string >   m_sPresetListResponse;
	SCatalog m_catalog;
	SDeviceInfoResponse m_info;
	SDeviceStatusResponse m_status;
    volatile bool m_is200Ok;
    bool  m_bSubscribeResult;
    bool  m_broadcastResult;
};
}
#endif
