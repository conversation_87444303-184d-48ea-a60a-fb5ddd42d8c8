// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#include <odb/pre.hxx>

#include "video_server-odb.hxx"

#include <cassert>
#include <cstring>  // std::memcpy


#include <odb/mysql/traits.hxx>
#include <odb/mysql/database.hxx>
#include <odb/mysql/transaction.hxx>
#include <odb/mysql/connection.hxx>
#include <odb/mysql/statement.hxx>
#include <odb/mysql/statement-cache.hxx>
#include <odb/mysql/simple-object-statements.hxx>
#include <odb/mysql/container-statements.hxx>
#include <odb/mysql/exceptions.hxx>
#include <odb/mysql/simple-object-result.hxx>
#include <odb/mysql/enum.hxx>

namespace odb
{
  // VideoServer
  //

  struct access::object_traits_impl< ::db::VideoServer, id_mysql >::extra_statement_cache_type
  {
    extra_statement_cache_type (
      mysql::connection&,
      image_type&,
      id_image_type&,
      mysql::binding&,
      mysql::binding&)
    {
    }
  };

  access::object_traits_impl< ::db::VideoServer, id_mysql >::id_type
  access::object_traits_impl< ::db::VideoServer, id_mysql >::
  id (const id_image_type& i)
  {
    mysql::database* db (0);
    ODB_POTENTIALLY_UNUSED (db);

    id_type id;
    {
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        id,
        i.id_value,
        i.id_null);
    }

    return id;
  }

  access::object_traits_impl< ::db::VideoServer, id_mysql >::id_type
  access::object_traits_impl< ::db::VideoServer, id_mysql >::
  id (const image_type& i)
  {
    mysql::database* db (0);
    ODB_POTENTIALLY_UNUSED (db);

    id_type id;
    {
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        id,
        i.id_value,
        i.id_null);
    }

    return id;
  }

  bool access::object_traits_impl< ::db::VideoServer, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // id
    //
    t[0UL] = 0;

    // videoSourceName
    //
    if (t[1UL])
    {
      i.videoSourceName_value.capacity (i.videoSourceName_size);
      grew = true;
    }

    // accessType
    //
    t[2UL] = 0;

    // ip
    //
    if (t[3UL])
    {
      i.ip_value.capacity (i.ip_size);
      grew = true;
    }

    // port
    //
    t[4UL] = 0;

    // userName
    //
    if (t[5UL])
    {
      i.userName_value.capacity (i.userName_size);
      grew = true;
    }

    // password
    //
    if (t[6UL])
    {
      i.password_value.capacity (i.password_size);
      grew = true;
    }

    // factory
    //
    if (t[7UL])
    {
      i.factory_value.capacity (i.factory_size);
      grew = true;
    }

    // sipid
    //
    if (t[8UL])
    {
      i.sipid_value.capacity (i.sipid_size);
      grew = true;
    }

    // status
    //
    t[9UL] = 0;

    // subNum
    //
    t[10UL] = 0;

    // isDel
    //
    t[11UL] = 0;

    return grew;
  }

  void access::object_traits_impl< ::db::VideoServer, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i,
        mysql::statement_kind sk)
  {
    ODB_POTENTIALLY_UNUSED (sk);

    using namespace mysql;

    std::size_t n (0);

    // id
    //
    if (sk != statement_update)
    {
      b[n].buffer_type = MYSQL_TYPE_LONGLONG;
      b[n].is_unsigned = 1;
      b[n].buffer = &i.id_value;
      b[n].is_null = &i.id_null;
      n++;
    }

    // videoSourceName
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.videoSourceName_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.videoSourceName_value.capacity ());
    b[n].length = &i.videoSourceName_size;
    b[n].is_null = &i.videoSourceName_null;
    n++;

    // accessType
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.accessType_value;
    b[n].is_null = &i.accessType_null;
    n++;

    // ip
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.ip_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.ip_value.capacity ());
    b[n].length = &i.ip_size;
    b[n].is_null = &i.ip_null;
    n++;

    // port
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.port_value;
    b[n].is_null = &i.port_null;
    n++;

    // userName
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.userName_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.userName_value.capacity ());
    b[n].length = &i.userName_size;
    b[n].is_null = &i.userName_null;
    n++;

    // password
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.password_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.password_value.capacity ());
    b[n].length = &i.password_size;
    b[n].is_null = &i.password_null;
    n++;

    // factory
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.factory_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.factory_value.capacity ());
    b[n].length = &i.factory_size;
    b[n].is_null = &i.factory_null;
    n++;

    // sipid
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.sipid_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.sipid_value.capacity ());
    b[n].length = &i.sipid_size;
    b[n].is_null = &i.sipid_null;
    n++;

    // status
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.status_value;
    b[n].is_null = &i.status_null;
    n++;

    // subNum
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.subNum_value;
    b[n].is_null = &i.subNum_null;
    n++;

    // isDel
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.isDel_value;
    b[n].is_null = &i.isDel_null;
    n++;
  }

  void access::object_traits_impl< ::db::VideoServer, id_mysql >::
  bind (MYSQL_BIND* b, id_image_type& i)
  {
    std::size_t n (0);
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.id_value;
    b[n].is_null = &i.id_null;
  }

  bool access::object_traits_impl< ::db::VideoServer, id_mysql >::
  init (image_type& i,
        const object_type& o,
        mysql::statement_kind sk)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (sk);

    using namespace mysql;

    bool grew (false);

    // id
    //
    if (sk == statement_insert)
    {
      long unsigned int const& v =
        o.id;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.id_value, is_null, v);
      i.id_null = is_null;
    }

    // videoSourceName
    //
    {
      ::std::string const& v =
        o.videoSourceName;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.videoSourceName_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.videoSourceName_value,
        size,
        is_null,
        v);
      i.videoSourceName_null = is_null;
      i.videoSourceName_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.videoSourceName_value.capacity ());
    }

    // accessType
    //
    {
      int const& v =
        o.accessType;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.accessType_value, is_null, v);
      i.accessType_null = is_null;
    }

    // ip
    //
    {
      ::std::string const& v =
        o.ip;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.ip_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.ip_value,
        size,
        is_null,
        v);
      i.ip_null = is_null;
      i.ip_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.ip_value.capacity ());
    }

    // port
    //
    {
      int const& v =
        o.port;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.port_value, is_null, v);
      i.port_null = is_null;
    }

    // userName
    //
    {
      ::std::string const& v =
        o.userName;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.userName_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.userName_value,
        size,
        is_null,
        v);
      i.userName_null = is_null;
      i.userName_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.userName_value.capacity ());
    }

    // password
    //
    {
      ::std::string const& v =
        o.password;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.password_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.password_value,
        size,
        is_null,
        v);
      i.password_null = is_null;
      i.password_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.password_value.capacity ());
    }

    // factory
    //
    {
      ::std::string const& v =
        o.factory;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.factory_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.factory_value,
        size,
        is_null,
        v);
      i.factory_null = is_null;
      i.factory_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.factory_value.capacity ());
    }

    // sipid
    //
    {
      ::std::string const& v =
        o.sipid;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.sipid_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.sipid_value,
        size,
        is_null,
        v);
      i.sipid_null = is_null;
      i.sipid_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.sipid_value.capacity ());
    }

    // status
    //
    {
      int const& v =
        o.status;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.status_value, is_null, v);
      i.status_null = is_null;
    }

    // subNum
    //
    {
      int const& v =
        o.subNum;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.subNum_value, is_null, v);
      i.subNum_null = is_null;
    }

    // isDel
    //
    {
      bool const& v =
        o.isDel;

      bool is_null (false);
      mysql::value_traits<
          bool,
          mysql::id_long >::set_image (
        i.isDel_value, is_null, v);
      i.isDel_null = is_null;
    }

    return grew;
  }

  void access::object_traits_impl< ::db::VideoServer, id_mysql >::
  init (object_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    // id
    //
    {
      long unsigned int& v =
        o.id;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.id_value,
        i.id_null);
    }

    // videoSourceName
    //
    {
      ::std::string& v =
        o.videoSourceName;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.videoSourceName_value,
        i.videoSourceName_size,
        i.videoSourceName_null);
    }

    // accessType
    //
    {
      int& v =
        o.accessType;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.accessType_value,
        i.accessType_null);
    }

    // ip
    //
    {
      ::std::string& v =
        o.ip;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.ip_value,
        i.ip_size,
        i.ip_null);
    }

    // port
    //
    {
      int& v =
        o.port;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.port_value,
        i.port_null);
    }

    // userName
    //
    {
      ::std::string& v =
        o.userName;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.userName_value,
        i.userName_size,
        i.userName_null);
    }

    // password
    //
    {
      ::std::string& v =
        o.password;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.password_value,
        i.password_size,
        i.password_null);
    }

    // factory
    //
    {
      ::std::string& v =
        o.factory;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.factory_value,
        i.factory_size,
        i.factory_null);
    }

    // sipid
    //
    {
      ::std::string& v =
        o.sipid;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.sipid_value,
        i.sipid_size,
        i.sipid_null);
    }

    // status
    //
    {
      int& v =
        o.status;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.status_value,
        i.status_null);
    }

    // subNum
    //
    {
      int& v =
        o.subNum;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.subNum_value,
        i.subNum_null);
    }

    // isDel
    //
    {
      bool& v =
        o.isDel;

      mysql::value_traits<
          bool,
          mysql::id_long >::set_value (
        v,
        i.isDel_value,
        i.isDel_null);
    }
  }

  void access::object_traits_impl< ::db::VideoServer, id_mysql >::
  init (id_image_type& i, const id_type& id)
  {
    {
      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.id_value, is_null, id);
      i.id_null = is_null;
    }
  }

  const char access::object_traits_impl< ::db::VideoServer, id_mysql >::persist_statement[] =
  "INSERT INTO `wn_access_front_end` "
  "(`id`, "
  "`name`, "
  "`access_type`, "
  "`ip`, "
  "`port`, "
  "`username`, "
  "`password`, "
  "`factory`, "
  "`sipid`, "
  "`status`, "
  "`sub_num`, "
  "`is_del`) "
  "VALUES "
  "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

  const char access::object_traits_impl< ::db::VideoServer, id_mysql >::find_statement[] =
  "SELECT "
  "`wn_access_front_end`.`id`, "
  "`wn_access_front_end`.`name`, "
  "`wn_access_front_end`.`access_type`, "
  "`wn_access_front_end`.`ip`, "
  "`wn_access_front_end`.`port`, "
  "`wn_access_front_end`.`username`, "
  "`wn_access_front_end`.`password`, "
  "`wn_access_front_end`.`factory`, "
  "`wn_access_front_end`.`sipid`, "
  "`wn_access_front_end`.`status`, "
  "`wn_access_front_end`.`sub_num`, "
  "`wn_access_front_end`.`is_del` "
  "FROM `wn_access_front_end` "
  "WHERE `wn_access_front_end`.`id`=?";

  const char access::object_traits_impl< ::db::VideoServer, id_mysql >::update_statement[] =
  "UPDATE `wn_access_front_end` "
  "SET "
  "`name`=?, "
  "`access_type`=?, "
  "`ip`=?, "
  "`port`=?, "
  "`username`=?, "
  "`password`=?, "
  "`factory`=?, "
  "`sipid`=?, "
  "`status`=?, "
  "`sub_num`=?, "
  "`is_del`=? "
  "WHERE `id`=?";

  const char access::object_traits_impl< ::db::VideoServer, id_mysql >::erase_statement[] =
  "DELETE FROM `wn_access_front_end` "
  "WHERE `id`=?";

  const char access::object_traits_impl< ::db::VideoServer, id_mysql >::query_statement[] =
  "SELECT "
  "`wn_access_front_end`.`id`, "
  "`wn_access_front_end`.`name`, "
  "`wn_access_front_end`.`access_type`, "
  "`wn_access_front_end`.`ip`, "
  "`wn_access_front_end`.`port`, "
  "`wn_access_front_end`.`username`, "
  "`wn_access_front_end`.`password`, "
  "`wn_access_front_end`.`factory`, "
  "`wn_access_front_end`.`sipid`, "
  "`wn_access_front_end`.`status`, "
  "`wn_access_front_end`.`sub_num`, "
  "`wn_access_front_end`.`is_del` "
  "FROM `wn_access_front_end`";

  const char access::object_traits_impl< ::db::VideoServer, id_mysql >::erase_query_statement[] =
  "DELETE FROM `wn_access_front_end`";

  const char access::object_traits_impl< ::db::VideoServer, id_mysql >::table_name[] =
  "`wn_access_front_end`";

  void access::object_traits_impl< ::db::VideoServer, id_mysql >::
  persist (database& db, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    callback (db,
              static_cast<const object_type&> (obj),
              callback_event::pre_persist);

    image_type& im (sts.image ());
    binding& imb (sts.insert_image_binding ());

    if (init (im, obj, statement_insert))
      im.version++;

    im.id_value = 0;

    if (im.version != sts.insert_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_insert);
      sts.insert_image_version (im.version);
      imb.version++;
    }

    {
      id_image_type& i (sts.id_image ());
      binding& b (sts.id_image_binding ());
      if (i.version != sts.id_image_version () || b.version == 0)
      {
        bind (b.bind, i);
        sts.id_image_version (i.version);
        b.version++;
      }
    }

    insert_statement& st (sts.persist_statement ());
    if (!st.execute ())
      throw object_already_persistent ();

    obj.id = id (sts.id_image ());

    callback (db,
              static_cast<const object_type&> (obj),
              callback_event::post_persist);
  }

  void access::object_traits_impl< ::db::VideoServer, id_mysql >::
  update (database& db, const object_type& obj)
  {
    ODB_POTENTIALLY_UNUSED (db);

    using namespace mysql;
    using mysql::update_statement;

    callback (db, obj, callback_event::pre_update);

    mysql::transaction& tr (mysql::transaction::current ());
    mysql::connection& conn (tr.connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    id_image_type& idi (sts.id_image ());
    init (idi, id (obj));

    image_type& im (sts.image ());
    if (init (im, obj, statement_update))
      im.version++;

    bool u (false);
    binding& imb (sts.update_image_binding ());
    if (im.version != sts.update_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_update);
      sts.update_image_version (im.version);
      imb.version++;
      u = true;
    }

    binding& idb (sts.id_image_binding ());
    if (idi.version != sts.update_id_image_version () ||
        idb.version == 0)
    {
      if (idi.version != sts.id_image_version () ||
          idb.version == 0)
      {
        bind (idb.bind, idi);
        sts.id_image_version (idi.version);
        idb.version++;
      }

      sts.update_id_image_version (idi.version);

      if (!u)
        imb.version++;
    }

    update_statement& st (sts.update_statement ());
    if (st.execute () == 0)
      throw object_not_persistent ();

    callback (db, obj, callback_event::post_update);
    pointer_cache_traits::update (db, obj);
  }

  void access::object_traits_impl< ::db::VideoServer, id_mysql >::
  erase (database& db, const id_type& id)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    id_image_type& i (sts.id_image ());
    init (i, id);

    binding& idb (sts.id_image_binding ());
    if (i.version != sts.id_image_version () || idb.version == 0)
    {
      bind (idb.bind, i);
      sts.id_image_version (i.version);
      idb.version++;
    }

    if (sts.erase_statement ().execute () != 1)
      throw object_not_persistent ();

    pointer_cache_traits::erase (db, id);
  }

  access::object_traits_impl< ::db::VideoServer, id_mysql >::pointer_type
  access::object_traits_impl< ::db::VideoServer, id_mysql >::
  find (database& db, const id_type& id)
  {
    using namespace mysql;

    {
      pointer_type p (pointer_cache_traits::find (db, id));

      if (!pointer_traits::null_ptr (p))
        return p;
    }

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);

    if (l.locked ())
    {
      if (!find_ (sts, &id))
        return pointer_type ();
    }

    pointer_type p (
      access::object_factory<object_type, pointer_type>::create ());
    pointer_traits::guard pg (p);

    pointer_cache_traits::insert_guard ig (
      pointer_cache_traits::insert (db, id, p));

    object_type& obj (pointer_traits::get_ref (p));

    if (l.locked ())
    {
      select_statement& st (sts.find_statement ());
      ODB_POTENTIALLY_UNUSED (st);

      callback (db, obj, callback_event::pre_load);
      init (obj, sts.image (), &db);
      load_ (sts, obj, false);
      sts.load_delayed (0);
      l.unlock ();
      callback (db, obj, callback_event::post_load);
      pointer_cache_traits::load (ig.position ());
    }
    else
      sts.delay_load (id, obj, ig.position ());

    ig.release ();
    pg.release ();
    return p;
  }

  bool access::object_traits_impl< ::db::VideoServer, id_mysql >::
  find (database& db, const id_type& id, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);
    assert (l.locked ()) /* Must be a top-level call. */;

    if (!find_ (sts, &id))
      return false;

    select_statement& st (sts.find_statement ());
    ODB_POTENTIALLY_UNUSED (st);

    reference_cache_traits::position_type pos (
      reference_cache_traits::insert (db, id, obj));
    reference_cache_traits::insert_guard ig (pos);

    callback (db, obj, callback_event::pre_load);
    init (obj, sts.image (), &db);
    load_ (sts, obj, false);
    sts.load_delayed (0);
    l.unlock ();
    callback (db, obj, callback_event::post_load);
    reference_cache_traits::load (pos);
    ig.release ();
    return true;
  }

  bool access::object_traits_impl< ::db::VideoServer, id_mysql >::
  reload (database& db, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);
    assert (l.locked ()) /* Must be a top-level call. */;

    const id_type& id (object_traits_impl::id (obj));
    if (!find_ (sts, &id))
      return false;

    select_statement& st (sts.find_statement ());
    ODB_POTENTIALLY_UNUSED (st);

    callback (db, obj, callback_event::pre_load);
    init (obj, sts.image (), &db);
    load_ (sts, obj, true);
    sts.load_delayed (0);
    l.unlock ();
    callback (db, obj, callback_event::post_load);
    return true;
  }

  bool access::object_traits_impl< ::db::VideoServer, id_mysql >::
  find_ (statements_type& sts,
         const id_type* id)
  {
    using namespace mysql;

    id_image_type& i (sts.id_image ());
    init (i, *id);

    binding& idb (sts.id_image_binding ());
    if (i.version != sts.id_image_version () || idb.version == 0)
    {
      bind (idb.bind, i);
      sts.id_image_version (i.version);
      idb.version++;
    }

    image_type& im (sts.image ());
    binding& imb (sts.select_image_binding ());

    if (im.version != sts.select_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_select);
      sts.select_image_version (im.version);
      imb.version++;
    }

    select_statement& st (sts.find_statement ());

    st.execute ();
    auto_result ar (st);
    select_statement::result r (st.fetch ());

    if (r == select_statement::truncated)
    {
      if (grow (im, sts.select_image_truncated ()))
        im.version++;

      if (im.version != sts.select_image_version ())
      {
        bind (imb.bind, im, statement_select);
        sts.select_image_version (im.version);
        imb.version++;
        st.refetch ();
      }
    }

    return r != select_statement::no_data;
  }

  result< access::object_traits_impl< ::db::VideoServer, id_mysql >::object_type >
  access::object_traits_impl< ::db::VideoServer, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));

    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.select_image_binding ());

    if (im.version != sts.select_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_select);
      sts.select_image_version (im.version);
      imb.version++;
    }

    std::string text (query_statement);
    if (!q.empty ())
    {
      text += " ";
      text += q.clause ();
    }

    q.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        text,
        false,
        true,
        q.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::object_result_impl<object_type> > r (
      new (shared) mysql::object_result_impl<object_type> (
        q, st, sts, 0));

    return result<object_type> (r);
  }

  unsigned long long access::object_traits_impl< ::db::VideoServer, id_mysql >::
  erase_query (database& db, const query_base_type& q)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));

    std::string text (erase_query_statement);
    if (!q.empty ())
    {
      text += ' ';
      text += q.clause ();
    }

    q.init_parameters ();
    delete_statement st (
      conn,
      text,
      q.parameters_binding ());

    return st.execute ();
  }
}

#include <odb/post.hxx>
