
#include "../include/ailog.h"
#include <iostream>
#include <boost/log/core.hpp>
#include <boost/log/utility/setup/common_attributes.hpp>
#include <boost/log/utility/setup/file.hpp>
#include <boost/log/utility/setup/console.hpp>
#include <boost/log/expressions.hpp>
#include <boost/log/support/date_time.hpp>
#include <boost/log/sinks/async_frontend.hpp>

namespace logging = boost::log;
namespace sinks = boost::log::sinks;
namespace attrs = boost::log::attributes;
namespace src = boost::log::sources;
namespace expr = boost::log::expressions;
namespace keywords = boost::log::keywords;
using boost::shared_ptr;

BOOST_LOG_ATTRIBUTE_KEYWORD(severitylevel, "Severity", logging::trivial::severity_level)
BOOST_LOG_ATTRIBUTE_KEYWORD(timestamp, "TimeStamp", boost::posix_time::ptime)

namespace ai 
{
    /**
     * [in] appName: 应用程序名称,生成的日志文件名以该名称开头
     * [in] logPath: 日志文件目录,不输入则为当前路径
     * [in] maxfiles: 日志文件最大个数 超过清除旧文件
     */
    bool LogMaker::initLog(const std::string& appName, const std::string& logPath, int maxfiles)
    {
        //当前临时文件
        std::string strName = appName;
        transform(strName.begin(), strName.end(), strName.begin(), ::toupper);
        std::string workingFile = logPath + "/" + strName + "-%Y-%m-%d.log";

        //日志行格式控制
        logging::formatter logFormat = expr::stream << expr::format_date_time(timestamp, "[%m-%d %H:%M:%S]")
            << "[" << severitylevel << "]" << expr::message;

        //命令行创建
        auto console_sink = logging::add_console_log(std::clog,
            keywords::auto_flush = true,
            keywords::format = logFormat);

        //异步文件创建
        boost::shared_ptr<sinks::text_file_backend> backend = boost::make_shared<sinks::text_file_backend>(
            keywords::file_name = workingFile,
            //keywords::target_file_name = finalFile,
            keywords::open_mode = std::ios::app | std::ios::out,
            keywords::time_based_rotation = sinks::file::rotation_at_time_point(0, 0, 0),  //每天重建
            keywords::enable_final_rotation = false,
            keywords::auto_flush = true,
            keywords::auto_newline_mode = sinks::auto_newline_mode::always_insert
            );
        using sink_t = sinks::asynchronous_sink<sinks::text_file_backend, sinks::unbounded_fifo_queue>;
        boost::shared_ptr<sink_t> asyncsink = boost::shared_ptr<sink_t>(new sink_t(backend));
        asyncsink->set_formatter(logFormat);
        asyncsink->locked_backend()->set_file_collector(sinks::file::make_collector(
            keywords::target = logPath,
            keywords::min_free_space = 100 * 1024 * 1024,
            keywords::max_files = maxfiles
        ));
        asyncsink->locked_backend()->scan_for_files();

        logging::add_common_attributes();
        logging::core::get()->add_thread_attribute("Scope", attrs::named_scope());
        logging::core::get()->add_global_attribute("ThreadID", attrs::current_thread_id());
        logging::core::get()->add_sink(console_sink);
        logging::core::get()->add_sink(asyncsink);
        //默认日志级别控制
        setLogLevel(severity_level::trace);
        return true;
    }

    void LogMaker::finiLog()
    {
        logging::core::get()->remove_all_sinks();
    }

    void LogMaker::setLogLevel(severity_level level)
    {
        logging::core::get()->set_filter(severitylevel >= level);
    }

    LogMaker::LogMaker(severity_level level, const char* szFile, const char* function, int line)
    : m_level(level), m_function( function ), m_line( line )
    {
        std::string sFile( szFile );
        size_t pos = sFile.find_last_of('/');
        if ( pos != std::string::npos )
            sFile = sFile.substr( pos+1, sFile.length() );
        m_file = sFile;
        if ( m_function == "operator()") //lambda函数
            m_function = "";
    }

    LogMaker::~LogMaker()
    {
        if (!m_file.empty())
            BOOST_LOG_SEV(m_scl, m_level) << "[" << m_file << ":" << m_line << ":" << m_function << "] " << m_message.str();
        else
            BOOST_LOG_SEV(m_scl, m_level) << " " << m_message.str();
        m_message.clear();
    }
}
