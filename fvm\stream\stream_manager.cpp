/**
 * Project FVM
 */

#include "stream_manager.h"
#include "stream_factory.h"
#include <protocol/protocol_manager.h>
#include <protocol/status_manager.h>
#include <protocol/protocol_sender.h>
#include "stream/output/video_recorder.h"
#include "platform/platform_manager.h"
#include "boost/timer.hpp"
#include "protocol/event_manager.h"
#include "util/config/fvm_config.h"

/**
 * StreamManager implementation
 *  @brief: 视频流管理 通道创建更新，暂停恢复，事件录像等
 */
namespace fvm::stream
{
    using namespace data;
    using namespace network;
    using namespace timer;
    using namespace boost::gregorian;
    using namespace protocol;

    using std::make_shared;

    // 检测通道列表 <channelID, DetectChannelPtr>
    std::map<int, DetectChannelPtr> detectChannels;
    boost::fibers::recursive_mutex detectChannelLock;

    //! 进程对应检测通道 key:processId
    std::map<int, std::map<int, DetectChannelPtr>> processDetectChannels;

    // 当前使用的临时通道列表 <channelID, DetectChannelPtr>，可能包含detectchannel，也可能同一个管道中既有temp也有set
    std::map<ChannelType, StreamPipePtr> curTempChannels;
    //为没有分配检测通道的视频使用的临时通道列表
    std::map<ChannelType, StreamPipePtr> tempChannels;

    // [抢球联动] 球机通道列表
    #define DOME_STREAM_ID_START 30000
    std::map<std::string, StreamPipePtr> domeChannels;

    // 前端接入平台列表 <frontID, FrontPlatformPtr>
    std::map<int, FrontPlatformPtr> frontPlatforms;
    // 时间切换方案定时器到时通知
    OnTimeProgramsTimerExpired onTimeProgramsTimerExpired;

    /**
     * 注册协议信号
     */
    void registerProtocols()
    {
        PROTOCOL_MANAGER.onIvaRequestInitMessage.connect([](RequestInit msg){
            startStreams(msg.iIndexid, true);
        });
        PROTOCOL_MANAGER.onRequestTempVideoMessage.connect([](RequestTempVideo msg){
            return playTempChannels(msg.videoId, std::nullopt, std::nullopt, msg.streamId);
        });
        PROTOCOL_MANAGER.onRequestSetVideoMessage.connect([](RequestSetVideo msg){
            return playTempChannels(msg.videoId, msg.presetId, msg.actionId, msg.streamId);
        });
        PROTOCOL_MANAGER.onMonitorChangedMessage.connect([]{
            platform::notifyGumMsm();
            return DATA_MANAGER.update(nullopt);            
        });
        PROTOCOL_MANAGER.onChannelChangedMessage.connect([](FVMChannelChanged msg){
            DATA_MANAGER.updateVideoSourcesInfo(false, msg.videoId);
            auto channelSource = DATA_MANAGER.getChannelSource(msg.channelId);
            if (channelSource.has_value()){
                stopDetectChannel(msg.channelId, false);
            }
            restartDetectChannel(msg.channelId, msg.videoId);
            DATA_MANAGER.writeVideoChange(msg.videoId);
        });
        PROTOCOL_MANAGER.onVideoPauseMessage.connect([](RequestVideoPause msg){
            return setDetectChannelStatus(msg.videoId, std::nullopt, msg.reason);
        });
        PROTOCOL_MANAGER.onVideoResumeMessage.connect([](RequestVideoResume msg){
            return setDetectChannelStatus(msg.videoId, msg.presetId, msg.reason);
        });
        PROTOCOL_MANAGER.onSetPosition.connect([](SetPosition msg) {
            return savePresetPosition(msg.videoId, msg.presetId);
        });
        PROTOCOL_MANAGER.onFvmChangedMessage.connect([](FVMChangedId msg){
            platform::notifyChanged();
            DATA_MANAGER.updateRemotes(msg.accessId);
            //将临时通道中视频前端为这个id的通道停掉
            for (auto& tempChannel : tempChannels) {
                if (tempChannel.second && tempChannel.second->getInputVideoServerId() == msg.accessId ) {
                    auto pipe = tempChannel.second;
                    pipe->stopPipe(true, true);
                }
            }
            DATA_MANAGER.setRemoteStatus(true, nullopt, (int)StreamInputType::File);
		    DATA_MANAGER.setRemoteStatus(true, nullopt, (int)StreamInputType::RTSP);
            updatePlatform(msg.accessId);
        });
        PROTOCOL_MANAGER.onRTSPChangedMessage.connect([](RequestRTSPChanged msg){
            for (const auto& videoId : msg.videoIds){
              DATA_MANAGER.updateVideoSourcesInfo(false, videoId);
            }
        });
        PROTOCOL_MANAGER.onEventOccurInfo.connect([](EventOccurInfo msg){
            eventOccurred(msg);
        });

        PROTOCOL_MANAGER.onEventRemoveInfo.connect([](EventRemoveInfo msg){
            eventRemoved(msg);
        });

        PROTOCOL_MANAGER.onIvaVideoQuaAlarm.connect([](VideoQuaAlarmConf msg) {
            postVideoQuaAlarm(msg);
        });

        PROTOCOL_MANAGER.onIvaVideoRecovery.connect([](VideoQuaRecovery msg) {
            postVideoQuaRecovery(msg);
        });


        PROTOCOL_MANAGER.onRemoteChangedMessage.connect([](RequestRemoteChanged msg){
            //platform::notifyChanged();
            DATA_MANAGER.updateRemotes(msg.iRemoteId);
            DATA_MANAGER.updateVideoSourcesInfo(false, nullopt, msg.iRemoteId);
        });
        PROTOCOL_MANAGER.onRemoteStatusMessage.connect([](PostRemoteStatus msg) {
            DATA_MANAGER.setRemoteStatus(msg.iStatus, msg.iRemoteId, nullopt );
            });

        PROTOCOL_MANAGER.onPtzControl.connect([](PtzControl msg) {
            controlPtz(msg.videoId, msg.action, msg.step);
            });
        PROTOCOL_MANAGER.onPresetControl.connect([](PresetControl msg) {
            controlPreset(msg.videoId, msg.presetId, msg.action);
            });
        PROTOCOL_MANAGER.onFocusEvent.connect([](FocusEvent msg) {
            focusEvent(msg.channelId, msg.rect);
            });
    }

    /**
     * 初始化 管理器 （注册信号等）
     */
    void initStreamManager()
    {
        // 协议信号绑定
        DATA_MANAGER.onRestart.connect([](const optional<int>& processId) { return startStreams(processId); });
        registerProtocols();
        setTimeProgramsTimers();
        // 创建临时通道
        tempChannels[ChannelType::TempRequest] = make_shared<StreamPipe>();
        tempChannels[ChannelType::TempSetArea] = make_shared<StreamPipe>();

        initDomeEventTypes();
    }

    /**
     * @brief 启动通道，配置通道资源
     * @note 一般发生在FVM启动、下发配置、iva请求
     * @param[in] processId 进程ID
     * @param[in] ivaRequest 是否重新发送数据（iva请求）
     */
    void startStreams(const optional<int> &processId, bool ivaRequest)
    {
        stopDetectChannels(processId);
        stopTempChannels();

        protocol::sendSystemConfig(processId);
        protocol::sendAlgorithmParam(processId);
        protocol::sendProcessBasicConfigs(processId);

        std::vector<worker::FiberFuture> futures;
        auto channelSources = DATA_MANAGER.getProcessChannelsSource(processId);
        for (const auto&[id, ptr] : channelSources)
        {
            auto channelId = id;
            auto videoSourceInfoPtr = ptr;
            futures.emplace_back(worker::post(worker::WorkerType::Channel, [&, channelId, videoSourceInfoPtr](){
                if (isNeedRestartDetectChannel(channelId, videoSourceInfoPtr, ivaRequest))
                {
                    restartDetectChannel(channelId, (int) videoSourceInfoPtr->videoSourcePtr->getId());
                }
                else if (isOnlyRestoreDetectRequest(channelId, videoSourceInfoPtr, ivaRequest))
                {
                    restoreDetect(channelId);
                }
            }));
        }

        for(auto& f: futures)
        {
            if(f.has_value())
                f->wait();
        }
        DATA_MANAGER.writeProcessVideoChange(processId);
    }

    /**
     * @brief      根据web业务操作逻辑、iva请求 判断是否仅直接恢复检测，不重启fvm管道
     */
    bool isOnlyRestoreDetectRequest(int channelId, const VideoSourceInfoPtr &videoSourceInfoPtr, bool ivaRequest)
    {
        if ((nullptr == videoSourceInfoPtr) || (nullptr == videoSourceInfoPtr->videoSourcePtr))
        {
            return false;
        }
        auto detectChannelPtr = getDetectChannel(channelId);
        if (!detectChannelPtr)
        {
            return false;
        }

        //! web进入画检测区后 1：非文件流修改配置后，点击下发配置 2、未修改配置，点击下发配置。 都直接恢复检测
        auto currentDetectable = DATA_MANAGER.queryChannelDetectable(channelId);
        auto detectState = detectChannelPtr->getDetectStatus();
        auto isNeedRestore = ((detectState != ChannelDetectState::Detecting)
                           && ((currentDetectable == ChannelDetectable::RestoreDetectable) || currentDetectable == ChannelDetectable::OffsetDetectable));

        //! 1、iva启动后发送ivaRequest，重新请求配置 2、web端通道状态由暂停状态变为可恢复检测状态
        if (ivaRequest || isNeedRestore)
            return true;

        return false;
    }

    /**
     * @brief      开始恢复通道检测
     */
    void restoreDetect(int channelId)
    {
        auto detectChannelPtr = getDetectChannel(channelId);
        if (!detectChannelPtr)
            return;
        detectChannelPtr->restoreDetect(StreamRestoreReason::Init);

    }
	/**
     * @brief      获取所有临时通道
     */
    std::map<ChannelType, StreamPipePtr> getTempChannels()
    {
        return tempChannels;
    }

    /**
     * @brief "视频查看"，"设置检测区" 视频推流播放处理
     * @param[in] videoId：视频id;
     * @param[in] presetId:预置位id; actId:当前视频的平台对应实际预置位id, 可选参数，有值表示是"视频查看"，否则是"设置检测区"
     * @param[in] streamId：rtmp推流id
     */
    void playTempChannels(int videoId, const optional<int> &presetId, const optional<int> &actId, int streamId)
    {
        //! 检查视频资源是否存在
        auto videoSourceInfo = DATA_MANAGER.getVideoChannelSource(videoId);
        if (!videoSourceInfo.has_value())
        {
            //若不存在，更新一次视频资源
            boost::timer debugTimer;
            DATA_MANAGER.updateVideoSourcesInfo(false, videoId);
            std::cout << "updateVideoSourcesInfo use: " << debugTimer.elapsed() * 1000 << " ms\n";
            videoSourceInfo = DATA_MANAGER.getVideoChannelSource(videoId);
            if (!videoSourceInfo.has_value())
                return;
        }
        auto videoSourceInfoPtr = videoSourceInfo.value();
        //! 画检测区播放临时通道视频，停止检测通道的事件检测
        ChannelType pipeType;
        OutputTargetType outputType;
        if (presetId.has_value())
        {
            pipeType = ChannelType::TempSetArea;
            outputType = OutputTargetType::TempSetArea;
        }
        else
        {
            pipeType = ChannelType::TempRequest;
            outputType = OutputTargetType::TempRequest;
        }
        //删除原curTemp中对应的output
        if (curTempChannels.find(pipeType) != curTempChannels.end())
        {
            curTempChannels[pipeType]->removeOutputTarget(outputType);
        }
        StreamPipePtr pipe = nullptr;

        //配置文件处理结束循环的逻辑
        if (SETTINGS->fileRepeat())
        {
            //先看这个视频是否绑定了检测通道
            int channelId = (int)videoSourceInfoPtr->channelId;
            auto detectChannelPtr = getDetectChannel(channelId);
            if (detectChannelPtr)
            {
                pipe = detectChannelPtr;
                if (presetId.has_value())
                    detectChannelPtr->pauseDetect(StreamPauseReason::SetDetectArea);
            }
            else //没有检测通道，从临时通道中生成
            {
                //先在固定临时通道中找是不是有这个视频，若有，直接拿过来用
                for (auto& tempChannel : tempChannels) {
                    if (tempChannel.second && tempChannel.second->getInputVideoId() == videoId) {
                        pipe = tempChannel.second;
                        break;
                    }
                }

                //在固定球机通道中找是不是有这个视频，若有，直接拿过来用
                for (auto& domeChannel : domeChannels) {
                    if (domeChannel.second && domeChannel.second->getInputVideoId() == videoId) {
                        pipe = domeChannel.second;
                        break;
                    }
                }
            }
        }

        //否则使用固定临时通道
        if (!pipe) {
            pipe = tempChannels[pipeType];
            auto input = createStreamInput(videoSourceInfoPtr);
            pipe->initInput(input);
            pipe->startPipe();
        }
        //创建一个新的输出
        auto rtmpOutput = createStreamOutput(StreamOuputType::RTMP, streamId);
        rtmpOutput->setOutputTarget(outputType);
        bool outputAdded = pipe->addOutput(rtmpOutput);

        if(outputAdded)
            curTempChannels[pipeType] = pipe;

        if (presetId.has_value() && actId.has_value())  //有预置位时，调用实际预置位，不需要其他操作
        {
            auto pId = presetId.value();
            auto aId = actId.value();
            pipe->asyncCallCameraPreset(pId, aId);
            //            if ( videoSourceInfoPtr->presetInfosMap.find( pId ) != videoSourceInfoPtr->presetInfosMap.end() )
            //            {
            //                videoSourceInfoPtr->presetInfosMap[pId].presetPtr->setActPreset( aId );
            //            }
        }

    }

    /**
     * @brief 创建检测通道
     * @param[in] channelId：通道id, videoId：视频id; channelSources:通道视频资源
     */
    void createDetectChannel(int channelId, int videoId, const VideoSourceInfoPtr &channelSources)
    {
        auto currChannelProcessId = DATA_MANAGER.getChannelsProcessId(channelId);
        
        std::lock_guard<boost::fibers::recursive_mutex> lock(detectChannelLock);
        //创建通道时，如果已经存在，先销毁，保证IVA正常工作
        if (detectChannels.find(channelId) != detectChannels.end())
        {
            auto detectChannelPtr = detectChannels.at(channelId);
            if (detectChannelPtr)
            {
                onTimeProgramsTimerExpired.disconnect(channelId);
                detectChannelPtr->pauseDetect(StreamPauseReason::Init);
                detectChannelPtr->stopPipe(true, true);
                protocol::pauseStreamStatus(channelId);
                detectChannels.erase(channelId);
                clearChannelEvents(channelId);
                //! 清理进程表中对应的通道信息
                for (auto& [processId, channels] : processDetectChannels)
                {
                    if (channels.find(channelId) != channels.end())
                    {
                        channels.erase(channelId);
                        break;
                    }
                }
            }
        }

        detectChannels[channelId] = make_shared<DetectChannel>();
        detectChannels[channelId]->onChannelStreamRestored.connect([=](auto reason) {
            int height = 0, width = 0;
            if ((detectChannels[channelId]) && detectChannels[channelId]->getCodecInfo())
            {
                height = detectChannels[channelId]->getCodecInfo()->getHeight();
                width = detectChannels[channelId]->getCodecInfo()->getWidth();
            }
            int presetId = DATA_MANAGER.getCurrPresetId(channelSources);
            ChannelRestoreConf channelRestoreConf(channelId, videoId, presetId, width, height, (int) reason);
            PROTOCOL_MANAGER.sendToIVA(ProtocolType::UDP_CHANNEL_RESTORECONF,channelRestoreConf, currChannelProcessId);
            ai::LogInfo << "Channel restored ! " << channelId << " width " << width  << " height "<< height;
        });
        detectChannels[channelId]->onChannelStreamPaused.connect([=](auto reason) {
            int height = 0, width = 0;
            if ((detectChannels[channelId]) && detectChannels[channelId]->getCodecInfo())
            {
                height = detectChannels[channelId]->getCodecInfo()->getHeight();
                width = detectChannels[channelId]->getCodecInfo()->getWidth();
            }
            ChannelPauseConf channelPauseConf(channelId, videoId, width, height, (int) reason);
            PROTOCOL_MANAGER.sendToIVA(ProtocolType::UDP_CHANNEL_PAUSECONF,channelPauseConf, currChannelProcessId);
            //protocol::pauseStreamStatus(channelId, videoId);
            ai::LogInfo << "Channel Paused ! " << channelId << " video: " << videoId;
        });
        detectChannels[channelId]->onStreamCodecInfoRetrieved.connect([=](auto codec) {
            protocol::restoreStreamStatus(channelId, videoId);
        });
        detectChannels[channelId]->onStreamInputLost.connect([=]() {
            auto videoId = detectChannels[channelId]->getInputVideoId();
            protocol::pauseStreamStatus(channelId, videoId);
        });
        detectChannels[channelId]->onPTZPresetChecked.connect([=](auto time, auto isPresetChange)
                                                              {filterEvent(time,isPresetChange,channelId);});
        auto input = createStreamInput(channelSources);
        detectChannels[channelId]->initInput(input);

        StreamOuputType ouputType;
        if (SETTINGS->outrtp() == 0)                //0为rtp(默认) 1为udp
            ouputType = StreamOuputType::RTP;
        else
            ouputType = StreamOuputType::UDP;
        auto rtpOutput = createStreamOutput(ouputType, 20000 + (channelId - 1) * 2); //!< iva收流通道从(channelId-1)开始计数
        auto recoderOut = createStreamOutput(StreamOuputType::File, 0, "/data/opt/tomcat/webapps/eventvideo/");  //路径修改需要注意
        //录像参数设置
        auto pRecord = std::dynamic_pointer_cast<VideoRecorder>(recoderOut);
        pRecord->setChannelId(channelId);
        pRecord->setBeforeAfterTime(DATA_MANAGER.getParamProgData(FVM_PRE_RECORD, 10)
            , DATA_MANAGER.getParamProgData(FVM_RECORD_TIME, 30));
        pRecord->bgRecordStart();

        auto rtmpPort = DATA_MANAGER.getParamProgData(FVM_RTMP_PORT, 0);
        if ( rtmpPort > 0 )
        {
            auto rtmpOutput = createStreamOutput(StreamOuputType::RTMP, rtmpPort + channelId - 1);
            detectChannels[channelId]->initOutputs({rtpOutput, rtmpOutput, recoderOut});
        }
        else
        {
            detectChannels[channelId]->initOutputs({rtpOutput, recoderOut});
        }
        onTimeProgramsTimerExpired.connect(channelId, [=]() {if (detectChannels[channelId]) detectChannels[channelId]->onTimeProgramsTimerExpired();});

        detectChannels[channelId]->startPipe();

        processDetectChannels[currChannelProcessId].emplace(channelId, detectChannels[channelId]);
    }

    /**
    * @brief 创建球机录像通道
    * @param[in] groupId：接入id, channelSource:通道视频资源
    */
    void createDomeChannel(std::string groupId, VideoSourceInfoPtr& channelSource)
    {
        std::lock_guard<boost::fibers::recursive_mutex> lock(detectChannelLock);
        if (domeChannels.find(groupId) != domeChannels.end())
        {
            return;
        }

        domeChannels[groupId] = make_shared<StreamPipe>();

       // auto rtpOutput = createStreamOutput(StreamOuputType::RTMP, DOME_STREAM_ID_START + groupId);
        auto recoderOut = createStreamOutput(StreamOuputType::File, 0, "/data/opt/tomcat/webapps/eventvideo/"); 
        //录像参数设置
        auto pRecord = std::dynamic_pointer_cast<VideoRecorder>(recoderOut);
        static int index = 0;
        pRecord->setChannelId(DOME_STREAM_ID_START + index++);
        pRecord->setBeforeAfterTime(DATA_MANAGER.getParamProgData(FVM_PRE_RECORD, 10)
            , DATA_MANAGER.getParamProgData(FVM_RECORD_TIME, 30));

        auto input = createStreamInput(channelSource);
        domeChannels[groupId]->initInput(input);
        //domeChannels[groupId]->initOutputs({ rtpOutput, recoderOut });
        domeChannels[groupId]->initOutputs({ recoderOut });
        domeChannels[groupId]->startPipe();
    }

    /**
     * @brief      根据web业务操作逻辑、iva请求 判断是否需要重启FVM流通道
     */
    bool isNeedRestartDetectChannel(int channelId, const VideoSourceInfoPtr &videoSourceInfoPtr, bool ivaRequest)
    {
        if ((nullptr == videoSourceInfoPtr) || (nullptr == videoSourceInfoPtr->videoSourcePtr))
        {
            return false;
        }

        auto isNewAssignedChannel = (nullptr == getDetectChannel(channelId));
        if (isNewAssignedChannel)
            return true;

        if (ivaRequest) //! 有iva请求时，只发检测参数即可，不用重启fvm流通道
        {
            protocol::sendChannelDetectParam(channelId);
            boost::this_fiber::sleep_for(std::chrono::milliseconds(2000));  //!< 等iva图像检测通道历史数据清理完
            return false;
        }

        return false;
    }

    /**
     * @brief      重启FVM检测通道
     * @param[in]  channelId：通道id videoId:视频id
     */
    void restartDetectChannel(int channelId, int videoId)
    {
        //! 通道或视频不存在，不处理
        auto channelSources = DATA_MANAGER.getChannelSource(channelId);
        if (!channelSources.has_value())
        {
            return;
        }
        auto channelSourcesPtr = channelSources.value();
        if (nullptr == channelSourcesPtr->videoSourcePtr)
        {
            return;
        }
        if (((int) channelSourcesPtr->videoSourcePtr->getId() != videoId)
            || (channelSourcesPtr->videoSourcePtr->getId() <= 0))
        {
            return;
        }

        auto detectChannelPtr = getDetectChannel(channelId);
        if (detectChannelPtr)
        {
            stopDetectChannel(channelId, false);
        }

        protocol::sendChannelDetectParam(channelId);
        boost::this_fiber::sleep_for(std::chrono::milliseconds(2000));  //!< 等iva图像检测通道历史数据清理完
        createDetectChannel(channelId, videoId, channelSourcesPtr);

        // 创建[枪球联动] 球机通道  deviceType = 2;
        auto domeGroupId = channelSourcesPtr->videoSourcePtr->getGroupId();
        auto domeGroupUUID = channelSourcesPtr->videoSourcePtr->getGroupUUID();
        std::optional<VideoSourceInfoPtr> domeVideoSource;
        if (!domeGroupUUID.empty())
        {
            domeVideoSource = DATA_MANAGER.queryDomeVideoSource(domeGroupUUID);
            if (domeVideoSource.has_value())
                createDomeChannel(domeGroupUUID, domeVideoSource.value());
        }
        else if (domeGroupId > 0)
        {
            domeVideoSource = DATA_MANAGER.queryDomeVideoSource(domeGroupId);
            if (domeVideoSource.has_value())
                createDomeChannel(std::to_string(domeGroupId), domeVideoSource.value());
        }
    }

    /**
     * @brief 设置检测通道检测状态
     * @param[in] videoId：视频id
     * @param[in] presetId:预置位id，没有值时表示是 暂停检查通道，否则是恢复检测通道
     * @param[in] reason:状态原因 (暂停原因、恢复原因)
     */
    void setDetectChannelStatus(int videoId, const std::optional<int> &presetId, int reason)
    {
        //! 检测视频是否存在
        auto videoSourceInfo = DATA_MANAGER.getVideoChannelSource(videoId);
        if (!videoSourceInfo.has_value())
        {
            return;
        }
        int channelId = (int) videoSourceInfo.value()->channelId;
        auto detectChannelPtr = getDetectChannel(channelId);
        if (!detectChannelPtr)
        {
            return;
        }

        if (presetId.has_value())
        {  //!< VideoResume消息
            auto streamReason = static_cast<StreamRestoreReason>(reason);
            detectChannelPtr->restoreDetect(streamReason);
        }
        else
        {               //!< VideoPause消息
            auto streamReason = static_cast<StreamPauseReason>(reason);
            //bool notify = streamReason != StreamPauseReason::OffsetEvent;
            detectChannelPtr->pauseDetect(streamReason);
        }
    }

    /**
     * @brief 设置当前画面为该预置位的基准画面
     * @param[in] videoId：视频id
     * @param[in] presetId:预置位id
     */
    void savePresetPosition(int videoId, int presetId)
    {
        auto videoSourceInfo = DATA_MANAGER.getVideoChannelSource(videoId);
        if (!videoSourceInfo.has_value())
            return;

        auto videoSourceInfoPtr = videoSourceInfo.value();
        auto detectChannelPtr = getDetectChannel(videoSourceInfoPtr->channelId);
        PresetPosition position;
        bool writeResult = false;
        if (detectChannelPtr)
        {
            writeResult = detectChannelPtr->writePresetPosition(presetId, position);
        }
        else
        {
            for (auto &[channelType, tempChannel] : tempChannels) {
                if (tempChannel && tempChannel->getInputVideoId() == videoId) {
                    writeResult = tempChannel->writePresetPosition(presetId, position);
                    break;
                }
            }
        }

        /// 子节点级联时，如果该通道是子节点的，web将保存预置位的消息发送到子节点，子节点保存好预置位后上报消息给平台
        if (writeResult)
        {
            bool isPlatform = true;
            VideoPresetPosition presetPosition;
            presetPosition.presetId = presetId;
            presetPosition.position = std::to_string(position.x) + "," + std::to_string(position.y) + "," + std::to_string(position.z);
            PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_PRESET_POSITION_INFO, presetPosition, isPlatform);
        }
    }

    /**
     * @brief      停止检测通道
     * @param[in]  processId:   进程id,停止进程对应的管道。 nullopt，所有进程
     * @param[in]  checkInput:  true: 需要检查通道变化才销毁管道 false: 不用检测管道变化，一律全部销毁
     * @note       下发配置之前，数据库中进程对应的通道可能变化，故从保存的processDetectChannels获取进程对应的通道，而不是从数据库接口获取。以保证以前的通道都停止。
     */
    void stopDetectChannels(const optional<int> &processId, bool checkInput)
    {
        std::map<int, std::map<int, DetectChannelPtr>> processChannels;
        if (!processId.has_value())
        {
            processChannels = processDetectChannels;
        }
        else
        {
            if (processDetectChannels.find(processId.value()) != processDetectChannels.end())
                processChannels.emplace(processId.value(), processDetectChannels.at(processId.value()));
        }

        for (auto& channels : processChannels)
        {
            for (auto&[channelId, channel] : channels.second)
            {
                stopDetectChannel(channelId, checkInput);
            }
        }

    }

    /**
     * @brief      停止临时通道
     * @param[in]  checkInput:  true: 需要检查通道变化才销毁管道 false: 不用检测管道变化，一律全部销毁
     */
    void stopTempChannels(bool checkInput)
    {
        for (auto &[channelType, streamPipePtr] : tempChannels)
        {
            if (!streamPipePtr)
                continue;

            if (checkInput)
            {
                auto videoId = streamPipePtr->getInputVideoId();  //!< 临时通道还未初始化input和output，返回0
                if (videoId <= 0)
                    continue;
                auto inputChange = false;
                auto videoSourceInfo = DATA_MANAGER.getVideoChannelSource(videoId);
                if (videoSourceInfo.has_value() && videoSourceInfo.value()->videoSourcePtr)
                {
                    if (videoId != (int)videoSourceInfo.value()->videoSourcePtr->getId()
                    || videoSourceInfo.value()->videoSourcePtr->getIsChange())
                        inputChange = true;
                }
                else
                {
                    inputChange = true;
                }
                if (!inputChange)
                    continue;
            }

            ai::LogInfo << "Stop temp videoID: " << streamPipePtr->getInputVideoId();

            streamPipePtr->stopPipe(true, true);
            tempChannels[channelType] = make_shared<StreamPipe>();
        }
    }

    /**
     * @brief      停止球机通道
     */
    void stopDomeChannels()
    {
        for (auto& [groupId, streamPipePtr] : domeChannels)
        {
            if (!streamPipePtr)
                continue;

            auto videoId = streamPipePtr->getInputVideoId();  //!< 临时通道还未初始化input和output，返回0
            if (videoId <= 0)
                continue;

            auto inputChange = false;
            auto videoSourceInfo = DATA_MANAGER.getVideoChannelSource(videoId);
            if (videoSourceInfo.has_value() && videoSourceInfo.value()->videoSourcePtr)
            {
                if (videoId != (int)videoSourceInfo.value()->videoSourcePtr->getId()
                    || videoSourceInfo.value()->videoSourcePtr->getIsChange())
                    inputChange = true;
            }
            else
            {
                inputChange = true;
            }

            if (inputChange)
            {
                streamPipePtr->stopPipe(true, true);
            }
        }
    }

    /**
     * @brief      停止检测通道
     * @param[in]  channelId:   需要停止的通道id
     * @param[in]  checkInput:  true: 需要检查通道变化才销毁管道 false: 不用检测管道变化，直接销毁
     */
    void stopDetectChannel(int channelId, bool checkInput)
    {
        std::lock_guard<boost::fibers::recursive_mutex> lock(detectChannelLock);
        if (detectChannels.find(channelId) == detectChannels.end())
            return;

        auto detectChannelPtr = detectChannels.at(channelId);
        if (detectChannelPtr == nullptr)
            return;

        if (!checkInput || detectChannelPtr->isInputChanged())
        {
            onTimeProgramsTimerExpired.disconnect(channelId);
            detectChannelPtr->pauseDetect(StreamPauseReason::Init);
            detectChannelPtr->stopPipe(true, true);
            protocol::pauseStreamStatus(channelId);
            detectChannels.erase(channelId);
            clearChannelEvents(channelId);
            //! 清理进程表中对应的通道信息
            for (auto& [processId, channels] : processDetectChannels)
            {
                if (channels.find(channelId) != channels.end())
                {
                    channels.erase(channelId);
                    break;
                }
            }
        }
    }

    /**
     * @brief      获取检测通道
     * @param[in]  channelId:获取指定通道id的通道信息，nullopt时获取所有通道的参数
     */
    std::map<int, DetectChannelPtr> getDetectChannels()
    {
        std::lock_guard<boost::fibers::recursive_mutex> lock(detectChannelLock);
        return detectChannels;
    }

    /**
     * @brief      获取检测通道
     * @param[in]  channelId:获取指定通道id的通道信息
     */
    DetectChannelPtr getDetectChannel(int channelId)
    {
        std::lock_guard<boost::fibers::recursive_mutex> lock(detectChannelLock);
        if (detectChannels.find(channelId) != detectChannels.end())
        {
            return detectChannels.at(channelId);
        }
        return nullptr;
    }

    /**
     * @brief      获取球机通道
     * @param[in]  groupId:球机接入id
     */
    StreamPipePtr getDomeChannel(std::string groupId)
    {
        std::lock_guard<boost::fibers::recursive_mutex> lock(detectChannelLock);
        if (domeChannels.find(groupId) != domeChannels.end())
        {
            return domeChannels.at(groupId);
        }
        return nullptr;
    }

    /**
     * @brief      获取球机通道
     */
    StreamPipePtr getDomeChannelBySource(data::VideoSourcePtr videoSourcePtr)
    {
        std::lock_guard<boost::fibers::recursive_mutex> lock(detectChannelLock);
        auto groupID = videoSourcePtr->getGroupId();
        auto groupUUID = videoSourcePtr->getGroupUUID();
        if (groupUUID.empty() && groupID > 0)
        {
            groupUUID = std::to_string(groupID);
        }
        if (!groupUUID.empty() && domeChannels.find(groupUUID) != domeChannels.end())
        {
            return domeChannels.at(groupUUID);
        }
        return nullptr;
    }


    /**
     * @brief      停止所有管道输入输出，清除状态、销毁资源
     */
    void disposeStreamManager()
    {
        //! 停止所有检测管道
        stopDetectChannels(nullopt, false);
        //! 停止所有临时管道
        stopTempChannels(false);
        //! 停止所有球机管道
        stopDomeChannels();

        //所有前端状态改为离线
        DATA_MANAGER.setRemoteStatus(false, nullopt, nullopt );
    }


    /**
     * @brief 查找并设置时间切换方案对应的定时时间。
     */
    void setTimeProgramsTimers()
    {
        auto timePrograms = DATA_MANAGER.getProgram();

        if (timePrograms.empty())
        {
            ai::LogError << "no time program exists. configure or check it";
            return;
        }

        //! 第一个时间切换方案
        auto [firstStartDate, firstMorningTime, firstEveningTime] = timePrograms[0];
        auto now = boost::posix_time::second_clock::local_time();
        firstStartDate = date(now.date().year(), firstStartDate.month(),firstStartDate.day());
        auto firstProgramMorning = Ptime(firstStartDate, boost::posix_time::seconds(firstMorningTime));   //!< 年/月/日 早晨时间
        auto firstProgramEvening = Ptime(firstStartDate, boost::posix_time::seconds(firstEveningTime));   //!< 年/月/日 晚上时间

        int32_t repeatTimes = 0 ;
        boost::posix_time::ptime programMorning, programEvening, nextProgramMorning, nextProgramEvening;

        if (timePrograms.size() > 1)
        {
            bool hasFindDate = false;
            for (auto it = timePrograms.begin() ; std::next(it) != timePrograms.end(); it++)
            {
                auto [startDate, morningTime, eveningTime] = *it;
                auto [nextStartDate, nextMorningTime, nextEveningTime] = *std::next(it);

                if (startDate.is_not_a_date() || nextStartDate.is_not_a_date())
                {
                    ai::LogError << "startDate is an invalid date, morningTime:" << morningTime << " eveningTime:" << eveningTime;
                    continue;
                }
                startDate = date(now.date().year(), startDate.month(),startDate.day());
                programMorning = Ptime(startDate, boost::posix_time::seconds(morningTime));   //!< 年/月/日 早晨时间
                programEvening = Ptime(startDate, boost::posix_time::seconds(eveningTime));   //!< 年/月/日 晚上时间

                nextStartDate = date(now.date().year(), nextStartDate.month(),nextStartDate.day());
                nextProgramMorning = Ptime(nextStartDate, boost::posix_time::seconds(nextMorningTime));
                nextProgramEvening = Ptime(nextStartDate, boost::posix_time::seconds(nextEveningTime));

                if ((now > programMorning) && (now < nextProgramMorning))
                {
                    hasFindDate = true;
                    repeatTimes = (int32_t)(nextStartDate - now.date() - days(1)).days() ;
                    break;
                }
            }
            //!< 没找到取最后一个
            if (!hasFindDate)
            {
                auto& [startDate, morningTime, eveningTime] = *std::prev(timePrograms.end());

                startDate = date(now.date().year(), startDate.month(),startDate.day());
                programMorning = Ptime(startDate, boost::posix_time::seconds(morningTime));   //!< 年/月/日 早晨时间
                programEvening = Ptime(startDate, boost::posix_time::seconds(eveningTime));   //!< 年/月/日 晚上时间
                nextProgramMorning = firstProgramMorning;
                nextProgramEvening = firstProgramEvening;
                //! 最后一个切换方案日期与第一个的切换方案日期 时间间隔
                repeatTimes = (int32_t)(firstStartDate + years(1) - now.date() - days(1)).days() ;
            }

        }
        else if (timePrograms.size() == 1)  //!< 如果只配置了一个时间切换方案，则该时间切换方案的定时器一直循环
        {
            programMorning = firstProgramMorning;
            programEvening = firstProgramEvening;
            repeatTimes = -1;
        }

        auto todayProgramMorning = Ptime(now.date(), boost::posix_time::seconds(programMorning.time_of_day().total_seconds()));
        auto todayProgramEvening = Ptime(now.date(), boost::posix_time::seconds(programEvening.time_of_day().total_seconds()));

        auto eveningRepeatTimes = repeatTimes;
        auto morningRepeatTimes = repeatTimes;
        if (now > todayProgramMorning)    //!< 当天时间已经过了早晨的定时时间，则定时第二天早上的时间
        {
            todayProgramMorning += days(1);
            if (timePrograms.size() != 1) //!< 多个时间切换方案，如果只有一个切换方案则循环执行，不用判断日期跨越以及重复次数
            {
                if ( repeatTimes > 0)
                {
                    morningRepeatTimes -= 1;
                }
                else //!< 当天时间已经过了早晨的定时时间，且和第二个时间方案早晨相差1天，即repeatTimes = 0，则执行下个时间切换方案
                {
                    morningRepeatTimes = (int32_t)(programMorning.date() + years(1) - todayProgramMorning.date() - days(1)).days();
                    todayProgramMorning = Ptime(todayProgramMorning.date(), boost::posix_time::seconds(nextProgramMorning.time_of_day().total_seconds()));
                }
            }

        }
        if (now > todayProgramEvening)  //! 和上面逻辑类似
        {
            todayProgramEvening += days(1);
            if (timePrograms.size() != 1)
            {
                if (repeatTimes > 0)
                {
                    if (timePrograms.size() != 1)
                        eveningRepeatTimes -= 1;
                }
                else
                {
                    eveningRepeatTimes = (int32_t) (programEvening.date() + years(1) - todayProgramEvening.date() - days(1)).days();
                    todayProgramEvening = Ptime(todayProgramEvening.date(), boost::posix_time::seconds(nextProgramEvening.time_of_day().total_seconds()));
                }
            }
        }

        todayProgramMorning += boost::posix_time::milliseconds (5000);  //!< 额外加长一点时间，防止前面日期比较执行有时间消耗，且刚好超过定时时间
        todayProgramEvening += boost::posix_time::milliseconds (5000);


        ai::LogInfo << "program morning timer:" << boost::posix_time::to_simple_string(todayProgramMorning) << " repeatTimes:" << morningRepeatTimes;
        ai::LogInfo << "program evening timer:" << boost::posix_time::to_simple_string(todayProgramEvening) << " repeatTimes:" << eveningRepeatTimes;
        auto morningTimePtr = TIMER_MANAGER.addTimer(todayProgramMorning,[=](auto timerPtr){ timeProgramTimerCallBack(timerPtr);}, std::chrono::hours(24), (int32_t)morningRepeatTimes);
        auto eveningTimePtr = TIMER_MANAGER.addTimer(todayProgramEvening,[=](auto timerPtr){ timeProgramTimerCallBack(timerPtr);}, std::chrono::hours(24), (int32_t)eveningRepeatTimes);
    }

    /**
     * @brief 时间切换方案定时器回调函数
     */
    void timeProgramTimerCallBack(const TimerPtr& timerPtr)
    {
        worker::post(worker::WorkerType::Channel, [&]() {onTimeProgramsTimerExpired();});

        auto now = boost::posix_time::second_clock::local_time();
        ai::LogInfo << "time program timer expired:" << boost::posix_time::to_simple_string(now);

        //! 最后一次重复, 开始设置下一个时间方案的定时器
        if (timerPtr && timerPtr->repeatTimes == 0)
        {
            setTimeProgramsTimers();
        }
    }

    /**
     * @brief 云台控制
     */
    void controlPtz(int videoId, int action, int step)
    {
        //! 检查视频资源是否存在
        auto videoSourceInfo = DATA_MANAGER.getVideoChannelSource(videoId);
        if (!videoSourceInfo.has_value())
        {
			//若不存在，更新一次视频资源
        	DATA_MANAGER.updateVideoSourcesInfo(false, videoId);
			videoSourceInfo = DATA_MANAGER.getVideoChannelSource(videoId);
        	if (!videoSourceInfo.has_value())
            return;
        }
        ai::LogInfo << "videoId:" << videoId<<" action:" << action<<" step:" << step;

        auto videoSourceInfoPtr = videoSourceInfo.value();

        auto input = createStreamInput(videoSourceInfoPtr);
        input->controlPtz(action, step);
    }

    /**
     * @brief 操作云台预置位
     */
    void controlPreset(int videoId, int actPresetId, int action)
    {
        auto videoSourceInfo = DATA_MANAGER.getVideoChannelSource(videoId);
        if (!videoSourceInfo.has_value())
        {
            DATA_MANAGER.updateVideoSourcesInfo(false, videoId);
            videoSourceInfo = DATA_MANAGER.getVideoChannelSource(videoId);
            if (!videoSourceInfo.has_value())
                return;
        }
       
        auto videoSourceInfoPtr = videoSourceInfo.value();
        auto input = createStreamInput(videoSourceInfoPtr);
        if (action == 1)
        {
            ai::LogInfo << "videoId:" << videoId << " save preset:" << actPresetId;
            input->saveCameraPreset(actPresetId);
        }
        else
        {
            ai::LogInfo << "videoId:" << videoId << " call preset:" << actPresetId;
            input->callCameraPreset(actPresetId);
        }
    }
}