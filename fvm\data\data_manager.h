/**
 * Project FVM
 */
#pragma once

#include <string>
#include <boost/serialization/singleton.hpp>
#include <boost/fiber/recursive_mutex.hpp>
#include <boost/signals2.hpp>
#include "video_source_info.h"
#include "config_param_info.h"
#include "nv_aimonitor31_view.h"
#include "nv_aimonitor31_view-odb.hxx"
#include "video_source.h"
#include "video_source-odb.hxx"
#include "process_config.h"
#include "process_config-odb.hxx"
#include "video_server.h"
#include "video_server-odb.hxx"
#include "event_type.h"
#include "event_type-odb.hxx"
#include "check_area_type.h"
#include "check_area_type-odb.hxx"
#include "param_prog.h"
#include "param_prog-odb.hxx"

/**
 * @brief: 数据管理
 *          包含数据库配置更新读取及信号驱动等
 */
namespace fvm {
    namespace data {
        using std::optional;
        using std::nullopt;

        typedef std::map<int, VideoSourceInfoPtr> VideoSrcInfoMap;
        typedef std::map<int, VideoSrcInfoMap> ProcessVideoInfoMap;

        typedef boost::signals2::signal<void(std::optional<int>)> OnDataUpdated;
        typedef boost::signals2::signal<void(int)> OnChannelConfigUpdated;
        typedef std::shared_ptr<db::VideoServer> VideoServerPtr;

        #define VIDEO_INFO_LOCKGUARD std::lock_guard<boost::fibers::recursive_mutex> m(mutexVideoInfo)

        class DataManager :public boost::noncopyable {

        public:
            ~DataManager();
            /**
             * @brief 初始化数据库连接，初始化配置数据
             */
            bool init();

            /**
             * @brief 初始化完成，发送下发配置信号
             */
            void start();

            /**
             * @brief web下发配置，更新数据，发送下发配置信号
             */
            bool update(const std::optional<int> &processId);

            /**
             * @brief 更新所有全局配置参数
             */
            bool updateParams();

            /**
             * @brief     获取本地IP
             */
            std::string getLocalIP() { return localIP; };

            /**
             * @brief     获取 fvm消息端口
             */
            uint16_t getFvmPort() { return platformPort; };

            /**
            * @brief       将进程对应的通道的数据库wn_video_source表的is_change字段改写为0，表示已经读取了变化的信息
            * @param[in]   processId: 进程id
            */
            void writeProcessVideoChange(const std::optional<int> &processId = std::nullopt);

            /**
             * @brief       将数据库wn_video_source表的is_change字段改写为0，表示已经读取了变化的信息
             * @param[in]   videoId: 视频id
             */
            void writeVideoChange(int videoId);

            /**
             * @brief     获取 iva消息端口
             */
            uint16_t getIvaPort(int processId);

            /**
             * @brief     获取平台IP
             */
            std::string getPlatformIP() { return platformIP; };

            /**
             * @brief     是否平台
             */
            bool isPlatform();


            /**
             * @brief     查询球机视频资源表数据
             * @param[in]   groupId: 球机前端id
             */
            std::optional<VideoSourceInfoPtr> queryDomeVideoSource(int groupId);

            /**
             * @brief     查询球机视频资源表数据
             * @param[in]   groupId: 球机前端id
             */
            std::optional<VideoSourceInfoPtr> queryDomeVideoSource(std::string groupUUID);


            /**
             * @brief     查询视频资源
             * @param[in]   id: 视频接入前端id
             * @param[string]   address  uuid
             */
            std::optional<VideoSourceInfoPtr> queryVideoSource(int id, std::string& address);

            /**
             * @brief     获取MSMIP
             */
            std::string getMsmIP() { return msmIP; };

            /**
           * @brief     获取 msm消息端口
           */
            uint16_t getMsmPort() { return msmPort; };

            /**
             * @brief     获取GUMIP
             */
            std::string getGumIP() { return gumIP; };

            /**
           * @brief     获取 GUM消息端口
           */
            uint16_t getGumPort() { return gumPort; };

            /**
             * @brief     已绑定通道查询
             */
            ProcessVideoInfoMap getAssignedChannels() {VIDEO_INFO_LOCKGUARD; return assignedChannels;};

            /**
             * @brief      获取所有视频资源
             */
            VideoSrcInfoMap getVideoSources() {VIDEO_INFO_LOCKGUARD; return videoSources;};

            /**
             * @brief      查询当前通道的可检测性
             */
            ChannelDetectable queryChannelDetectable(int channelId);

            /**
            * @brief       查询视频管理的可用性
            */
            bool queryAccessFrontDetectable(int id);

            /**
             * @brief      获取所有的sdk设备的接入前端表id wn_access_front_end上的id
             */
            void queryGetPlatformId(std::vector<int>& ids);
            /**
             * @brief      获取进程对应的通道视频资源
             * @param[in]  processId:进程id, nullopt 表示所有进程
             */
            VideoSrcInfoMap getProcessChannelsSource(const std::optional<int> &processId = std::nullopt);

            /**
             * @brief      获取channelId对应的视频资源
             */
            std::optional<VideoSourceInfoPtr> getChannelSource(int channelId);

            /**
             * @brief      获取videoId对应的通道资源
             */
            std::optional<VideoSourceInfoPtr> getVideoChannelSource(int videoId);

            /**
             * @brief     获取程序配置参数
             * @param[in] key:配置参数key ,defaultVal:数据库中没有该参数时，使用该默认值
             */
            std::string getParamProgData(const std::string& key, const std::string& defaultVal);

            /**
             * @brief     获取程序配置参数
             * @param[in] key:配置参数key ,defaultVal:数据库中没有该参数时，使用该默认值
             */
            int getParamProgData(const std::string& key, const int defaultVal);

            /**
             * @brief     获取事件类型配置
             */
            EventTypeVec getEventTypes();

            /**
             * @brief     获取检测区类型
             */
            CheckAreaTypeVec getCheckAreaType();

            /**
             * @brief     获取时间方案配置
             */
            ProgramTimeVec getProgram();

            /**
             * @brief     获取灵敏度配置参数
             */
            AlgoParamsVec getAlgoParams();

            /**
             * @brief 获取远端信息
             */
            VideoServerPtr getVideoServer(int remoteId);

            /**
             * @brief     获取iva进程配置信息
             * @param[in] processId: 获取指定进程id的进程配置，nullopt 则返回所有进程配置
             * @return    ProcessConfigsVec 返回的进程配置
             */
            ProcessConfigsVec getProcessConfigs(const std::optional<int>& processId = nullopt);

            /**
             * @brief     获取通道对应的进程id,找不到进程id,返回默认进程1
             */
            int getChannelsProcessId(int channelId);

            /**
             * @brief     获取当前视频资源的预置位
             */
            PresetPtr getCurrPreset(const VideoSourceInfoPtr& videoInfoPtr);

            /**
             * @brief     获取当前视频资源的预置位id
             */
            int getCurrPresetId(const VideoSourceInfoPtr& videoSrc);

            bool splitPosition(const std::string& position, double& x, double& y, double& z);
            /**
             * @brief     获取视频资源当前预置位的基准坐标值
             */
            bool getCurrPresetPosition(const VideoSourceInfoPtr& videoSrc, double& x, double& y, double& z);

            /**
             * @brief     获取通道对应预置位的坐标
             */
            bool getPresetPosition(int channelId, int actPresetId, double& x, double& y, double& z);

            /**
             * @brief     往videoId对应的表wn_preset中presetId记录写入预置位基准坐标
             */
            bool writePresetPosition(int videoId, int presetId, double x, double y, double z);

            /**
             * @brief     判断当前视频对应预置位是否有检测区和感兴趣区
             */
            bool hasCheckAreaRoi(const VideoSourceInfoPtr& videoSrc, int presetId);

            /**
             * @brief     更新视频接入前端配置
             */
            void updateRemotes(int remoteId = -1);

            /**
             * @brief       根据视频接入前端remoteId或者接入类型streamType设置视频接入前端服务器状态 （数据库wn_access_front_end表的status字段）
             * @param[in]   online: 视频接入前端状态
             * @param[in]   remoteId: 视频接入前端id,可选参数 nullopt，则根据streamType查询设置
             * @param[in]   streamType: 视频接入前端类型,可选参数 nullopt，则根据remoteId查询设置
             */
            void setRemoteStatus(bool online, const std::optional<int>& remoteId, const std::optional<int>& streamType);

            /**
             * @brief     更新资源列表信息，以及关联的配置信息
             * @param[in] assigned: true查询分配了通道的视频资源 false查询所有视频资源
             * @param[in] videoId: 更新videoId对应的视频资源。可选参数，nullopt时，则根据其他条件更新视频资源
             * @param[in] remoteId 更新remoteId对应的视频资源。可选参数，nullopt时，则根据其他条件更新视频资源
             */
            bool updateVideoSourcesInfo(bool assigned, std::optional<int> videoId = std::nullopt, std::optional<int> remoteId = nullopt);

            std::map<int, std::set<int>> queryMonitorChannelIds();

            /**
             * @brief     更新视频接入平台状态
             * @param[in]   id: 视频接入前端id
             * @param[in]   status: 视频接入平台状态 1-在线 0-不在线
             * @return      返回成功 失败
             */
            bool updateVideoServerInfo(int id, int status);

            /**
             * @brief     插入新的视频通道 如果已存在自动忽略
             * @param[in]   id: 视频接入前端id
             * @param[in]   szName: 视频名称
             * @param[in]   szAddr: 视频地址
             */
            void insertVideoSource(int id, std::string szName, std::string  szAddr);

            /**
             * @brief   (二代平台)  插入新的视频通道 如果已存在自动忽略
             * @param[in]   id: 视频接入前端id
             * @param[in]   szName: 视频名称
             * @param[in]   szAddr: 视频地址
             * @param[in]   szCode: 编码信息
             */
            void insertVideoSource(int id, std::string szName, std::string szAddr, std::string szCode);

            /**
             * @brief  更新 视频通道 名称
             * @param[in]   id: 视频接入前端id
             * @param[in]   szAddr: 视频地址
             * @param[in]   szName: 视频名称
             */
            void updateVideoSourceName(int id, std::string szAddr, std::string szName);

        public:
            //! SIGNAL
            OnDataUpdated onRestart;                        //!< 下发配置 （初始化）
            OnChannelConfigUpdated onChannelConfigUpdated;  //!< 通道配置变化

        private:

            //! 全局配置 相关更新接口
            void updateIvaProcess();                        //!< 更新iva进程配置
            void updateEventTypes();                        //!< 更新事件类型配置
            void updateCheckAreaTypes();                    //!< 更新检测区域类型配置
            void updateAlgoParams();                        //!< 更新灵敏度参数配置
            bool updateParamProg();                         //!< 更新程序运行参数配置
            bool updateLocalIp();                           //!< 获取系统本地IP接口
            void updateProgram();                           //!< 更新时间切换方案配置

            /**
             * @brief     更新预置位信息，以及预置位下的关联配置
             * @param[in] videoId: 视频资源id
             */
            bool updatePresets(int videoId);

            /**
             * @brief 更新预置位运行信息
             */
            bool updatePrograms(int videoId, int presetId);

            /**
             * @brief 更新预置位下的roi信息，以及偏移检测区，通过view一次关联读取
             */
            bool updatePresetAreas(int videoId, int presetId);
            /**
             * @brief 更新预置位下的偏移检测区
             */
            bool updateOffsets(int videoId, int presetId);

            /**
             * @brief 更新预置位下的车道线配置
             */
            bool updateLaneLine(int videoId, int presetId);

            /**
             * @brief 更新预置位下的roi信息
             */
            bool updateRois(int videoId, int presetId);

            /**
             * @brief 更新特征检测区信息
             */
            bool updateFeatureArea(int videoId, int presetId, int roiId);

            /**
             * @brief 更新roi下的车道信息
             */
            bool updateLanes(int videoId, int presetId, int roiId);

            /**
             * @brief 更新roi下的子区域信息
             */
            bool updateCheckAreas(int videoId, int presetId, int roiId);

            /**
             * @brief       根据前端ID获取视频接入类型
             * @param[in]   frontEndID: 前端id
             * @param[out]  type: 前端接入类型
             */
            bool queryVideoStreamType(StreamInputType& type, int frontEndID);

            /**
             * @brief 更新当前机器的检测仪信息
             */
            bool updateMonitor();

            /**
             * @brief      查询当前检测仪的detectPoint对应的streamId,即通道号
             * @param[in]  detectPointId: wn_video_source中对应的detect_point_id
             * @param[out] channelId: detectPoint对应的通道号
             */
            bool queryDetectPointStreamId(int detectPointId, int& channelId);

            /**
             * @brief      从video name字段中获取桩号
             */
            std::string getLocation(const std::string& name);

        private:
            uint16_t        monitorId;                         //!< 当前FVM的monitorId
            std::string     localIP;                           //!< 本地IP
            std::string     platformIP;                        //!< 平台fvmIP
            uint16_t        platformPort;                      //!< 平台fvm端口
            uint16_t        fvmPort;                           //!< 当前进程的fvm端口
            uint16_t        ivaPort;                           //!< IVA消息接收端口
            std::string     gumIP;                             //!< GUM消息接收地址
            uint16_t        gumPort;                           //!< GUM消息接收端口
            std::string     msmIP;                             //!< MSM消息接收地址
            uint16_t        msmPort;                           //!< MSM消息接收端口

            boost::fibers::recursive_mutex mutexVideoInfo;                         //!< 视频资源锁
            VideoSrcInfoMap videoSources;                      //!< 所有视频资源信息 包括预置位、感兴趣区、个性参数 key：videoID
            ProcessVideoInfoMap assignedChannels;              //!< 当前检测仪分配了通道视频资源信息 包括预置位、感兴趣区、个性参数(processId,(channelId, videoSrcInfo))

            boost::fibers::recursive_mutex mutexConfigInfo;                        //!< 全局配置参数锁
            ConfigParamInfo configParamInfo;                   //!< 全局参数

            // <ip, uuid>
            std::map<std::string, std::string> mapGroupUUIDs;//!< 枪球打组
        };
    }
    typedef boost::serialization::singleton<data::DataManager> SingletonDataManager;
    #define DATA_MANAGER SingletonDataManager::get_mutable_instance()
}
