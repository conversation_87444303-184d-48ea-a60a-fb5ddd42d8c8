/**
 * @addtogroup odbDatabaseGroup
 * @brief 检测区域类型
 * @{
 */
#ifndef _CECKAREATYPE_H
#define _CECKAREATYPE_H


#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>
namespace db {

/**
 * @brief  检测区域类型: 对应数据库aimonitorV3的表wn_check_area_type
 */
#pragma db object table("wn_check_area_type")
class CheckAreaType {
public:

    CheckAreaType(const std::string &name, const std::string &remark)
            : name(name), remark(remark) {
    }

    unsigned long getId() const {
        return id;
    }

    const std::string &getName() const {
        return name;
    }

    const std::string &getRemark() const {
        return remark;
    }

private:

    friend class odb::access;

    CheckAreaType() {}

private:
#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("name")  type("VARCHAR(255)")
    std::string name;                   //!< 检测区域类型name

#pragma db column("remark")  type("VARCHAR(255)")
    std::string remark;                 //!< 检测区域类型remark
};
}
#endif //_CECKAREATYPE_H

/**
 * @}
 */