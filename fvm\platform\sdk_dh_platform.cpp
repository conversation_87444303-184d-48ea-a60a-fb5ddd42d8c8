/**
 * Project FVM
 */
#ifdef SDK_DH_SUPPORTED
#include <iostream>
#include "sdk_dh_platform.h"
#include "dhnetsdk.h"
#include "dhconfigsdk.h"
#include "util/worker_pool.h"
#include "ailog.h"
#include <boost/property_tree/ptree.hpp>
#include <boost/property_tree/json_parser.hpp>


namespace fvm::platform
{
    using namespace data;
    using namespace timer;

    //! 登录请求时间间隔
    constexpr auto LOGIN_INTERVAL             = std::chrono::seconds(10);
    //! 摄像头资源更新时间间隔
    constexpr auto UPDATE_RESOURCE_INTERVAL   = std::chrono::seconds(30);

    #define WAIT_TIME 10


    /**
     * @brief 平台登录错误查询表
     */
    std::map<int,std::string> loginErrorTable =
    {
      {0,"Login Success"    },   {1,"Invalid password"}, {2,"Invalid account"},
      {3,"Login Timeout"},       {4,"Repeat Login"},     {5,"User Account is Locked"},
      {6,"User In Blacklist"},   {7,"Device Busy"},      {8,"Sub Connect Failed"},
      {9,"Host Connect Failed"}, {10,"Max Connect"},     {11,"Max Connect"},
      {12,"UKey Info Error"},    {13,"No Authorized"},   {18,"Device Account isn't Initialized"}
    };


    /**
     * @brief     SDK实时流数据回调接口
     * @param[in] dwDataType:数据类型 0: 原始数据 1：带有帧信息的数据 2：yuv数据 3：pcm音频数据
     */
    void CALLBACK realDataCallBack(LLONG lRealHandle, DWORD dwDataType, BYTE *pBuffer, DWORD dwBufSize, LLONG param, LDWORD dwUser)
    {
        if (dwDataType != 0 || dwBufSize == 0 || dwUser == 0)
            return;

        auto *dhChannel = (DHChannel *)( dwUser );
        if( dhChannel && dhChannel->receiveRealData)
        {
            dhChannel->receiveRealData(pBuffer, dwBufSize);
        }
    }

    bool DHChannel::play(const RecvRealData& callback)
    {
        if (loginId == 0)
        {
            ai::LogWarn << "device is not logged in";
            return false;
        }
        if (playHandle != 0)  //!< 该通道已经在播放,todo
            return true;

        //! 选择要播放的码流类型
        DH_RealPlayType  realPlayType = DH_RType_Realplay;

        int typeValue = DATA_MANAGER.getParamProgData( DH_STREAM_TYPE, 1);
        if (typeValue == 1)      //!< 子码流1
            realPlayType = DH_RType_Realplay_1;
        else if (typeValue == 2) //!< 子码流2
            realPlayType = DH_RType_Realplay_2;
        else if (typeValue == 3) //!< 子码流3
            realPlayType = DH_RType_Realplay_3;

        playHandle = CLIENT_RealPlayEx(loginId, channelId - 1, nullptr, realPlayType );
        if ( playHandle == 0 )
        {
            auto err = CLIENT_GetLastError()&(0x7fffffff);
            if ( realPlayType != DH_RType_Realplay && err == NET_OPEN_CHANNEL_ERROR )  //可能不支持第三码流 todo
            {
                realPlayType = DH_RType_Realplay;
                playHandle = CLIENT_RealPlayEx(loginId, channelId - 1, nullptr, realPlayType );
            }
            if ( playHandle == 0 )
                return false;
        }

        receiveRealData = callback;
        CLIENT_SetRealDataCallBackEx2( playHandle, realDataCallBack, (LDWORD)this, 0x0f );

        ai::LogInfo << "DH channel:" << name << " play ok";
        return true;
    }

    void DHChannel::stop()
    {
        if( playHandle != 0 ) // 如果取流不成功,则不必停止流.
        {
            CLIENT_StopRealPlay( playHandle );
            playHandle = 0;
            ai::LogInfo << "DH channel:" << name << " stop play";
        }
    }

    //切换设置到预置点
    bool DHChannel::ptzControl(DWORD cmd, DWORD lParam1, DWORD lParam2, DWORD lParam3, BOOL dwStop, void* param4)
    {
        if (loginId == 0)
        {
            ai::LogWarn << "device is not logged in";
            return false;
        }

        BOOL ret = CLIENT_DHPTZControlEx2(loginId, channelId - 1 , cmd, lParam1, lParam2, lParam3, dwStop, param4);
        if (!ret)
        {
            ai::LogError << "channelId:"<< channelId <<" CLIENT_DHPTZControlEx2 failed : " << CLIENT_GetLastError();
        }
        return ret;
    }

    //获取当前ptz坐标
    bool DHChannel::getPtzPosition(double& x, double& y, double& z)
    {
        if (loginId == 0)
        {
            ai::LogWarn << "device is not logged in";
            return false;
        }

        int nRet = 0;
        DH_PTZ_LOCATION_INFO stuInfo = {0};
        stuInfo.nChannelID = channelId - 1;
        if (CLIENT_QueryDevState(loginId, DH_DEVSTATE_PTZ_LOCATION, (char*)&stuInfo, sizeof(stuInfo), &nRet))
        {
            x = (double)stuInfo.nPTZPan;
            y = (double)stuInfo.nPTZTilt;
            z = (double)stuInfo.nPTZZoom;
            return true;
        }
        else
        {
            auto err = CLIENT_GetLastError()&(0x7fffffff);
            ai::LogError << "channelId:"<< channelId <<" CLIENT_ParseData failed : " << err ;
        }
        return true;
    }

    /**
     * @brief 解析获取到的预置位信息
     * @attention presetInfo.pstPtzPreset 由调用方释放
     */
    bool parsePTZPresetData(const char* szJsonBuf, PTZ_PRESET_INFO& presetInfo)
    {
        if (szJsonBuf == nullptr)
        {
            ai::LogError << "parsePTZPresetData: szJsonBuf is nullptr";
            return false;
        }

        try {
            std::stringstream ss(szJsonBuf);
            boost::property_tree::ptree pt;
            // 解析 JSON 数据到 ptree
            boost::property_tree::read_json(ss, pt);

            // 获取 table 数组
            auto table = pt.get_child("params.table").front().second;

            // 初始化 PTZ_PRESET_INFO 结构体
            presetInfo.dwMaxPtzPresetNum = table.size();
            presetInfo.dwRetPtzPresetNum = 0;
            presetInfo.pstPtzPreset = new PTZ_PRESET[presetInfo.dwMaxPtzPresetNum];

            size_t index = 0;
            // 遍历 table 数组
            for (const auto& presetNode : table)
            {
                auto preset = presetNode.second;

                // 赋值到 PTZ_PRESET 结构体
                PTZ_PRESET &presetStruct = presetInfo.pstPtzPreset[index];
                presetStruct.bEnable = preset.get<bool>("Enable");
                auto name = preset.get<std::string>("Name");
                std::strncpy(presetStruct.szName, name.c_str(), MAX_PTZ_PRESET_NAME_LEN - 1);
                presetStruct.szName[MAX_PTZ_PRESET_NAME_LEN - 1] = '\0';

                auto position = preset.get_child("Position");
                size_t posIndex = 0;
                for (const auto& posNode : position)
                {
                    switch (posIndex)
                    {
                        case 0:
                            presetStruct.stPosition.nPositionX = static_cast<int>(posNode.second.get_value<double>());
                            break;
                        case 1:
                            presetStruct.stPosition.nPositionY = static_cast<int>(posNode.second.get_value<double>());
                            break;
                        case 2:
                            presetStruct.stPosition.nZoom = static_cast<int>(posNode.second.get_value<double>());
                            break;
                        default:
                            break;
                    }
                    posIndex++;
                }

                if (presetStruct.bEnable)
                {
                    presetInfo.dwRetPtzPresetNum++;
                }
                index++;
            }

#if 0
            std::cout << "Preset Info:" << std::endl;
            std::cout << "Max Preset Num: " << presetInfo.dwMaxPtzPresetNum << std::endl;
            std::cout << "Ret Preset Num: " << presetInfo.dwRetPtzPresetNum << std::endl;
            for (size_t i = 0; i < presetInfo.dwMaxPtzPresetNum; ++i)
            {
                PTZ_PRESET &preset = presetInfo.pstPtzPreset[i];
                std::cout << "Preset " << i + 1 << ":" << std::endl;
                std::cout << "  Enable: " << (preset.bEnable ? "true" : "false") << std::endl;
                std::cout << "  Name: " << preset.szName << std::endl;
                std::cout << "  Position: (" << preset.stPosition.nPositionX << ", "<< preset.stPosition.nPositionY << ", "<< preset.stPosition.nZoom << ")" << std::endl;
            }
#endif
        }
        catch (const std::exception& e)
        {
            ai::LogError << "ParseData failed: " << e.what();
            return false;
        }
        return true;
    }

    bool DHChannel::getPresetPosition(int presetId, double& x, double& y, double& z)
    {
        if (loginId == 0)
        {
            ai::LogWarn << "device is not logged in";
            return false;
        }

        char szJsonBuf[1024 * 80] = {0};
        int error = 0;
        if (CLIENT_GetNewDevConfig(loginId, CFG_CMD_PTZ_PRESET, -1, szJsonBuf, sizeof(szJsonBuf), &error))
        {
            PTZ_PRESET_INFO stuInfo = {0};
            if (parsePTZPresetData(szJsonBuf, stuInfo))
            {
                if (presetId >= 0 && presetId < stuInfo.dwMaxPtzPresetNum)
                {
                    x = static_cast<double>(stuInfo.pstPtzPreset[presetId-1].stPosition.nPositionX);
                    y = static_cast<double>(stuInfo.pstPtzPreset[presetId-1].stPosition.nPositionY);
                    z = static_cast<double>(stuInfo.pstPtzPreset[presetId-1].stPosition.nZoom);
                    delete[] stuInfo.pstPtzPreset;
                    return true;
                }
                else
                {
                    ai::LogWarn << "Invalid preset ID" << presetId << " in channel" << channelId << ", ptzPresetNum: " << stuInfo.dwMaxPtzPresetNum;
                    delete[] stuInfo.pstPtzPreset;
                    return false;
                }
            }
            else
            {
                auto err = CLIENT_GetLastError()&(0x7fffffff);
                ai::LogWarn << "Failed to parse data from json, error code: " << err;
            }
        }
        else
        {
            auto err = CLIENT_GetLastError()&(0x7fffffff);
            ai::LogWarn << "Failed to get device config, error code: " << err;
        }
        return false;
    }

    bool DHChannel::savePreset(int iActPreset)
    {
        return ptzControl(DH_PTZ_POINT_SET_CONTROL, 0, iActPreset, 0);
    }

    /**
     * @brief  初始化SDK，只调用一次
     */
    void SDKDHPlatform::initSDK()
    {
        auto disConnectFunc = [](LLONG lLoginID, char *pchDVRIP, LONG nDVRPort, LDWORD dwUser)
        {
            DATA_MANAGER.setRemoteStatus(false, lLoginID, nullopt);
        };

        auto haveReConnectFunc = [](LLONG lLoginID, char *pchDVRIP, LONG nDVRPort, LDWORD dwUser)
        {
            DATA_MANAGER.setRemoteStatus(true, lLoginID, nullopt);
        };

        //! 初始化SDK
        CLIENT_Init(disConnectFunc, 0);

        //! 设置断开自动重连
        CLIENT_SetAutoReconnect(haveReConnectFunc, 0);

    }

    /**
     * @brief 清理 SDK
     */
    void SDKDHPlatform::cleanupSDK()
    {
        CLIENT_Cleanup();
    }

    SDKDHPlatform::~SDKDHPlatform()
    {
        dispose();
    }

    /**
     * @brief 初始化, 登录、注册登录定时器、资源扫描定时器
     */
    void SDKDHPlatform::init(data::VideoServerPtr svrPtr)
    {
        if (!svrPtr)
            return;

        FrontPlatform::init(svrPtr);
    }

    /**
     * @brief  销毁，取消定时器，停止播放，退出登录
     */
    void SDKDHPlatform::dispose()
    {
        exit = true;                                 //1.阻止startPlay
        if (updateResTimer)                          //2.停止扫描资源定时器
        {
            TIMER_MANAGER.removeTimer(updateResTimer);
            updateResTimer = nullptr;
        }
        if (loginTimer)                              //3.停止登录定时器
        {
            TIMER_MANAGER.removeTimer(loginTimer);
            loginTimer = nullptr;
        }

        {
            //这里必须保证都停止播放才能退出登录
            std::lock_guard lock(channelsLock);
            for (auto& channel : channels)           //4.停止所有通道
            {
                channel.stop();
            }
            channels.clear();
        }

        if (loginId > 0)                             //5.退出登录
        {
            auto ret = CLIENT_Logout(loginId);
            ai::LogInfo << serverPtr->getIp() << " LOGOUT " << " UserID: " << loginId;
            loginId = 0;
            online = false;
        }
    }

    /**
     * @brief               对应的大华通道，并播放
     * @param[in] addr：    视频address,根据address播放对应大华通道（eg: **************:3 对应大华3通道）
     * @param[in] callback: 实时流收取回调
     */
    bool SDKDHPlatform::startPlay(const std::string& addr, const RecvRealData& callback)
    {
        bool ret = false;
        if (exit)
            return ret;

        //解析通道号
        int channelId(0);
        if (!getChanelId(addr, channelId))
            return ret;

        //等待登录
        int timeout = 0;
        if(!online)
        {
            login();
        }
        while (!online)
        {
            //超过10秒没登录上 此次播放失败
            if ((timeout++ > WAIT_TIME * 2) || exit)
                return ret;
            boost::this_fiber::sleep_for(std::chrono::milliseconds(500));
        }

        {
            std::lock_guard lock(channelsLock);
            auto channelItr = find_if(channels.begin(), channels.end(), [&](DHChannel& channel){
                return channel.channelId == channelId;
            });
            if (channelItr != channels.end())
                ret = channelItr->play(callback);
        }
        
        if (!ret) {
            // 播放失败
            {
                std::lock_guard lock(channelsLock);
                for (auto& channel : channels) {
                    channel.stop();
                }
                channels.clear();
            }

            if (loginId > 0) {
                auto ret = CLIENT_Logout(loginId);
                loginId = 0;
                online = false;
                bNeedGetResource = true;
            }
        }

        return ret;
    }

    /**
     * @brief 停止对应的大华通道播放
     */
    void SDKDHPlatform::stopPlay(const std::string& addr)
    {
        //解析通道号
        int channelId(0);
        if (!getChanelId(addr, channelId))
            return;

        std::lock_guard lock(channelsLock);
        auto channelItr = find_if(channels.begin(), channels.end(), [&](DHChannel& channel){
            return channel.channelId == channelId;
        });

        if (channelItr != channels.end())
        {
            channelItr->stop();     //停止播放
        }
    }

    /**
     * @brief  登录认证
     */
    bool SDKDHPlatform::login()
    {
        if(loginIgnoreCount > 0)
        {
            loginIgnoreCount--;
            return false;
        }
        NET_DEVICEINFO_Ex deviceInfo;
        memset(&deviceInfo, 0, sizeof(NET_DEVICEINFO_Ex));

        //每次都会获取新数据
        auto ip       = serverPtr->getIp().c_str();
        WORD port     = serverPtr->getPort();
        auto userName = serverPtr->getUserName().c_str();
        auto password = serverPtr->getPassword().c_str();

        int errcode = 0;
        {
            std::lock_guard<std::mutex> loginLock(loginMtx);
//            NET_IN_LOGIN_WITH_HIGHLEVEL_SECURITY stInParam;
//            memset(&stInParam, 0, sizeof(stInParam));
//            stInParam.dwSize = sizeof(stInParam);
//            strncpy(stInParam.szIP, ip, sizeof(stInParam.szIP) - 1);
//            strncpy(stInParam.szPassword, password, sizeof(stInParam.szPassword) - 1);
//            strncpy(stInParam.szUserName, userName, sizeof(stInParam.szUserName) - 1);
//            stInParam.nPort = port;
//            stInParam.emSpecCap = EM_LOGIN_SPEC_CAP_TCP;
//
//            NET_OUT_LOGIN_WITH_HIGHLEVEL_SECURITY stOutParam;
//            memset(&stOutParam, 0, sizeof(stOutParam));
//            stOutParam.dwSize = sizeof(stOutParam);
//
//            long devLoginID = CLIENT_LoginWithHighLevelSecurity(&stInParam, &stOutParam);
            long devLoginID = CLIENT_LoginEx2(ip, port, userName, password, EM_LOGIN_SPEC_CAP_TCP, nullptr, &deviceInfo, &errcode);
            if (devLoginID == 0)
            {
                lastErrorString = "CLIENT_LoginEx2 " + (loginErrorTable.find(errcode) != loginErrorTable.end()) ? loginErrorTable[errcode] : "Unknown Error";
                if (errcode != lastError)
                {
                    std::string errorInfo = (loginErrorTable.find(errcode) != loginErrorTable.end()) ? loginErrorTable[errcode] : "Unknown Error";
                    ai::LogError << "DH platform:" << ip << " userName:" << userName << " password:" << password << " login error:" << errorInfo;
                }
                lastError = errcode;

                // 登录失败 清除之前数据，保证设备故障恢复后能正常取流
                {
                    // 这里必须保证都停止播放才能退出登录
                    std::lock_guard lock(channelsLock);
                    // 停止所有通道
                    for (auto& channel : channels) {
                        channel.stop();
                    }
                    channels.clear();
                }

                if (loginId > 0) {
                    ai::LogInfo << "DH platform:" <<  ip << " LOGINOUT  UserID:" << loginId;
                    auto ret = CLIENT_Logout(loginId);
                    loginId = 0;
                    online = false;
                    bNeedGetResource = true;
                }

                if(errcode == 1 or errcode == 2 or errcode == 4 or errcode == 5 or errcode == 6)
                {
                    //用户问题登录失败
                    loginIgnoreCount = AUTH_LOGIN_IGNORE_COUNT;
                }
                else if(errcode == 9)
                {
                    //网络问题登录失败
                    loginIgnoreCount = NET_LOGIN_IGNORE_COUNT;
                }
                else
                {
                    loginIgnoreCount = LOGIN_IGNORE_COUNT;
                }

                // 登录失败更新平台状态为离线
                DATA_MANAGER.updateVideoServerInfo(serverPtr->getId(), 0);
                
                return false;
            }

            if (loginId == 0) {
                loginId = devLoginID;
                bNeedGetResource = true;
            }
            else {
                // 退出当前登录ID
                CLIENT_Logout(devLoginID);
            }
        }

        lastError = 0;
        loginIgnoreCount = 0;
        lastErrorString = "";
        ai::LogInfo << "DH platform:" <<  ip << " LOGIN  UserID:" << loginId;

        //! 获取前端视频输入/输出通道数
        int retLen = 0;
        NET_DEV_CHN_COUNT_INFO devChannelCntInfo = {sizeof(NET_DEV_CHN_COUNT_INFO)};
        devChannelCntInfo.stuVideoIn.dwSize = sizeof(devChannelCntInfo.stuVideoIn);
        devChannelCntInfo.stuVideoOut.dwSize = sizeof(devChannelCntInfo.stuVideoOut);

        if(CLIENT_QueryDevState( loginId, DH_DEVSTATE_DEV_CHN_COUNT, (char*)&devChannelCntInfo, (int)devChannelCntInfo.dwSize, &retLen))
        {
            channelNum = devChannelCntInfo.stuVideoIn.nMaxTotal;
        }
        else
        {
            channelNum = deviceInfo.nChanNum;
        }
        online = true;
        //更新平台状态为在线 页面的状态会变化
        DATA_MANAGER.updateVideoServerInfo(serverPtr->getId(), 1);

        // 只在第一次登录时更新资源
        if ( bNeedGetResource )
            updateResource();

        return true;
    }

    /**
     * @brief  更新通道资源，并写入数据库
     */
    void SDKDHPlatform::updateResource()
    {
        if ( channelNum > 0 )
        {
            for ( int i = 0; i < channelNum; i++ )
            {

                if (channels.end() != std::find_if(channels.begin(),channels.end(),[=](DHChannel& channel){return (channel.channelId-1) == i;}))
                    continue;

                std::string channelAddress = serverPtr->getIp() + ":" + std::to_string( i+1 );
                auto& channelName = channelAddress;
                {
                    std::lock_guard lock(channelsLock);
                    channels.emplace_back(loginId, channelName, i+1);
                }
                //wn_access_front_end的id做一次存在查询 检查serverPtr->getId()记录存在
                if (DATA_MANAGER.queryAccessFrontDetectable(serverPtr->getId()))
                {
                    DATA_MANAGER.insertVideoSource(serverPtr->getId(), channelName, channelAddress);
                }
            }

            bNeedGetResource = false;
        }
    }

    /**
     * @brief  根据address找到对应大华通道（eg: **************:3 对应大华3通道）
     */
    bool SDKDHPlatform::getChanelId(const std::string& addr, int& channelId)
    {
        try
        {
            //分离addr得到通道号 例如 ***********:33 分离 iRet = 33
            channelId = std::stoi(addr.substr(addr.find_last_of(':') + 1));
            return true;
        }
        catch (...)
        {
            //传入异常的字符串
            ai::LogError << "addrUrl: " << addr << " is error url!";
            return false;
        }
    }

    std::string SDKDHPlatform::getLastError(void)
    {
        return lastErrorString;
    }

    bool SDKDHPlatform::ptzControl(const std::string& addr, int iPreset)
    {
        std::lock_guard lock(channelsLock);
        int iChanel(0);
        if (!getChanelId(addr, iChanel))
            return false;

        auto findk = find_if(channels.begin(), channels.end(), [&](DHChannel& channel){
            return channel.channelId == iChanel;
        });

        if (findk != channels.end())
        {
            return findk->ptzControl(DH_PTZ_POINT_MOVE_CONTROL, 0, iPreset, 0);
        }
        return false;
    }

    //获取当前ptz坐标
    bool SDKDHPlatform::getPtzPosition(const std::string& addr, double& x, double& y, double& z)
    {
        int channelId(0);
        if (!getChanelId(addr, channelId))
            return false;

        std::lock_guard lock(channelsLock);
        auto channelItr = find_if(channels.begin(), channels.end(), [&](DHChannel& channel){
            return channel.channelId == channelId;
        });

        if (channelItr != channels.end())
        {
            return channelItr->getPtzPosition(x, y, z);
        }
        return false;
    }

    // 云台保存预置位
    bool SDKDHPlatform::savePreset(const std::string& addr, int presetId)
    {
        int channelId(0);
        if (!getChanelId(addr, channelId))
            return false;

        std::lock_guard lock(channelsLock);
        auto channelItr = find_if(channels.begin(), channels.end(), [&](DHChannel& channel){
            return channel.channelId == channelId;
        });

        if (channelItr != channels.end())
        {
            return channelItr->savePreset(presetId);
        }
        return false;
    }

    static const std::map<int, DWORD> actionMapping = {
            {1, DH_PTZ_LEFT_CONTROL},
            {2, DH_PTZ_RIGHT_CONTROL},
            {3, DH_PTZ_UP_CONTROL},
            {4, DH_PTZ_DOWN_CONTROL},
            {5, DH_PTZ_ZOOM_DEC_CONTROL},
            {6, DH_PTZ_ZOOM_ADD_CONTROL},
            {7, DH_PTZ_FOCUS_ADD_CONTROL},
            {8, DH_PTZ_FOCUS_DEC_CONTROL},
            {9, DH_PTZ_APERTURE_DEC_CONTROL},
            {10, DH_PTZ_APERTURE_ADD_CONTROL}
    };

    // action: 云台动作  1：向左 2：向右 3：向上 4：向下 5：变倍短 6：变倍长 7：聚焦近 8：聚焦远 9：光圈小 10：光圈大 11：灯光关 12：灯光开
    bool SDKDHPlatform::controlPtz(const std::string& addr, int action, int step)
    {
        int channelId(0);
        if (!getChanelId(addr, channelId))
            return false;

        std::lock_guard lock(channelsLock);
        auto channelItr = find_if(channels.begin(), channels.end(), [&](DHChannel& channel){
            return channel.channelId == channelId;
        });

        if (channelItr != channels.end())
        {
            if (action == 0)
            {
                if (lastPTZcmd > 0)
                {
                    int cmd = lastPTZcmd;
                    lastPTZcmd = 0;
                    BOOL dwStop = true;
                    return channelItr->ptzControl(lastPTZcmd, 0, step, 0, dwStop);
                }
            }
            else
            {
                if (lastPTZcmd > 0)
                {
                    BOOL dwStop = true;
                    channelItr->ptzControl(lastPTZcmd, 0, step, 0, dwStop);
                    lastPTZcmd = 0;
                }
                if (actionMapping.find(action) != actionMapping.end())
                {
                    lastPTZcmd = actionMapping.at(action);
                    return channelItr->ptzControl(lastPTZcmd, 0, step, 0);
                }
            }
        }
        return false;
    }

    bool SDKDHPlatform::getPresetPosition(const std::string& addr, int presetId, double& x, double& y, double& z)
    {
        int channelId(0);
        if (!getChanelId(addr, channelId))
            return false;

        std::lock_guard lock(channelsLock);
        auto channelItr = find_if(channels.begin(), channels.end(), [&](DHChannel& channel){
            return channel.channelId == channelId;
        });

        if (channelItr != channels.end())
        {
            return channelItr->getPresetPosition(presetId, x, y, z);
        }
        return false;
    }
}
#endif

