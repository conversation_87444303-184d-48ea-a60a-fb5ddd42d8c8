#ifndef WIN32
#include <sys/time.h>
#include <unistd.h>
#endif

#include "ProtocolGb28181.hpp"
#include <pjlib.h>
#include <pjlib-util.h>

#include <boost/date_time/posix_time/posix_time.hpp>

#include "Gb28181Parser.hpp"
#include "Gb28181ParserRtsp.hpp"
#include "Gb28181ParserSdp.hpp"
#include "Gb28181Xml.hpp"
#include "Gb28181Sdp.hpp"
#include "StructExchange.hpp"
#include "Gb28181XmlTag.hpp"
#include "../../UsgManager/include/WtoeSipFuncItf.hpp"
//#include "../../RtspClientHandler/src/RtspClient.hpp"

namespace gb28181
{

    CProtocolGb28181::CProtocolGb28181()
            : m_wsf( 0 ), m_isOk( false ), m_sn(0)
    {
        m_format = "";
        m_srcAddr = "";
    }

    bool CProtocolGb28181::startup( usg::IWtoeSipFunc* wsf, const std::string &relativePath )
    {
        if( m_isOk )
            return true;

        if( wsf == 0 )
            return false;
        m_wsf = wsf;
        m_relativePath = relativePath;

        int rc = 0;
        rc = pj_init();
        if( rc != 0 )
            return false;

        rc = pjlib_util_init();
        if( rc != 0 )
            return false;

        if( !m_configFile.init( m_relativePath ) )
            return false;

// 	if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_RtspClientHandler_RtspClientHandler, m_rtspClientMgr ) )
// 	{
// 		return false;
// 	}

        m_isOk = true;
        return true;
    }
    bool CProtocolGb28181::shutdown()
    {
        // 反初始化所有历史流与下载链路
// 	{
// 		lock_type l( m_mutex );
// 		std::map<std::string, msg::CSpIRtspClient>::iterator it = m_rtstClientMap.begin();
// 		for ( ; it != m_rtstClientMap.end(); ++it )
// 		{
// 			if ( it->second )
// 			{
// 				it->second->fini();
// 			}
// 		}
// 	}

//	m_rtstClientMap.clear();
//	m_rtspClientMgr.reset();

        m_sn = 0;
        m_isOk = false;
        pj_shutdown();
        m_wsf = 0;
        return true;
    }

    bool CProtocolGb28181::getAccess(
            std::string &rAddr, uint16_t &rPort, std::string &rCode,
            std::string &lAddr, uint16_t &lPort, std::string &lCode )
    {
        if( !m_isOk )
            return false;
        SAccess local = m_configFile.getLocal();
        SAccess remote = m_configFile.getRemote();
        rAddr = remote.ip;
        rPort = remote.port;
        rCode = remote.addrCode;
        lAddr = local.ip;
        lPort = local.port;
        lCode = local.addrCode;
        return true;
    }

    bool CProtocolGb28181::getUserNameAndPasswd( std::string &userName, std::string &password )
    {
        if ( !m_isOk )
            return false;
        SAccess local = m_configFile.getLocal();
        userName = local.usrName;
        password = local.password;

        return true;
    }

// BOOL SetPrivilege(LPCTSTR lpszPrivilege, BOOL bEnablePrivilege)
// {
// 	TOKEN_PRIVILEGES tp;
// 	HANDLE hToken;
// 	LUID luid;
//
// 	if( !OpenProcessToken(GetCurrentProcess(),
// 		TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY,
// 		&hToken) )
// 	{
// 		return FALSE;
// 	}
//
// 	if( !LookupPrivilegeValue(NULL,             // lookup privilege on local system
// 		lpszPrivilege,    // privilege to lookup
// 		&luid) )          // receives LUID of privilege
// 	{
// 		return FALSE;
// 	}
//
// 	tp.PrivilegeCount = 1;
// 	tp.Privileges[0].Luid = luid;
//
// 	if( bEnablePrivilege )
// 		tp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;
// 	else
// 		tp.Privileges[0].Attributes = 0;      // Enable the privilege or disable all privileges.
//
// 	if( !AdjustTokenPrivileges(hToken,
// 		FALSE,
// 		&tp,
// 		sizeof(TOKEN_PRIVILEGES),
// 		(PTOKEN_PRIVILEGES) NULL,
// 		(PDWORD) NULL) )
// 	{
// 		return FALSE;
// 	}
//
// 	if( GetLastError() == ERROR_NOT_ALL_ASSIGNED )
// 	{
// 		//The token does not have the specified privilege.
// 		return FALSE;
// 	}
//
// 	return TRUE;
// }

    bool CProtocolGb28181::receivePkg( const std::string &sid, const std::string &resId, const std::string &inString, std::string &outString)
    {
        return receivePkg(sid,resId,inString,outString,false);
    }

/**
 * sid 是sip协议中的call-id
 * 这个receive用来处理answer。成功的。
 * 当下主要用于实时视频流和历史流的200OK的处理
 */
    bool CProtocolGb28181::receivePkg( const std::string &sid, const std::string &resId, const std::string &inString, std::string &outString ,const bool answer)
    {
        if( !m_isOk )
            return false;

        /* 目前存在三种情况
         * 1、XML格式，主要针对一般的情况
         * 2、历史流控制
         * 3、实时流、历史流和下载请求
         */

        //先判断是否是XML格式的字符串
        if( 0 == ::strnicmp( inString.c_str(), "<?xml", 5 ) )
        {
            gb28181::CGb28181Parser p( m_wsf, &m_configFile, sid, resId, inString, m_type );
            if( !p.isOk() )
                return false;
            if( !p.parserReceivePkg() )
                return false;
            outString = p.getResultStr();
        }
        else if ( 0 == ::strnicmp( inString.c_str(), "PLAY", 4 ) )
        {
            gb28181::SHistoryRtspInfo info;
            gb28181::CGb28181ParserRtsp p( inString );
            if( !p.getRtspInfo( info ) )
                return false;

//		lock_type l( m_mutex );
// 		std::map<std::string, msg::CSpIRtspClient>::iterator it = m_rtstClientMap.find( sid );
// 		if ( it != m_rtstClientMap.end() )
// 		{
// 			if ( it->second )
// 			{
//                 if (info.dRangeStart != 0)
//                 {
//                     it->second->sendModifyTimespan(info.dRangeStart,info.dRangeEnd);
//                     return true;
//                 }
//
//                 //info.dscale>1快进，info.dscale<1慢放，info.dscale=1正常播放
//                 if (info.dscale >= 1)
//                     it->second->sendModifySpeed( info.dscale, 1 );
//                 else if (info.dscale < 1 && info.dscale > 0.00001)
//                     it->second->sendModifySpeed( 1, 1/info.dscale);
//
//         //        //只有当正常播放时才需要调用sendReplay
//         //        if( info.dscale == 1 )
// 				    //it->second->sendReplay();
// 			}
// 			else
// 			{
// 				return false;
// 			}
// 		}
// 		else
// 		{
// 			return false;
// 		}
        }
        else if ( 0 == ::strnicmp( inString.c_str(), "PAUSE", 5 ) )
        {
//		lock_type l( m_mutex );
// 		std::map<std::string, msg::CSpIRtspClient>::iterator it = m_rtstClientMap.find( sid );
// 		if ( it != m_rtstClientMap.end() )
// 		{
// 			if ( it->second )
// 			{
// 				it->second->sendPause();
// 			}
// 			else
// 			{
// 				return false;
// 			}
// 		}
// 		else
// 		{
// 			return false;
// 		}
        }
        else if (0 == ::strnicmp( inString.c_str(), "TEARDOWN", 8 ))
        {
//        lock_type l( m_mutex );
//         std::map<std::string, msg::CSpIRtspClient>::iterator it = m_rtstClientMap.find( sid );
//         if ( it != m_rtstClientMap.end() )
//         {
//             if ( it->second )
//             {
//                 it->second->fini();
//             }
//             else
//             {
//                 return false;
//             }
//         }
//         else
//         {
//             return false;
//         }
        }
        else if ( 0 == ::strnicmp( inString.c_str(), "Date:", 5 ) )
        {
            std::string sub = inString.substr( 6 );

            int year, month, day, hour, minute, second, ret1;
            sscanf( sub.c_str(), "%d-%d-%dT%d:%d:%d.%d", &year, &month, &day, &hour, &minute, &second, &ret1 );

#ifdef WIN32
            SYSTEMTIME time1;
		memset( &time1, 0, sizeof( SYSTEMTIME ) );

		time1.wYear = year;
		time1.wMonth = month;
		time1.wDay = day;
		time1.wHour = hour;
		time1.wMinute = minute;
		time1.wSecond = second;

		if( !SetLocalTime( &time1 ) )
		{
			long nerror = GetLastError();
			std::cerr << ".......modify systime error : " << nerror << "..........." << std::endl;
			return true;
		}
#else
            tm time1;
            memset( &time1, 0, sizeof( tm ) );

            time1.tm_year = year - 1900;
            time1.tm_mon = month - 1;
            time1.tm_mday = day;
            time1.tm_hour = hour;
            time1.tm_min = minute;
            time1.tm_sec = second;

            time_t ttv = mktime( &time1 );
            if( ttv == -1 )
            {
                std::cerr << ".......modify systime error : " << -1 << "..........." << std::endl;
                return true;
            }

            timeval tv;
            tv.tv_sec = ( long )ttv;
            tv.tv_usec = 0;

            settimeofday( &tv, 0 );
#endif

            return true;
        }
        else
        {
            gb28181::CGb28181ParserSdp p( m_wsf, &m_configFile, sid, resId, inString ,answer);
            if( !p.isOk() )
                return false;
            if( !p.parserReceivePkg() )
                return false;

            m_format = p.getFormat();
            outString = p.getResultStr();
            m_srcAddr = p.getSrcAddr();

            SHistoryMediaResponse historyMediaRes = p.getHistoryMediaInfo();
            if ( historyMediaRes.playUrl.empty() )
            {
                // 实时流 直接跳出
                return true;
            }

            // 判断是否已存在，则直接退出，针对同时发送多条同一指令的情况
//		{
//			lock_type l( m_mutex );
// 			std::map<std::string, msg::CSpIRtspClient>::iterator it = m_rtstClientMap.find( sid );
// 			if ( it != m_rtstClientMap.end() )
// 				return true;
//		}

            // 历史流建立RTSP链接
// 		msg::CSpIRtspClient rtspClient;
// 		rtspClient = m_rtspClientMgr->createRtspClient( historyMediaRes.playUrl, boost::bind( &CProtocolGb28181::receive, this, _1, _2, _3 ) );
// 		if ( !rtspClient )
// 			return false;

            // 设置数据接收端地址
// 		msg::IRtspClient *rtspClientTemp = rtspClient.get();
// 		((msg::CRtspClient*)rtspClientTemp)->setReceiverAddr( historyMediaRes.receiveAddr );
//
// 		if ( !rtspClient->init() )
// 			return false;
//
// 		if ( !rtspClient->sendModifyTimespan( historyMediaRes.beginTime, historyMediaRes.endTime ) )
// 		{
// 			rtspClient->fini();
// 			return false;
// 		}

            // 暂时认为SIP可区分每个回放链接
//		lock_type l( m_mutex );
// 		m_rtstClientMap.insert( std::make_pair( sid, rtspClient ) );
//         m_inviteMap.insert(std::make_pair(sid,p.getInviteType()));
        }

        return true;
    }

    bool CProtocolGb28181::receivePkg( const std::string &sid, const std::string &resId, const std::string &inString, std::vector<std::string> &outString )
    {
        if( !m_isOk )
            return false;

        //先判断是否是XML格式的字符串
        if( 0 == ::strnicmp( inString.c_str(), "<?xml", 5 ) )
        {
            gb28181::CGb28181Parser p( m_wsf, &m_configFile, sid, resId, inString, m_type );
            if( !p.isOk() )
                return false;
            if( !p.parserReceivePkg() )
                return false;
            p.getResultVec(outString);
        }

        return true;
    }

    bool CProtocolGb28181::receiveBye( const std::string &sid )
    {
        if( !m_isOk )
            return false;

        lock_type l( m_mutex );
// 	std::map<std::string, msg::CSpIRtspClient>::iterator it = m_rtstClientMap.find( sid );
// 	if ( it == m_rtstClientMap.end() )
// 	{
// 		// 实时流
// 		if( !m_wsf->onReceiveRealMediaBye( sid ) )
// 			return false;
// 	}
// 	else
// 	{
// 		// 历史流
// 		if ( it->second )
// 		{
// 			(it->second)->sendPause();
// 			(it->second)->fini();
// 			m_rtstClientMap.erase(it);
//             m_inviteMap.erase(sid);
// 		}
// 	}

        return true;
    }
    bool CProtocolGb28181::receiveCancel( const std::string &sid )
    {
        if( !m_isOk )
            return false;

        lock_type l( m_mutex );
// 	std::map<std::string, msg::CSpIRtspClient>::iterator it = m_rtstClientMap.find( sid );
// 	if ( it == m_rtstClientMap.end() )
// 	{
// 		// 实时流
// 		if( !m_wsf->onReceiveRealMediaCancel( sid ) )
// 			return false;
// 	}
// 	else
// 	{
// 		// 历史流
// 		if ( it->second )
// 		{
// 			(it->second)->sendPause();
// 			(it->second)->fini();
// 			m_rtstClientMap.erase(it);
//             m_inviteMap.erase(sid);
// 		}
// 	}

        return true;
    }
    bool CProtocolGb28181::receiveAck( const std::string &sid )
    {
        if( !m_isOk )
            return false;

        lock_type l( m_mutex );
// 	std::map<std::string, msg::CSpIRtspClient>::iterator it = m_rtstClientMap.find( sid );
// 	if ( it == m_rtstClientMap.end() )
// 	{
// 		// 实时流
// 		if( !m_wsf->onReceiveRealMediaAck( sid ) )
// 			return false;
// 	}
// 	else
// 	{
//         // 历史流
//         std::map<std::string ,int>::iterator iter = m_inviteMap.find( sid );
//         if (iter == m_inviteMap.end())
//             return false;
//
//         int type = iter->second;
//
// 		//回放
// 		if ( 1 == type )
// 		{
// 			(it->second)->sendReplay();
// 		}
//         else if (2 == type)
//         {
//             (it->second)->sendDownload();
//         }
// 	}

        return true;
    }

    bool CProtocolGb28181::setSesstionType( const usg::ESESSTION_TYPE type )
    {
        if( !m_isOk )
            return false;
        m_type = type;
        return true;
    }

    bool CProtocolGb28181::getSesstionType( usg::ESESSTION_TYPE &type )
    {
        if( !m_isOk )
            return false;
        type = m_type;
        return true;
    }

    bool CProtocolGb28181::getCatalogItemSize( size_t &size )
    {
        if( !m_isOk )
            return false;
        size = m_configFile.getResSize();
        return true;
    }
    bool CProtocolGb28181::getCatalogString( const size_t from, const size_t to, std::string &outString )
    {
        if( !m_isOk )
            return false;

        /*
         * 取出从from到to个item,然后形成发送串.
         */
        SAccess local = m_configFile.getLocal();
        gb28181::SCatalog gbtInfo;
        if( !m_configFile.getResMulti( from, to, gbtInfo.subList ) )
            return false;
        gbtInfo.parentAddr = local.addrCode;
        gbtInfo.subNum = gbtInfo.subList.size();

        gb28181::CGb28181Xml xml( 0 );
        if( !xml.isOk() )
            return false;
        if( !xml.getString( gbtInfo, outString ) )
            return false;
        //boost::shared_ptr<gb28181::CGb28181Xml> xml( new gb28181::CGb28181Xml( 0 ) );
        //if ( !xml->isOk() )
        //    return false;
        //if ( !xml->getString( gbtInfo, outString ) )
        //    return false;
        return true;
    }

    bool CProtocolGb28181::getKeepaliveString( std::string &outString )
    {
        if( !m_isOk )
            return false;

        SAccess local = m_configFile.getLocal();

        gb28181::CGb28181Xml xml( 0 );
        if( !xml.isOk() )
            return false;
        if( !xml.getKeepAliveString( local.addrCode, outString ) )
            return false;

        return true;

    }

    bool CProtocolGb28181::getRealMediaString( const uint8_t imageSize,const std::string& clientAddr, std::string &outString )
    {
        SRealMedia from;
        from.privilege = "%00%11";
        from.supportAudioTypes.push_back( gb28181::EAUDIOTYPE_G_711 );
        //from.supportFormatTypes.push_back( gb28181::EFORMATTYPE_4CIF );
        gb28181::EFormatType type = gb28181::EFORMATTYPE_4CIF;
        switch( imageSize )
        {
            case 1:
                type = gb28181::EFORMATTYPE_CIF;
                break;
            case 3:
                type = gb28181::EFORMATTYPE_4CIF;
                break;
            default:
		        type = gb28181::EFORMATTYPE_16CIF;

        }
        from.supportFormatTypes.push_back( type );
        from.supportVideoTypes.push_back( gb28181::EVIDEOTYPE_H_264 );
        from.maxBitrate = 2048;//TO DO 修改 以上变量的值，暂时是不确定的
        if ( !strToSocketUrl( clientAddr,from.socketType,from.sockAddr,from.sockPort ) )
        {
            return false;
        }

        CGb28181Sdp sdp;
        if ( !sdp.makeString( from, outString ) )
        {
            return false;
        }

        return true;

    }

    bool CProtocolGb28181::getHistoryListString( const uint32_t startTime, const uint32_t endTime, std::string &outString, std::string &sn )
    {
//    boost::shared_ptr<gb28181::CGb28181Xml> xml( new gb28181::CGb28181Xml( 0 ) );
//    if ( !xml->isOk() )
//        return false;
        gb28181::CGb28181Xml xml( 0 );
        if( !xml.isOk() ) return false;

        SAccess local = m_configFile.getLocal();

        SFileList from;
        from.beginTime = utcTime_t2UtcString( startTime );
        if( endTime > 0x7fffffff )
        {
            uint32_t tmpendtime = 0x7fffffff;
            from.endTime = utcTime_t2UtcString( tmpendtime );
        }
        else
            from.endTime = utcTime_t2UtcString( endTime );
        from.filepath = "64010000002100000001";
        from.address = local.ip;
        from.secrecy = "0";
        from.recordId = "64010000003000000001";
        from.fileType = 1;

        if ( !xml.makeString( from, outString, sn ) )
            return false;
        return true;
    }

    bool CProtocolGb28181::getHistoryMediaString( const std::string& fileName, const uint32_t startTime, const uint32_t endTime, std::string &outString )
    {
        return getHistoryMediaString(fileName,startTime,endTime,6000,outString);
    }

    bool CProtocolGb28181::getHistoryMediaString( const std::string& fileName, const uint32_t startTime, const uint32_t endTime, const ACE_INET_Addr &resAddr,/*const int rtpPort,*/ std::string &outString )
    {
        SHistoryMedia from;
        from.name = fileName;
        from.resAddr = resAddr.get_host_addr();//local.ip;
        valueToLexical< int, std::string >(startTime,from.beginTime);
        valueToLexical< int, std::string >(endTime > 0x7fffffff ? 0x7fffffff : endTime,from.endTime );
        from.fileType = 1;
        from.rtpPort = resAddr.get_port_number();//rtpPort;
        from.maxBitrate = 2048;
        from.privilege = "%00%11";// TO DO 结束时间、文件类型，maxBitrate 类型的值需要修改

        gb28181::CGb28181Sdp sdp;
        if ( !sdp.makeString( from, false, outString ) )
            return false;

        return true;
    }

    bool CProtocolGb28181::getHistoryMediaString( const std::string& fileName, const uint32_t startTime, const uint32_t endTime,const int rtpPort, std::string &outString )
    {
        SAccess local = m_configFile.getLocal();

        SHistoryMedia from;
        from.name = fileName;
        from.resAddr = local.ip;
        valueToLexical< int, std::string >(startTime,from.beginTime);
        valueToLexical< int, std::string >(endTime > 0x7fffffff ? 0x7fffffff : endTime, from.endTime);
        from.fileType = 1;
        from.rtpPort = rtpPort;
        from.maxBitrate = 2048;
        from.privilege = "%00%11";// TO DO 结束时间、文件类型，maxBitrate 类型的值需要修改

        gb28181::CGb28181Sdp sdp;
        if ( !sdp.makeString( from,false, outString ) )
            return false;

        return true;
    }
    bool CProtocolGb28181::getDownloadMediaString( const std::string& fileName, const uint32_t startTime, const uint32_t endTime,const int rtpPort, std::string &outString )
    {
        SAccess local = m_configFile.getLocal();

        SHistoryMedia from;
        from.name = fileName;
        from.resAddr = local.ip;
        valueToLexical< int, std::string >(startTime,from.beginTime);
        valueToLexical< int, std::string >(endTime> 0x7fffffff ? 0x7fffffff : endTime, from.endTime );

        from.fileType = 1;
        from.rtpPort = rtpPort;
        from.maxBitrate = 2048;
        from.privilege = "%00%11";// TO DO 结束时间、文件类型，maxBitrate 类型的值需要修改

        gb28181::CGb28181Sdp sdp;
        if ( !sdp.makeString( from,true, outString ) )
            return false;

        return true;
    }

    bool CProtocolGb28181::getDownloadMediaString( const std::string& fileName, const uint32_t startTime, const uint32_t endTime,const ACE_INET_Addr &resAddr, std::string &outString )
    {
        SHistoryMedia from;
        from.name = fileName;
        from.resAddr = resAddr.get_host_addr();;
        valueToLexical< int, std::string >(startTime,from.beginTime);
        valueToLexical< int, std::string >(endTime > 0x7fffffff ? 0x7fffffff : endTime, from.endTime);
        from.fileType = 1;
        from.rtpPort = resAddr.get_port_number();
        from.maxBitrate = 2048;
        from.privilege = "%00%11";// TO DO 结束时间、文件类型，maxBitrate 类型的值需要修改

        gb28181::CGb28181Sdp sdp;
        if ( !sdp.makeString( from,true, outString ) )
            return false;

        return true;
    }
    bool CProtocolGb28181::getPtzCommandString( const usg::SPtzCommand& command, std::string &outString )
    {
//     boost::shared_ptr<gb28181::CGb28181Xml> xml( new gb28181::CGb28181Xml( 0 ) );
//     if ( !xml->isOk() )
//         return false;
        gb28181::CGb28181Xml xml( 0 );
        if( !xml.isOk() )
            return false;

        SPtzCommand to;
        if ( !transPtzCommandWtoeToGb28181(command,to))
        {
            return false;
        }
        to.sn = boost::lexical_cast<std::string>(m_sn);
        if ( !xml.makeString( to, outString) )
            return false;
        m_sn++;
        return true;
    }

    bool CProtocolGb28181::getPresetListString( const uint8_t formIndex, const uint8_t toIndex, std::string &outString )
    {
//     boost::shared_ptr<gb28181::CGb28181Xml> xml( new gb28181::CGb28181Xml( 0 ) );
//     if ( !xml->isOk() )
//         return false;
        gb28181::CGb28181Xml xml( 0 );
        if( !xml.isOk() )
            return false;

        SPresetList from;
        from.fromIndex = formIndex;
        from.toIndex = toIndex;
        from.privilege = "%00%11";

        if ( !xml.makeString( from, outString ) )
            return false;

        return true;
    }

    void CProtocolGb28181::receive( uint8_t* dat, uint32_t len, void* pParam )
    {
        std::cout << "+";
    }

    bool CProtocolGb28181::receiveSubscribePkg( const std::string &sid, const std::string &resId, const std::string &inString, std::string &outString )
    {
        if( !m_isOk )
            return false;

        /* 目前存在一种情况
         * 1、XML格式，主要针对一般的情况
         */

        //先判断是否是XML格式的字符串
        if( 0 == ::strnicmp( inString.c_str(), "<?xml", 5 ) )
        {
            gb28181::CGb28181Parser p( m_wsf, &m_configFile, sid, resId, inString, m_type );
            if( !p.isOk() )
                return false;
            if( !p.parserSubscribeReceivePkg() )
                return false;
            outString = p.getResultStr();
        }

        m_subscribeResid = resId;

        return true;
    }

    bool CProtocolGb28181::getDeviceConfigResponseString( bool isOk, const std::string &sn, const std::string &rsid, std::string &outString )
    {
        outString =
                "<Response>"
                "<CmdType>Catalog</CmdType>"
                "<SN>";
        outString = outString + sn
                    + "</SN>"
                    + "<DeviceID>";
        outString += rsid;
        outString = outString + "</DeviceID>"
                    + "<Result>";
        if( isOk )
            outString = outString + "OK</Result>";
        else
            outString = outString + "ERROR</Result>";

        outString = outString + "</Response>";
        outString = gb28181::XML_PROLOG_HEAD+outString;
        return true;
    }

    bool CProtocolGb28181::getQueryDeviceConfigResponseString( const std::string &sn, const std::string &sid, const SDeviceConfigQueryResult &result, std::string &outString )
    {
        CGb28181Xml xml( 0 );

        return xml.getDeviceConfigQuery( sn, sid, result, outString );
    }

    bool CProtocolGb28181::getAlarmNotifyString( const std::string &sn, const boost::uuids::uuid &resId, const SAlarmInfo &info, std::string &outString )
    {
        CGb28181Xml xml( 0 );

        std::string sid;
        if( !m_configFile.getResAddrCode( resId, sid ) ) return false;
        return xml.getAlarmNotify( sn, sid, info, outString );
    }

    bool CProtocolGb28181::getCatalogNotifyString( const std::string &sn, const std::string &sid, std::vector<std::string> &outString )
    {
        if( !m_isOk )
            return false;

        /*
         * 取出从from到to个item,然后形成发送串.
         */
        size_t allSize = m_configFile.getResSize();

        gb28181::SCatalog gbtInfo;
        if( !m_configFile.getResMulti( 1, allSize, gbtInfo.subList ) )
            return false;

// 	gb28181::SCatalog gbtInfo;
// 	if( !m_wsf->getCatalog( gbtInfo.subList ) ) return false;

        gbtInfo.subNum = gbtInfo.subList.size();
        gbtInfo.coding = m_subscribeResid;
        gbtInfo.sn = sn;

        gb28181::CGb28181Xml xml( 0 );
        if( !xml.isOk() )
            return false;
        if( !xml.getString( gbtInfo, outString ) )
            return false;

        return true;
    }

    bool CProtocolGb28181::getCatalogItemNotifyString( const std::string &sn, const gb28181::SCatalog::SItem &item, std::vector<std::string> &outString )
    {
        if( !m_isOk )
            return false;

        /*
         * 取出从from到to个item,然后形成发送串.
         */
//    size_t allSize = 1;//m_configFile.getResSize();
        m_configFile.updataItem( item );

        gb28181::SCatalog gbtInfo;
//     if( !m_configFile.getResMulti( 1, allSize, gbtInfo.subList ) )
//         return false;

// 	gb28181::SCatalog gbtInfo;
// 	if( !m_wsf->getCatalog( gbtInfo.subList ) ) return false;

        gbtInfo.subList.push_back( item );
        gbtInfo.subNum = 1;//gbtInfo.subList.size();
        gbtInfo.coding = m_subscribeResid;
        gbtInfo.sn = sn;

        gb28181::CGb28181Xml xml( 0 );
        if( !xml.isOk() )
            return false;
        if( !xml.getString( gbtInfo, outString ) )
            return false;

        return true;
    }

    bool CProtocolGb28181::getPlayCommandString(const int cseq,std::string &outString)
    {
        std::string strCseq = "";
        valueToLexical<int ,std::string>(cseq,strCseq);
        outString  = "PLAY RTSP/1.0\r\nCSeq: ";
        outString += strCseq;
        outString += "\r\nScale:1.0";
        outString += "\r\nRange: npt=now-\r\n";
        return true;
    }

    bool CProtocolGb28181::getPauseCommandString(const int cseq,std::string &outString)
    {
        std::string strCseq = "";
        valueToLexical<int ,std::string>(cseq,strCseq);
        outString  = "PAUSE RTSP/1.0\r\nCSeq: ";
        outString += strCseq;
        outString += "\r\nPausetime: now\r\n";
        return true;
    }

    bool CProtocolGb28181::getSetSpeedCommandString(const int cseq,uint8_t n,uint8_t d,std::string &outString)
    {
        float scale = 1.0f;

        if ( n == 1 )
        {
            if ( d > 0 && d <= 20 )
                scale = 1 / float(d);
        }
        else if ( d == 1 )
        {
            if ( n > 0 && n <= 20 )
                scale = float(n);
        }

        std::string strCseq = "";
        valueToLexical<int ,std::string>(cseq,strCseq);

        char strScale[10] = {0};
        sprintf(strScale,"%0.1f",scale);

        outString  = "PLAY RTSP/1.0\r\nCSeq: ";
        outString += strCseq;
        outString += "\r\nScale: ";
        outString += strScale;
        outString += "\r\nRange: npt=0-\r\n";
        return true;
    }

    bool CProtocolGb28181::getSetTimeSpanCommandString(const int cseq,uint32_t s,uint32_t e,std::string &outString)
    {
        std::string strCseq = "";
        valueToLexical<int ,std::string>(cseq,strCseq);

        std::string strS= "";
        valueToLexical<uint32_t ,std::string>(s,strS);

        std::string strE= "";
        valueToLexical<uint32_t ,std::string>(e,strE);

        outString  = "PLAY RTSP/1.0\r\nCSeq: ";
        outString += strCseq;
        outString += "\r\nScale:1.0";
        outString += "\r\nRange: npt=";
        outString += strS;
        outString += "-";
        if (e != 0)
            outString += strE;
        outString += "\r\n";
        return true;
    }

    bool CProtocolGb28181::getTeardownCommandString(const int cseq,std::string &outString)
    {
        std::string strCseq = "";
        valueToLexical<int ,std::string>(cseq,strCseq);
        outString  = "TEARDOWN RTSP/1.0\r\nCSeq: ";
        outString += strCseq;
        outString += "\r\n";
        return true;
    }

    bool CProtocolGb28181::getSubscribeCatalogString(const uint32_t startTime, const uint32_t endTime, std::string &outString)
    {
        if( !m_isOk )
            return false;

        gb28181::CGb28181Xml xml( 0 );
        if( !xml.isOk() )
            return false;
        if( !xml.getSubscribeCatalogString(startTime,endTime, outString ) )
            return false;

        return true;
    }

    bool CProtocolGb28181::getSubscribeAlarmString(const SAlarmSubscribeParam &param, std::string &outString)
    {
        if( !m_isOk )
            return false;

        gb28181::CGb28181Xml xml( 0 );
        if( !xml.isOk() )
            return false;
        if( !xml.getSubscribeAlarmString(param, outString ) )
            return false;

        return true;
    }

    bool CProtocolGb28181::getReceiveBroadcastResponse( const std::string &sn, const std::string deviceId, bool isOk, std::string &outString )
    {
        if( !m_isOk )
            return false;

        gb28181::CGb28181Xml xml( 0 );
        if( !xml.isOk() )
            return false;
        if( !xml.getReceiveBroadcastResponseString( sn, deviceId, isOk, outString ) )
            return false;

        return true;
    }

    bool CProtocolGb28181::getAudioBroadcastString(const std::string &sourceId, const std::string &targetId, std::string &outString)
    {
        if( !m_isOk )
            return false;

        gb28181::CGb28181Xml xml( 0 );
        if( !xml.isOk() )
            return false;
        if( !xml.getAudioBroadcastString(sourceId,targetId, outString ) )
            return false;

        return true;
    }

    bool CProtocolGb28181::getDeviceRebootString( std::string &outString )
    {
        gb28181::CGb28181Xml xml( 0 );
        if ( !xml.isOk() )
            return false;

        gb28181::SDeviceReboot reboot;
        reboot.sn = boost::lexical_cast<std::string>(m_sn);

        if ( !xml.makeString( reboot, outString ) )
            return false;

        m_sn++;
        return true;
    }

    bool CProtocolGb28181::getRecordContronlString( std::string &outString, bool flag )
    {
        gb28181::CGb28181Xml xml( 0 );
        if ( !xml.isOk() )
            return false;

        gb28181::SRecordContronl record;
        record.flag = flag;
        record.sn = boost::lexical_cast<std::string>(m_sn);

        if ( !xml.makeString( record, outString ) )
            return false;

        m_sn++;
        return true;
    }

    bool CProtocolGb28181::getGuardContronlString( std::string &outString, bool flag )
    {
        gb28181::CGb28181Xml xml( 0 );
        if ( !xml.isOk() )
            return false;

        gb28181::SGuardContronl guard;
        guard.flag = flag;
        guard.sn = boost::lexical_cast<std::string>(m_sn);
        if ( !xml.makeString( guard, outString ) )
            return false;

        m_sn++;
        return true;
    }

    bool CProtocolGb28181::getAlarmResetString( std::string &outString )
    {
        gb28181::CGb28181Xml xml( 0 );
        if ( !xml.isOk() )
            return false;

        gb28181::SAlarmReset alarm;
        alarm.sn = boost::lexical_cast<std::string>(m_sn);

        if ( !xml.makeString( alarm, outString) )
            return false;

        m_sn++;
        return true;
    }

    bool CProtocolGb28181::getQueryDeviceCatalogString( std::string &outString )
    {
        gb28181::CGb28181Xml xml( 0 );
        if ( !xml.isOk() )
            return false;

        gb28181::SDeviceCatalog catalog;
        catalog.sn = boost::lexical_cast<std::string>(m_sn);

        if ( !xml.makeString( catalog, outString ) )
            return false;

        m_sn++;
        return true;
    }

    bool CProtocolGb28181::getQueryDeviceInfoString( std::string &outString, std::string &sn )
    {
        gb28181::CGb28181Xml xml( 0 );
        if ( !xml.isOk() )
            return false;

        gb28181::SDeviceInfo info;
        info.sn = boost::lexical_cast<std::string>(m_sn);

        if ( !xml.makeString( info, outString, sn ) )
            return false;

        m_sn++;
        return true;
    }

    bool CProtocolGb28181::getQueryDeviceStatusString( std::string &outString, std::string &sn )
    {
        gb28181::CGb28181Xml xml( 0 );
        if ( !xml.isOk() )
            return false;

        gb28181::SDeviceStatus status;
        status.sn = boost::lexical_cast<std::string>(m_sn);

        if ( !xml.makeString( status, outString, sn ) )
            return false;

        m_sn++;
        return true;
    }

    bool CProtocolGb28181::getReplayEndNotifyString( const std::string &sn, const boost::uuids::uuid &resId, std::string &outString )
    {
        if( !m_isOk )
            return false;

        CGb28181Xml xml( 0 );

        std::string sid;
        if( !m_configFile.getResAddrCode( resId, sid ) ) return false;
        return xml.getReplayEndString( sn, sid, outString );
    }

    bool CProtocolGb28181::setCatalogItems( const std::vector< SCatalog::SItem > &items )
    {
        m_configFile.setCatalogs( items );
        return true;
    }

    bool CProtocolGb28181::getFormatInfo( std::string &format )
    {
        format = m_format;
        return true;
    }


    bool CProtocolGb28181::getSrcAddr( std::string &addr )
    {
        addr = m_srcAddr;
        return ( !addr.empty() );
    }

}

usg::IProtocolWtoe *getProtocolLibInstance()
{
    static boost::shared_ptr< usg::IProtocolWtoe > pw;
    if( !pw )
    {
        pw.reset( new_o( gb28181::CProtocolGb28181 ), gb28181::CProtocolGb28181::Deleter() );
    }
    if( pw )
    {
        return pw.get();
    }

    return 0;
}
