/**
 * Project FVM
 * 前端平台的一些通用操作，如登录sdk，初始化可用端口范围，获取当前空闲端口
 */

#include "platform_manager.h"
#include <boost/asio.hpp>
#include "stream/stream_factory.h"
#ifdef SDK_HK_SUPPORTED
#include "sdk_hk_platform.h"
#endif
#ifdef SDK_DH_SUPPORTED
#include "sdk_dh_platform.h"
#endif
#ifdef SDK_MPC_SUPPORTED
#include "MpcNetSdkItf.hpp"
#include "ace/INET_Addr.h"
#endif
#include <thread>

namespace fvm::platform {

    using namespace std::literals::chrono_literals;

    std::mutex mutexPort;
    unsigned short tcpListenPort = 0;
    unsigned short udpListenPort = 0;

    //系统允许使用的端口范围
    unsigned short availPortBegin = 0;
    unsigned short availPortEnd = 0;

    //处理请求流的回应消息
    std::mutex mutexRequestRet;
    std::map<int, RequestRetHandler> mapRetHandler;

    static std::atomic<bool> running = false;
    static std::atomic<bool> fvmChanged = true;
    constexpr auto DEVICE_LOGIN_CYCLE = 600;  // 十分钟

    std::thread deviceLoginThread;

    void deviceLogin();

    /** 
    * 关于平台的一些初始化操作，包含响应请求流，获取可用端口范围，以及通知gum关于fvm启动的消息，SDK的全局初始化
    */
    bool initPlatformManager() {

        PROTOCOL_MANAGER.onRequestVideoRet.connect( [](RequestVideoRet msg ) {
            dealRequestRet( msg );
        } );
#ifdef SDK_HK_SUPPORTED
        SDKHKPlatform::initSDK();
#endif
#ifdef SDK_DH_SUPPORTED
        //大华初始化
        SDKDHPlatform::initSDK();
#endif
#ifdef SDK_MPC_SUPPORTED
        //二代平台
        mpc::nsdk::Runtime::init();
#endif   
        getPortRange();

        notifyGumMsm();

        running = true;

        if (DATA_MANAGER.isPlatform()) {
            deviceLoginThread = std::thread(&deviceLogin);
        }
        return true;
    }

    /**
    * SDK的全局反初始化
    */
    bool disposePlatformManager() 
	{
        running = false;

        if ( deviceLoginThread.joinable() ) {
            deviceLoginThread.join();
        }

#ifdef SDK_HK_SUPPORTED
        //海康SDK清理
        SDKHKPlatform::cleanupSDK();
#endif
#ifdef SDK_DH_SUPPORTED
        //大华SDK清理
        SDKDHPlatform::cleanupSDK();
#endif
#ifdef SDK_MPC_SUPPORTED
        //二代平台
        mpc::nsdk::Runtime::fini();
#endif   
        return true;
    }


	/**
     * 通知gum和msm更新一次当前远端信息  
	 */
    void notifyGumMsm() {
	
        FVMChangedId msg;        
		PROTOCOL_MANAGER.sendTo(DATA_MANAGER.getGumIP(), DATA_MANAGER.getGumPort(), network::ProtocolType::UDP_FVM_CHANGED, msg);
    }


    unsigned short getAvailPort( bool isUdp) {
        auto& port = isUdp ? udpListenPort : tcpListenPort;
        std::lock_guard < std::mutex> lock(mutexPort);
        while (true)
        {
            port += 2;
            if (port >= availPortEnd)
                port = availPortBegin;
            if (isPortUsable(port, isUdp ) && isPortUsable(port + 1, isUdp))
            {
                return port;
            }
        }
        return 0;
    }

    bool registerMsgPuller(int videoId, RequestRetHandler handler) {
        std::lock_guard<std::mutex> lock( mutexRequestRet );
        mapRetHandler[videoId] = handler;
        return true;
    }

    void unregisterMsgPuller(int videoId) {
        std::lock_guard<std::mutex> lock( mutexRequestRet );
        if ( mapRetHandler.find( videoId ) != mapRetHandler.end() )
            mapRetHandler.erase( videoId );
    }

    void dealRequestRet(RequestVideoRet msg ) {
        RequestRetHandler handler = nullptr;
        {
            std::lock_guard<std::mutex> lock(mutexRequestRet);
            if (mapRetHandler.find(msg.iVideoId) == mapRetHandler.end())
                return;
            handler = mapRetHandler[msg.iVideoId];
        }
        if ( handler )
        {
			std::string szSrc = "";
			if (msg.iResult == 0)  //请求成功
				szSrc = msg.srcAddr;
            handler( szSrc );
            ai::LogInfo << "request video " << msg.iVideoId << " ret: " << msg.iResult << " dest: " << msg.destAddr << " src: " << szSrc;
        }
    }

    /** 
    * 通过生成一个tcp或udp的socket，并绑定指定端口来判断该端口是否被使用
    * todo：后期还需要判断该udp端口上是否已有程序在发流（因为收流端异常退出时，下级lrg并不会停止往该端口发流)
    */
    bool isPortUsable(unsigned short port, bool isUdp)
    {
        int32_t sock = -1;
        sock = ::socket(AF_INET, isUdp ? SOCK_DGRAM : SOCK_STREAM, 0);
        if (sock == -1)
        {
            /** SOCKET错误 */
            return false;
        }

        struct sockaddr_in addr;
        addr.sin_family = AF_INET;
        addr.sin_addr.s_addr = INADDR_ANY;
        addr.sin_port = htons(port);

        /** 通过bind测试 */
        bool ret = (::bind(sock, (struct sockaddr*)&addr, sizeof(addr)) >= 0 );
        //bind成功，等待10ms后再关闭
        if ( ret )  
            std::this_thread::sleep_for(10ms);

        ::close(sock);
        return ret;
    }

   /**
   * 启动时获得系统允许使用的端口范围，并随机给出tcp和udp的起始分配端口（必须是双数）
   */ 
    void getPortRange()
    {
        FILE* fp = fopen("/proc/sys/net/ipv4/ip_local_port_range", "r");
        if (fp != NULL)
        {
            char buf[100];
            if (fgets(buf, 100, fp) != NULL)
            {
                std::string szTmp(buf);
                if (szTmp.find_last_of('\n') != std::string::npos)
                    szTmp.erase(szTmp.find_last_of('\n'));
                size_t pos = szTmp.find('\t');
                if (pos != std::string::npos)
                {
                    uint16_t i1 = atoi(szTmp.substr(0, pos).c_str());
                    uint16_t i2 = atoi(szTmp.substr(pos + 1, szTmp.length()).c_str());
                    if (i1 > 0 && i2 > 0 && i1 < i2)
                    {
                        availPortBegin = i1;
                        availPortEnd = i2;

                        srand((unsigned int)time(NULL));
                        tcpListenPort = (rand() % 137) + availPortBegin;
                        if (tcpListenPort % 2 != 0)
                            tcpListenPort++;
                        udpListenPort = (rand() % 137) + availPortBegin;
                        if (udpListenPort % 2 != 0)
                            udpListenPort++;
                        ai::LogInfo << "TCP START LISTENPORT IS " << tcpListenPort;
                        ai::LogInfo << "UDP START LISTENPORT IS " << udpListenPort;
                    }
                }
            }
            fclose(fp);
        }
    }

    void notifyChanged()
    {
        fvmChanged = true;
    }

    void deviceLogin()
    {
        using std::chrono::steady_clock;
        auto startTime = steady_clock::now();
        while ( running ) {
            if ( !fvmChanged  && std::chrono::duration_cast<std::chrono::seconds>(steady_clock::now() - startTime).count() < DEVICE_LOGIN_CYCLE ) {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                continue;
            }

            // 数据库查询=2的id 将登录器打开
            std::vector<int> platformIds;
            DATA_MANAGER.queryGetPlatformId(platformIds);
            for (auto& it : platformIds) {
                if ( !running ) {
                    break;
                }
                auto platform = fvm::stream::getFrontPlatform(it);
                if (platform)
                    platform->login();
                
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }

            startTime = steady_clock::now();
            if ( fvmChanged )
                fvmChanged = false;
        }
    }
}