#ifndef QUESTCATALOGSET_HPP_
#define QUESTCATALOGSET_HPP_

#include <map>
#include <set>
#include <string>

#include <boost/thread/mutex.hpp>
#include <boost/noncopyable.hpp>

#include "../include/WtoeStruct.hpp"

#include "../include/UsgManagerCfg.hpp"

namespace usg {

    struct SCatalogInfo
    {
        std::string sid;
        std::string devid;
        uint32_t num;
        std::map< std::string, SCatalog::SItem > itms;

        SCatalogInfo()
        {

        }

        SCatalogInfo( const SCatalogInfo &o )
        {
            sid = o.sid;
            devid = o.devid;
            num = o.num;
            itms = o.itms;
        }

        SCatalogInfo& operator= ( const SCatalogInfo &o )
        {
            sid = o.sid;
            devid = o.devid;
            num = o.num;
            itms = o.itms;

            return *this;
        }
    };
///< 汇聚类，用来提供对数据访问的汇聚
///  sip线程将目录查询响应推送上来的数据写入
///  检查线程，定期检查数据是否推送完整，一旦完整就从中取出
    class USGMANAGER_PRIVATE CQuestCatalogSet : public boost::noncopyable
    {
        CQuestCatalogSet();
    public:
        virtual ~CQuestCatalogSet();

    public:
        static bool init();
        static bool fini();
        static CQuestCatalogSet* get();

    public:
        bool createSet( const std::string &devid );
        ///< sip线程使用，写入数据
        bool add( const std::string &sid, const std::string &devid, int num, const SCatalog &catalog );
        ///< 检查线程使用，在超时前数据全部推送完成时使用
        bool takeCatalog( const std::string &sid, const std::string &devid, SCatalog &infos );
        ///< 检查线程，超时时使用
        bool removeCatalog( const std::string &sid, const std::string &devid, SCatalog &infos );
        ///< 检查线程检查数据是否推送完整时使用
        bool check( std::string &sid, const std::string &devid );
        bool hasChange( const std::string &devid );

    private:
        boost::mutex m_mutex;

        std::map< std::pair< std::string, std::string >, SCatalogInfo > m_catalogInputs;
        std::map< std::pair< std::string, std::string >, SCatalogInfo > m_catalogOuts;
        std::set< std::string > m_devIds;

        std::map< std::pair< std::string, std::string >, int > m_oldCounts;

    private:
        static CQuestCatalogSet *m_catalogSet;
    };

}

#endif
