
#include <iostream>
#include "stream/stream_manager.h"
#include "protocol/protocol_manager.h"
#include <protocol/status_manager.h>
#include "platform/platform_manager.h"
#include "util/cli_operator.h"
#include "util/license/check_license.h"
#include "util/config/fvm_config.h"
#include <csignal>
#include "ailog.h"
#include "util/common.h"
#include "util/timer/timer_manager.h"

#include "util/worker_pool.h"

#define CHECK_INIT(STEP, CONDITION)  \
    if(!(CONDITION))  \
    {  \
        ai::LogError << STEP << " ERROR!"; \
        for (int i = 0; i < 5; i++) \
        { \
            if (!appRunning) break; \
            std::this_thread::sleep_for(1s); \
        } \
        return 0; \
    }  \

bool appRunning = true;
void onSignalAction(int signum, siginfo_t* siginfo, void* ucontext);

using namespace fvm;

int main(int argc, const char* argv[])
{
    //只显示版本信息
    if ( argc == 2 && strcmp( argv[1], "-ver" ) == 0 )
    {
        showVersion();
        return 0;
    }
    struct sigaction signalAction = {};
    signalAction.sa_sigaction = onSignalAction;
    signalAction.sa_flags = SA_SIGINFO;
    sigaction(SIGINT, &signalAction, nullptr);
    sigaction(SIGTERM, &signalAction, nullptr);
    sigaction(SIGUSR1, &signalAction, nullptr);
    sigaction(SIGUSR2, &signalAction, nullptr);
    sigaction(SIGPIPE, &signalAction, nullptr);

    // 初始化日志
    auto appName = "fvm"s;
    CHECK_INIT("LOG", ai::InitLog(appName, "logs", SETTINGS->logNum()));

    // 打印Version
    showVersion();

    // 处理重复的进程ID启动
    std::string processName = appName + std::to_string(SETTINGS->procID());
    if (isRunning(processName))
    {
        ai::LogError << processName << " is already running\n !";
        return 1;
    }

    // 检查License
    time_t licenseTimeBegin, licenseTimeEnd;
    auto pidTxtPath = "/var/run/"s + appName + ".pid"s;
    auto productName = argc > 1 ? std::string(argv[1]) : ""s;
    CHECK_INIT("LICENSE", !checkLicense(productName, licenseTimeBegin, licenseTimeEnd));

    // 初始化数据库连接，初始化配置数据
    CHECK_INIT("DATABASE", DATA_MANAGER.init());

    // 启动定时器线程
    timer::TIMER_MANAGER.start();

    // 启动任务池
    worker::init();

    // 开启状态管理器
    protocol::initStatusManager();

    // 初始化视频管理器
    stream::initStreamManager();

    // 初始化网络库
    CHECK_INIT("NETWORK", PROTOCOL_MANAGER.init());

    //初始化平台管理相关参数 
    platform::initPlatformManager();

    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // 启动主要流程
    DATA_MANAGER.start();

    // 初始化启动控制台，添加用户命令
    startCli();

    while (appRunning)
    {
        std::this_thread::sleep_for(std::chrono::seconds (1));
        time_t now = time(NULL);
        if ( now < licenseTimeBegin || now > licenseTimeEnd )
        {
            ai::LogError << "License is expired";
            break;
        }
    }

    protocol::disposeStatusManager();
    stream::disposeStreamManager();
    platform::disposePlatformManager();
    network::stopServers();

    worker::close();
    timer::TIMER_MANAGER.stop();
    ai::LogInfo << "FVM EXIT!  ";
    ai::FiniLog();
    return 0;
}

void onSignalAction(int signum, siginfo_t* siginfo, void* ucontext) {
    switch (signum) {
        case SIGINT:
        case SIGTERM:
        case SIGUSR1:
        case SIGUSR2:
            ai::LogInfo << "receive  signal set appRunning = false!";
            appRunning = false;
            break;
        case SIGPIPE:
            ai::LogInfo << "receive SIGPIPE signal !";
            DATA_MANAGER.init();
        default:
            break;
    }
}
