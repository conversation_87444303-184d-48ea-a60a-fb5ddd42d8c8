/**
 * Project FVM
 */
#pragma once
#include <optional>
#include <mutex>
#include <atomic>
#include <condition_variable>
#include <boost/fiber/all.hpp>

/**
 * @brief: 任务池管理
 *
 */
namespace gum::worker
{
    typedef std::optional<boost::fibers::future<void>> FiberFuture;

    /*
     * 任务类型
     */
    enum class WorkerType
    {
        Message,
    };

    /*
     * 初始化 任务池
     */
    void init();

    /*
     * 关闭 任务池队列
     */
    void close();

    /*
     * 上报新任务
     * @param type 任务类型
     * @param work 具体任务
     */
    FiberFuture post(WorkerType type, std::function<void(void)> work);

    /*
     * 上报新任务 (额外线程池)
     * @param work 具体任务
     */
    void post(std::function<void(void)> work);

    /*
     * 任务条件封装 (Fiber)
     */
    struct FiberDone {
    private:
        boost::fibers::condition_variable   cond;
        boost::fibers::mutex                mutex;
        std::atomic_bool          ready = false;

    public:

        void reset(){ready= false;}
        bool finished(){return ready;}

        void wait()
        {
            std::unique_lock<boost::fibers::mutex > lock( mutex);
            cond.wait( lock, [this](){ return ready.operator bool(); });
        }

        void stop()
        {
            ready = true;
        }

        void notify()
        {
            stop();
            cond.notify_one();
        }
    };

    /*
     * 任务条件封装 (Thread)
     */
    struct ThreadDone {
    private:
        std::condition_variable   cond;
        std::mutex                mutex;
        std::atomic_bool          ready = false;

    public:

        void reset(){ready= false;}
        bool finished(){return ready;}

        void wait()
        {
            std::unique_lock<std::mutex > lock( mutex);
            cond.wait( lock, [this](){ return ready.operator bool(); });
        }

        void stop()
        {
            ready = true;
        }

        void notify()
        {
            stop();
            cond.notify_one();
        }
    };
}

#define WORKER_SLEEP boost::this_fiber::sleep_for