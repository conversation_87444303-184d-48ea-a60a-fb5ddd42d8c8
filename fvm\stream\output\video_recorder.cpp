/**
 * Project FVM
 */

#include "video_recorder.h"
#include "protocol/protocol_manager.h"
#include "util/config/fvm_config.h"
#include "util/common.h"
#include "ailog.h"
#include <dirent.h>

#ifdef __cplusplus
extern "C"
{
#endif
#include <libavformat/avformat.h>
#ifdef __cplusplus
}
#endif


/**
 * VideoRecorder implementation
 * 
 * 视频录像器
 * 
 * 中间队列缓存预录像数据 (默认10s时长)
 */

namespace fvm::stream
{
    using namespace std::chrono;

    Recorder::Recorder(RecorderType myType):recordType(myType)
    {
    }

    Recorder::~Recorder(void)
    {
        closeFile();
        std::lock_guard<boost::fibers::mutex> lock(packetsMutex);
        packets.clear();
    }

    //设置ffmpeg一些参数
    void Recorder::setParameters(CodecInfoPtr pCodecInfo)
    {
        if (pCodecInfo)
        {
            codecpar = pCodecInfo->getAVCodecParam();
            timebase = pCodecInfo->getTimebase();
            frameRate = pCodecInfo->getFrameRate();
            if (frameRate.den != 0)
                frameRateNum = frameRate.num / frameRate.den;
        }
    }

    //发数据
    void Recorder::pushData(const PacketDataPtr packet)
    {
        if (isFinished())
            return;
        std::lock_guard<boost::fibers::mutex> lock(packetsMutex);
        packets.push_front(packet);
    }

    //设置缓存数据
    void Recorder::setListData(std::list<PacketDataPtr> datas)
    {
        if (isFinished())
            return;
        std::lock_guard<boost::fibers::mutex> lock(packetsMutex);
        packets = datas;
    }

    //录像是否完成
    bool Recorder::isFinished(void)
    {
        return bFinishState;
    }

    //创建录像文件
    bool Recorder::createFile(std::string szFile, ExtensionType myType)
    {
        if (!codecpar)
            return false;
        int ret;
        std::string extType;
        if (myType == ExtensionType::MP4)
            extType = "mp4";
        else if (myType == ExtensionType::FLV)
            extType = "flv";
        else
            extType = "flv";    //默认flv
        if (0 > (ret = avformat_alloc_output_context2(&formatCtx, nullptr, extType.c_str(), szFile.c_str())))
        {
            ai::LogError << szFile << " avformat_alloc error!";
            ret = AVERROR_UNKNOWN;
            return false;
        }
        outStream = avformat_new_stream(formatCtx, nullptr);
        if (0 > (ret = avcodec_parameters_copy(outStream->codecpar, codecpar)))
        {
            closeFile();
            return false;
        }
        outStream->codecpar->codec_tag = 0;
        if (!(formatCtx->flags & AVFMT_NOFILE))
        {
            ret = avio_open(&formatCtx->pb, szFile.c_str(), AVIO_FLAG_WRITE);
            if (ret < 0)
            {
                closeFile();
                return false;
            }
        }
        if (0 > (ret = avformat_write_header(formatCtx, nullptr)))
        {
            closeFile();
            return false;
        }
        frameIndex = 0;
        dstTb = formatCtx->streams[0]->time_base;
        bFileCreated = true;
        bFinishState = false;
        return true;
    }

    //关闭释放文件
    void Recorder::closeFile(void)
    {
        bFinishState = true;
        if (!formatCtx)
            return;
        if (bFileCreated)
        {
            av_write_trailer(formatCtx);
            bFileCreated = false;
        }
        /* close output */
        if (!(formatCtx->oformat->flags & AVFMT_NOFILE))
            avio_closep(&formatCtx->pb);
        avformat_close_input(&formatCtx);
        avformat_free_context(formatCtx);
        frameIndex = 0;
        ptsStart = dtsStart = 0;
        recordLength = 0;
        firstIFrameFound = false;
        formatCtx = nullptr;
    }

    //录像主逻辑过程
    void Recorder::process(void)
    {
        //状态不对退出
        if (!codecpar || isFinished())
            return;
        //1.处理创建录像文件
        if (!bFileCreated)
        {
            if (!createRecord())
                return;
        }

        //2.获取数据
        AVPacket* packet = getPacketData(!firstIFrameFound);
        if (!packet)
            return;
        else
            firstIFrameFound = true;

        //3.写录像文件
        writePacket(packet);

        //4.检查结束录像条件
        checkFinished();
    }

    //安全检查获取pkt checkIFrame是否检查I帧
    AVPacket* Recorder::getPacketData(bool checkIFrame)
    {
        //1.取数据
        PacketDataPtr packet;
        {
            std::lock_guard<boost::fibers::mutex> lock(packetsMutex);
            if (packets.empty())
                return nullptr;
            packet = packets.back();
            packets.pop_back();
            if (!packet)
                return nullptr;
        }
        //安全检查
        if (!formatCtx || !bFileCreated)
            return nullptr;
        //2.packet数据
        AVPacket* pkt = packet->getData(true);      //true时是深拷贝
        if (!pkt)
        {
            return nullptr;
        }
        if (checkIFrame)
        {
            if (pkt->flags & AV_PKT_FLAG_KEY)       //判断是I帧
            {
                if (pkt->pts != AV_NOPTS_VALUE)
                    ptsStart = pkt->pts;
                if (pkt->dts != AV_NOPTS_VALUE)
                    dtsStart = pkt->dts;
                return pkt;
            }
            else {
                av_packet_unref(pkt);
                av_packet_free(&pkt);
                return nullptr;
            }
        }
        return pkt;
    }

    //保存写数据
    void Recorder::writePacket(AVPacket* pkt)
    {
        //计算pts
        if (pkt->duration == 0)
        {
            int64_t m_duration = (int64_t)((double)AV_TIME_BASE / av_q2d(frameRate)) / 1000;
            int64_t duration = av_rescale(m_duration, timebase.den, outStream->time_base.den);
            pkt->duration = duration;
        }
        if (pkt->pts == AV_NOPTS_VALUE)
        {
            pkt->pts = pkt->duration * frameIndex;
        }
        if (pkt->dts == AV_NOPTS_VALUE)
        {
            pkt->dts = pkt->duration * frameIndex;
        }

        pkt->pts = av_rescale_q_rnd(pkt->pts - ptsStart, timebase, dstTb, (AVRounding)(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));
        // pkt->dts = av_rescale_q_rnd(pkt->dts - dtsStart, timebase, dstTb, (AVRounding)(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));
        pkt->dts = pkt->pts;
        pkt->duration = av_rescale_q(pkt->duration, timebase, dstTb);
        pkt->stream_index = 0;
        pkt->pos = -1;
        //写pkg
        recordLength += pkt->size;
        int ret = av_interleaved_write_frame(formatCtx, pkt);
        //最后释放
        av_packet_unref(pkt);
        av_packet_free(&pkt);
        if (ret < 0)
        {
            writeErrorNum++;        //累计连续错误次数
        }
        else
        {
            writeErrorNum = 0;      //归0
            frameIndex++;           //帧数累计
        }
    }




    /**
    * @brief                       构造函数 
    * @param[in] localPath         本地录像路径
    * @param[in] webUrl            完成录像提交web的url
    * @param[in] beforeTime        预录像时长
    * @param[in] afterTime         发生后录像时长
    * @param[in] pCodecInfo        解码相关参数
    */
    EventRecorder::EventRecorder(std::string localPath, std::string webUrl, int iBeforeTime, int iAfterTime, CodecInfoPtr pCodecInfo)
        :Recorder(RecorderType::Event)
    {
        setParameters(pCodecInfo);
        beforeTime = iBeforeTime;
        afterTime = iAfterTime;
        szWeburl = webUrl;
        //本地录像绝对地址
        szLocalFile = createDir(localPath, szWeburl);
        //录像总帧数
        videoLength = (beforeTime + afterTime) * getFrameRateNum();
        //设置录像最大时长
        setMaxRecordTime();
    }

    bool EventRecorder::createRecord(void)
    {
        return createFile(szLocalFile, ExtensionType::MP4);
    }

    void EventRecorder::checkFinished(void)
    {
        //判断结束录像任务
        if (frameIndex > videoLength || writeErrorNum > 100)    //如果连续超过100次写错误就结束
        {
            if (writeErrorNum > 100)
                ai::LogError << "av_interleaved_write_frame error! | writeErrorNum > 100";
            closeFile();
            VideoFinished eventInfo;
            eventInfo.eventVideo = szWeburl;
            PROTOCOL_MANAGER.sendToWEB(network::ProtocolType::HTTP_EVENT_VIDEO_FINISHED, eventInfo);
        }
    }

    /**
    * @brief                        是否可追加
    * @param[in] seconds            可追加时长 单位秒
    */
    bool EventRecorder::isAddible(int seconds)
    {
        //追加后视频长度
        int newLength = frameIndex + (beforeTime + seconds) * getFrameRateNum();
        if (newLength > maxLength)
            return false;           //不可追加
        return true;
    }

    /**
    * @brief                        追加多少秒录像
    * @param[in] seconds            可追加时长 单位秒
    */
    void EventRecorder::addTime(int seconds)
    {
        videoLength = frameIndex + (beforeTime + seconds) * getFrameRateNum();
    }

    /**
    * @brief                        处理文件夹的创建
    * @param[in] address            实际录像系统的前缀路径
    * @param[in] szWeburl           web传来的路径
    * @param[out]                   实际录像文件的绝对地址
    */
    std::string EventRecorder::createDir(const std::string& address,const std::string& szWeburl)
    {
        //http://*************:8080/eventvideo/20190411133000_1.mp4
        //http://*************:8080/eventvideo/2021-03-16/20210316133000_1.mp4
        std::string szNewSaveFile = szWeburl;
        std::string szActPath = address;
        size_t pos = szNewSaveFile.find_last_of('/');
        std::string szUrl, szFile;
        if (pos != std::string::npos)
        {
            szUrl = szNewSaveFile.substr(0, pos + 1);
            szFile = szNewSaveFile.substr(pos + 1, szNewSaveFile.length());
            size_t pos2 = szNewSaveFile.find("eventvideo/");
            if (pos2 != std::string::npos && (pos2 + 11) != pos)  //说明中间有子目录，要创建
            {
                std::string szTmpPath = szNewSaveFile.substr(pos2 + 11, pos - pos2 - 10);
                szActPath += szTmpPath;
#if defined (WIN32)
                int status = _chdir(szActPath.c_str());
                if (status < 0)
                {
                    status = _mkdir(szActPath.c_str());
                }
#else
                if (access(szActPath.c_str(), 0) != 0)
                {
                    mkdir(szActPath.c_str(), 0777);
                }
#endif
            }
        }
        else
        {
            //获取webip 需要从数据库获取
            szUrl = "http://127.0.0.1:8080/eventvideo/";
            szFile = szNewSaveFile;
        }
        //本地路径
        return szActPath + szFile;
    }

    //录像文件最大时间 (单位 秒)
    void EventRecorder::setMaxRecordTime(int time)
    {
        //录像最大帧数
        maxLength = time * getFrameRateNum();
    }



    /**
    * @brief                       构造函数
    * @param[in] iChannelId        通道id
    * @param[in] begintime         开始录像时刻 类似 xx:xx 如 00:00
    * @param[in] endtime           结束录像时刻 类似 xx:xx 如 23:59
    * @param[in] iDays             保留天数
    * @param[in] iSection          录像文件分段间隔(单位 分钟)
    */
    TaskRecorder::TaskRecorder(int iChannelId, std::string begintime, std::string endtime, int iDays, int iSection)
        :Recorder(RecorderType::Task)
    {
        channelId = iChannelId;
        days = iDays;
        section = iSection;
        filesize = section * 1024 * 1024;
        begintimeSec = stringToSec(begintime);
        endtimeSec = stringToSec(endtime);
        recordFileName.clear();
    }

    bool TaskRecorder::isFinished(void)
    {
        auto bRet = checkTime();
        if (!bRet)
            closeFile();        //超出时间段条件 请求关闭录像
        return !bRet;
    }

    bool TaskRecorder::createRecord(void)
    {
        //创建文件前做一次 清理文件名超过日期的视频
        cleanOldVideo(SETTINGS->recordPath());
        //计算文件名
        if (recordFileName.empty())
            recordFileName = createFileName();
        auto bRet = createFile(recordFileName);
        if (bRet)
        {
            recordFileName.clear();
            startPoint = steady_clock::now();   //创建成功记录开始时间
        }
        return bRet;
    }

    void TaskRecorder::checkFinished(void)
    {
        //按section分钟间隔分段 
        //if (duration_cast<minutes>(steady_clock::now() - startPoint).count() >= section)
        //按文件大小间隔分段
        if (recordLength >= filesize)
        {
            closeFile();
        }
    }

    //检查录像时间范围条件
    bool TaskRecorder::checkTime(void)
    {
        std::chrono::system_clock::time_point time_point_now = std::chrono::system_clock::now();
        time_t now = std::chrono::system_clock::to_time_t(time_point_now);
        tm* t = localtime(&now);
        int nowSec = t->tm_hour * 3600 + t->tm_min * 60 + t->tm_sec;
        if (nowSec > begintimeSec && nowSec < endtimeSec)
            return true;
        return false;
    }

    //创建录像名称
    std::string TaskRecorder::createFileName(void)
    {
        std::chrono::system_clock clock;
        auto tSeconds = std::chrono::duration_cast<std::chrono::seconds>(clock.now().time_since_epoch());
        time_t secNow = tSeconds.count();
        auto tm = std::localtime(&secNow);
        std::stringstream ss;
        ss << std::put_time(tm, "%Y%m%d%H%M%S");
        auto tMilli = std::chrono::duration_cast<std::chrono::milliseconds>(clock.now().time_since_epoch());
        auto ms = tMilli - tSeconds;
        ss << std::setfill('0') << std::setw(3) << ms.count();
        std::string chId = std::string("ch") + std::to_string(channelId);
        std::string newName = ss.str();
        newName = SETTINGS->recordPath() + "/" + chId + "/" +
            std::to_string(section) + "MB_" + newName + ".flv";
        //创建文件夹
        const char* chIdPath = (SETTINGS->recordPath() + "/" + chId).c_str();
#if defined (WIN32)
        int status = _chdir(chIdPath);
        if (status < 0)
        {
            status = _mkdir(SETTINGS->recordPath().c_str());
            status = _mkdir(chIdPath);
        }
#else
        if (access(chIdPath, 0) != 0)
        {
            mkdir(SETTINGS->recordPath().c_str(), 0777);
            mkdir(chIdPath, 0777);
        }
#endif
        return newName;
    }

    //清理过期视频  recordPath=./video/
    void TaskRecorder::cleanOldVideo(std::string recordPath)
    {
        auto filenameCheckTime = [](std::string logname, int date)->bool 
        {
            //logname 一般类似为1min_20220516134818700.flv
            std::string datename = logname.substr(logname.find_last_of('_') + 1, 8);
            //std::cout << datename << std::endl;
            struct tm ptm = { 0 };
            ptm.tm_year = atoi(datename.substr(0, 4).c_str()) - 1900;
            ptm.tm_mon = atoi(datename.substr(4, 2).c_str()) - 1;
            ptm.tm_mday = atoi(datename.substr(6, 2).c_str());
            //现在时间
            auto tt = system_clock::to_time_t(system_clock::now());
            struct tm* ptmnow = localtime(&tt);
            int ret = (mktime(ptmnow) - mktime(&ptm)) / 24 / 3600;
            //std::cout << "天数差：" << ret << std::endl;
            if (ret >= date)
                return true;
            return false;
        };

        //通道路径类似./video/ch1
        std::string chIdPath = recordPath + "/ch" + std::to_string(channelId) + "/";
        std::string exname = ".flv";

        struct dirent* direntp;
        char childdir[255];
        DIR* dirp = opendir(chIdPath.c_str());
        if (dirp != NULL)
        {
            while ((direntp = readdir(dirp)) != NULL)
            {
                if (strcmp((direntp->d_name + (strlen(direntp->d_name) - 4)), exname.c_str()) == 0)
                {
                    if (filenameCheckTime(direntp->d_name, days))
                    {
                        //std::cout << "path-" << chIdPath.c_str() << direntp->d_name << std::endl;
                        remove(std::string(chIdPath + direntp->d_name).c_str());
                    }
                }
            }
            closedir(dirp);
        }
    };



    /**
    * @brief                        构造函数
    * @param[in] iChannelId         通道号
    * @param[in] iSeconds           单位秒
    * @param[in] pCodecInfo         解码相关参数
    */
    CmdRecorder::CmdRecorder(int iChannelId, int iSeconds, CodecInfoPtr pCodecInfo)
        :Recorder(RecorderType::Cmd)
    {
        setParameters(pCodecInfo);
        channelId = iChannelId;
        setSeconds(iSeconds);
    }

    bool CmdRecorder::createRecord(void)
    {
        return createFile(createFileName());
    }

    void CmdRecorder::checkFinished(void)
    {
        //判断结束录像任务
        if (frameIndex > videoLength || writeErrorNum > 100)    //如果连续超过100次写错误就关闭
        {
            if (writeErrorNum > 100)
                ai::LogError << "av_interleaved_write_frame error! | writeErrorNum > 100";
            closeFile();
        }
    }

    //设置录像时长
    void CmdRecorder::setSeconds(int iSeconds)
    {
        seconds = iSeconds;
        videoLength = seconds * getFrameRateNum();
    }

    //创建录像名称
    std::string CmdRecorder::createFileName(void)
    {
        std::chrono::system_clock clock;
        auto tSeconds = std::chrono::duration_cast<std::chrono::seconds>(clock.now().time_since_epoch());
        time_t secNow = tSeconds.count();
        auto tm = std::localtime(&secNow);
        std::stringstream ss;
        ss << std::put_time(tm, "%H%M%S");
        auto tMilli = std::chrono::duration_cast<std::chrono::milliseconds>(clock.now().time_since_epoch());
        auto ms = tMilli - tSeconds;
        ss << std::setfill('0') << std::setw(3) << ms.count();
        std::string newName = ss.str();
        newName = SETTINGS->recordPath() + "/ch" + std::to_string(channelId) + "_" + 
            std::to_string(seconds) + "s_" + newName + ".flv";
        //创建文件夹
#if defined (WIN32)
        int status = _chdir(SETTINGS->recordPath().c_str());
        if (status < 0)
        {
            status = _mkdir(SETTINGS->recordPath().c_str());
        }
#else
        if (access(SETTINGS->recordPath().c_str(), 0) != 0)
        {
            mkdir(SETTINGS->recordPath().c_str(), 0777);
        }
#endif
        return newName;
    }



    //构造函数
    VideoRecorder::VideoRecorder(StreamOuputType streamType) :StreamOutput(streamType)
    {
        recorders.reserve(10);   //事件最多2个+命令行1个=最多3个录像任务 这里留10个
    }

    //发数据
    void VideoRecorder::pushData(const PacketDataPtr packet)
    {
        if (!jobIsRunning())
            return ;

        if (codecInfo)
        {
            //保持预录像缓存数据
            mutexOutput.lock();
            if (packetDatas.size() > beforeBufNum)
                packetDatas.resize(beforeBufNum);  //TODO  存在风险。。 基于I帧范围 跳动控制，
            mutexOutput.unlock();
        }
        StreamOutput::pushData(packet);

        std::lock_guard<boost::fibers::mutex> lock(recorderMutex);
        for (auto& myrecoder : recorders)
        {
            myrecoder->pushData(packet);
        }
        if (taskRecorder)
            taskRecorder->pushData(packet);
    }

    //设置通道ID
    void VideoRecorder::setChannelId(int iChannelId)
    {
        channelId = iChannelId;
    }

    //设置预录像和事件录像时长
    void VideoRecorder::setBeforeAfterTime(int iBeforeTime, int iAfterTime)
    {
        beforeTime = iBeforeTime;
        afterTime = iAfterTime;
    }

    //事件开始录像
    std::string VideoRecorder::eventStart(std::string szWebUrl)
    {
        //没流录不了
        if (!jobIsRunning() || !codecInfo)
            return "";
        //判断录像是否可追加的处理 [根据业务recorders现在最多3个任务]
        {
            std::lock_guard<boost::fibers::mutex> lock(recorderMutex);
            for (auto& it : recorders)
            {
                if (it->getRecorderType() == RecorderType::Event)
                {
                    auto myrecoder = std::dynamic_pointer_cast<EventRecorder>(it);
                    if (myrecoder->isAddible(afterTime))
                    {
                        myrecoder->addTime(afterTime);
                        return myrecoder->urlPath();       //追加返回之前的url
                    }
                }
            }
        }

        ai::LogInfo << "eventStart channelId:"<<channelId<<", address:"<<address<<", szWebUrl:"<<szWebUrl<<"\n     codecInfo frameRate:"<<codecInfo->getFrameRate().num<<"/"<<codecInfo->getFrameRate().den
            <<"  timeBase:"<<codecInfo->getTimebase().num<<"/"<<codecInfo->getTimebase().den;

        //创建新录像对象
        auto taskRecoder = std::make_shared<EventRecorder>(address, szWebUrl,
            beforeTime,
            afterTime,
            codecInfo
            );
        //缓存数据传入录像对象
        {
            std::lock_guard<boost::fibers::mutex> lock(mutexOutput);
            //设置预录像缓存
            taskRecoder->setListData(packetDatas);
        }
        //创建任务 加锁
        {
            std::lock_guard<boost::fibers::mutex> lock(recorderMutex);
            recorders.emplace_back(taskRecoder);
        }
        return taskRecoder->urlPath();
    }

    //命令行保存录像
    bool VideoRecorder::cmdSaveStart(int second)
    {
        //没流录不了
        if (!jobIsRunning() || !codecInfo)
            return false;
        //限制命令行录像只有一个录像任务
        {
            std::lock_guard<boost::fibers::mutex> lock(recorderMutex);
            for (auto& it : recorders)
            {
                if (it->getRecorderType() == RecorderType::Cmd)
                {
                    return false;
                }
            }
        }
        //创建新录像对象
        auto cmdRecorder = std::make_shared<CmdRecorder>(channelId, second, codecInfo);
        //创建任务 加锁
        {
            std::lock_guard<boost::fibers::mutex> lock(recorderMutex);
            recorders.emplace_back(cmdRecorder);
            return true;
        }
    }

    //命令行录像手动停止
    bool VideoRecorder::cmdSaveStop(void)
    {
        std::lock_guard<boost::fibers::mutex> lock(recorderMutex);
        bool bRet(false);
        for (auto& it : recorders)
        {
            if (it->getRecorderType() == RecorderType::Cmd)
            {
                auto myrecoder = std::dynamic_pointer_cast<CmdRecorder>(it);
                myrecoder->setSeconds(0);
                bRet = true;
            }
        }
        return bRet;
    }

    //启动后台记录录像
    void VideoRecorder::bgRecordStart(void)
    {
        //根据 mChannelId 读取配置文件
        std::string recordCfg = std::string("./config/record.ini");
        if (access(recordCfg.c_str(), 0) == 0)
        {
            INI::File iniFile;
            iniFile.Load(recordCfg);
            auto sec = iniFile.FindSection("record");
            if (!sec)
                return;
            //解析配置文件参数
            auto begintime = sec->GetValue("begintime", "12:00 ").AsString();
            auto endtime = sec->GetValue("endtime", "14:00 ").AsString();
            int days = sec->GetValue("days", 3).AsInt();                    //保存录像天数
            //int section = sec->GetValue("section", 30).AsInt();           //间隔自动分段,单位分钟
            int section = sec->GetValue("section", 300).AsInt();            //间隔自动分段,单位MB
            auto channels = sec->GetValue("channels", "0").AsString();      //需要录像的通道(从1开始) 填1,2,3  或1-3
            if(!channels.empty()&& channels!="0")
            {
                std::cout << "Start record channels: " << channels << std::endl;
            }
            //检查ini录像的通道
            auto checkChannels = [](std::string str,int channelId)->bool
            {
                std::stringstream iss(str);
                std::string token;
                try
                {
                    while (getline(iss, token, ','))
                    {
                        if (token.find('-') == std::string::npos)
                        {
                            if (channelId == stoi(token))
                                return true;
                        }
                        else {
                            std::stringstream item(token);
                            std::string token2;
                            std::vector<std::string> twoStr;
                            while (getline(item, token2, '-'))
                            {
                                twoStr.emplace_back(token2);
                            }
                            if (twoStr.size() == 2)
                            {
                                for (int i = stoi(twoStr[0]); i <= stoi(twoStr[1]); i++)
                                {
                                    if (channelId == i)
                                        return true;
                                }
                            }
                        }
                    }
                }
                catch (...)
                {
                    std::cout << " check channels:" << str << std::endl;
                    std::cout << " channels like: channels = 1,2,3" << std::endl;
                    std::cout << "       or like: channels = 1-3" << std::endl;
                }
                return false;
            };

            //如果ini里面包含 channelId 就录像
            if (checkChannels(channels,channelId))
            {
                //创建后台记录型录像
                taskRecorder = std::make_shared<TaskRecorder>(channelId, begintime, endtime, days, section);
                ai::LogDebug << "                   TaskRecorder start!";
            }
        }
    }

    //处理函数
    void VideoRecorder::process()
    {
        bool bfirstCalcFps(true), bfirstSetParameter(true);
        while (jobIsRunning())
        {
            // 等待码流信息
            if (!codecInfo)
            {
                boost::this_fiber::sleep_for(std::chrono::milliseconds(20));
                continue;
            }
            if (bfirstCalcFps)
            {
                if (codecInfo->getFrameRate().den != 0)
                    beforeBufNum = beforeTime * codecInfo->getFrameRate().num / codecInfo->getFrameRate().den;
                bfirstCalcFps = false;
            }
            //录像任务[含命令行创建的]
            for (auto it = recorders.begin(); it != recorders.end();)
            {
                auto myrecoder = *it;
                //维护录像完成的操作
                if (myrecoder->isFinished())
                {
                    std::lock_guard<boost::fibers::mutex> lock(recorderMutex);
                    it = recorders.erase(it);
                }
                else {
                    myrecoder->process();
                    ++it;
                }
            }

            //守护录像任务 直到run结束
            if (taskRecorder)
            {
                if (bfirstSetParameter)
                {
                    taskRecorder->setParameters(codecInfo);
                    bfirstSetParameter = false;
                }
                taskRecorder->process();
            }

            if(recorders.size() > 0)
                boost::this_fiber::sleep_for(std::chrono::milliseconds(10));
            else
                boost::this_fiber::sleep_for(std::chrono::milliseconds(20));
        }
        //线程结束全部录像任务清理
        {
            std::lock_guard<boost::fibers::mutex> lock(recorderMutex);
            recorders.clear();           //全部停止
            taskRecorder.reset();        //删除日志型录像
            taskRecorder = nullptr;
        }
    }

}


