﻿#ifndef _WTOE_UDP_LIB_H
#define _WTOE_UDP_LIB_H

#include <string>
#include "wtoeSocketComm.h"

class SelectThread;
class MessageHandle;
class MessageSNSave;
// http 工具类
class WTOESOCKET_API WtoeUDPLib
{
public:
    WtoeUDPLib();
	~WtoeUDPLib();


//基本接口
public:
	void SetDestPort(int iPort);
	void SetDestIP(std::string strIp);
	int StartUDPServer( std::string Ip,unsigned short Port);
	int udpSendTo(std::string ip, unsigned short port, std::string strData);
	int GetPointList(const std::string& strPoint, std::vector<WTOEPoint> &vtPoint);
	int GetKeyValue( const std::string& szSrc, std::vector<Param>& vtValue );
	int GetIPAddress(std::vector<std::string> &vtIpList);

//业务接口
public:

	////////////////////////////////////////////////////////////fvm 向 iva 发送消息接口定义/////////////////////////////////////
	//3.1.1重置通道
	int	resetChannel(int iChannelID, std::string strIp = "", unsigned short uPort = 0);
	//3.1.2发送系统配置
	int configSystem(SystemConfig &sysConfig, std::string strIp = "", unsigned short uPort = 0);
	//3.1.3发送默认灵敏度参数
	int algorithmParam(AlgorithmParam & algorithmParam, std::string strIp = "", unsigned short uPort = 0);
	//3.1.4配置基本通道数据
	int configBasicChannel(std::vector<ChannelBasicConf> &vtChannelBasicConf, std::string strIp = "", unsigned short uPort = 0);
	//3.1.5发送通道检测参数
	int channelDetectParam(ChannelDetectParam &channelDetectParam, std::string strIp = "", unsigned short uPort = 0);
	//3.1.6恢复通道
	int restoreChannel(ChannelRestoreConf &channelRestoreConf, std::string strIp = "", unsigned short uPort = 0);
	//3.1.7暂停通道
	int pauseChannel( ChannelPauseConf& channelPauseConf, std::string strIp = "", unsigned short uPort = 0);
	
////////////////////////////////////////////////////////////fvm 向 iva 发送消息接口定义/////////////////////////////////////

	
////////////////////////////////////////////////////////////iva 向 fvm 发送消息接口定义/////////////////////////////////////
	//3.3.1请求初始化
	int requestInit(int index);
	//3.3.2上报事件
	int eventOccured(EventOccurInfo & eventOccurInfo);
	//3.3.3请求日夜切换
	int requestDayNight(RequestDayNightInfo &requestDayNightInfo);

////////////////////////////////////////////////////////////iva 向 fvm 发送消息接口定义/////////////////////////////////////



////////////////////////////////////////////////////////////节点 向 平台 发送消息接口定义/////////////////////////////////////
	//3.5.1节点向平台请求GB / T28181视频流
	int request28181Video(int iVideoID, std::string strDestAddr,std::string strPlatIP,int iPort, int iReturnPort );
	//3.5.2平台向节点回应请求GB/T28181视频流
	int request28181VideoResult(int iVideoID, std::string strDestAddr, int iResult, std::string strPlatIP, int iPort);
	//3.5.3节点向平台请求停止GB/T28181视频流
	int stop28181Video(int iVideoID, std::string strDestAddr, std::string strPlatIP, int iPort, int iReturnPort);
	//3.5.4平台向节点回应停止GB/T28181视频流
	int stop28181VideoResult(int iVideoID, std::string strDestAddr, int iResult, std::string strPlatIP, int iPort);
	//3.5.5节点向平台请求控制GB/T28181预置位
	int ptz28181Oper(int iVideoID, int iPresetID, std::string strPlatIP, int iPort);

////////////////////////////////////////////////////////////节点 向 平台 发送消息接口定义/////////////////////////////////////

////////////////////////////////////////////////////////////msm/gum 向 fvm发送消息回应 ////////////////////////////////////////
	int retRequestVideo( RequestVideoRet ret, std::string strIp = "", unsigned short uPort = 0);
	int retRequestStopVideo( RequestStopVideoRet ret, std::string strIp = "", unsigned short uPort = 0);
	int retRequestPtzOper( RequestPtzOperRet ret, std::string strIp = "", unsigned short uPort = 0);
	//通知前端视频有变化
	int sendRemoteChange( int remoteId, std::string strIp = "", unsigned short uPort = 0 );
	//通知前端状态变化
	int postRemoteStatus( int remoteId, int status, std::string strIp = "", unsigned short uPort = 0 );
//////////////////////////////////////////所有的回调设置////////////////////////////////////////////////////////////////
	void RegisterResetChannel(CBHandleResetChannel cb,void*pUserData);
	void RegisterConfigSystem(CBHandleConfigSystem cb, void*pUserData);
	void RegisterAlgorithmParam(CBHandleAlgorithmParam cb, void* pUserData);
	void RegisterBasicChannel(CBHandleconfigBasicChannel cb, void*pUserData);
	void RegisterPauseChannel(CBHandlePauseChannel cb, void*pUserData);
	void RegisterDetectParam(CBHandleDetectParam cb, void* pUserData);
	void RegisterRestoreChannel(CBHandleChannelRestore cb, void*pUserData);
	void RegisterRequestInit(CBHandleRequestInit cb, void*pUserData);
	void RegisterEventOccured(CBHandleEventOccured cb,void *pUserData);
	void RegisterRequestDayNight(CBHandleRequestDayNight cb,void *pUserData);

	//3.6.1新增/修改接入前端(SDK,28181)
	void RegisterHandleFVMChanged(CBHandleFVMChanged cb, void*pUserData);
	void RegisterHandleChannelChanged(CBHandleChannelChanged cb, void* pUserData);
	//3.6.2新增/修改接入前端(RTSP)
	void RegisterHandleRTSPChanged(CBHandleRTSPChanged cb, void*pUserData);
	//3.6.3请求视频查看
	void RegisterHandleRequestTempVideo(CBHandleRequestTempVideo cb, void*pUserData);
	//3.6.4请求设置检测区
	void RegisterHandleRequestSetVideo(CBHandleRequestSetVideo cb, void*pUserData);
	//3.6.5恢复/暂停视频资源
	void RegisterHandleVideoPause(CBHandleVideoPause cb, void*pUserData);
	//3.6.5.1恢复视频资源
	void RegisterHandleVideoResume(CBHandleVideoResume cb, void* pUserData);
	//3.6.6下发数据
	void RegisterHandleMonitorChanged(CBHandleMonitorChanged cb, void*pUserData);

	void RegisterHandleRequest28181Video(CBHandleRequest28181Video cb, void*pUserData);
	void RegisterHandleRequest28181VideoResult(CBHandleRequest28181VideoResult cb, void*pUserData);
	void RegisterHandleStop28181Video(CBHandleStop28181Video cb, void*pUserData);
	void RegisterHandleStop28181VideoResult(CBHandleStop28181VideoResult cb, void*pUserData);
	void RegisterHandlePtz28181Oper(CBHandlePtz28181Oper cb, void*pUserData);
	void RegisterMsgReceived(CBHandleMsgReceived cb, void*pUserData);

	//3.6.7请求IVA视频
	void RegisterRequestIvaVideo(CBHandleRequestIvaVideo cb,void*pUserData);
	//3.8.2停止结构化视频编码
	void RegisterStopIvaVideo(CBHandleStopIvaVideo cb, void* pUserData);

	void RegisterHandleRequestVideo( CBHandleRequestVideo, void* pUserData);
	void RegisterHandleRequestStopVideo( CBHandleRequestStopVideo, void* pUserData );
	void RegisterHandleRequestPtzOper( CBHandleRequestPtzOper, void* pUserData );
	void RegisterHandleRemoteChanged( CBHandleRemoteChanged, void* pUserData );

private:

	int port;
	std::string ip;
	SelectThread  *pSelectThread;
	MessageHandle *pMessageHandle;
	//MessageSNSave *pMessageSNSave;
};
#endif
