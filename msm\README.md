# MSM

* [目录说明](#目录说明)
* [自定义视频输入、输出](#自定义流元件)
* [编译步骤](#编译步骤)
* [安装步骤](#安装步骤)

## 目录说明
.<br>
├── bin 安装脚本
├── src
  ├── [app](app)    `程序入口`<br>
  ├── [DatabaseManaager](DatabaseManaagerMgr)    `数据库访问模块`<br>
  ├── [DataMgr](DataMgr)    `二代平台数据管理`<br>
  ├── [include](include)    `通用方法`<br>
  ├── [MsgHandler](MsgHandler)    `消息回调处理`<br>
  ├── [wtoeSocket](wtoeSocket)    `与FVM消息处理模块`<br>


```
```

## 编译步骤

### 1.依赖包: <br/>
&emsp;[boost 1.46.1 +](https://www.boost.org/)  <br>
&emsp;[ace 6.0.1 +]
&emsp;[MPC 2.10 +]
&emsp;[js 1.8.0 +]
&emsp; wtoe

1. /home/<USER>/3rdlibs <br>

### 2.编译msm下所有库
  进入src 直接运行make，即可编译所有库

## 安装步骤
  进入bin目录，在debug目录下有所有so和appD
  检查3rdlibs下有所有需要的依赖库，目前在虚拟机 192.168.77.174下，/home/<USER>/3rdlibsFfmpeg 和 /home/<USER>/3rdlibs 以及系统目录下
  运行 sh genBin.sh，即可生成压缩包  msm-1.0-20211125.tar.bz2  
  将压缩包解压到 /data/opt下，得到msm目录
  
cd /data/opt/msm
./install.sh
service msm restart 即可运行成screen