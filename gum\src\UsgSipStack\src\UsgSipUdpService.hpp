#ifndef SIPUDPSERVICE_HPP_
#define SIPUDPSERVICE_HPP_

#include <pjlib.h>
#include <pjsip.h>

#include "../include/UsgSipStackItf.hpp"
#include "../include/UsgSipStackCfg.hpp"

namespace usg {

    class CInviteSession;
    class CNotifySession;
    class CRegistSession;
    class CDdcpDoSession;

    class USGSIPSTACK_PRIVATE CUsgSipUdpService : public IUsgSipUdpService
    {
    public:
//    CUsgSipUdpService( const std::string &lAddr, uint16_t lPort, const std::string &lCode );
        CUsgSipUdpService( const std::string &lAddr, uint16_t lPort, const std::string &lCode, const std::string& xmlType );
        virtual ~CUsgSipUdpService();

    public:
        virtual bool init();
        virtual bool fini();
        virtual bool okey();

        virtual IInviteSession *inviteSession( ICtxHandler *handler );
        virtual INotifySession *notifySession( ICtxHandler *handler );
        virtual IRegistSession *registSession( ICtxHandler *handler );
        virtual IDdcpDoSession *ddcpDoSession( ICtxHandler *handler );

    private:
        static pj_bool_t onRxRequest( pjsip_rx_data *rdata );
        static pj_bool_t onRxResponse( pjsip_rx_data *rdata );

        static int handleEvent( void *arg );

    private:
        CInviteSession *m_inviteSession;
        CNotifySession *m_notifySession;
        CRegistSession *m_registSession;
        CDdcpDoSession *m_ddcpDoSession;

    private:
        const std::string m_addr; // 本地
        const uint16_t    m_port; // 本地
        const std::string m_code; // 本地
        std::string       m_from;

    private:
        pj_caching_pool m_cache;
        pj_pool_t      *m_pool;

    private:
        pj_sockaddr     m_lSock;
        pjsip_endpoint *m_endPoint;

    private:
        std::string     m_modStr;
        pjsip_module    m_module;

    private:
        pj_thread_t    *m_thread;
        bool            m_isExit;
        const std::string m_xmlType;
    private:
        pj_mutex_t     *m_mutex;
    };

}

#endif // SIPUDPSERVICE_HPP_
