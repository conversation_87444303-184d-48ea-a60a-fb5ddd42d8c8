/**
 * Project FVM
 */
#include <regex>
#include "data_manager.h"
#include "database_process.h"

#include "nv_aimonitor31_view.h"
#include "nv_aimonitor31_view-odb.hxx"
#include "video_source.h"
#include "video_source-odb.hxx"
#include "video_server.h"
#include "video_server-odb.hxx"
#include "video_logic.h"
#include "video_logic-odb.hxx"
#include "preset.h"
#include "preset-odb.hxx"
#include "monitor.h"
#include "monitor-odb.hxx"
#include "detection_point.h"
#include "detection_point-odb.hxx"
#include "preset_roi_feature-odb.hxx"

#include "util/network_util.h"
#include "util/config/fvm_config.h"
#include "ailog.h"

#include <boost/uuid/random_generator.hpp>
#include <boost/uuid/uuid_io.hpp>

using namespace boost::gregorian;
using namespace odb::core;

/**
 * DataManager implementation
 * @brief: 数据管理
 *          包含数据库配置更新读取及信号驱动等
 */
namespace fvm::data
{
    #define DEV_PLAT_IP "*************"
    #define GUM_PORT 5100
    #define MSM_PORT 5200

    DataManager::~DataManager()
    {
        db::deinit();
    }
    /**
     * @brief 初始化数据库连接，初始化配置数据
     */
    bool DataManager::init()
    {
        if (!db::init("root", "welltrans8746", SETTINGS->dbName(), SETTINGS->dbHost(), 3306)) {
            return false;
        }
        if (!this->updateParams()) {
            return false;
        }
        this->updateVideoSourcesInfo(false);

        //先把前端状态都改为离线
		setRemoteStatus(false, nullopt, nullopt);
		//再把文件流和rtsp状态改为在线，表示fvm启动
		setRemoteStatus(true, nullopt, (int)StreamInputType::File);
		setRemoteStatus(true, nullopt, (int)StreamInputType::RTSP);

        return true;
    }

    /**
     * @brief 初始化完成，发送下发配置信号
     */
    void DataManager::start()
    {
        this->onRestart(nullopt);
    }

    /**
     * @brief web下发配置，更新数据，发送下发配置信号
     */
    bool DataManager::update(const std::optional<int> &processId)
    {
        if (!this->updateParams()) {
            return false;
        }
        this->updateVideoSourcesInfo(false);
        this->onRestart(processId);

        //下发配置时可能有新增的文件流和rtsp，状态改为在线
        setRemoteStatus(true, nullopt, (int)StreamInputType::File);
        setRemoteStatus(true, nullopt, (int)StreamInputType::RTSP);

        return true;
    }

    /**
     * @brief 更新系统全局参数配置
     */
    bool DataManager::updateParams()
    {
        if (!updateParamProg()) {
            return false;
        }
        updateIvaProcess();
        updateEventTypes();
        updateCheckAreaTypes();
        updateAlgoParams();
        updateRemotes();
        updateProgram();

        return true;
    }

    /**
     * @brief      获取进程对应的通道视频资源
     * @param[in]  processId:进程id, nullopt 表示所有进程
     */
    VideoSrcInfoMap DataManager::getProcessChannelsSource(const std::optional<int> &processId)
    {
        VIDEO_INFO_LOCKGUARD;
        VideoSrcInfoMap processChannels;
        if (processId.has_value())
        {
            if (assignedChannels.find(processId.value()) != assignedChannels.end())
            {
                return assignedChannels.at(processId.value());
            }
            else
            {
                ai::LogError << "process " << processId.value() << " channel resource not found ";
                return processChannels;
            }
        }
        else
        {
            for (const auto&[processId, processVideoSrcInfo] : assignedChannels)
            {
                for (const auto&[channelId, videoSrcInfo] : processVideoSrcInfo)
                {
                    processChannels.emplace(channelId, videoSrcInfo);
                }
            }
            return processChannels;
        }
    }

    /**
     * @brief      获取channelId对应的视频资源
     */
    std::optional<VideoSourceInfoPtr> DataManager::getChannelSource(int channelId)
    {

        if (channelId <= 0)
        {
            ai::LogError << "Invalid channel id : " << channelId;
            return std::nullopt;
        }
        else
        {
            VIDEO_INFO_LOCKGUARD;
            for (const auto &processChannels : assignedChannels)
            {
                for (const auto&[_channelId, VideoSourceInfoPtr] : processChannels.second)
                {
                    if (_channelId == channelId)
                    {
                        return VideoSourceInfoPtr;
                    }
                }
            }
            ai::LogWarn << "Channel " << channelId << " resources not found";
            return std::nullopt;
        }
    }

    /**
     * @brief      获取videoId对应的通道资源
     */
    std::optional<VideoSourceInfoPtr> DataManager::getVideoChannelSource(int videoId)
    {

        if (videoId <= 0)
        {
            ai::LogError << "Invalid videoId id : " << videoId;
            return std::nullopt;
        }
        VIDEO_INFO_LOCKGUARD;
        auto it = std::find_if(videoSources.begin(), videoSources.end(),
                               [videoId](const std::pair<int, VideoSourceInfoPtr> &pair) {
                                   return (int) pair.second->videoSourcePtr->getId() == videoId;
                               });
        if (it == videoSources.end())
        {
            ai::LogError << "video " << videoId << " resource not found ";
            return std::nullopt;
        }
        return it->second;
    }

    /**
     * @brief     获取通道对应的进程id,找不到进程id,返回默认进程1
     */
    int DataManager::getChannelsProcessId(int channelId)
    {
        int processId = 1;
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        for (const auto &process : configParamInfo.processConfigs)
        {
            if (channelId >= process.getStartChannel() && channelId <= process.getEndChannel())
            {
                return (int) process.getId();
            }
        }
        return processId;
    }

    /**
     * @brief     获取程序配置参数
     * @param[in] key:配置参数key ,defaultVal:数据库中没有该参数时，使用该默认值
     */
    std::string DataManager::getParamProgData(const std::string &key, const std::string &defaultVal)
    {
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        for (const auto &paramProg : configParamInfo.paramProgData)
        {
            if (paramProg.paramKey == key)
            {
                return paramProg.paramValue;
            }
        }

        ai::LogInfo << key << " not found in wn_param_prog database, use default value " << defaultVal;
        db::ParamProgData param;
        param.paramKey = key;
        param.paramValue = defaultVal;
        configParamInfo.paramProgData.emplace_back(param);
        return defaultVal;
        /// TODO 写数据库

    }

    int DataManager::getParamProgData(const std::string& key, const int defaultVal)
    {
        int ret(0);
        try {
            ret = std::stoi(getParamProgData(key, std::to_string(defaultVal)));
        }
        catch (...) 
        {
            std::cerr << "getParamProgData key:"<< key <<" parse error!"<< std::endl;
        };
        return ret;
    }

    /**
     * @brief     获取事件类型配置
     */
    EventTypeVec DataManager::getEventTypes()
    {
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        return configParamInfo.eventTypes;
    }

    /**
     * @brief     获取检测区类型
     */
    CheckAreaTypeVec DataManager::getCheckAreaType()
    {
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        return configParamInfo.checkAreaTypes;
    }

    /**
     * @brief     获取时间方案配置
     */
    ProgramTimeVec DataManager::getProgram()
    {
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        return configParamInfo.programTimeVec;
    }


    /**
     * @brief     获取灵敏度配置参数
     */
    AlgoParamsVec DataManager::getAlgoParams()
    {
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        return configParamInfo.algoParams;
    }

    /**
     * @brief     获取iva进程配置信息
     * @param[in] processId: 获取指定进程id的进程配置，nullopt 则返回所有进程配置
     * @return    ProcessConfigsVec 返回的进程配置，empty, 则是单进程版本
     */
    ProcessConfigsVec DataManager::getProcessConfigs(const std::optional<int>& processId)
    {
        ProcessConfigsVec processConfig;
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        if (!processId.has_value())
            return configParamInfo.processConfigs;
        else if (processId.value() > 0)
        {
            for (const auto &process : configParamInfo.processConfigs)
            {
                if ((int) process.getProcessId() == processId)
                {
                    processConfig.clear();
                    processConfig.emplace_back(process);
                    break;
                }
            }
            if (!configParamInfo.processConfigs.empty() && processConfig.empty())
            {
                ai::LogError << " get process :" << processId.value() << " configs failed";
            }
        }
        return processConfig;
    }

    /**
     * @brief 更新进程配置等
     */
    void DataManager::updateIvaProcess()
    {
        std::vector<db::ProcessConfig> results = {};
        odb::query<db::ProcessConfig> query(odb::query<db::ProcessConfig>::id > 0);
        if (!db::queryData(results, query))
        {
            ai::LogError << "query ProcessConfig failed in database ! ";
            return;
        }

        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        configParamInfo.processConfigs = results;
    }

    /**
     * @brief      查询当前通道的可检测性
     */
    ChannelDetectable DataManager::queryChannelDetectable(int channelId)
    {
        ChannelDetectable detectable = ChannelDetectable::None;
        std::vector<db::DetectPointVideoSourceData> results{};
        odb::query<db::DetectPointVideoSourceData> query( odb::query<db::DetectPointVideoSourceData>::DetectionPoint::streamId == channelId);
        if (!db::queryData(results, query))
        {
            ai::LogError << "query VideoSource is_detectable failed in database ! channelId " << channelId;
            return detectable;
        }
        if (results.empty() || nullptr == results.at(0).videoSourcePtr)
            return detectable;

        detectable = static_cast<ChannelDetectable>(results.at(0).videoSourcePtr->getIsDetectable());
        return detectable;
    }

    /**
     * @brief      查询视频管理的可用性
     */
    bool DataManager::queryAccessFrontDetectable(int id)
    {
        std::vector<db::VideoServer> results = {};
        odb::query<db::VideoServer> query(odb::query<db::VideoServer>::id == id);
        bool ret = db::queryData(results, query);
        if (results.empty() || !ret)
            return false;
        return true;
    }

    /**
     * @brief      获取所有的sdk设备的接入前端表id wn_access_front_end上的id
     */
    void DataManager::queryGetPlatformId(std::vector<int>& ids)
    {
        std::vector<db::VideoServer> results = {};
        odb::query<db::VideoServer> query(odb::query<db::VideoServer>::accessType == 2); //accessType=2为sdk类型
        bool ret = db::queryData(results, query);
        for (auto& it:results)
        {
            ids.push_back(it.getId());
        }
    }


    /**
     * @brief 更新程序运行参数配置
     */
    bool DataManager::updateParamProg()
    {
        //! 查找FVM的配置参数
        std::vector<db::ParamProgData> results = {};
        odb::query<db::ParamProgData> queryFvm(odb::query<db::ParamProg>::program != "Web" );
        bool ret = db::queryData(results, queryFvm);

        if (results.empty() || !ret)
        {
            ai::LogError << "query FVM params failed in database ! ";
            return false;
        }

        bool findOffsetWaitTime = false;
        for (const auto &paramProg : results)
        {
            if (paramProg.paramKey == "mainIp")
            {
                std::string ip = paramProg.paramValue;
                if (ip.empty() || ip.find('.') == std::string::npos)
                {
                    ai::LogError << "no valid platform ip: " << ip;
                    return false;
                }
                if (ip == DEV_PLAT_IP)
                    ip = "";
                platformIP = ip;
            }
            else if (paramProg.paramKey == "FVM_port")
            {
                uint16_t port = std::stoi(paramProg.paramValue);
                if (port == 0)
                    port = 5000;
                platformPort = port;
            }
            else if (paramProg.paramKey == "IVA_port")
            {
                uint16_t port = std::stoi(paramProg.paramValue);
                if (port == 0)
                    port = 5002;
                ivaPort = port;
            }
            else if (paramProg.paramKey == "GUM_port")
            {
                std::string addr = paramProg.paramValue;
                uint16_t port = 0;
                std::string ip = "";
                size_t pos = addr.find(":");
                if (pos != std::string::npos)
                {
                    ip = addr.substr(0, pos);
                    port = std::stoi(addr.substr(pos + 1, addr.length()));
                }
                else
                    port = std::stoi(addr);
                if (port == 0)
                    port = GUM_PORT;
                gumPort = port;
                if (ip == "")
                    ip = platformIP;
                gumIP = ip;
            }
            else if (paramProg.paramKey == "MSM_port")
            {
                std::string addr = paramProg.paramValue;
                size_t pos = addr.find(":");
                uint16_t port = 0;
                std::string ip = "";
                if (pos != std::string::npos)
                {
                    ip = addr.substr(0, pos);
                    port = std::stoi(addr.substr(pos + 1, addr.length()));
                }
                else
                    port = std::stoi(addr);
                if (port == 0)
                    port = MSM_PORT;
                msmPort = port;
                if (ip == "")
                    ip = "127.0.0.1";
                msmIP = ip;
            }
            else if (paramProg.paramKey == FVM_OFFSET_WAIT_TIME)
            {
                findOffsetWaitTime = true;
            }
        }

        {
            std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
            configParamInfo.paramProgData = results;
        }
        //! 数据库种没有该字段，则新新插入一条
        if (!findOffsetWaitTime)
        {
            db::ParamProgData param = {FVM_OFFSET_WAIT_TIME,"10"};
            db::ParamProg paramProg(FVM_OFFSET_WAIT_TIME, param.paramKey, param.paramValue, "FVM", "OffsetWaitTime",false,false);
            if (db::insertData(paramProg) < 0)
                ai::LogError << "failed to insert OffsetWaitTime into wn_param_prog table !";
            else
            {
                std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
                configParamInfo.paramProgData.emplace_back(param);
            }
        }

        if (!updateLocalIp()) {
            return false;
        }
        updateMonitor(); //! 获取到localIp后，通过localIp获取当前设备的monitor参数

        return true;
    }

    /**
     * @brief 更新事件类型配置
     */
    void DataManager::updateEventTypes()
    {
        std::vector<db::EventType> results = {};
        odb::query<db::EventType> query = {};
        bool ret = db::queryData(results, query);
        if (results.empty() || !ret)
        {
            ai::LogError << "query wn_event_type failed in database ! ";
            return;
        }
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        configParamInfo.eventTypes = results;
    }

    /**
     * @brief 更新时间切换方案配置
     */
    void DataManager::updateProgram()
    {
        std::vector<db::Program> results{};
        odb::query<db::Program> query(odb::query<db::Program>::isDel != true);
        bool ret = db::queryData(results, query);
        if (results.empty() || !ret)
        {
            ai::LogError << "query wn_program failed in database ! ";
            return;
        }

        ProgramTimeVec programTimeVec;
        for (const auto& result : results)
        {
            try {
                ProgramTime programTime;
                programTime.startDate = from_simple_string("2000-" + result.getStartDate());
                programTime.morningTime = std::stoll(result.getMorningTime());
                programTime.eveningTime = std::stoll(result.getEveningTime());
                programTimeVec.emplace_back(programTime);
            }
            catch (...) {
                ai::LogError << "program parse failed  ! startTime:" << result.getStartDate() << " morningTime:" << result.getMorningTime() << " eveningTime:" << result.getEveningTime();
            }
        }

        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        configParamInfo.programTimeVec = programTimeVec;

    }

    /**
     * @brief 更新检测区域类型配置
     */
    void DataManager::updateCheckAreaTypes()
    {
        std::vector<db::CheckAreaType> results = {};
        odb::query<db::CheckAreaType> query = {};
        bool ret = db::queryData(results, query);
        if (results.empty() || !ret)
        {
            ai::LogError << "query wn_check_area_type failed in database ! ";
            return;
        }
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        configParamInfo.checkAreaTypes = results;
    }

    /**
     * @brief 更新灵敏度参数配置
     */
    void DataManager::updateAlgoParams()
    {
        std::vector<db::AlgoParamsData> results = {};
        typedef odb::query<db::AlgoParamsData> algoQuery;
        odb::query<db::AlgoParamsData> q((algoQuery::AlgParamPlan::planId > 0) + "ORDER BY" + algoQuery::AlgParamPlan::planId + "ASC");
        bool ret = db::queryData(results, q);
        if (results.empty() || !ret)
        {
            ai::LogError << "query AlgoParamsData failed in database ! ";
            return;
        }
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        configParamInfo.algoParams = results;
    }

    /**
     * @brief 更新视频接入前端等
     */
    void DataManager::updateRemotes(int remoteId)
    {
        std::vector<db::VideoServer> results = {};
        typedef odb::query<db::VideoServer> query;
        query q = {};
        if (-1 == remoteId)
        {
            q = query(query::id > 0 && query::isDel == false);
        }
        else
        {
            q = query(query::id == remoteId && query::isDel == false);
        }
        if (!db::queryData(results, q))
        {
            ai::LogError << "query VideoServer failed in database ! ";
            return;
        }
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        if (remoteId > 0)
        {
            auto it = std::find_if(configParamInfo.remotes.begin(), configParamInfo.remotes.end(),
                                   [remoteId](const db::VideoServer &server) {
                                       return (int) server.getId() == remoteId;
                                   });
            if (it != configParamInfo.remotes.end())
            {
                configParamInfo.remotes.erase(it);
            }

            if (!results.empty())
                configParamInfo.remotes.emplace_back(results.at(0));
        }
        else
        {
            configParamInfo.remotes = results;
        }

    }

    bool DataManager::updateMonitor()
    {
        std::vector<db::Monitor> results{};
        odb::query<db::Monitor> query(odb::query<db::Monitor>::isEnable == true);
        if (!db::queryData(results, query))
        {
            ai::LogError << "query wn_monitor failed in database ! ";
            return false;
        }
        int channelCount = 0;
        int firstMonitorChannelCnt = 0;
        int firstMonitorId = 0;
        int mainMonitorChannelCnt = 0;
        int mainMonitorId = 0;
        for (const auto &result : results)
        {
            //! 初始ip，直接返回
            if (result.getIp() == DEV_PLAT_IP)
            {
                channelCount = (int) result.getDetectPointCnt();
                monitorId = (int) result.getId();;
                break;
            }
            //! 找到本机ip
            if (result.getIp() == localIP)
            {
                channelCount = (int) result.getDetectPointCnt();
                monitorId = (int) result.getId();
                break;
            }

            //! 找到第一个 主monitor
            if (result.getIsMain() && mainMonitorId == 0)
            {
                mainMonitorChannelCnt = (int) result.getDetectPointCnt();
                mainMonitorId = (int) result.getId();
            }
            //! 找到第一个 monitor
            if (firstMonitorId == 0)
            {
                firstMonitorChannelCnt = (int) result.getDetectPointCnt();
                firstMonitorId = (int) result.getId();
            }
        }
        //! 没找到本机，可能是ip还没改
        if (channelCount == 0 || monitorId == 0)
        {

            if (mainMonitorChannelCnt > 0 && mainMonitorId > 0)  //!< 说明没有改过配置
            {
                channelCount = mainMonitorChannelCnt;
                monitorId = mainMonitorId;
            }
            else if (firstMonitorChannelCnt > 0 && firstMonitorId > 0)  //!< 使用第一monitor参数
            {
                channelCount = firstMonitorChannelCnt;
                monitorId = firstMonitorId;
            }
            else
            {
                ai::LogError << "Failed to find local Monitor, local ip :" << localIP;
                return false;
            }
        }
        return true;
    }

    /**
     * @brief     更新资源列表信息，以及关联的配置信息
     * @param[in] assigned: true查询分配了通道的视频资源 false查询所有视频资源
     * @param[in] videoId: 更新videoId对应的视频资源。可选参数，nullopt时，则根据其他条件更新视频资源
     * @param[in] remoteId 更新remoteId对应的视频资源。可选参数，nullopt时，则根据其他条件更新视频资源
     */
    bool DataManager::updateVideoSourcesInfo(bool assigned, std::optional<int> videoId, std::optional<int> remoteId)
    {

        std::vector<db::VideoSource> results = {};
        odb::query<db::VideoSource> query = {};
        if (assigned)
        {
            query = odb::query<db::VideoSource>((odb::query<db::VideoSource>::detectPointId > 0) + "ORDER BY" +
                                                odb::query<db::VideoSource>::detectPointId);
        }
        if (videoId.has_value())
        {
            query += odb::query<db::VideoSource>(odb::query<db::VideoSource>::id == videoId.value());
        }
        if (remoteId.has_value())
        {
            query += odb::query<db::VideoSource>(odb::query<db::VideoSource>::videoServerId == remoteId.value());
        }
        if (!db::queryData(results, query))
        {
            ai::LogError << "query wn_video_source failed in database ! ";
            return false;
        }

        if (results.empty())
        {
            ai::LogInfo << "no video resources found ";
            return false;
        }
        auto processChannels = queryMonitorChannelIds();
        VIDEO_INFO_LOCKGUARD;
        if (!videoId.has_value() && !remoteId.has_value())
        {
            if (!assigned)  //! 更新所有视频资源
            {
                videoSources.clear();
            }
            assignedChannels.clear();
        }
        for (auto &videoSrcData : results)
        {
            auto videoSrcPtr = std::make_shared<db::VideoSource>(videoSrcData);
            auto videoSrcInfoPtr = std::make_shared<VideoSourceInfo>();

            if (!queryVideoStreamType(videoSrcInfoPtr->streamInputType, (int) videoSrcPtr->getVideoServerId()))
                continue;

            queryDetectPointStreamId((int) videoSrcPtr->getDetectPointId(), videoSrcInfoPtr->channelId);
            videoSrcInfoPtr->videoSourcePtr = videoSrcPtr;

            if (videoSrcInfoPtr->channelId > 0)
            {
                for (const auto&[processId, channels] : processChannels)
                {
                    if (channels.find(videoSrcInfoPtr->channelId) != channels.end())
                    {
                        assignedChannels[processId].insert_or_assign(videoSrcInfoPtr->channelId, videoSrcInfoPtr);
                    }
                }
            }
            int _videoId = (int) videoSrcPtr->getId();
            videoSources[_videoId] = videoSrcInfoPtr;            //!< 所有视频资源信息

            //! 更新Preset
            if (!updatePresets(_videoId))
                return false;
        }
        return true;
    }

    /**
     * @brief      查询当前检测仪的detectPoint对应的streamId,即通道号
     * @param[in]  detectPointId: wn_video_source中对应的detect_point_id
     * @param[out] channelId: detectPoint对应的通道号
     */
    bool DataManager::queryDetectPointStreamId(int detectPointId, int& channelId)
    {
        if (detectPointId <= 0)  //!< 没有分配检测通道
            return false;

        std::vector<db::DetectionPoint> results = {};
        odb::query<db::DetectionPoint> query (odb::query<db::DetectionPoint>::id == detectPointId
                                              && odb::query<db::DetectionPoint>::monitorId == monitorId);
        if (!db::queryData(results, query))
        {
            ai::LogError << "query wn_detection_point with detectPointId " << detectPointId << " monitorId :" << monitorId << "failed in database ! ";
            return false;
        }
        if (results.empty())
            return false;

        channelId = (int)results.at(0).getStreamId();
        return true;
    }


    /**
     * @brief     获取当前视频资源的预置位
     */
    PresetPtr DataManager::getCurrPreset(const VideoSourceInfoPtr& videoInfoPtr)
    {
        if (!videoInfoPtr || !videoInfoPtr->videoSourcePtr)
            return nullptr;

        //! 获取当前的日期时间 月/日 以及 总秒数
        auto ptimeNow = boost::posix_time::second_clock::local_time();
        auto dateNow = date(2000, ptimeNow.date().month(), ptimeNow.date().day());  //!< 月日  注：只进行日月比较，所以统一转换成2000年，防止跨年时比较异常
        auto totalSeconds = ptimeNow.time_of_day().total_seconds();                    //!< 将当前时间(时/分/秒) 转成总秒数

        //! 首先查找日期信息  月/日
        PresetDateProgramInfo dateProgramInfos = videoInfoPtr->presetProgramInfos;
        PresetTimeProgramInfo timeProgramInfo;

        bool hasFindDate = false;
        if (dateProgramInfos.size() > 1)
        {
            for (auto itDate = dateProgramInfos.begin() ; std::next(itDate) != dateProgramInfos.end(); itDate++)
            {
                if (dateNow >= itDate->first && dateNow < std::next(itDate)->first)  //!< 当前日期是否在时间切换区间内，如不是，则使用后一个时间 eg: [1月16 ~ 6月1],在这个范围内则用1月16的时间，否则使用6月1日
                {
                    hasFindDate = true;
                    timeProgramInfo = itDate->second;
                    break;
                }
            }
        }
        //!< 没找到取最后一个
        if (!hasFindDate && !dateProgramInfos.empty())
            timeProgramInfo = std::prev(dateProgramInfos.end())->second;

        //! 查找时间信息  秒
        if (timeProgramInfo.size() > 1)
        {
            for (auto itTime = timeProgramInfo.begin(); std::next(itTime) != timeProgramInfo.end(); itTime++)
            {
                if (totalSeconds >= itTime->first && totalSeconds < std::next(itTime)->first)  //!< 当前日期是否在时间切换区间内，如不是，则使用后一个时间 eg: [7:00 ~ 23:00],在这个范围内则用7:00的时间，否则使用23:00
                {
                    return itTime->second;;
                }
            }
        }

        //!< 没找到取最后一个
        if (!timeProgramInfo.empty())
            return std::prev(timeProgramInfo.end())->second;

        if (dateProgramInfos.empty() || timeProgramInfo.empty())
        {
            ai::LogWarn << "no Time Program associated with videoId " << videoInfoPtr->videoSourcePtr->getId();
            if (videoInfoPtr->presetInfosMap.empty())
                return nullptr;
            else
                return videoInfoPtr->presetInfosMap.begin()->second.presetPtr;
        }

        return nullptr;
    }

    /**
     * @brief     获取当前视频资源的预置位id
     */
    int DataManager::getCurrPresetId(const VideoSourceInfoPtr& videoInfoPtr)
    {
        auto presetPtr = getCurrPreset( videoInfoPtr );
        if ( presetPtr )
            return presetPtr->getId();

        return 0;
    }

    /**
     * @brief     从字符串中解析预置位坐标（eg: x:0.0123|y:0.2356|z:0.789）
     */
    bool DataManager::splitPosition(const std::string& position, double& x, double& y, double& z)
    {
        try
        {
            std::regex re{R"(-?\d+(\.?\d+)?)"};    //!< 取数据库坐标字符串中的浮点数
            std::sregex_iterator pos(position.cbegin(), position.cend(), re);
            decltype(pos) end;

            std::vector<std::string> positions;
            for (; pos != end; ++pos)
            {
                positions.emplace_back(pos->str());
            }

            if (positions.size() == 3)
            {
                x =  std::stod(positions[0]);
                y =  std::stod(positions[1]);
                z =  std::stod(positions[2]);
                return true;
            }
        }
        catch(const std::exception& e)
        {
            ai::LogError <<"error:" << e.what();
            return false;
        }
        return false;
    }

    /**
     * @brief     获取视频资源当前预置位的基准坐标值
     */
    bool DataManager::getCurrPresetPosition(const VideoSourceInfoPtr& videoSrc, double& x, double& y, double& z)
    {
        auto presetPtr = getCurrPreset( videoSrc );
        if ( !presetPtr )
            return false;

        auto position = presetPtr->getPosition();
        if (position.empty())
            return false;

        if (!splitPosition(position, x,y,z))
        {
            ai::LogError << "failed to get actual preset:" << presetPtr->getActPreset() << " position:" << position << " channel:"<< videoSrc->channelId;
            return false;
        }
        return true;
    }

    /**
     * @brief     获取通道对应预置位的坐标
     */
    bool DataManager::getPresetPosition(int channelId, int actPresetId, double& x, double& y, double& z)
    {

        auto channelSource = DATA_MANAGER.getChannelSource(channelId);
        if (!channelSource.has_value() || !channelSource.value())
            return false;

        auto& videoSrcInfoPtr = channelSource.value();

        for (const auto &[presetId, presetInfo] : channelSource.value()->presetInfosMap)
        {
            if (!presetInfo.presetPtr)
                return false;

            if ((int)presetInfo.presetPtr->getActPreset() == actPresetId)
            {
                auto position = presetInfo.presetPtr->getPosition();
                if (position.empty())
                    return false;
                if (!splitPosition(position, x,y,z))
                {
                    ai::LogError << "failed to get actual preset:" << presetInfo.presetPtr->getActPreset() << " position:" << position << " channel:"<< channelId;
                    return false;
                }
                return true;
            }
        }
        ai::LogError << "failed to find actual preset:" << actPresetId << " channel:" << channelId;
        return false;
    }

    /**
     * @brief     往videoId对应的表wn_preset中presetId记录写入预置位基准坐标
     */
    bool DataManager::writePresetPosition(int videoId, int presetId, double x, double y, double z)
    {

        auto videoSource = DATA_MANAGER.getVideoChannelSource(videoId);
        if (!videoSource.has_value() || !videoSource.value() || !videoSource.value()->videoSourcePtr)
            return false;

        auto& videoSrcInfoPtr = videoSource.value();
        if (videoSrcInfoPtr->presetInfosMap.find(presetId) == videoSrcInfoPtr->presetInfosMap.end())
            return false;

        auto presetPtr = videoSrcInfoPtr->presetInfosMap[presetId].presetPtr;
        if (!presetPtr)
            return false;

        std::string position = "x:" + std::to_string(x) + "|" + "y:" + std::to_string(y) + "|" + "z:" + std::to_string(z);
        presetPtr->setPosition(position);

        if (!db::updateData(*presetPtr))
        {
            ai::LogError << "write position:"<< position << " failed, videoId " << videoId << " presetId " << presetId;
            return false;
        }
        videoSrcInfoPtr->videoSourcePtr->setIsChange(true);

        if (!db::updateData(*videoSrcInfoPtr->videoSourcePtr))
        {
            ai::LogError << "write wn_video_source failed, video id " << videoSrcInfoPtr->videoSourcePtr->getId();
            return false;
        }
        ai::LogInfo << "write position:" << position << " videoId " << videoId << " presetId " << presetId;
        return true;
    }


    /**
     * @brief     判断当前视频对应预置位是否有检测区和感兴趣区
     */
    bool DataManager::hasCheckAreaRoi(const VideoSourceInfoPtr& videoSrc, int presetId)
    {
        if (!videoSrc)
            return false;

        if (videoSrc->presetInfosMap.find(presetId) == videoSrc->presetInfosMap.end())
            return false;

        auto currentPresetPtr = videoSrc->presetInfosMap[presetId].presetPtr;
        if (!currentPresetPtr)
        {
            ai::LogWarn << "channel:"<< videoSrc->channelId << " no preset";
            return false;
        }
        if (currentPresetPtr->getCheckArea().empty())
        {
            ai::LogWarn << "channel:"<< videoSrc->channelId << " current preset: " << currentPresetPtr->getId() << " no check area";
            return false;
        }
        auto roiInfosMap = videoSrc->presetInfosMap[presetId].roiInfosMap;
        if (roiInfosMap.empty())
        {
            ai::LogWarn << "channel:"<< videoSrc->channelId << " current preset: " << currentPresetPtr->getId() << " no roi config";
            return false;
        }

        for (auto& [id, roiInfo] : roiInfosMap)
        {
            if (!roiInfo.roiPtr)
                continue;
            if (!roiInfo.roiPtr->getCheckArea().empty())
                return true;
        }
        ai::LogWarn << "channel:"<< videoSrc->channelId << " current preset: " << currentPresetPtr->getId() << " no roi check area";
        return false;
    }

    /**
     * @brief 更新预置位信息，以及预置位下的关联配置
     */
    bool DataManager::updatePresets(int videoId)
    {

        std::vector<db::Preset> results = {};
        odb::query<db::Preset> query(odb::query<db::Preset>::videoId == videoId);
        if (!db::queryData(results, query))
        {
            ai::LogError << "query Preset associated with videoId " << videoId << " failed in wn_preset database ! ";
            return false;
        }

        auto videoSrcInfoPtr = videoSources[videoId];
        if (videoSrcInfoPtr != nullptr)
        {
            for (const auto &result : results)
            {
                auto presetPtr = std::make_shared<db::Preset>(result);
                int presetId = (int) result.getId();
                videoSrcInfoPtr->presetInfosMap[presetId].presetPtr = presetPtr;

                updatePrograms(videoId, presetId); //!< 更新Programs
                updateOffsets(videoId, presetId);  //!< 更新偏移检测区Offsets
                updateRois(videoId, presetId);     //!< 更新感兴趣区ROI
                updateLaneLine(videoId, presetId); //!< 更新车道线配置
            }
        }
        return true;
    }

    /**
     * @brief 更新预置位运行信息
     */
    bool DataManager::updatePrograms(int videoId, int presetId)
    {

        std::vector<db::PresetProgramData> results = {};
        odb::query<db::PresetProgramData> query(odb::query<db::PresetProgramData>::Preset::id == presetId);
        if (!db::queryData(results, query))
        {
            ai::LogError << "query Program associated with presetId " << presetId << " failed in database ! ";
            return false;
        }
        auto videoSrcInfoPtr = videoSources[videoId];
        if (videoSrcInfoPtr != nullptr)
        {
            for (const auto &result : results)
            {
                int programInfoId = (int) result.programInfoPtr->getId();
                std::string startDateStr = result.programPtr->getStartDate();
                std::string switchTimeStr = result.programInfoPtr->getSwitchTime();
                videoSrcInfoPtr->presetInfosMap[presetId].programInfosMap[programInfoId].id = programInfoId;
                videoSrcInfoPtr->presetInfosMap[presetId].programInfosMap[programInfoId].startDate = startDateStr;
                videoSrcInfoPtr->presetInfosMap[presetId].programInfosMap[programInfoId].switchTime = switchTimeStr;

                //! 将时间切换方案组成 map<日期 <时间，preset>>，方便判断 eg: <1-16, <07:00, presetPtr>> , <6-1, <10:38, presetPtr>>
                auto startDate = from_simple_string("2000-" + startDateStr);   //!< 注：只进行日月比较，所以统一转换成2000年，防止跨年时比较异常
                uint32_t switchTime = std::stoll(switchTimeStr);
                videoSrcInfoPtr->presetProgramInfos[startDate].emplace(switchTime, videoSrcInfoPtr->presetInfosMap[presetId].presetPtr);
            }
        }
        return true;
    }

    /**
     * @brief 更新预置位下的roi信息，以及偏移检测区，通过view一次关联读取
     */
    bool DataManager::updatePresetAreas(int videoId, int presetId)
    {

        std::vector<db::PresetAreasData> results = {};
        odb::query<db::PresetAreasData> query(odb::query<db::PresetAreasData>::Preset::id == presetId);
        if (!db::queryData(results, query))
        {
            ai::LogError << "query PresetAreasData associated with presetId " << presetId << " failed in database ! ";
            return false;
        }

        auto videoSrcInfoPtr = videoSources[videoId];
        if (videoSrcInfoPtr != nullptr)
        {
            for (const auto &result : results)
            {
                int roiId = (int) result.presetRoiPtr->getId();
                int offsetId = (int) result.presetOffsetPtr->getId();
                videoSrcInfoPtr->presetInfosMap[presetId].roiInfosMap[roiId].roiPtr = result.presetRoiPtr;
                videoSrcInfoPtr->presetInfosMap[presetId].offsetAreasMap[offsetId] = result.presetOffsetPtr;
            }
        }
        return true;
    }

    /**
     * @brief 更新预置位下的偏移检测区
     */
    bool DataManager::updateOffsets(int videoId, int presetId)
    {

        std::vector<db::PresetOffsetData> results = {};
        odb::query<db::PresetOffsetData> query(odb::query<db::PresetOffsetData>::PresetOffset::presetId == presetId);
        if (!db::queryData(results, query))
        {
            ai::LogError << "query PresetOffset associated with presetId " << presetId
                         << " failed in wn_preset_offset database ! ";
            return false;
        }

        auto videoSrcInfoPtr = videoSources[videoId];
        if (videoSrcInfoPtr != nullptr)
        {
            for (const auto &result : results)
            {
                int offsetId = (int) result.presetOffsetPtr->getId();
                videoSrcInfoPtr->presetInfosMap[presetId].offsetAreasMap[offsetId] = result.presetOffsetPtr;
            }
        }
        return true;
    }

    /**
     * @brief 更新预置位下的车道线配置
     */
    bool DataManager::updateLaneLine(int videoId, int presetId)
    {
        std::vector<db::LaneLineData> results = {};
        odb::query<db::LaneLineData> query(odb::query<db::LaneLineData>::PresetLaneLine::presetId == presetId);
        if (!db::queryData(results, query))
        {
            ai::LogError << "query LaneLineData associated with presetId " << presetId
                         << " failed in wn_preset_lane_line database ! ";
            return false;
        }

        auto videoSrcInfoPtr = videoSources[videoId];
        if (!videoSrcInfoPtr)
            return false;

        for (const auto &result : results)
        {
            videoSrcInfoPtr->presetInfosMap[presetId].presetLaneLinePtr = result.presetLaneLinePtr;
            break;
        }
        return true;
    }

    /**
     * @brief 更新预置位下的roi信息
     */
    bool DataManager::updateRois(int videoId, int presetId)
    {
        std::vector<db::PresetRoiData> results = {};
        odb::query<db::PresetRoiData> query(odb::query<db::PresetRoiData>::PresetRoi::presetId == presetId);
        if (!db::queryData(results, query))
        {
            ai::LogError << "query PresetRoi associated with presetId " << presetId
                         << " failed in wn_preset_roi database! ";
            return false;
        }

        auto videoSrcInfoPtr = videoSources[videoId];
        if (videoSrcInfoPtr != nullptr)
        {
            for (const auto &result : results)
            {
                int roiId = (int) result.presetRoiPtr->getId();
                videoSrcInfoPtr->presetInfosMap[presetId].roiInfosMap[roiId].roiPtr = result.presetRoiPtr;
                updateFeatureArea(videoId, presetId, roiId);  //!< 更新特征提取区域
                updateLanes(videoId, presetId, roiId);        //!< 更新车道信息
                updateCheckAreas(videoId, presetId, roiId);   //!< 更新子区域信息
            }
        }
        return true;
    }

    /**
     * @brief 更新roi下的子区域信息
     */
    bool DataManager::updateCheckAreas(int videoId, int presetId, int roiId)
    {
        std::vector<db::ROICheckAreaData> results = {};
        odb::query<db::ROICheckAreaData> query(odb::query<db::ROICheckAreaData>::PresetRoi::id == roiId);
        if (!db::queryData(results, query))
        {
            ai::LogError << "query CheckArea associated with roiId " << roiId
                         << " failed in wn_preset_check_area database! ";
            return false;
        }

        auto videoSrcInfoPtr = videoSources[videoId];
        if (videoSrcInfoPtr != nullptr)
        {
            for (const auto &result : results)
            {
                int checkAreaId = (int) result.presetCheckAreaPtr->getId();
                videoSrcInfoPtr->presetInfosMap[presetId].roiInfosMap[roiId].checkAreasMap[checkAreaId] = result.presetCheckAreaPtr;
            }

        }
        return true;
    }

    /**
     * @brief 更新roi下的特征提取区域
     */
    bool DataManager::updateFeatureArea(int videoId, int presetId, int roiId)
    {
        std::vector<db::PresetRoiFeature> results = {};
        odb::query<db::PresetRoiFeature> query(odb::query<db::PresetRoiFeature>::roiId == roiId);
        if (!db::queryData(results, query))
        {
            ai::LogError << "query feature roi associated with roiId " << roiId << " failed in wn_preset_lane database! ";
            return false;
        }

        auto videoSrcInfoPtr = videoSources[videoId];
        if (videoSrcInfoPtr != nullptr)
        {
            for (const auto &result : results)
            {
                videoSrcInfoPtr->presetInfosMap[presetId].roiInfosMap[roiId].featureArea = std::make_shared<db::PresetRoiFeature>(result);
                break;
            }
        }
        return true;
    }


    /**
     * @brief 更新roi下的车道信息
     */
    bool DataManager::updateLanes(int videoId, int presetId, int roiId)
    {
        std::vector<db::ROILaneData> results = {};
        odb::query<db::ROILaneData> query(odb::query<db::ROILaneData>::PresetRoi::id == roiId);
        if (!db::queryData(results, query))
        {
            ai::LogError << "query Lane associated with roiId " << roiId << " failed in wn_preset_lane database! ";
            return false;
        }

        auto videoSrcInfoPtr = videoSources[videoId];
        if (videoSrcInfoPtr != nullptr)
        {
            for (const auto &result : results)
            {
                int laneId = (int) result.presetLanePtr->getId();
                videoSrcInfoPtr->presetInfosMap[presetId].roiInfosMap[roiId].lanesMap[laneId] = result.presetLanePtr;
            }
        }
        return true;
    }

    /**
     * @brief       根据前端ID获取视频接入类型
     * @param[in]   frontEndID: 前端id
     * @param[out]  type: 前端接入类型
     */
    bool DataManager::queryVideoStreamType(StreamInputType& type, int frontEndID)
    {
        std::vector<db::VideoServer> results = {};
        odb::query<db::VideoServer> query(odb::query<db::VideoServer>::id == frontEndID);
        bool ret = db::queryData(results, query);
        if (results.empty() || !ret)
        {
            ai::LogError << "query access_type associated with frontEndID " << frontEndID
                         << " failed in wn_access_front_end database! ";
            return false;
        }

        type = (StreamInputType) results.at(0).getAccessType();

        return true;
    }

    /**
     * @brief       查询当前检测仪上的所有检测通道，并保存到进程表中
     * @return      map[processId, channelIds],进程对应的所有通道号
     */
    std::map<int, std::set<int>> DataManager::queryMonitorChannelIds()
    {
        std::map<int, std::set<int>> processChannels{};
        std::vector<db::DetectionPoint> detectionPoints{};
        odb::query<db::DetectionPoint> q(odb::query<db::DetectionPoint>::monitorId == this->monitorId);

        if (!db::queryData(detectionPoints, q))
        {
            ai::LogError << "query wn_detection_point failed in database ! ";
            return processChannels;
        }

        if (detectionPoints.empty())
        {
            ai::LogInfo << "Failed to find detection_point of the monitor :" << monitorId;
            return processChannels;
        }
        auto processConfigs = getProcessConfigs(nullopt);
        for (const auto &detectionPoint : detectionPoints)
        {
            //int channelId = (int) detectionPoint.getId();
            int channelId = (int) detectionPoint.getStreamId();
            if (processConfigs.empty())//!< 单进程版本
            {
                processChannels[1].emplace(channelId);
            }
            else //!< 多进程版本
            {
                for (const auto &process : processConfigs)
                {
                    if (channelId >= process.getStartChannel() && channelId <= process.getEndChannel())
                    {
                        processChannels[(int) process.getId()].emplace(channelId);
                    }
                }
            }
        }
        return processChannels;
    }

    /**
     * @brief       将进程对应的通道的视频以及未分配通道号的视频的数据库wn_video_source表的is_change字段改写为0，表示已经读取了变化的信息
     * @param[in]   processId: 进程id
     */
    void DataManager::writeProcessVideoChange(const std::optional<int> &processId)
    {
        auto channelSources = getProcessChannelsSource(processId);
        for (auto &channelSource: channelSources)
        {
            if (nullptr == channelSource.second)
                return;
            if (nullptr == channelSource.second->videoSourcePtr)
                return;

            if (channelSource.second->videoSourcePtr->getIsChange())
            {
                channelSource.second->videoSourcePtr->setIsChange(false);
                if (!db::updateData(*channelSource.second->videoSourcePtr))
                {
                    ai::LogError << "write wn_video_source failed, video id "
                                 << channelSource.second->videoSourcePtr->getId();
                }
            }
        }

        //! 更新为分配的通道的视频的is_change字段
        auto allVideoSources = getVideoSources();
        for (auto& [videoId, videoSourceInfoPtr] : allVideoSources)
        {
            if (!videoSourceInfoPtr || !videoSourceInfoPtr->videoSourcePtr)
                continue;

            if ((videoSourceInfoPtr->channelId <= 0) && (videoSourceInfoPtr->videoSourcePtr->getIsChange()))
            {
                videoSourceInfoPtr->videoSourcePtr->setIsChange(false);
                if (!db::updateData(*videoSourceInfoPtr->videoSourcePtr))
                {
                    ai::LogError << "write wn_video_source failed, video id "
                                 << videoId;
                }
            }
        }
    }

    /**
     * @brief       将数据库wn_video_source表的is_change字段改写为0，表示已经读取了变化的信息
     * @param[in]   videoId: 视频id
     */
    void DataManager::writeVideoChange(int videoId)
    {
        auto videoSrcInfoPtr  = getVideoChannelSource(videoId);
        if (!videoSrcInfoPtr.has_value() || !videoSrcInfoPtr.value()->videoSourcePtr)
            return;

        auto videoSrcPtr = videoSrcInfoPtr.value()->videoSourcePtr;
        if (videoSrcPtr->getIsChange())
        {

            videoSrcPtr->setIsChange(false);
            if (!db::updateData(*videoSrcPtr))
            {
                ai::LogError << "write wn_video_source failed, video id "
                             << videoSrcPtr->getId();
            }
        }
    }
    /**
     * @brief       根据视频接入前端remoteId或者接入类型streamType设置视频接入前端服务器状态 （数据库wn_access_front_end表的status字段）
     * @param[in]   online: 视频接入前端状态
     * @param[in]   remoteId: 视频接入前端id,可选参数 nullopt，则根据streamType查询设置
     * @param[in]   streamType: 视频接入前端类型,可选参数 nullopt，则根据remoteId查询设置
     */
    void DataManager::setRemoteStatus(bool online, const std::optional<int>& remoteId, const std::optional<int>& streamType)
    {
        if (!online && !remoteId.has_value() && !streamType.has_value())
        {
			std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
            for (auto& r : configParamInfo.remotes)
            {
				r.setStatus((int)online);
				if (!db::updateData(r))
				{
					ai::LogError << "write wn_access_front_end failed";
				}
            }
            return;
        }
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        for (auto& r : configParamInfo.remotes)
        {
            if ((!remoteId.has_value() || (int)r.getId() == remoteId.value())
                && (!streamType.has_value() || (int)r.getAccessType() == streamType.value()))
            {
                if ((int)online != r.getStatus())
                {
                    r.setStatus((int)online);
                    if (!db::updateData(r))
                    {
                        ai::LogError << "write wn_access_front_end failed";
                    }
                }
            }
        }
    }

    bool DataManager::updateLocalIp()
    {
        std::vector<std::string> localIps;
        network::util::getLocalIp(localIps);

        if (localIps.empty())
        {
            ai::LogError << "Failed to read local ip";
            return false;
        }
        else
        {
            std::ostringstream localIpsStr;
            for (auto& ipStr : localIps)
            {
                localIpsStr << "  " << ipStr;
            }
            ai::LogInfo << "IP CONFIG: \n" << localIpsStr.str();
        }

        if (platformIP.empty() || platformIP.find('.') == std::string::npos)
        { //! 平台地址格式不对 , 找第一个(非127.0.0.1)本地ip
            for (const auto &_localIP : localIps)
            {
                if (_localIP == "127.0.0.1")  //! 不处理该ip
                    continue;
                platformIP = _localIP;
                localIP = platformIP;
            }
            if (gumIP == "")
                gumIP = platformIP;
        }
        else
        {
            std::string ip;
            std::size_t pos = platformIP.length();
            std::size_t compare = platformIP.find('.');
            for (const auto &_localIP : localIps)
            {
                if (_localIP == "127.0.0.1")  //! 不处理该ip
                    continue;

                if (DEV_PLAT_IP == platformIP)
                {
                    ai::LogInfo << "CHANGE PLAT_IP TO LOCAL_IP : " << _localIP;
                    platformIP = _localIP;
                    localIP = _localIP;
                    return true;
                }

                if (_localIP == platformIP)
                {
                    ip = _localIP;
                    break;
                }

                if (ip.empty())
                    ip = _localIP;  //! 先设置第一个IP
                size_t match = 0;
                std::size_t len = _localIP.length();
                for (size_t i = 0, j = 0; i < pos && j < len; i++, j++)
                {
                    if (platformIP[i] != _localIP[j])
                    {
                        if (match > compare)
                        {
                            compare = match;
                            ip = _localIP;
                        }
                        break;
                    }
                    else
                        match++;
                }
            }
            pos = ip.find(':');
            if (pos != std::string::npos)
                ip = ip.substr(0, pos);
            
            ai::LogInfo << "Local IP read from system: " << ip;
            if (!ip.empty() && (ip != "127.0.0.1"))
            {
                localIP = ip;
            }

            if (localIP.empty())
            {
                localIP = SETTINGS->localIP();
                ai::LogWarn << "Failed to read local network IP, fallback to default setting: " << localIP;
            }
        }

        if (platformIP.empty() || localIP.empty())
            ai::LogError << "Failed to read platform IP " << platformIP << " or local IP " << localIP;
        else
            ai::LogInfo << "platform IP is : " << platformIP << " local IP is : " << localIP;

        if (localIP.empty()) {
            return false;
        }

        return true;
    }

    VideoServerPtr DataManager::getVideoServer(int remoteId)
    {
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        for (auto &server : configParamInfo.remotes)
        {
            if (server.getId() == remoteId)
            {
                return std::make_shared<db::VideoServer>(server);
            }
        }
        return data::VideoServerPtr();
    }

    uint16_t DataManager::getIvaPort(int processId)
    {
        std::lock_guard<boost::fibers::recursive_mutex> lock(mutexConfigInfo);
        if (configParamInfo.processConfigs.empty())
            return ivaPort;
        else
        {
            auto itr = std::find_if(configParamInfo.processConfigs.begin(), configParamInfo.processConfigs.end(), [processId](db::ProcessConfig& processCfg){
                return (int)processCfg.getProcessId() == processId;
            });
            if (itr != configParamInfo.processConfigs.end())
            {
                return itr->getProcessPort();
            }
            else
            {
                ai::LogError << "get process " << processId << " iva port failed";
                return ivaPort;
            }
        }
    }

    /**
     * @brief     更新视频接入平台状态
     * @param[in]   id: 视频接入前端id
     * @param[in]   status: 视频接入平台状态 1-在线 0-不在线
     * @return      返回成功 失败
     */
    bool DataManager::updateVideoServerInfo(int id, int status)
    {
        //更新平台状态为在线 页面的状态会变化
        auto accessFront = getVideoServer(id);
        if (!accessFront)
            return false;
        accessFront->setStatus(status);
        if (!db::updateData(*accessFront))
        {
            ai::LogError << "write wn_access_front_end failed, video id =" << id;
            return false;
        }
        return true;
    }

    /**
     * @brief      从video name字段中获取桩号
     */
    std::string DataManager::getLocation(const std::string& name)
    {
        std::string szLocation;
        {
            std::vector<std::string> vecTmp;
            std::string szTmp;
            std::string szSplit;
            bool foundK = false;
            for (size_t i = 0; i < name.length(); i++)
            {
                char ch = name[i];
                if (ch == 'K')
                {
                    szTmp = "";
                    vecTmp.clear();
                    foundK = true;
                }
                else if (foundK)
                {
                    std::string sch = name.substr(i, 1);
                    if (ch >= '0' && ch <= '9')
                        szTmp += sch;
                    else if (ch == '+' || ch == '-')
                    {
                        if (!szSplit.empty() || szTmp.empty())  //出错，不处理
                        {
                            vecTmp.clear();
                            break;
                        }
                        szSplit = sch;
                        vecTmp.push_back(szTmp);
                        szTmp = "";
                    }
                }
            }

            if (vecTmp.size() == 1)
            {
                szLocation = "K" + vecTmp[0];
                if (!szSplit.empty() && !szTmp.empty())
                    szLocation += szSplit + szTmp.substr(0, 3);
            }
        }
        return szLocation;
    }

    /**
     * @brief     插入新的视频通道 如果已存在自动忽略
     * @param[in]   id: 视频接入前端id
     * @param[in]   szName: 视频名称
     * @param[in]   szAddr: 视频地址
     */
    void DataManager::insertVideoSource(int id, std::string szName, std::string szAddr)
    {
        //先根据ID和szAddr查下数据是否存在
        odb::query<db::VideoSource> q(odb::query<db::VideoSource>::videoServerId == id
            && odb::query<db::VideoSource>::address == szAddr);
        std::vector<db::VideoSource> vecResult = {};
        queryData(vecResult, q);
        //wn_access_front_end表获取
        VideoServerPtr serverPtr = getVideoServer(id);

        //wn_video_source表中是否已存在 且 关联表必须有效
        if (vecResult.empty() && serverPtr)
        {
            auto location = getLocation(szName);
            db::VideoSource videodata(szName, szAddr, id, location, false, false, 0, "", 0, 0, false, false);
            int newId = db::insertData(videodata);
            if (newId > -1)
            {
                ai::LogTrace << "HK insertVideo " << newId << " NAME " << szAddr;
                //获取生成的ID 处理wn_video_logic表
                std::string szVideoId = std::to_string(newId);
                db::VideoLogic videologic1(szVideoId + "-Area1->2", newId, 1);
                db::insertData(videologic1);
                db::VideoLogic videologic2(szVideoId + "-Area2->1", newId, 2);
                db::insertData(videologic2);
            };
        }
    }

    /**
     * @brief   (二代平台)  插入新的视频通道 如果已存在自动忽略
     * @param[in]   id: 视频接入前端id
     * @param[in]   szName: 视频名称
     * @param[in]   szAddr: 视频地址
     * @param[in]   szCode: 编码信息
     */
    void DataManager::insertVideoSource(int id, std::string szName, std::string szAddr, std::string szCode)
    {
        odb::query<db::VideoSource> q(odb::query<db::VideoSource>::videoServerId == id
            && odb::query<db::VideoSource>::address == szAddr);
        std::vector<db::VideoSource> vecResult = {};
        queryData(vecResult, q);
        VideoServerPtr serverPtr = getVideoServer(id);

        //wn_video_source表中是否已存在 且 关联表必须有效
        if (vecResult.empty() && serverPtr)
        {
            auto location = getLocation(szName);

            std::string groupUUID = "";
            int domePreset = 0;
            int videoType = 0;
            if (szCode != "")
            {
                size_t pos = szCode.find('#');
                if (pos != std::string::npos) {
                    std::string ip = szCode.substr(0, pos);
                    std::string number_str = szCode.substr(pos + 1);

                    ai::LogInfo << "domelinkage insertVideo " << szName << " NAME " << szAddr;
                    if (number_str == "0")
                    {
                        domePreset = 11;
                        videoType = 1;
                    }
                    else if (number_str == "1")
                    {
                        domePreset = 12;
                        videoType = 1;
                    }
                    else if (number_str == "2")
                    {
                        domePreset = 0;
                        videoType = 2;
                    }
                    else
                    {
                        domePreset = -1;
                    }

                    if (domePreset >= 0)
                    {
                        if (mapGroupUUIDs.find(ip) == mapGroupUUIDs.end())
                        {
                            boost::uuids::uuid uid = boost::uuids::random_generator()();
                            std::string szUuid = boost::uuids::to_string(uid);
                            boost::algorithm::erase_all(szUuid, "-");
                            ai::LogInfo <<  "NEW group id: " << szUuid;
                            groupUUID = szUuid;
                            mapGroupUUIDs[ip] = groupUUID;
                        }
                        else
                        {
                            groupUUID = mapGroupUUIDs[ip];
                        }
                    }
                }
                else {
                    ai::LogWarn << "Other device: " << szCode;
                }
            }

            db::VideoSource videodata(szName, szAddr, id, location, false, false, 0, "", 0, 0, false, false);
            videodata.setGroupUUID(groupUUID);
            int newId = db::insertData(videodata);
            if (newId > -1)
            {
                ai::LogTrace << "PLAT insertVideo " << newId << " NAME " << szAddr;
                //获取生成的ID 处理wn_video_logic表
                std::string szVideoId = std::to_string(newId);
                db::VideoLogic videologic1(szVideoId + "-Area1->2", newId, 1);
                db::insertData(videologic1);
                db::VideoLogic videologic2(szVideoId + "-Area2->1", newId, 2);
                db::insertData(videologic2);
            };
        }
    }

    /**
     * @brief  更新 视频通道 名称
     * @param[in]   id: 视频接入前端id
     * @param[in]   szAddr: 视频地址
     * @param[in]   szName: 视频名称
     */
    void DataManager::updateVideoSourceName(int id, std::string szAddr, std::string szName)
    {
        odb::query<db::VideoSource> q(odb::query<db::VideoSource>::videoServerId == id
            && odb::query<db::VideoSource>::address == szAddr);
        std::vector<db::VideoSource> vecResult = {};
        queryData(vecResult, q);
        if (!vecResult.empty())
        {
            auto& videoSource = vecResult[0];
            videoSource.setName(szName);
            db::updateData(videoSource);
        }
    }

    /**
     * @brief     是否平台
     */
    bool DataManager::isPlatform() {
        if (platformIP.empty() || localIP.empty()) {
            return false;
        }

        if (platformIP == localIP) {
            return true;
        }

        return false;
    }

    /**
     * @brief     查询球机视频资源表数据
     * @param[in]   groupId: 球机前端id
     */
    std::optional<VideoSourceInfoPtr> DataManager::queryDomeVideoSource(int groupId)
    {
        if (groupId <= 0)
        {
            ai::LogError << "Invalid dome groupId id : " << groupId;
            return std::nullopt;
        }
        VIDEO_INFO_LOCKGUARD;
        auto it = std::find_if(videoSources.begin(), videoSources.end(),
            [groupId](const std::pair<int, VideoSourceInfoPtr>& pair) {
                return (int)pair.second->videoSourcePtr->getGroupId() == groupId && pair.second->videoSourcePtr->getVideoType() == 2;
            });
        if (it == videoSources.end())
        {
            ai::LogError << "groupId " << groupId << "dome resource not found ";
            return std::nullopt;
        }
        return it->second;
    }

    /**
     * @brief     查询球机视频资源表数据
     * @param[in]   groupUUID: 枪球 组uuid
     */
    std::optional<VideoSourceInfoPtr> DataManager::queryDomeVideoSource(std::string groupUUID)
    {
        if (groupUUID.empty())
        {
            ai::LogError << "Empty dome groupUUID id : " << groupUUID;
            return std::nullopt;
        }
        VIDEO_INFO_LOCKGUARD;
        auto it = std::find_if(videoSources.begin(), videoSources.end(),
            [groupUUID](const std::pair<int, VideoSourceInfoPtr>& pair) {
                return pair.second->videoSourcePtr->getGroupUUID() == groupUUID && pair.second->videoSourcePtr->getVideoType() == 2;
            });
        if (it == videoSources.end())
        {
            ai::LogError << "groupId " << groupUUID << "dome resource not found ";
            return std::nullopt;
        }
        return it->second;
    }

    /**
     * @brief     查询视频资源
     * @param[in]   id: 视频接入前端id
     * @param[string]   address  uuid
     */
    std::optional<VideoSourceInfoPtr> DataManager::queryVideoSource(int id, std::string& address)
    {
        VIDEO_INFO_LOCKGUARD;
        auto it = std::find_if(videoSources.begin(), videoSources.end(),
            [id, address](const std::pair<int, VideoSourceInfoPtr>& pair) {
                return pair.second->videoSourcePtr->getAddress() == address && pair.second->videoSourcePtr->getVideoServerId() == id;
            });

        if (it == videoSources.end())
            return std::nullopt;
        else
            return it->second;
    }
}