#ifndef MAINAPP_HPP_
#define MAINAPP_HPP_

#include "wtoe/PackageManager/PackageManagerExp.hpp"
namespace ais
{

class CMainApp : public wtoe::CMainApplication
{
public:
	CMainApp( int argc, char *argv[], const wtoe::CCmndLineOptionsDescription &desc);
	virtual ~CMainApp();

public:
	virtual int run();
	virtual void signalVProc( int sig ){};
	virtual bool setUp();

private:

private:
	int m_argc;
	char** m_argv;
};
} //namespace ais

#endif // MAINAPP_HPP_


