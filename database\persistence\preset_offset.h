/**
 * @addtogroup odbDatabaseGroup
 * @brief 预置位对应的偏移配置区信息
 * @{
 */
#ifndef _PRESETOFFSET_H
#define _PRESETOFFSET_H


#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
/**
 * @brief  预置位对应的偏移配置区信息 对应数据库aimonitorV3的表wn_preset_offset
 */
#pragma db object table("wn_preset_offset")
class PresetOffset {
public:

    PresetOffset(unsigned long presetId,
        const std::string& offsetArea,
        bool isDel,
        bool isEnable
    )
        :  presetId(presetId), offsetArea(offsetArea),
        isDel(isDel), isEnable(isEnable)
    {
    }

    unsigned long getId() const {
        return id;
    }

    unsigned long getPresetId()const {
        return presetId;
    }

    void setPresetId(unsigned long id) {
        this->presetId = id;
    }

    const std::string& getOffsetArea() const {
        return offsetArea;
    }

    void setOffsetArea(const std::string& area) {
        this->offsetArea = area;
    }


    bool getIsDel() const {
        return isDel;
    }

    void setIsDel(bool del) {
        this->isDel = del;
    }

    bool getIsEnable() const {
        return isEnable;
    }

    void setIsEnable(bool enable) {
        this->isEnable = enable;
    }

private:

    friend class odb::access;
    PresetOffset() {}

private:
#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("preset_id")
    unsigned long presetId;             //!< 预置位id 对应表wn_preset(preset) id

#pragma db column("offset_area")  type("VARCHAR(255)")
    std::string offsetArea;             //!< 配置的偏移区域，用于判断画面是否偏移

#pragma db column("is_del") type("INT")
    bool isDel;                         //!< 是否删除

#pragma db column("is_enable") type("INT")
    bool isEnable;                      //!< 是否使能
};
}
#endif //_PRESETOFFSET_H
/**
 * @}
 */
