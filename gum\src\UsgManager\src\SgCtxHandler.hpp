#ifndef SGCTXHANDLER_HPP_
#define SGCTXHANDLER_HPP_

#include "UsgSipStack/include/UsgSipStackExp.hpp"
#include "UsgManager/include/UsgManagerItf.hpp"
#include "UsgManager/include/ProtocolWtoeItf.hpp"
#include "UsgManager/include/WtoeSipFuncItf.hpp"

namespace usg
{

class CUsgManager;

class CSgCtxHandler : public usg::ICtxHandler
{
public:
    CSgCtxHandler();

    bool init( IProtocolWtoe *protocolWtoe, CUsgManager *sg, IWtoeSipFunc* sipFunc );
    void fini();

private:
    virtual bool commitRegist( const std::string &sid );

    virtual bool commitSubscribe( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result );
    virtual bool commitNotify( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result );

    virtual bool commitDdcpDo( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result );
    //for gb28181
    virtual bool commitDdcpDo( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::vector<std::string> &resultVec );

    virtual bool commitInvite( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result );
    virtual bool commitCancel( const std::string &sid );
    virtual bool commitAck( const std::string &sid );
    virtual bool commitBye( const std::string &sid );

    virtual bool commitAnswer( const std::string &sid, const char *xml, size_t len, bool status, int type );
    virtual bool commitRegist( const std::string &sid,const std::string &oid,int expries );

    virtual bool setCseqValues( const std::string& sid ,int cseq );
    virtual bool getCsqlValues( const std::string& sid, int& cseq );

    bool setFormatValues( const std::string& sid, std::string format );
    bool getFormatValues( const std::string& sid, std::string &format );

	bool setSrcAddr( const std::string& sid, std::string srcAddr );
	bool getSrcAddr( const std::string& sid, std::string &srcAddr );
private:
    IProtocolWtoe *m_protocolWtoe;
    CUsgManager  *m_sg;
    IWtoeSipFunc* m_sipFunc;

};

}

#endif
