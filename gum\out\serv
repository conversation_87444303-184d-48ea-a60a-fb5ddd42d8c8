#!/bin/bash
#
### BEGIN INIT INFO
# Provides:          gum
# Required-Start:    $remote_fs $syslog
# Required-Stop:     $remote_fs $syslog
# Should-Start:      $network $time
# Should-Stop:       $network $time
# Default-Start:     2 3 4 5
# Default-Stop:      0 1 6
# Short-Description: Start and stop the gum server daemon
# Description:       Enable gum provided by daemon.
### END INIT INFO
#


var=$0
KIND=${var##*/}
KIND=${KIND:0-3}
PROG=$(echo $KIND | tr '[a-z]' '[A-Z]')

DAEMON="screen -dmS $KIND /usr/sbin/"$KIND"Run.sh"

# Where to keep a log file
APPLOG="/var/log/$KIND.log"


# Check if server is running
get_running () {
	  ps -aux 2|grep $KIND"D" | grep -v grep | wc -l
}

start() {

if [ "$(get_running)" -eq 1 ];then
        echo -n "$KIND is already started"
        echo ""
        exit 1
fi
	echo -n "Starting $KIND services: "
	$DAEMON&
	# 如果上面的命令成功，则显示一个绿色的 [ OK ] ，否则显示 [ FAILURE ]
	if [ "$?" -eq 0 ] ;
	then
		echo $KIND" is started"
	else
		echo $KIND" did not start. Please check logs for more details."
	fi
	return $?
}

stop() {
	if [ "$(get_running)" -eq 1 ];
	then
		killall $KIND"D"
		if [ "$?" -eq 0 ];
		then
            count=0
            while [ "$(get_running)" -eq 1 ]
            do
                count=$(($count+1))
                if [ $count -gt 30 ]
                then
                    ret=$(ps -a | grep "$KIND"D)
                    pid=${ret%% *}
                    kill -9 $pid
                    break;
                fi
                sleep 1
            done

			echo $KIND" is stopped"
		else
			echo "Attempt to shutdown $KIND timed out"
		fi
	else
		echo "$KIND is already stopped"
	fi
	return $?
}

restart() {
	stop
        while [ "$(get_running)" -eq 1 ]
        do
	  sleep 1
        done
	start
}

rhstatus() {
	if [ "$(get_running)" -eq 1 ];
	then
		echo "$KIND is running"
	else
		echo "$KIND is not running"
		exit 3
	fi
}


# Allow status as non-root.
if [ "$1" = status ]; then
       rhstatus
       exit $?
fi

case "$1" in
  start)
  	restart
	;;
  stop)
  	stop
	;;
  status)
  	rhstatus
	;;
  restart)
        restart
        ;;
  *)
	echo $"Usage: $0 {start|stop|status|restart}"
	exit 2
esac

exit $?
