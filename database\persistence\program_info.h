/**
 * @addtogroup odbDatabaseGroup
 * @brief 夏天冬天外场预置位信息
 * @{
 */
#ifndef _PROGRAMINFO_H
#define _PROGRAMINFO_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {

/**
 * @brief  夏天冬天外场预置位信息 对应数据库aimonitorV3的表wn_program_info
 */
#pragma db object table("wn_program_info")
class ProgramInfo {
public:

    ProgramInfo(const std::string& switchTime,
        unsigned long presetId,
        unsigned long programId,
        bool isDel,
        bool isEnable
    )
        : switchTime(switchTime), presetId(presetId), programId(programId),
        isDel(isDel), isEnable(isEnable)
    {
    }

    unsigned long getId() const {
        return id;
    }

    const std::string& getSwitchTime() const {
        return switchTime;
    }

    void setSwitchTime(const std::string& time) {
        this->switchTime = time;
    }

    unsigned long getPresetId()const {
        return presetId;
    }

    void setPresetId(unsigned long id) {
        this->presetId = id;
    }


    unsigned long getProgramId()const {
        return programId;
    }

    void setProgramId(unsigned long id) {
        this->programId = id;
    }

    bool getIsDel() const {
        return isDel;
    }

    void setIsDel(bool del) {
        this->isDel = del;
    }

    bool getIsEnable() const {
        return isEnable;
    }

    void setIsEnable(bool enable) {
        this->isEnable = enable;
    }

private:

    friend class odb::access;
    ProgramInfo() {}


private:

#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("switch_time")  type("VARCHAR(255)")
    std::string switchTime;             //!< 切换时间  在夏天\冬天外场(wn_program)中定义

#pragma db column("preset_id")
    unsigned long presetId;             //!< 预置位id 对应表wn_preset(preset) id TODO:

#pragma db column("program_id")
    unsigned long programId;            //!< 夏天\冬天外场时间信息 对应表wn_program(program)的id

#pragma db column("is_del") type("INT")
    bool isDel;                         //!< 是否删除

#pragma db column("is_enable") type("INT")
    bool isEnable;                      //!< 是否使能
};
}
#endif //_PROGRAMINFO_H
/**
 * @}
 */