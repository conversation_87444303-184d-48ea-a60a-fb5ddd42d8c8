#!/bin/bash
PROGRAM=msm


CONFIG="debug"
APPNAME="appD"  

BINDIR=$PROGRAM
NEEDDIR=$CONFIG
INSTALLFILE="install.sh"
THRIDLIBS="3rdlibs"


echo "program is: "$PROGRAM
echo "config is: " $CONFIG

CONFIGDIR="config"
EXECUTEDIR="bin"
FILENAME=msm-1.0-"`date '+%Y%m%d'`.tar.bz2"

echo "filename is: " $FILENAME

#create $PROGRAM directory
if [ -d $BINDIR ]
then
    rm $BINDIR -fr
fi

mkdir $BINDIR/$CONFIGDIR -p
mkdir $BINDIR/$EXECUTEDIR -p
mkdir $BINDIR/log -p

# copy 3rdlibs file
echo "Ready to copying 3rdlibs"
/bin/cp $THRIDLIBS/* $BINDIR/$EXECUTEDIR	

# copy $PROGRAM bin file
cp $NEEDDIR/$APPNAME $BINDIR/$EXECUTEDIR/$PROGRAM"D"
cp $NEEDDIR/*.so* $BINDIR/$EXECUTEDIR

# copy config file
cp $CONFIGDIR/org/*.* $BINDIR/$CONFIGDIR

# copy install script
cp $INSTALLFILE $BINDIR/install.sh
chmod +x $BINDIR/install.sh




# copy startup script
cp serv $BINDIR/$EXECUTEDIR/$PROGRAM

# tar package
tar -cjvf $FILENAME $BINDIR

rm $BINDIR -fr

echo $FILENAME" is generated."

