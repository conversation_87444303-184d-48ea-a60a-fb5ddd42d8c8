
#include "streamsocket.h"
#include <cstddef>
#include <boost/thread.hpp>

namespace fvm::stream
{
    //传入socket
    TcpSession::TcpSession(TCP::socket socket, Channel& ch, RecvCallback fb) : mSocket(std::move(socket))
        , channel(ch), fRecvCallback(fb)
    {
        BoostError ec;
        TCP::endpoint ep = mSocket.local_endpoint(ec);
        localIpPort = ep.address().to_v4().to_string() + ":" + std::to_string(ep.port());
        ep = mSocket.remote_endpoint(ec);
        remoteIpPort = ep.address().to_v4().to_string() + ":" + std::to_string(ep.port());

        //永远不超时
        deadTimer.expires_at(Steadytimer::time_point::max());
    }

    TcpSession::~TcpSession(void)
    {
        if (!stopped())
            stop();
    }

    //打开sock读
    void TcpSession::start(void)
    {
        if (bStatus)
            return;
        bStatus = true;
        channel.join(shared_from_this());
        do_read_header();
        check_deadline(deadTimer);
    }

    //关闭sock
    void TcpSession::stop(void)
    {
        if (!bStatus)
            return;
        bStatus = false;
        channel.leave(shared_from_this());
        BoostError ignored_error;
        mSocket.close(ignored_error);
        deadTimer.cancel();
    }

    //查询停止状态
    bool TcpSession::stopped(void) const
    {
        return !mSocket.is_open();
    }

    std::string TcpSession::getLocalIpPort(void)
    {
        return localIpPort;
    }

    std::string TcpSession::getRemoteIpPort(void)
    {
        return remoteIpPort;
    }

    //读头获得数据长
    void TcpSession::do_read_header(void)
    {
        memset(recvData, 0, BUF_SIZE);
        //设置async_read 10s超时
        deadTimer.expires_after(std::chrono::seconds(10));
        boost::asio::async_read(mSocket,
            boost::asio::buffer(recvData, TCPHEADER_LEN),
            [this](BoostError ec, std::size_t length)
            {
                if (stopped())
                    return;
                if (!ec)
                {
                    //注意此处为16进制
                    uint16_t tmp = 0;
                    memcpy(&tmp, boost::asio::buffer(recvData).data(), TCPHEADER_LEN);
                    size_t bodyLen = boost::asio::detail::socket_ops::network_to_host_short(tmp);
                    do_read_body(bodyLen);
                }
                else
                    stop();
            });
    }

    //读全部buff
    void TcpSession::do_read_body(size_t bodyLen)
    {
        boost::asio::async_read(mSocket,
            boost::asio::buffer(recvData, bodyLen),
            [this](BoostError ec, std::size_t length)
            {
                if (!ec)
                {
                    //将接受的数据回调出去
                    if (fRecvCallback)
                        fRecvCallback(recvData, length);
                    do_read_header();
                }
                else
                    stop();
            });
    }

    //检测超时断线
    void TcpSession::check_deadline(Steadytimer& deadline)
    {
        deadline.async_wait(
            [this, &deadline](const BoostError& error)
            {
                // 检查如果session会话停了就不继续检测
                if (stopped())
                    return;
                //超时触发stop
                if (deadline.expiry() <= Steadytimer::clock_type::now())
                    stop();
                else
                    check_deadline(deadline);       // 没超时继续回到异步保活检测去
            });
    }



    TcpServer::TcpServer(void) :tcpAccept(io_service)
    {
    }

    TcpServer::~TcpServer(void)
    {
        //注意 确保所有安全退出
        stop();
    }

    //启动服务器
    bool TcpServer::start(RecvCallback pCb, int uPort)
    {
        if (bStatus)
            return true;
        //设置回调
        pCallback = pCb;
        //服务器启动
        BoostError ec;
        TCP::endpoint endpoint = TCP::endpoint(TCP::v4(), uPort);
        tcpAccept.open(endpoint.protocol());
        tcpAccept.set_option(TCP::acceptor::reuse_address(true));
        tcpAccept.bind(endpoint,ec);
        if (ec)
            return false;       //端口被占用
        tcpAccept.listen();
        do_accept();
        //后面考虑run_one去轮询
        //boost::thread(boost::bind(&boost::asio::io_context::run_one, &io_service_)).detach();
        thd = std::thread([&] {run_io_service(true);});
        bStatus = true;
        return true;
    }

    //停止服务器
    void TcpServer::stop(void)
    {
        if (curr_session)
        {
            curr_session->stop();
            curr_session.reset();
        }
        //channel_里面维护了curr_session的清理
        tcpAccept.close();
        io_service.stop();
        if (thd.joinable())
            thd.join();
        io_service.reset();
        bStatus = false;
    }

    //连接状态
    bool TcpServer::isConnect(void)
    {
        return (curr_session != nullptr && !curr_session->stopped());
    }

    //打开异步接受
    void TcpServer::do_accept(void)
    {
        tcpAccept.async_accept(
            [this](BoostError ec, TCP::socket socket)
            {
                if (!ec)
                {
                    //仅仅保留一个 强制退出之前的连接
                    if (curr_session)
                        curr_session->stop();
                    curr_session.reset(new TcpSession(std::move(socket), channel, pCallback));
                    curr_session->start();
                }
                do_accept();
            });
    }



    UdpServer::UdpServer(void) : mSocket(io_service)
    {
    }

    UdpServer::~UdpServer(void)
    {
        //确保所有安全退出
        stop();
    }

    //启动服务器
    bool UdpServer::start(RecvCallback pCb, int uPort)
    {
        if (bStatus)
            return true;
        //设置回调
        pCallback = pCb;
        //服务器启动
        //io_service_.restart();
        senderPoint = UDP::endpoint(UDP::v4(), uPort);
        mSocket.open(UDP::v4());
        boost::system::error_code ec;
        mSocket.bind(senderPoint, ec);
        if (ec)
            return false;       //端口被占用
        //打开异步接受
        do_receive();
        thd = std::thread([&] {run_io_service(true);});
        bStatus = true;
        return true;
    }

    //停止服务器
    void UdpServer::stop(void)
    {
        //停止io_service
        io_service.stop();
        if (thd.joinable())
            thd.join();
        io_service.reset();
        bStatus = false;
    }

    //打开异步数据接受
    void UdpServer::do_receive(void)
    {
        memset(recvData, 0, BUF_SIZE);
        mSocket.async_receive(
            boost::asio::buffer(recvData, BUF_SIZE),
            [this](BoostError ec, std::size_t bytes_recvd)
            {
                if (!ec && bytes_recvd > 0)
                {
                    if (pCallback)
                        pCallback(recvData, bytes_recvd);
                }
                do_receive();
            });
    }

}