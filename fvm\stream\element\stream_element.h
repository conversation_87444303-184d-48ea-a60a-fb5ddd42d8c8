/**
 * Project FVM
 */
#pragma once

#include <memory>
#include <atomic>
#include <boost/signals2.hpp>
#include "util/worker_pool.h"
#include "boost/format.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/time.h>
#ifdef __cplusplus
}
#endif


/**
 * @brief: 视频流元件定义
 *          主要定义视频流通用状态机，统一控制
 *          派生视频输入、输出元件
 */
namespace fvm::stream {

    using std::static_pointer_cast;
    using std::optional;
    using std::nullopt;
    using std::max;
    using std::min;

    // 等待一段时间（秒）
#define WAIT_FOR_SECONDS(seconds) \
    { \
        for (int i = 0; i < 2 * seconds; i++)  \
        {  \
            if (!jobIsRunning())  \
                break;  \
            boost::this_fiber::sleep_for(std::chrono::milliseconds(500)); \
        } \
    }

    // 在额外线程中运行任务
#define DO_IN_THREAD(work) \
    {                      \
        auto promisep(std::make_shared<boost::fibers::promise<bool>>()); \
        boost::fibers::future<bool> future( promisep->get_future() ); \
        worker::post([&, promisep]()  \
        { \
            { \
               work \
            } \
            promisep->set_value(true);  \
        }); \
        future.get(); \
    }

    /**
     * 流元件 状态
     */
    enum class StreamElementStatus
    {
        Null,
        Ready,
        Paused,
        Playing,
        Disposed
    };
    typedef boost::signals2::signal<void(StreamElementStatus)> OnStreamStatusChanged;

    /**
     * 流元件 基类， 派生 视频流输入和输出类
     */
    class StreamElement : public std::enable_shared_from_this<StreamElement> {
        friend class StreamPipe;
    public:
        /**
         * 请求元件开始
         */
        virtual void startPlay();

        /**
         * 请求元件停止
         * @param dispose 是否标记销毁
         */
        virtual void stopPlay(bool dispose = false);

        /**
         * 标记释放
         */
        virtual void dispose();

         /**
         * 等待元件结束
         */
         virtual void waitForFinished();

        /**
         * 是否可以开始
         */
        inline bool playable() { return status == StreamElementStatus::Ready || status == StreamElementStatus::Paused; }

        /**
         * 是否已开始
         */
        inline bool isPlaying() { return status == StreamElementStatus::Playing; };

        /**
         * 是否已暂停
         */
        inline bool isPaused() { return status == StreamElementStatus::Paused; };

        /**
         * 是否已初始化
         */
        inline bool isInited() { return status != StreamElementStatus::Null; };

        /**
         * 是否已销毁
         */
        inline bool isDisposed() { return status == StreamElementStatus::Disposed; };

        /**
         * 获取元件状态字符
         */
        const std::string getStatusString();

        /**
        * 获取错误信息
        */
        virtual std::string getLastError(void) { return lastError; };

        /**
        * 获取重复错误计数
        */
        int getErrorCount(void) { return errorCount; };

        /**
        * 设置错误信息
        * @param lastError      log内容
        * @param lastErrorCode  错误码
        */
        void setLastError(std::string error, int code = 0);

        /**
        * 获取输入输出地址
        */
        const std::string getAddress(void) { return address; };

    public:// Signal
        /**
         * 状态变更信号
         */
        OnStreamStatusChanged onStreamStatusChanged;

    protected:
        void inited();
        void played();
        void paused();

        /**
         * 当前worker 是否标记运行
         */
        inline bool jobIsRunning(){return !flagRequestExitJob;};

    protected:

        /**
         * ***【视频输入处理函数】
         */
        virtual void process() = 0;

        /**
         * 信息打印（附带调试协程线程信息）
         */
        void printInfo(const std::string &info);

        /**
         * 获取ffmpeg错误字符输出
         */
        const std::string getErrorString(int avError);

        /**
         * 输入输出地址
         */
        const char* streamUrl(){return address.c_str();}
        std::string address;

        /*
         * 任务类别
         */
        virtual worker::WorkerType workerType() =0;

    private:
        /**
         * ***【当前任务是否标记退出】
         */
        std::atomic<bool> flagRequestExitJob = false;

        /**
         * 当前元件是否标记销毁
         */
        std::atomic<bool> markDispose = false;

        /**
         * 当前元件状态
         */
        std::atomic<StreamElementStatus> status = StreamElementStatus::Null;

        /**
         * 当前任务future
         */
        worker::FiberFuture future;

        /**
         * 错误信息
         */
        std::string lastError;

        /**
         * 错误码
         */
        int lastErrorCode = 0;

        /**
         * 重试次数
         */
        int errorCount = 0;
    };
}
