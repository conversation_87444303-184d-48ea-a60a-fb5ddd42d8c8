/*
 * This code implements the MD5 message-digest algorithm.
 * The algorithm was written by <PERSON>.  This code was
 * written by <PERSON> in 1993, our understanding is 
 * that no copyright is claimed and that 
 * this code is in the public domain.
 *
 * Equivalent code is available from RSA Data Security, Inc.
 * This code has been tested against that, and is 
 * functionally equivalent,
 *
 * To compute the message digest of a chunk of bytes, declare an
 * MD5Context structure, pass it to MD5Init, call MD5Update as
 * needed on buffers full of bytes, and then call MD5Final, which
 * will fill a supplied 16-byte array with the digest.
 */

#include "md5.hpp"

/* If we haven't brought in an accelerated MD5, use our own */
#ifndef WORDS_BIGENDIAN
#define byteReverse(buf, len)	       /* Nothing */
#else
void byteReverse(unsigned char *buf, unsigned longs);

#ifndef ASM_MD5
/*
 * Note: this code is harmless on little-endian machines.
 */
void byteReverse(unsigned char *buf, unsigned longs)
{
    uint32_t t;
    do {
	t = (uint32_t) ((unsigned) buf[3] << 8 | buf[2]) << 16 |
	    ((unsigned) buf[1] << 8 | buf[0]);
	*(uint32_t *) buf = t;
	buf += 4;
    } while (--longs);
}
#endif  // ifndef ASM_MD5
#endif  // ifndef WORDS_BIGENDIAN





/*
 * Start MD5 accumulation.  Set bit count to 0 and buffer to mysterious
 * initialization constants.
 */
void MD5Init(context_md5_t *ctx)
{
    ctx->buf[0] = 0x67452301;
    ctx->buf[1] = 0xefcdab89;
    ctx->buf[2] = 0x98badcfe;
    ctx->buf[3] = 0x10325476;

    ctx->bits[0] = 0;
    ctx->bits[1] = 0;
}

/*
 * Update context to reflect the concatenation of another buffer full
 * of bytes.
 */
void MD5Update(context_md5_t *ctx, const unsigned char *buf, size_t len)
{
    uint32_t t;

    /* Update bitcount */

    t = ctx->bits[0];
    if ((ctx->bits[0] = t + ((uint32_t) len << 3)) < t)
	ctx->bits[1]++;		/* Carry from low to high */
    ctx->bits[1] += len >> 29;

    t = (t >> 3) & 0x3f;	/* Bytes already in shsInfo->data */

    /* Handle any leading odd-sized chunks */

    if (t) {
	unsigned char *p = (unsigned char *) ctx->in + t;

	t = 64 - t;
	if (len < t) {
	    memcpy(p, buf, len);
	    return;
	}
	memcpy(p, buf, t);
	byteReverse(ctx->in, 16);
	MD5Transform(ctx->buf, (uint32_t *) ctx->in);
	buf += t;
	len -= t;
    }
    /* Process data in 64-byte chunks */

    while (len >= 64) {
	memcpy(ctx->in, buf, 64);
	byteReverse(ctx->in, 16);
	MD5Transform(ctx->buf, (uint32_t *) ctx->in);
	buf += 64;
	len -= 64;
    }

    /* Handle any remaining bytes of data. */

    memcpy(ctx->in, buf, len);
}

/*
 * Final wrapup - pad to 64-byte boundary with the bit pattern 
 * 1 0* (64-bit count of bits processed, MSB-first)
 */
void MD5Final(unsigned char digest[16], context_md5_t *ctx)
{
    unsigned count;
    unsigned char *p;

    /* Compute number of bytes mod 64 */
    count = (ctx->bits[0] >> 3) & 0x3F;

    /* Set the first char of padding to 0x80.  This is safe since there is
       always at least one byte free */
    p = ctx->in + count;
    *p++ = 0x80;

    /* Bytes of padding needed to make 64 bytes */
    count = 64 - 1 - count;

    /* Pad out to 56 mod 64 */
    if (count < 8) {
	/* Two lots of padding:  Pad the first block to 64 bytes */
	memset(p, 0, count);
	byteReverse(ctx->in, 16);
	MD5Transform(ctx->buf, (uint32_t *) ctx->in);

	/* Now fill the next block with 56 bytes */
	memset(ctx->in, 0, 56);
    } else {
	/* Pad block to 56 bytes */
	memset(p, 0, count - 8);
    }
    byteReverse(ctx->in, 14);

    /* Append length in bits and transform */

    // the two lines below generated this error:
    // "md5.c:147:5: warning: dereferencing type-punned pointer will break strict-aliasing rules [-Wstrict-aliasing]"

    //((uint32_t *) ctx->in)[14] = ctx->bits[0];
    //((uint32_t *) ctx->in)[15] = ctx->bits[1];

    // We will manually expand the cast into two statements to make
    // the compiler happy...

    uint32_t *ctxin = (uint32_t *)ctx->in; 
    ctxin[14] = ctx->bits[0];
    ctxin[15] = ctx->bits[1];

    MD5Transform(ctx->buf, (uint32_t *) ctx->in);
    byteReverse((unsigned char *) ctx->buf, 4);
    memcpy(digest, ctx->buf, 16);

    memset(ctx, 0, sizeof(* ctx));	/* In case it's sensitive */
    /* The original version of this code omitted the asterisk. In
       effect, only the first part of ctx was wiped with zeros, not
       the whole thing. Bug found by Derek Jones. Original line: */
    // memset(ctx, 0, sizeof(ctx));	/* In case it's sensitive */
}

#ifndef ASM_MD5

/* The four core functions - F1 is optimized somewhat */

/* #define F1(x, y, z) (x & y | ~x & z) */
#define F1(x, y, z) (z ^ (x & (y ^ z)))
#define F2(x, y, z) F1(z, x, y)
#define F3(x, y, z) (x ^ y ^ z)
#define F4(x, y, z) (y ^ (x | ~z))

/* This is the central step in the MD5 algorithm. */
#ifdef __PUREC__
#define MD5STEP(f, w, x, y, z, data, s) \
	( w += f /*(x, y, z)*/ + data,  w = w<<s | w>>(32-s),  w += x )
#else
#define MD5STEP(f, w, x, y, z, data, s) \
	( w += f(x, y, z) + data,  w = w<<s | w>>(32-s),  w += x )
#endif

/*
 * The core of the MD5 algorithm, this alters an existing MD5 hash to
 * reflect the addition of 16 longwords of new data.  MD5Update blocks
 * the data and converts bytes into longwords for this routine.
 */
void MD5Transform(uint32_t buf[4], uint32_t const in[16])
{
    uint32_t a, b, c, d;

    a = buf[0];
    b = buf[1];
    c = buf[2];
    d = buf[3];

#ifdef __PUREC__	/* PureC Weirdness... (GG) */
    MD5STEP(F1(b,c,d), a, b, c, d, in[0] + 0xd76aa478L, 7);
    MD5STEP(F1(a,b,c), d, a, b, c, in[1] + 0xe8c7b756L, 12);
    MD5STEP(F1(d,a,b), c, d, a, b, in[2] + 0x242070dbL, 17);
    MD5STEP(F1(c,d,a), b, c, d, a, in[3] + 0xc1bdceeeL, 22);
    MD5STEP(F1(b,c,d), a, b, c, d, in[4] + 0xf57c0fafL, 7);
    MD5STEP(F1(a,b,c), d, a, b, c, in[5] + 0x4787c62aL, 12);
    MD5STEP(F1(d,a,b), c, d, a, b, in[6] + 0xa8304613L, 17);
    MD5STEP(F1(c,d,a), b, c, d, a, in[7] + 0xfd469501L, 22);
    MD5STEP(F1(b,c,d), a, b, c, d, in[8] + 0x698098d8L, 7);
    MD5STEP(F1(a,b,c), d, a, b, c, in[9] + 0x8b44f7afL, 12);
    MD5STEP(F1(d,a,b), c, d, a, b, in[10] + 0xffff5bb1L, 17);
    MD5STEP(F1(c,d,a), b, c, d, a, in[11] + 0x895cd7beL, 22);
    MD5STEP(F1(b,c,d), a, b, c, d, in[12] + 0x6b901122L, 7);
    MD5STEP(F1(a,b,c), d, a, b, c, in[13] + 0xfd987193L, 12);
    MD5STEP(F1(d,a,b), c, d, a, b, in[14] + 0xa679438eL, 17);
    MD5STEP(F1(c,d,a), b, c, d, a, in[15] + 0x49b40821L, 22);

    MD5STEP(F2(b,c,d), a, b, c, d, in[1] + 0xf61e2562L, 5);
    MD5STEP(F2(a,b,c), d, a, b, c, in[6] + 0xc040b340L, 9);
    MD5STEP(F2(d,a,b), c, d, a, b, in[11] + 0x265e5a51L, 14);
    MD5STEP(F2(c,d,a), b, c, d, a, in[0] + 0xe9b6c7aaL, 20);
    MD5STEP(F2(b,c,d), a, b, c, d, in[5] + 0xd62f105dL, 5);
    MD5STEP(F2(a,b,c), d, a, b, c, in[10] + 0x02441453L, 9);
    MD5STEP(F2(d,a,b), c, d, a, b, in[15] + 0xd8a1e681L, 14);
    MD5STEP(F2(c,d,a), b, c, d, a, in[4] + 0xe7d3fbc8L, 20);
    MD5STEP(F2(b,c,d), a, b, c, d, in[9] + 0x21e1cde6L, 5);
    MD5STEP(F2(a,b,c), d, a, b, c, in[14] + 0xc33707d6L, 9);
    MD5STEP(F2(d,a,b), c, d, a, b, in[3] + 0xf4d50d87L, 14);
    MD5STEP(F2(c,d,a), b, c, d, a, in[8] + 0x455a14edL, 20);
    MD5STEP(F2(b,c,d), a, b, c, d, in[13] + 0xa9e3e905L, 5);
    MD5STEP(F2(a,b,c), d, a, b, c, in[2] + 0xfcefa3f8L, 9);
    MD5STEP(F2(d,a,b), c, d, a, b, in[7] + 0x676f02d9L, 14);
    MD5STEP(F2(c,d,a), b, c, d, a, in[12] + 0x8d2a4c8aL, 20);

    MD5STEP(F3(b,c,d), a, b, c, d, in[5] + 0xfffa3942L, 4);
    MD5STEP(F3(a,b,c), d, a, b, c, in[8] + 0x8771f681L, 11);
    MD5STEP(F3(d,a,b), c, d, a, b, in[11] + 0x6d9d6122L, 16);
    MD5STEP(F3(c,d,a), b, c, d, a, in[14] + 0xfde5380cL, 23);
    MD5STEP(F3(b,c,d), a, b, c, d, in[1] + 0xa4beea44L, 4);
    MD5STEP(F3(a,b,c), d, a, b, c, in[4] + 0x4bdecfa9L, 11);
    MD5STEP(F3(d,a,b), c, d, a, b, in[7] + 0xf6bb4b60L, 16);
    MD5STEP(F3(c,d,a), b, c, d, a, in[10] + 0xbebfbc70L, 23);
    MD5STEP(F3(b,c,d), a, b, c, d, in[13] + 0x289b7ec6L, 4);
    MD5STEP(F3(a,b,c), d, a, b, c, in[0] + 0xeaa127faL, 11);
    MD5STEP(F3(d,a,b), c, d, a, b, in[3] + 0xd4ef3085L, 16);
    MD5STEP(F3(c,d,a), b, c, d, a, in[6] + 0x04881d05L, 23);
    MD5STEP(F3(b,c,d), a, b, c, d, in[9] + 0xd9d4d039L, 4);
    MD5STEP(F3(a,b,c), d, a, b, c, in[12] + 0xe6db99e5L, 11);
    MD5STEP(F3(d,a,b), c, d, a, b, in[15] + 0x1fa27cf8L, 16);
    MD5STEP(F3(c,d,a), b, c, d, a, in[2] + 0xc4ac5665L, 23);

    MD5STEP(F4(b,c,d), a, b, c, d, in[0] + 0xf4292244L, 6);
    MD5STEP(F4(a,b,c), d, a, b, c, in[7] + 0x432aff97L, 10);
    MD5STEP(F4(d,a,b), c, d, a, b, in[14] + 0xab9423a7L, 15);
    MD5STEP(F4(c,d,a), b, c, d, a, in[5] + 0xfc93a039L, 21);
    MD5STEP(F4(b,c,d), a, b, c, d, in[12] + 0x655b59c3L, 6);
    MD5STEP(F4(a,b,c), d, a, b, c, in[3] + 0x8f0ccc92L, 10);
    MD5STEP(F4(d,a,b), c, d, a, b, in[10] + 0xffeff47dL, 15);
    MD5STEP(F4(c,d,a), b, c, d, a, in[1] + 0x85845dd1L, 21);
    MD5STEP(F4(b,c,d), a, b, c, d, in[8] + 0x6fa87e4fL, 6);
    MD5STEP(F4(a,b,c), d, a, b, c, in[15] + 0xfe2ce6e0L, 10);
    MD5STEP(F4(d,a,b), c, d, a, b, in[6] + 0xa3014314L, 15);
    MD5STEP(F4(c,d,a), b, c, d, a, in[13] + 0x4e0811a1L, 21);
    MD5STEP(F4(b,c,d), a, b, c, d, in[4] + 0xf7537e82L, 6);
    MD5STEP(F4(a,b,c), d, a, b, c, in[11] + 0xbd3af235L, 10);
    MD5STEP(F4(d,a,b), c, d, a, b, in[2] + 0x2ad7d2bbL, 15);
    MD5STEP(F4(c,d,a), b, c, d, a, in[9] + 0xeb86d391L, 21);
#else
    MD5STEP(F1, a, b, c, d, in[0] + 0xd76aa478, 7);
    MD5STEP(F1, d, a, b, c, in[1] + 0xe8c7b756, 12);
    MD5STEP(F1, c, d, a, b, in[2] + 0x242070db, 17);
    MD5STEP(F1, b, c, d, a, in[3] + 0xc1bdceee, 22);
    MD5STEP(F1, a, b, c, d, in[4] + 0xf57c0faf, 7);
    MD5STEP(F1, d, a, b, c, in[5] + 0x4787c62a, 12);
    MD5STEP(F1, c, d, a, b, in[6] + 0xa8304613, 17);
    MD5STEP(F1, b, c, d, a, in[7] + 0xfd469501, 22);
    MD5STEP(F1, a, b, c, d, in[8] + 0x698098d8, 7);
    MD5STEP(F1, d, a, b, c, in[9] + 0x8b44f7af, 12);
    MD5STEP(F1, c, d, a, b, in[10] + 0xffff5bb1, 17);
    MD5STEP(F1, b, c, d, a, in[11] + 0x895cd7be, 22);
    MD5STEP(F1, a, b, c, d, in[12] + 0x6b901122, 7);
    MD5STEP(F1, d, a, b, c, in[13] + 0xfd987193, 12);
    MD5STEP(F1, c, d, a, b, in[14] + 0xa679438e, 17);
    MD5STEP(F1, b, c, d, a, in[15] + 0x49b40821, 22);

    MD5STEP(F2, a, b, c, d, in[1] + 0xf61e2562, 5);
    MD5STEP(F2, d, a, b, c, in[6] + 0xc040b340, 9);
    MD5STEP(F2, c, d, a, b, in[11] + 0x265e5a51, 14);
    MD5STEP(F2, b, c, d, a, in[0] + 0xe9b6c7aa, 20);
    MD5STEP(F2, a, b, c, d, in[5] + 0xd62f105d, 5);
    MD5STEP(F2, d, a, b, c, in[10] + 0x02441453, 9);
    MD5STEP(F2, c, d, a, b, in[15] + 0xd8a1e681, 14);
    MD5STEP(F2, b, c, d, a, in[4] + 0xe7d3fbc8, 20);
    MD5STEP(F2, a, b, c, d, in[9] + 0x21e1cde6, 5);
    MD5STEP(F2, d, a, b, c, in[14] + 0xc33707d6, 9);
    MD5STEP(F2, c, d, a, b, in[3] + 0xf4d50d87, 14);
    MD5STEP(F2, b, c, d, a, in[8] + 0x455a14ed, 20);
    MD5STEP(F2, a, b, c, d, in[13] + 0xa9e3e905, 5);
    MD5STEP(F2, d, a, b, c, in[2] + 0xfcefa3f8, 9);
    MD5STEP(F2, c, d, a, b, in[7] + 0x676f02d9, 14);
    MD5STEP(F2, b, c, d, a, in[12] + 0x8d2a4c8a, 20);

    MD5STEP(F3, a, b, c, d, in[5] + 0xfffa3942, 4);
    MD5STEP(F3, d, a, b, c, in[8] + 0x8771f681, 11);
    MD5STEP(F3, c, d, a, b, in[11] + 0x6d9d6122, 16);
    MD5STEP(F3, b, c, d, a, in[14] + 0xfde5380c, 23);
    MD5STEP(F3, a, b, c, d, in[1] + 0xa4beea44, 4);
    MD5STEP(F3, d, a, b, c, in[4] + 0x4bdecfa9, 11);
    MD5STEP(F3, c, d, a, b, in[7] + 0xf6bb4b60, 16);
    MD5STEP(F3, b, c, d, a, in[10] + 0xbebfbc70, 23);
    MD5STEP(F3, a, b, c, d, in[13] + 0x289b7ec6, 4);
    MD5STEP(F3, d, a, b, c, in[0] + 0xeaa127fa, 11);
    MD5STEP(F3, c, d, a, b, in[3] + 0xd4ef3085, 16);
    MD5STEP(F3, b, c, d, a, in[6] + 0x04881d05, 23);
    MD5STEP(F3, a, b, c, d, in[9] + 0xd9d4d039, 4);
    MD5STEP(F3, d, a, b, c, in[12] + 0xe6db99e5, 11);
    MD5STEP(F3, c, d, a, b, in[15] + 0x1fa27cf8, 16);
    MD5STEP(F3, b, c, d, a, in[2] + 0xc4ac5665, 23);

    MD5STEP(F4, a, b, c, d, in[0] + 0xf4292244, 6);
    MD5STEP(F4, d, a, b, c, in[7] + 0x432aff97, 10);
    MD5STEP(F4, c, d, a, b, in[14] + 0xab9423a7, 15);
    MD5STEP(F4, b, c, d, a, in[5] + 0xfc93a039, 21);
    MD5STEP(F4, a, b, c, d, in[12] + 0x655b59c3, 6);
    MD5STEP(F4, d, a, b, c, in[3] + 0x8f0ccc92, 10);
    MD5STEP(F4, c, d, a, b, in[10] + 0xffeff47d, 15);
    MD5STEP(F4, b, c, d, a, in[1] + 0x85845dd1, 21);
    MD5STEP(F4, a, b, c, d, in[8] + 0x6fa87e4f, 6);
    MD5STEP(F4, d, a, b, c, in[15] + 0xfe2ce6e0, 10);
    MD5STEP(F4, c, d, a, b, in[6] + 0xa3014314, 15);
    MD5STEP(F4, b, c, d, a, in[13] + 0x4e0811a1, 21);
    MD5STEP(F4, a, b, c, d, in[4] + 0xf7537e82, 6);
    MD5STEP(F4, d, a, b, c, in[11] + 0xbd3af235, 10);
    MD5STEP(F4, c, d, a, b, in[2] + 0x2ad7d2bb, 15);
    MD5STEP(F4, b, c, d, a, in[9] + 0xeb86d391, 21);
#endif

    buf[0] += a;
    buf[1] += b;
    buf[2] += c;
    buf[3] += d;
}

#endif


void hash_init_md5(void * ctx)
{
    MD5Init((context_md5_t *)ctx);
}

void hash_update_md5(void *ctx, const unsigned char *buf, size_t len)
{
  MD5Update((context_md5_t *)ctx,buf,len);
}

void hash_final_md5(void *ctx, unsigned char *digest)
{
  MD5Final(digest,(context_md5_t *)ctx);
}


std::map<std::string, std::string> procValue(const char* cmd, char chSplit = ':')
{
	std::map<std::string, std::string> retMap;
	FILE* fp = popen(cmd, "r");
	if (fp == NULL)
	{
		fprintf(stderr, "No dmidecode\n");
		return retMap;
	}
	char line[0x100];
	std::string key, value;
	bool isValue = false;

	while (fgets(line, sizeof line, fp) != NULL)
	{
		if (strchr(line, chSplit) == 0)
			continue;
		key = "";
		value = "";
		isValue = false;
		std::string tmp(line);

		for (size_t i = 0; i < tmp.length(); i++)
		{
			char c = tmp[i];
			if (c == '\r' || c == '\n' || c == '\t' || (key.empty() && c == ' ')
				|| (isValue && value.empty() && c == ' '))
				continue;
			if (c == chSplit)
			{
				isValue = true;
				continue;
			}


			if (!isValue)
				key += tmp[i];
			else
				value += tmp[i];
		}
		if (!key.empty() && !value.empty())
			retMap[key] = value;
	}
	pclose(fp);
	return retMap;
}

int getdiskid(char *id, size_t max)
{
	std::map<std::string, std::string> retMap;
	retMap = procValue("echo \"123456\" | sudo -S sh -c \"dmidecode -t 1\"");
//	strcpy(id, retMap["Serial Number"].c_str());
	strcpy(id, retMap["UUID"].c_str());
	return 0;
}


int getcpuid(char *id, size_t max)
{
	std::map<std::string, std::string> retMap;
	retMap = procValue("lscpu");
	strcpy(id, (retMap["Architecture"] + ":" + retMap["CPU(s)"]).c_str());
	return 0;
}

std::string getNetMac(const std::string& szDevName)
{
#if defined (WIN32)
	return "";
#else
	struct ifreq ifreq;    //ifreq结构体常用来配置和获取ip地址
	int sock;

	if ((sock = socket(AF_INET, SOCK_STREAM, 0)) < 0)
	{
		perror("socket");
		return "";
	}
	strcpy(ifreq.ifr_name, szDevName.c_str());

	if (ioctl(sock, SIOCGIFHWADDR, &ifreq) < 0)
	{
		perror("ioctl");
		return "";
	}
	char mac[32] = { 0 };
	sprintf(mac, "%02x:%02x:%02x:%02x:%02x:%02x",
		(unsigned char)ifreq.ifr_hwaddr.sa_data[0],
		(unsigned char)ifreq.ifr_hwaddr.sa_data[1],
		(unsigned char)ifreq.ifr_hwaddr.sa_data[2],
		(unsigned char)ifreq.ifr_hwaddr.sa_data[3],
		(unsigned char)ifreq.ifr_hwaddr.sa_data[4],
		(unsigned char)ifreq.ifr_hwaddr.sa_data[5]);
	return std::string(mac);
#endif
}

int getlocalmac(char *id, size_t max)
{
    struct ifaddrs *ifaddr=NULL, *ifa=NULL;
	void* tmpAddrPtr = NULL;
    if (getifaddrs(&ifaddr) == -1)
    {
        printf("getlocalmac getifaddrs error\n");
        return 0;
    }

    for (ifa = ifaddr; ifa != NULL; ifa = ifa->ifa_next)
    {
        if (ifa->ifa_addr == NULL)
            continue;
		if (ifa->ifa_addr->sa_family == AF_INET)
		{ // check it is IP4
			// is a valid IP4 Address
			tmpAddrPtr = &((struct sockaddr_in*)ifa->ifa_addr)->sin_addr;
			int flags = ifa->ifa_flags;
			bool isRunning = flags & IFF_RUNNING;
			char addressBuffer[INET_ADDRSTRLEN];
			inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);
			std::string szIp(addressBuffer);
			if (szIp != "127.0.0.1" && isRunning) //是否运行中
			{
				std::string szCardName(ifa->ifa_name);
				size_t pos = szCardName.find(':');
				if (pos != std::string::npos)
					szCardName = szCardName.substr(0, pos);
				std::string szMac = getNetMac(szCardName);
				snprintf(id, max, "%s", szMac.c_str());
				break;
			}
		}
		else if (ifa->ifa_addr->sa_family == AF_INET6)
		{ // check it is IP6
		}
	}
    freeifaddrs(ifaddr);
	return 0;
}

std::string datetimeTostring(time_t t)
{
	struct tm *time; //实例化tm结构指针
	char buf[128] = { 0 };
	time = localtime(&t); //localtime函数把从time取得的时间t换算成你电脑中的时间(就是你设置的地区)
	sprintf(buf, "%02d-%02d-%02d %02d:%02d:%02d", time->tm_year + 1900,time->tm_mon+1,time->tm_mday,time->tm_hour, time->tm_min, time->tm_sec);
	return std::string(buf);
}


time_t stringToDatetime(std::string str)
{
	char *cha = (char*)str.data();             // 将string转换成char*。
	struct tm tm_;                                    // 定义tm结构体。
	int year, month, day, hour, minute, second;// 定义时间的各个int临时变量。
	sscanf(cha, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);// 将string存储的日期时间，转换为int临时变量。
	tm_.tm_year = year - 1900;                 // 年，由于tm结构体存储的是从1900年开始的时间，所以tm_year为int临时变量减去1900。
	tm_.tm_mon = month - 1;                    // 月，由于tm结构体的月份存储范围为0-11，所以tm_mon为int临时变量减去1。
	tm_.tm_mday = day;                         // 日。
	tm_.tm_hour = hour;                        // 时。
	tm_.tm_min = minute;                       // 分。
	tm_.tm_sec = second;                       // 秒。
	tm_.tm_isdst = 0;                          // 非夏令时。
	time_t t_ = mktime(&tm_);                  // 将tm结构体转换成time_t格式。
	return t_;                                 // 返回值。 
}

bool  stringToDatetime(std::string str, time_t& t)
{
	char* cha = (char*)str.data();             // 将string转换成char*。
	struct tm tm_;                                    // 定义tm结构体。
	int year, month, day, hour, minute, second;// 定义时间的各个int临时变量。
	sscanf(cha, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);// 将string存储的日期时间，转换为int临时变量。

	//printf("year=%02d,month=%02d,day=%02d,hour=%02d,minute=%02d,second=%02d\n",year, month, day, hour, minute, second);
	tm_.tm_year = year - 1900;                 // 年，由于tm结构体存储的是从1900年开始的时间，所以tm_year为int临时变量减去1900。
	tm_.tm_mon = month - 1;                    // 月，由于tm结构体的月份存储范围为0-11，所以tm_mon为int临时变量减去1。
	tm_.tm_mday = day;                         // 日。
	tm_.tm_hour = hour;                        // 时。
	tm_.tm_min = minute;                       // 分。
	tm_.tm_sec = second;                       // 秒。
	tm_.tm_isdst = 0;                          // 非夏令时。


	if (year < 0 || month < 0 || day < 0 || hour < 0 || minute < 0 || second < 0)
		return false;

	t = mktime(&tm_);                  // 将tm结构体转换成time_t格式。

	return true;                                 // 返回值。 
}

//16进制字符串化
std::string hextostring(unsigned char *digest, int len)
{
	static char hex[] = "0123456789abcdef";
	std::string dest;

	for (int j = 0; j < len; j++) {
		dest.push_back(hex[(digest[j] >> 4) & 0xf]);
		dest.push_back(hex[digest[j] & 0xf]);
	}
	return dest;
}


time_t stringToTime_t(std::string str)
{
	time_t t1 = 0;
	char *p = (char *)str.c_str();
	int h1, l2;
	unsigned char uc1 = 0;

	for (uint32_t i = 0; i < str.length();)
	{
		if ((*p) <= '9' && (*p) >= '0')
		{
			h1 = (*p - 0x30) * 16;
		}
		else if ((*p) <= 'f' && (*p) >= 'a')
		{
			h1 = (*p - 0x61 + 10) * 16;
		}
		if ((*(p + 1)) <= '9' && (*(p + 1)) >= '0')
		{
			l2 = (*(p + 1) - 0x30);
		}
		else if ((*(p + 1)) <= 'f' && (*(p + 1)) >= 'a')
		{
			l2 = (*(p + 1) - 0x61 + 10);
		}
		uc1 = h1 + l2;
		memcpy((unsigned char*)&t1 + i / 2, &uc1, 1);

		p += 2;
		i += 2;
	}
	return t1;
}



std::string  getSrcdeviceinfo()
{
	std::string str;
	char id[1024] = { 0 };
	size_t max = 100;
	memset(id, 0x00, sizeof(id));
	getdiskid(id, max);
	//printf("getdiskid id[%s]\n", id);

	str += id;

	memset(id, 0x00, sizeof(id));
	getcpuid(id, max);
	//printf("getcpuid id[%s]\n", id);
	if (id != 0)
		str += "-" + std::string(id);

	memset(id, 0x00, sizeof(id));
	getlocalmac(id, max); //根据启用的网卡不同，得到的mac也不同，不可用这个判断
	//printf("getlocalmac id[%s]\n", id);
//	if (id != 0)
//		str += "-" + std::string(id);
	//printf("str=%s,len=%d\n", str.c_str(), str.length());
	return str;
}
std::string  getdeviceinfo( bool bShowOld )
{
	std::string str = getSrcdeviceinfo();
	int len = str.length();
	if (len == 0)
		return str;
	//E5C6E1CE-F033-B475-EB11-C9414AB98E52-aarch64:64
	std::string sNew = str;
	for (int i = 0; i < len; i++)
	{
		char ch = str[i];
		if (ch == 'z')
			ch = 'a';
		else if (ch == 'Z')
			ch = 'A';
		else if (ch == '9')
			ch = '0';
		else if ((ch >= '0' && ch < '9') || (ch >= 'a' && ch < 'z') || (ch >= 'A' && ch < 'Z'))
			ch = ch + 1;
		else if (ch == '-')
			ch = '*';
		else if (ch == ':')
			ch = '#';
		sNew[i] = ch;
	}
	if ( bShowOld )
		printf("old str is: %s\n", str.c_str());
	return sNew;
}

void genmd5(std::string str, unsigned char *digest)
{
	context_md5_t ctx;
	hash_init_md5(&ctx);

	char buf[1024] = { 0 };
	strcpy(buf, str.c_str());

	int len = strlen(buf);
	buf[len] = 10;

	hash_update_md5(&ctx, (const unsigned char *)buf, len + 1);
	hash_final_md5(&ctx, digest);
}


int parse(std::string strSrc, std::string &md5, time_t &begin, time_t &end)
{
	if (strSrc[strSrc.length()-1] == 10)
	{
		strSrc = strSrc.substr(0, strSrc.length() - 1);
	}
	if (strSrc.length() != 48)
	{
		printf("strSrc len[%d] error\n", (int)(strSrc.length()));
		return -1;
	}
	char bufmd5[100] = { 0 };
	char bufbegin[100] = { 0 };
	char bufend[100] = { 0 };
	char *p = (char *)strSrc.c_str();

	memcpy(bufmd5, p, 16);
	p += 16;
	memcpy(bufbegin, p, 8);
	p += 8;
	memcpy(bufmd5+16, p, 8);
	p += 8;
	memcpy(bufend, p, 8);
	p += 8;
	memcpy(bufmd5 + 24, p, 8);

	md5 = bufmd5;

	begin = stringToTime_t(bufbegin);
	end = stringToTime_t(bufend);
	return  0;
}