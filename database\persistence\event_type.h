/**
 * @addtogroup odbDatabaseGroup
 * @brief 事件类型
 * @{
 */
#ifndef _EVENTTYPE_H
#define _EVENTTYPE_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
/**
 * @brief: 事件类型 对应数据库aimonitorV3的表wn_event_type
 */
#pragma db object table("wn_event_type")
class EventType {
public:

    EventType(const std::string& name,
        unsigned long typeId,
        const std::string& remark,
        bool isDel,
        bool isEnable

    )
        : name(name), typeId(typeId), remark(remark),
        isDel(isDel), isEnable(isEnable)
    {
    }

    unsigned long getId() const {
        return id;
    }

    const std::string& getEvtTypeName() const {
        return name;
    }

    unsigned long geyEvtTypeId() const {
        return typeId;
    }

    const std::string& getEvtTypeRemark() const {
        return remark;
    }

    bool getIsDel() const {
        return isDel;
    }


    bool getIsEnable() const {
        return isEnable;
    }


private:

    friend class odb::access;
    EventType() {}

private:
#pragma db id auto
    unsigned long id;                  //!< 表ID

#pragma db column("name")  type("VARCHAR(255)")
    std::string name;                  //!< 事件名称

#pragma db column("type_id")
    unsigned long typeId;              //!< 事件类型id

#pragma db column("remark")  type("VARCHAR(255)")
    std::string remark;                //!< 事件摘要

#pragma db column("is_del") type("INT")
    bool isDel;                        //!< 是否删除

#pragma db column("is_enable") type("INT")
    bool isEnable;                     //!< 是否使能

};
}
#endif //_EVENTTYPE_H

/**
 * @}
 */