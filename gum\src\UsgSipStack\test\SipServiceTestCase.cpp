#include <iostream>

#include "../include/UsgSipStackExp.hpp"
#include "../src/UsgSipUdpService.hpp"
#include "../src/UsgSipStack.hpp"

#include "SipServiceTestCase.hpp"

namespace usg {

CPPUNIT_TEST_SUITE_REGISTRATION( CSipServiceTestCase );

class CCtxHandler : public ICtxHandler
{
public:
    virtual bool commitInvite( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result )
    {
        result = "<invite>0</invite>";
        return true;
    }
    virtual bool commitBye( const std::string &sid )
    {
        return true;
    }
    virtual bool commitAnswer( const std::string &sid, const char *xml, size_t len, bool status, int type )
    {
        return true;
    }
    virtual bool commitCancel( const std::string &sid )
    {
        return true;
    }
    virtual bool commitAck( const std::string &sid )
    {
        return true;
    }

    virtual bool commitDdcpDo( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result )
    {
        result = "<ddcp>0</ddcp>";
        return true;
    }

    virtual bool commitRegist( const std::string &sid )
    {
        return true;
    }
    virtual bool commitRegist( const std::string &sid,const std::string &oid,int expries )
    {
        return true;
    }

    virtual bool commitSubscribe( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result )
    {
        result = "<subscribe>0</subscribe>";
        return true;
    }

    virtual bool commitNotify( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result )
    {
        result = "<notify>0</notify>";
        return true;
    }
    virtual bool setCseqValues( const std::string& sid ,int cseq )
    {
        return true;
    }
    virtual bool getCsqlValues( const std::string& sid, int& cseq )
    {
        return true;
    }
};

void CSipServiceTestCase::testSipService()
{
 //   SipRuntime::init();

	CUsgSipStack sipstack;
	sipstack.startup();
	boost::shared_ptr<IUsgSipUdpService> service = sipstack.createUdpSipService( "127.0.0.1", 5060, "111111111100000000" );

    service->init();

    CCtxHandler handler;
    IInviteSession *invite = service->inviteSession( &handler );

    std::string sid;
    invite->invite( sid, "sip:xxx@127.0.0.1:5070", "<invite>invite</invite>" );

    invite->bye( sid );

    IDdcpDoSession *ddcpDo = service->ddcpDoSession( &handler );
    ddcpDo->ddcpDo( sid, "sip:xxx@127.0.0.1:5070", "<ddcp>do</ddcp>" );

//     IRegistSession *regist = service->registSession( &handler );
//     regist->regist( sid, "sip:xxx@127.0.0.1:5070" );

    INotifySession *notify = service->notifySession( &handler );
    notify->subscribe( sid, "sip:xxx@127.0.0.1:5070", "<subscribe>subscribe</subscribe>" );
    notify->notify( sid, "sip:xxx@127.0.0.1:5070", "<notify>notify</notify>" );


//    std::string str;
//    std::cin >> str;

    service->fini();
	service.reset();

	sipstack.shutdown();
//    SipRuntime::fini();
}

}
