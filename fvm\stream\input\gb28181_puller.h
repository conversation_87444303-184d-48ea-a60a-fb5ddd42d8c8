/**
 * Project FVM
 */

#pragma once
#include "platform_puller.h"
#include "streamsocket.h"

 /**
  * @brief: GB28181输入
  */
namespace fvm::stream
{
    class GB28181Puller : public PlatformPuller
    {
    public:
        GB28181Puller(FrontPlatformPtr platform);
    private:
        void initOptions() override;

        /**
         * 打开输入流
         */
        bool open() override;

        /**
         * 关闭输入流
         */
        void close() override;

        /**
        * 线程函数
        */
        void process() override;

        /**
        * 处理实时数据
        */
        void dealData(uint8_t* buf, int buf_size);

        /** 
        * 处理rtp包头
        */
        uint8_t* dealRtpHeader(uint8_t* buf, int len, int& outLen);

    private:
        int localPort = 0;                      //当前服务器监听的端口
        SocketServer::Ptr streamSock;           //网络模块
        CCircularQueue streamQueue;             //缓存队列
    };

}