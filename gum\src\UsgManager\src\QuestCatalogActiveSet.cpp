#include "../include/UsgManagerExp.hpp"
#include "UsgManager.hpp"

#include "QuestCatalogSet.hpp"
#include "QuestCatalogActiveSet.hpp"

#ifdef linux
#include <sys/prctl.h>
#endif

namespace usg {

    CQuestCatalogActiveSet::CQuestCatalogActiveSet()
    {

    }

    CQuestCatalogActiveSet::~CQuestCatalogActiveSet()
    {

    }

    CQuestCatalogActiveSet* CQuestCatalogActiveSet::m_catalogActiveSet = 0;

    bool CQuestCatalogActiveSet::init()
    {
        if( CQuestCatalogActiveSet::m_catalogActiveSet == 0 )
            m_catalogActiveSet = new CQuestCatalogActiveSet;
        return true;
    }

    bool CQuestCatalogActiveSet::fini()
    {
        if( CQuestCatalogActiveSet::m_catalogActiveSet != 0 )
        {
            delete CQuestCatalogActiveSet::m_catalogActiveSet;
            CQuestCatalogActiveSet::m_catalogActiveSet = 0;
        }

        return true;
    }

    CQuestCatalogActiveSet* CQuestCatalogActiveSet::get()
    {
        return m_catalogActiveSet;
    }

    bool CQuestCatalogActiveSet::removeActive( const std::string &sid, const std::string &devid )
    {
        boost::mutex::scoped_lock lock( m_mutex );

        std::pair< std::string, std::string > id( sid, devid );
        if( m_atives.find( id ) == m_atives .end() ) return false;

        m_atives[id]->quit();

        m_atives.erase( id );

/*	boost::thread( boost::bind( &CQuestCatalogActiveSet::pushCatalogInfos, this, sid, devid ) );*/

        return true;
    }

// void CQuestCatalogActiveSet::pushCatalogInfos( const std::string &sid, const std::string &devid )
// {
// 	SCatalog catalogInfos;
//
// 	if( !CQuestCatalogSet::get()->takeCatalog( sid, devid, catalogInfos ) ) return;
//
// 	if( catalogInfos.subItems.empty() ) return;
//
// 	///< 直接获取数据，并将数据给缓存
// 	boost::shared_ptr< IUsgManager > sg;
// 	if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return;
//
// 	boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
// 	if( sgc == 0 ) return;
//
// 	sgc->pushReceiveCatalog( catalogInfos );
// }

    bool CQuestCatalogActiveSet::creatActive( const std::string &sid, const std::string &devid, int waitSec )
    {
        boost::mutex::scoped_lock lock( m_mutex );

        std::pair< std::string, std::string > id( sid, devid );

        if( m_atives.find( id ) != m_atives.end() ) return true;

        m_atives[id] = new CQuestCatalogActive( sid, devid, waitSec );
        m_atives[id]->wait();
        return true;
    }

    bool CQuestCatalogActiveSet::cleanActives()
    {
        boost::mutex::scoped_lock lock( m_mutex );

        for( std::map< std::pair< std::string, std::string >, CQuestCatalogActive* >::iterator iter = m_atives.begin(); iter != m_atives.end(); ++iter )
        {
            ( *iter ).second->clean();
        }

        m_atives.clear();

        return true;
    }

    bool CQuestCatalogActiveSet::cutCalalog( const std::string &sid, const std::string &devid )
    {
        boost::mutex::scoped_lock lock( m_mutex );

        std::pair< std::string, std::string > id( sid, devid );
        if( m_atives.find( id ) == m_atives .end() ) return false;

//	m_atives[id]->quit();

        m_atives.erase( id );

        return true;
    }

    CQuestCatalogActive::CQuestCatalogActive( const std::string &sid, const std::string devid, int time )
            : m_sid( sid ), m_devid( devid ), m_time( time ), m_isWait( true ), m_isClean( false )
    {

    }

    CQuestCatalogActive::~CQuestCatalogActive()
    {

    }

    bool CQuestCatalogActive::wait()
    {
        m_waitThread.reset( new boost::thread( boost::bind( &CQuestCatalogActive::control, this ) ) );

        return true;
    }

    bool CQuestCatalogActive::quit()
    {
        boost::mutex::scoped_lock lock( m_mutex );

        m_isWait = false;

        m_cond.notify_all();
        if ( m_waitThread )
        {
            m_waitThread->join();
            m_waitThread.reset();
        }
        return true;
    }

    void CQuestCatalogActive::control()
    {
#ifdef linux
        char chBuf[255];
	sprintf(chBuf,"%s:%s", __FUNCTION__, __FILE__);
	prctl(PR_SET_NAME, chBuf );
#endif
        bool checkOk = false;
        std::string resultSid;
        int hasChangeFalse = 1;
        while( true )
        {
            boost::mutex::scoped_lock lock( m_mutex );
            if( !m_isWait )
            {
                return;
            }
            CQuestCatalogSet* pSet = CQuestCatalogSet::get();
            if ( !pSet )
                break;
            if( pSet->check( resultSid, m_devid ) )
            {
                checkOk = true;
                break;
            }
            else
            {
                if( !pSet->hasChange( m_devid ) )
                {
// 				std::cerr << "!CQuestCatalogSet::get()->hasChange( resultSid, m_devid ) hasChangeFalse : " << hasChangeFalse << std::endl;
                    if( hasChangeFalse > 3 ) break;
                }

                ++hasChangeFalse;
            }

            if( m_cond.timed_wait( lock, boost::get_system_time() + boost::posix_time::seconds( m_time ) ) )
            {
                if( m_isClean )
                {
                    delete this;
                    return;
                }
            }
        }
        if( !m_isWait )
            return;
        if( checkOk )  ///< 在规定时间内完成接收数据
        {
            SCatalog catalogInfos;
            CQuestCatalogSet* pSet = CQuestCatalogSet::get();
            if ( !pSet )
                return;
            if( !pSet->takeCatalog( resultSid, m_devid, catalogInfos ) ) return;

            if( !catalogInfos.subItems.empty() )
            {
                ///< 直接获取数据，并将数据给缓存
                boost::shared_ptr< IUsgManager > sg;
                if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return;

                boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
                if( sgc == 0 ) return;

                sgc->pushReceiveCatalog( catalogInfos );
            }

            CQuestCatalogActiveSet::get()->cutCalalog( m_sid, m_devid );
        }
        else
        {
            if( m_isWait ) ///< 超时
            {
                CQuestCatalogSet* pSet = CQuestCatalogSet::get();
                if ( !pSet )
                    return;
                SCatalog catalogInfos;
                if( pSet->removeCatalog( m_sid, m_devid, catalogInfos ) )
                {
                    if( !catalogInfos.subItems.empty() )
                    {
                        ///< 直接获取数据，并将数据给缓存
                        boost::shared_ptr< IUsgManager > sg;
                        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return;

                        boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
                        if( sgc == 0 ) return;

                        sgc->pushReceiveCatalog( catalogInfos );
                    }
                }
            }

            reQuestCalalog();
        }

        delete this;
    }

    void CQuestCatalogActive::reQuestCalalog()
    {
        CQuestCatalogActiveSet::get()->cutCalalog( m_sid, m_devid );

        boost::shared_ptr< IUsgManager > sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return;

        boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return;

        wtoe::CSharedTimer::sleep( 500 );
        sgc->onReceiveQueryDeviceCatalog( m_devid );
    }

    bool CQuestCatalogActive::clean()
    {
        boost::mutex::scoped_lock lock( m_mutex );

        m_isClean = true;
        m_cond.notify_all();
        return true;
    }

}
