#include "MessageSNSave.h"
#include "Utility.h"
#include <stdio.h>
#include <iostream>

MessageSNSave::MessageSNSave():Thread(false)
{
	SetDeleteOnExit(false);
}

MessageSNSave::~MessageSNSave(void)
{
	printf(" MessageSNSave::~~~~MessageSNSave() called !!!!!!!!!!!!!!!!!!\n");
}

void MessageSNSave::Run()
{
	while (IsRunning())
	{
		Utility::Sleep(500);
#if 0
		{
			m_mutex.Lock();
			m_lastSN.clear();
			m_mutex.Unlock();
		}
			Utility::Sleep(1000*30);
#endif
	}
}


void MessageSNSave::InsertSNLast(uint64_t sn)
{
#if 0
	Lock lock(m_mutex);

	std::list<uint64_t>::iterator it = m_lastSN.begin();

	for (; it != m_lastSN.end(); ++it)
	{
		if ((*it) < sn) continue;
		if ((*it) >= sn)
		{
			m_lastSN.insert(it,1,sn);
			break;
		}
	}

	if (it == m_lastSN.end())
	{
		m_lastSN.push_back(sn);
	}
#endif

	Lock lock(m_mutex);
	m_lastSN.push_back(sn);
	return;
}

bool MessageSNSave::IsExists(uint64_t sn)
{
	bool bRet = false;

	Lock lock(m_mutex);

	std::list<uint64_t>::iterator it = m_lastSN.begin();

	for (; it != m_lastSN.end(); ++it)
	{
		if ((*it) == sn)
		{
			bRet = true;
			break;
		}
	}
	return bRet;
}
