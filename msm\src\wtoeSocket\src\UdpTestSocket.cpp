#include "UdpTestSocket.h"

#include "rapidjson/reader.h"
#include "rapidjson/document.h"
#include <iostream>
#include "MessageHandle.h"
#include "Utility.h"
#include <stdio.h>

using namespace rapidjson;
using namespace std;


UdpTestSocket::UdpTestSocket(ISocketHandler& h)
:UdpSocket(h)
{
}

UdpTestSocket::~UdpTestSocket()
{
}


void UdpTestSocket::OnRawData(const char *p,size_t l,struct sockaddr *sa_from,socklen_t sa_len)
{
	if (sa_len == sizeof(struct sockaddr_in)) // IPv4
	{
		struct sockaddr_in sa;
		memcpy(&sa,sa_from,sa_len);
		ipaddr_t a;
		memcpy(&a,&sa.sin_addr,4);
		std::string ip;
		Utility::l2ip(a,ip);
#if 0
		printf("Received %lu bytes from: %s:%d\n", (unsigned long)l,ip.c_str(),ntohs(sa.sin_port));
		printf("%s\n",static_cast<std::string>(p).substr(0,l).c_str());
#endif

		string data = static_cast<std::string>(p).substr(0,l).c_str();

		Document doc;
		doc.Parse(data.c_str());

		if(doc.HasParseError())
		{
			return;
		}
		
		if (!doc.IsObject())
		{
			return;
		}

		if(!doc.HasMember("sn") || !doc["sn"].IsUint64())
		{
			return;
		}

		if(!doc.HasMember("type") || !doc["type"].IsString())
		{
			return;
		}

		if(!doc.HasMember("from") || !doc["from"].IsString())
		{
			return;
		}

		RawMessage message;
		message.sn =  doc["sn"].GetUint64();
		message.type = doc["type"].GetString();
		message.from = doc["from"].GetString();
		message.ipSrc = ip;
		message.strRawMessage = data;

		//if (!m_pMessageSNSave->IsExists(message.sn))
		{
			//m_pMessageSNSave->InsertSNLast(message.sn);
			m_pMessageHandle->InsertMessage(message);
		}

	}
	else
	if (sa_len == sizeof(struct sockaddr_in6)) // IPv6
	{
	}
}


void UdpTestSocket::setMessageHandle(MessageHandle *pMessageHandle)
{
	m_pMessageHandle = pMessageHandle;
}

/*
void UdpTestSocket::SetMessageSNSaveHandle(MessageSNSave *pMessageSnSaveHandle)
{
	m_pMessageSNSave = pMessageSnSaveHandle;
}
*/