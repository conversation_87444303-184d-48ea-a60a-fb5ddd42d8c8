
#include <fstream>
#include <pjlib.h>
#include <pjlib-util.h>

#include <boost/uuid/uuid_generators.hpp>
#include <boost/algorithm/string/classification.hpp>
#include <boost/algorithm/string/predicate.hpp>

#include "ConfigFile.hpp"
#include "../include/Gb28181Type.hpp"

namespace gb28181
{

static const std::string ACCESSNAME = "/access.cfg";
static const std::string RESNAME = "/res.cfg";

CConfigFile::CConfigFile()
{

}

bool CConfigFile::init( const std::string &relativePath )
{
    std::string p = relativePath + ACCESSNAME;
    if( !loadAccess( p ) )
        return false;
    p = relativePath + RESNAME;
//     if( !loadRes( p ) )
//         return false;
    return true;
}

bool CConfigFile::getResName( const std::string &addrCode, std::string &resName )
{
	boost::mutex::scoped_lock lock( m_mutex );
    resName = "";

	std::vector< SCatalog::SItem >::iterator iter = m_resEx.begin();
	while( iter != m_resEx.end() )
	{
		if( ( *iter ).itemAddr == addrCode )
		{
			 resName = ( *iter ).itemName;
			 return true;
		}

		++iter;
	}
//     if( m_resAddrCodeMap.find( addrCode ) == m_resAddrCodeMap.end() )
//         return false;
//     resName = m_resEx[ m_resAddrCodeMap[ addrCode ] ].itemName;
    return false;
}

bool CConfigFile::getResUuid( const std::string &addrCode, boost::uuids::uuid &resUid )
{
	boost::mutex::scoped_lock lock( m_mutex );
	std::vector< SCatalog::SItem >::iterator iter = m_resEx.begin();
	while( iter != m_resEx.end() )
	{
		if( ( *iter ).itemAddr == addrCode )
		{
			resUid = ( *iter ).resUuid;
			return true;
		}

		++iter;
	}
//     if( m_resAddrCodeMap.find( addrCode ) == m_resAddrCodeMap.end() )
//         return false;
//     resUid = m_resEx[ m_resAddrCodeMap[ addrCode ] ].resUuid;
    return false;
}
bool CConfigFile::getDecoderTag( const std::string &addrCode, std::string &dt )
{
    if( m_resAddrCodeMap.find( addrCode ) == m_resAddrCodeMap.end() )
        return false;
    dt = m_resEx[ m_resAddrCodeMap[ addrCode ] ].itemDecoderTag;
    return true;
}
bool CConfigFile::getSubImageSize( const std::string &addrCode, uint8_t &imageSize )
{
	boost::mutex::scoped_lock lock( m_mutex );
	std::vector< SCatalog::SItem >::iterator iter = m_resEx.begin();
	while( iter != m_resEx.end() )
	{
		if( ( *iter ).itemAddr == addrCode )
		{
			imageSize = ( *iter ).itemSubImageSize;
			return true;
		}

		++iter;
	}
//     if( m_resAddrCodeMap.find( addrCode ) == m_resAddrCodeMap.end() )
//         return false;
//     imageSize = m_resEx[ m_resAddrCodeMap[ addrCode ] ].itemSubImageSize;
    return false;
}
bool CConfigFile::getMasterImageSize( const std::string &addrCode, uint8_t &imageSize )
{
	boost::mutex::scoped_lock lock( m_mutex );
	std::vector< SCatalog::SItem >::iterator iter = m_resEx.begin();
	while( iter != m_resEx.end() )
	{
		if( ( *iter ).itemAddr == addrCode )
		{
			imageSize = ( *iter ).itemMasterImageSize;
			return true;
		}

		++iter;
	}
//     if( m_resAddrCodeMap.find( addrCode ) == m_resAddrCodeMap.end() )
//         return false;
//     imageSize = m_resEx[ m_resAddrCodeMap[ addrCode ] ].itemMasterImageSize;
    return false;
}
bool CConfigFile::getResAddrCode( const boost::uuids::uuid &resUid, std::string &addrCode )
{
	boost::mutex::scoped_lock lock( m_mutex );
	std::vector< SCatalog::SItem >::iterator iter = m_resEx.begin();
	while( iter != m_resEx.end() )
	{
		if( ( *iter ).resUuid == resUid )
		{
			addrCode = ( *iter ).itemAddr;
			return true;
		}

		++iter;
	}
//     if( m_resUuidMap.find( resUid ) == m_resUuidMap.end() )
//         return false;
//     addrCode = m_resEx[ m_resUuidMap[ resUid ] ].itemAddr;
    return false;
}

bool CConfigFile::getResMulti( const size_t from, const size_t to, std::vector< SCatalog::SItem > &ress )
{
	boost::mutex::scoped_lock lock( m_mutex );
    if( from > to )
        return false;
    if( from == 0 || to == 0 )
        return false;
    if( from > m_resEx.size() )
        return false;
    // 如果to过大,则结束截取.
    ress.clear();
    for( size_t i = from-1; i < to && i < m_resEx.size() ; ++i )
    {
        ress.push_back( m_resEx[i] );
    }
    return true;
}

size_t CConfigFile::getResSize()
{
	boost::mutex::scoped_lock lock( m_mutex );
    return m_resEx.size();
}

SAccess CConfigFile::getRemote()
{
    return m_remoteAccess;
}
SAccess CConfigFile::getLocal()
{
    return m_localAccess;
}

class CPoolDes
{
public:
    CPoolDes( pj_pool_t *pool, pj_caching_pool *caching_pool )
        :m_pool(pool)
        ,m_caching_pool(caching_pool){}
    ~CPoolDes()
    {
        if( m_pool )
        {
            pj_pool_release( m_pool );
            m_pool = 0;
        }
        if ( m_caching_pool )
        {
            pj_caching_pool_destroy( m_caching_pool );
        }
    }
private:
    pj_pool_t *m_pool;
    pj_caching_pool *m_caching_pool;
};

bool CConfigFile::loadAccess( const std::string &accessFile )
{
    std::ifstream in( accessFile.c_str() );
    if( !in.is_open() )
        return false;
    std::istreambuf_iterator< char > beg(in), end;
    std::string strdata(beg, end);//����string st;st.assign(beg,end);
    in.close();

    pj_caching_pool caching_pool;
    pj_pool_t *pool = 0;

    pj_caching_pool_init( &caching_pool, &pj_pool_factory_default_policy, 0 );
    pj_pool_factory *mem = 0;
    mem = &caching_pool.factory;
    pool = pj_pool_create( mem, "CConfigFile", 4096, 1024, 0 );
    if( pool == 0 )
        return false;

    {
//        CPoolDes poolDes( pool, &caching_pool );

        pj_str_t msg;
        msg.ptr = 0;
        msg.slen = 0;
        pj_xml_node *root = 0;

        pj_strdup2( pool, &msg, strdata.c_str() );
        if( msg.ptr == 0 )
            return false;
        root = pj_xml_parse( pool, msg.ptr, msg.slen );
        if( root == 0 )
            return false;

        std::string str;
        str.assign( root->name.ptr, root->name.slen );
        if( str != "access" )
            return false;

        pj_str_t dst;
        pj_xml_node *oneAccessRoot = 0;

#if 0
        pj_strdup2( pool, &dst, "remote" );
        oneAccessRoot = pj_xml_find_node( root, &dst );
        if( oneAccessRoot == 0 )
            return false;
        if( !pOneAccess( pool, oneAccessRoot, m_remoteAccess ) )
            return false;
#endif
        pj_strdup2( pool, &dst, "local" );
        oneAccessRoot = pj_xml_find_node( root, &dst );
        if( oneAccessRoot == 0 )
            return false;
        if( !pOneAccess( pool, oneAccessRoot, m_localAccess, true ) )
            return false;
    }

	pj_pool_release( pool );
	pool = 0;
	pj_caching_pool_destroy( &caching_pool );

    return true;
}

bool CConfigFile::pOneAccess( pj_pool_t *pool, pj_xml_node *p, SAccess &sa, bool isHasUserNamdAndPasswd )
{
    if( pool == 0 )
        return false;
    if( p == 0 )
        return false;

    pj_str_t dst;
    pj_xml_node *item = 0;
    SAccess tmp;
    std::string value;

    pj_strdup2( pool, &dst, "ip" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.ip.assign( item->content.ptr, item->content.slen );
    if( !isValidIpAddressType( tmp.ip ) )
        return false;

    pj_strdup2( pool, &dst, "port" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    value.assign( item->content.ptr, item->content.slen );
    if( !valueToLexical< std::string, uint16_t >( value, tmp.port ) )
        return false;

    pj_strdup2( pool, &dst, "code" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.addrCode.assign( item->content.ptr, item->content.slen );
    if( !isValidDeviceAddressType( tmp.addrCode ) )   {
        return false;
    }

	if ( isHasUserNamdAndPasswd )
	{
		pj_strdup2( pool, &dst, "userName" );
		item = pj_xml_find_node( p, &dst );
		if( item == 0 )
			return false;
		tmp.usrName.assign( item->content.ptr, item->content.slen );
		if( !isValidDeviceAddressType( tmp.usrName ) )
			return false;

		pj_strdup2( pool, &dst, "password" );
		item = pj_xml_find_node( p, &dst );
		if( item == 0 )
			return false;
		tmp.password.assign( item->content.ptr, item->content.slen );
		if( !isValidDeviceAddressType( tmp.password ) )
			return false;

	}

    sa = tmp;
    return true;
}

bool CConfigFile::loadRes( const std::string &resFile )
{
    std::ifstream in( resFile.c_str() );
    if( !in.is_open() )
        return false;
    std::istreambuf_iterator< char > beg(in), end;
    std::string strdata(beg, end);//����string st;st.assign(beg,end);
    in.close();

    pj_caching_pool caching_pool;
    pj_pool_t *pool = 0;

    pj_caching_pool_init( &caching_pool, &pj_pool_factory_default_policy, 0 );
    pj_pool_factory *mem = 0;
    mem = &caching_pool.factory;
    pool = pj_pool_create( mem, "CConfigFile", 4096, 1024, 0 );
    if( pool == 0 )
        return false;

    {
        CPoolDes poolDes( pool, &caching_pool );

        pj_str_t msg;
        msg.ptr = 0;
        msg.slen = 0;
        pj_xml_node *root = 0;

        pj_strdup2( pool, &msg, strdata.c_str() );
        if( msg.ptr == 0 )
            return false;
        root = pj_xml_parse( pool, msg.ptr, msg.slen );
        if( root == 0 )
            return false;

        std::string str;
        str.assign( root->name.ptr, root->name.slen );
        if( str != "res" )
            return false;

        pj_str_t dst;
        pj_xml_node *oneItemRoot = 0;

        pj_strdup2( pool, &dst, "item" );
        oneItemRoot = pj_xml_find_node( root, &dst );
        if( oneItemRoot == 0 )
            return false;
        int pos = 0;
        while( oneItemRoot != 0 )
        {
            SCatalog::SItem tmp;
            if( !pOneItem( pool, oneItemRoot, tmp ) )
                return false;

            m_resEx.push_back( tmp );
            m_resAddrCodeMap[ tmp.itemAddr ] = pos;
            m_resUuidMap[ tmp.resUuid ] = pos;
            ++pos;

            pj_xml_node *next = 0;
            next = pj_xml_find_next_node( root, oneItemRoot, &dst );
            oneItemRoot = next;
        }
    }

    return true;

}

bool CConfigFile::pOneItem( pj_pool_t *pool, pj_xml_node *p, SCatalog::SItem &oneItem )
{
    if( pool == 0 )
        return false;
    if( p == 0 )
        return false;

    pj_str_t dst;
    pj_xml_node *item = 0;
    SCatalog::SItem tmp;
    std::string value;

    pj_strdup2( pool, &dst, "code" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.itemAddr.assign( item->content.ptr, item->content.slen );
    if( !isValidDeviceAddressType( tmp.itemAddr ) )
        return false;

    pj_strdup2( pool, &dst, "uuid" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    value.assign( item->content.ptr, item->content.slen );
    boost::algorithm::erase_all( value, "-" );
    if ( value.size() != ( boost::uuids::uuid::static_size() + boost::uuids::uuid::static_size() ) )
        return false;
    if ( false == boost::algorithm::all( value, boost::algorithm::is_xdigit() ) ) {
        return false;
    }
	try
	{
        tmp.resUuid = boost::uuids::string_generator() ( value );
	}
	catch (...)
	{
		return false;
	}
    pj_strdup2( pool, &dst, "subimagesize" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    int intTmp = 0;
    if( !valueToLexical< std::string, int >( std::string( item->content.ptr, item->content.slen ), intTmp ) )
        return false;
    if( intTmp > 255 )
        return false;
    tmp.itemSubImageSize = intTmp;

    pj_strdup2( pool, &dst, "masterimagesize" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    intTmp = 0;
    if( !valueToLexical< std::string, int >( std::string( item->content.ptr, item->content.slen ), intTmp ) )
        return false;
    if( intTmp > 255 )
        return false;
    tmp.itemMasterImageSize = intTmp;

    pj_strdup2( pool, &dst, "name" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.itemName.assign( item->content.ptr, item->content.slen );

    pj_strdup2( pool, &dst, "restype" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.itemResType.assign( item->content.ptr, item->content.slen );
    if( !isValidResType( tmp.itemResType ) )
        return false;

    pj_strdup2( pool, &dst, "ressubtype" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.itemResSubType.assign( item->content.ptr, item->content.slen );
    if( !isValidResSubType( tmp.itemResSubType ) )
        return false;

    pj_strdup2( pool, &dst, "privilege" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.itemPrivilege.assign( item->content.ptr, item->content.slen );
    if( !isValidPrivilegeType( tmp.itemPrivilege ) )
        return false;

    pj_strdup2( pool, &dst, "status" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.itemStatus.assign( item->content.ptr, item->content.slen );
    if( !isValidBoolResultType( tmp.itemStatus ) )
        return false;

    pj_strdup2( pool, &dst, "longitude" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.itemLongitude.assign( item->content.ptr, item->content.slen );
    intTmp = 0;
    if( !valueToLexical< std::string, int >( tmp.itemLongitude, intTmp ) )
        return false;

    pj_strdup2( pool, &dst, "latitude" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.itemLatitude.assign( item->content.ptr, item->content.slen );
    intTmp = 0;
    if( !valueToLexical< std::string, int >( tmp.itemLatitude, intTmp ) )
        return false;

    pj_strdup2( pool, &dst, "elevation" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.itemElevation.assign( item->content.ptr, item->content.slen );
    intTmp = 0;
    if( !valueToLexical< std::string, int >( tmp.itemElevation, intTmp ) )
        return false;

    pj_strdup2( pool, &dst, "decodertag" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.itemDecoderTag.assign( item->content.ptr, item->content.slen );
    if( !isValidDecoderTagType( tmp.itemDecoderTag ) )
        return false;

    pj_strdup2( pool, &dst, "manufacturer" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.manufacturer.assign( item->content.ptr, item->content.slen );

    pj_strdup2( pool, &dst, "model" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.model.assign( item->content.ptr, item->content.slen );

    pj_strdup2( pool, &dst, "owner" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.owner.assign( item->content.ptr, item->content.slen );

    pj_strdup2( pool, &dst, "civilcode" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.civilcode.assign( item->content.ptr, item->content.slen );

    pj_strdup2( pool, &dst, "address" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.address.assign( item->content.ptr, item->content.slen );

    pj_strdup2( pool, &dst, "parental" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.parental.assign( item->content.ptr, item->content.slen );

    pj_strdup2( pool, &dst, "registerway" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.registerway.assign( item->content.ptr, item->content.slen );

    pj_strdup2( pool, &dst, "secrecy" );
    item = pj_xml_find_node( p, &dst );
    if( item == 0 )
        return false;
    tmp.secrecy.assign( item->content.ptr, item->content.slen );


    tmp.itemOperateType = EOPERATETYPE_ADD;
    tmp.itemSubnum = 0;

    oneItem = tmp;
    return true;
}

void CConfigFile::setCatalogs( const std::vector< SCatalog::SItem > &res )
{
	boost::mutex::scoped_lock lock( m_mutex );
	m_resEx = res;
}

void CConfigFile::updataItem( const SCatalog::SItem &item )
{
	boost::mutex::scoped_lock lock( m_mutex );

	for( std::vector< SCatalog::SItem >::iterator iter = m_resEx.begin(); iter != m_resEx.end(); ++iter )
	{
		if( ( *iter ).itemAddr != item.itemAddr ) continue;

		*iter = item;
	}
}

bool CConfigFile::getItem( const std::string &addrCode, SCatalog::SItem &item )
{
	boost::mutex::scoped_lock lock( m_mutex );

	for( std::vector< SCatalog::SItem >::iterator iter = m_resEx.begin(); iter != m_resEx.end(); ++iter )
	{
		if( ( *iter ).itemAddr != addrCode ) continue;

		item = *iter;

		return true;
	}

	return false;
}

}
