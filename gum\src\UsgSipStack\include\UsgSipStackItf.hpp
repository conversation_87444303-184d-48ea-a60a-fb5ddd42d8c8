#ifndef USGSIPSTACKITF_HPP_
#define USGSIPSTACKITF_HPP_

#include <string>
#include <stdint.h>
#include <vector>

#include <boost/shared_ptr.hpp>

#include <pjlib.h>
#include <pjsip.h>

#include "UsgSipStackCfg.hpp"

namespace usg {

interface ICtxHandler;

interface IInviteSession;
interface INotifySession;
interface IRegistSession;
interface IDdcpDoSession;
interface IUsgSipUdpService;

interface IUsgSipUdpService
{
    virtual bool init() = 0;
    virtual bool fini() = 0;
    virtual bool okey() = 0;

    virtual IInviteSession *inviteSession( ICtxHandler *handler ) = 0;
    virtual INotifySession *notifySession( ICtxHandler *handler ) = 0;
    virtual IRegistSession *registSession( ICtxHandler *handler ) = 0;
    virtual IDdcpDoSession *ddcpDoSession( ICtxHandler *handler ) = 0;

    virtual ~IUsgSipUdpService() {};
};

interface IInviteSession
{
    enum { SESSION_TYPE = 0 };

    virtual bool invite( std::string &sid, const std::string &sipUri, const std::string &result ) = 0;
    virtual bool cancel( const std::string &sid ) = 0;
    virtual bool bye( const std::string &sid ) = 0;
//	virtual bool ack( const std::string &cid, const std::string &sipUri ) { return true; };

    virtual bool bye( const std::string&sid, int cseq ) = 0;

    virtual bool getReadSipInfo( const std::string& sid,
        pjsip_uri *&target,
        pjsip_from_hdr *&from,
        pjsip_to_hdr *&to,
        pjsip_cid_hdr *&call_id ) = 0;

	virtual bool getReadSipInfo( const std::string& sid,
		pjsip_uri *&target,
		pjsip_from_hdr *&from,
		pjsip_to_hdr *&to,
		pjsip_cid_hdr *&call_id,
		pjsip_contact_hdr *&contact ) { return true; };

    //gb28181��Ŀ����
    virtual bool invite( std::string &sid, const std::string &sipUri, const std::string &subject, const std::string &result ) = 0;
	virtual bool invite( std::string &sid, const std::string &cid, const std::string &sipUri, const std::string &subject, const std::string &result ) { return true; };
   virtual bool setSipContactUri( const std::string sipContactUri ) = 0;

    virtual ~IInviteSession() {};
};

interface INotifySession
{
    enum { SESSION_TYPE = 1 };

    virtual bool subscribe( std::string &sid, const std::string &sipUri, const std::string &result ) = 0;
    virtual bool notify( std::string &sid, const std::string &sipUri, const std::string &result ) = 0;
	virtual bool notify( const std::string &sid, const std::string &sipUri, const std::vector<std::string> &result ) = 0;
	virtual bool setSipUrl( const std::string catalogSipUrl ) = 0;
    virtual void setExpries( uint32_t iExpries ){};

    virtual ~INotifySession() {};
};

interface IRegistSession
{
    enum { SESSION_TYPE = 2 };

//    virtual bool regist( std::string &sid, const std::string &sipUri ) = 0;

//	virtual bool unregist( std::string &sid, const std::string &sipUri ) = 0;

    virtual void setExpries( uint32_t iExpries ) = 0;

    virtual uint32_t getExpries(){return 0;};

	virtual void setUserNameAndPasswd( std::string &userName, std::string &passwd ) = 0;

    virtual ~IRegistSession() {};
};

interface IDdcpDoSession
{
    enum { SESSION_TYPE = 3 };

    virtual bool ddcpDo( std::string &sid, const std::string &sipUri, const std::string &result ) = 0;
	virtual bool ddcpDo( std::string &sid, const std::string &callid, const std::string &sipUri, const std::string &result ) { return true; };

    virtual bool ddcpDoByCallId( 
        const pjsip_uri *target,
        const pjsip_from_hdr *from,
        const pjsip_to_hdr *to,
        const pjsip_cid_hdr *call_id,       
        int cseq,
        const std::string &result ) = 0;

    //gb28181��Ŀ����
    virtual bool ddcpDoByCallId( 
        const pjsip_uri *target,
        const pjsip_from_hdr *from,
        const pjsip_to_hdr *to,
        const pjsip_cid_hdr *call_id,  
		const pjsip_contact_hdr *contact,
        int cseq,
        const std::string &xmlType,
        const std::string &result ) = 0;

	virtual bool setSipUrl( const std::string catalogSipUrl ) = 0;
    virtual ~IDdcpDoSession() {};
};

interface ICtxHandler
{
    virtual bool commitInvite( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result ) = 0;
    virtual bool commitCancel( const std::string &sid ) = 0;
    virtual bool commitAck( const std::string &sid ) = 0;
    virtual bool commitBye( const std::string &sid ) = 0;

    virtual bool commitDdcpDo( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result ) = 0;

    //for gb28181
    virtual bool commitDdcpDo( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::vector<std::string> &resultVec ){ return true; };

    virtual bool commitRegist( const std::string &sid ) = 0;
    virtual bool commitRegist( const std::string &sid,const std::string &oid,int expries ) = 0;

    virtual bool commitSubscribe( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result ) = 0;
    virtual bool commitNotify( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result ) = 0;
         
    virtual bool commitAnswer( const std::string &sid, const char *xml, size_t len, bool status, int type ) = 0;

    virtual bool setCseqValues( const std::string& sid ,int cseq ) = 0;
    virtual bool getCsqlValues( const std::string& sid, int& cseq ) = 0;

    virtual ~ICtxHandler() {};
};

bool registPjThread();

}

/* #define TRY_REGISTER_THIS_THREAD( RET ) \
 pj_thread_desc desc = {0}; \
 if( PJ_TRUE != pj_thread_is_registered() ) \
 { \
     pj_thread_t *this_thread = 0; \
     if( PJ_SUCCESS != pj_thread_register( 0, desc, &this_thread ) ) \
     return RET; \
 } */

#endif // SIPSTACKITF_HPP_

