#include <iostream>
#include <pjsip_simple.h>

#include "NotifySession.hpp"
#include "SipPoolGuard.hpp"
namespace usg {

    CNotifySession::CNotifySession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from )
            : m_pool( pool ), m_endPoint( endPoint ), m_handler( 0 ), m_from( from ),m_xmlType( "DDCP" ),m_iExpries( 60 )
    {

    }

    CNotifySession::CNotifySession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from,const std::string& xmlType )
            : m_pool( pool ), m_endPoint( endPoint ), m_handler( 0 ), m_from( from ),m_xmlType( xmlType ),m_iExpries( 60 )
    {

    }
    CNotifySession::~CNotifySession()
    {
    }

    CNotifySession *CNotifySession::setHandler( ICtxHandler *handler )
    {
        m_handler = handler;
        return this;
    }

    bool CNotifySession::subscribe( std::string &sid, const std::string &sipUri, const std::string &result )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;
        //CSipPoolGuard poolGuard;
        //pj_pool_t * pool = poolGuard.getPool();
        //if ( NULL == pool )
        //{
        //    return false;
        //}
        if ( sipUri.empty() || m_from.empty() )
        {
            return false;
        }
        pj_str_t uri = { (char *)sipUri.c_str(), (pj_ssize_t)sipUri.length() };
        pj_str_t from = { (char *)m_from.c_str(), (pj_ssize_t)m_from.length() };
        pjsip_tx_data *tdata = 0;

        if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, pjsip_get_subscribe_method(), &uri, &from, &uri, &from, 0, -1, 0, &tdata ) )
            return false;

        pj_str_t type = { (char*)"Application", 11 };
        std::string xmlType = m_xmlType;
        if ( m_xmlType == "MANSCDP" )
            xmlType += "+xml";
        //pj_str_t subtype = { "DDCP", 4 };
        pj_str_t subtype = { (char *)m_xmlType.c_str(), (pj_ssize_t)m_xmlType.length() };
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
        //tdata->msg->body = pjsip_msg_body_create( pool, &type, &subtype, &text );
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, &type, &subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;

        //添加Expires头字段
        pjsip_expires_hdr* expriesHdr = pjsip_expires_hdr_create( tdata->pool, m_iExpries );
        if ( expriesHdr == NULL )
            return false;
        pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)expriesHdr );

        //添加Event头字段
        pj_str_t eventheader = pj_str((char*)"Event");
        pj_str_t eventValue = pj_str((char*)"presence");
        pjsip_generic_string_hdr *eventHdr = pjsip_generic_string_hdr_create( tdata->pool, &eventheader, &eventValue);
        pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)eventHdr );

        /*
         * 插入dialog记录.
         */
        pjsip_cseq_hdr *cseq = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CSEQ, 0 );
        pjsip_cid_hdr  *cid  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CALL_ID, 0 );

        sid = createSid( cid, cseq );

        return PJ_SUCCESS == pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 );
    }

    bool CNotifySession::notify( std::string &sid, const std::string &sipUri, const std::string &result )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;
        //CSipPoolGuard poolGuard;
        //pj_pool_t * pool = poolGuard.getPool();
        //if ( NULL == pool )
        //{
        //    return false;
        //}

        pjsip_tx_data *tdata = 0;

        if ( sipUri.empty() || m_from.empty() )
        {
            return false;
        }
        pj_str_t uri = { (char *)sipUri.c_str(), (pj_ssize_t)sipUri.length() };
        pj_str_t from = { (char *)m_from.c_str(), (pj_ssize_t)m_from.length() };

        if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, pjsip_get_notify_method(), &uri, &from, &uri, &from, 0, -1, 0, &tdata ) )
            return false;

        pj_str_t type = { (char*)"Application", 11 };
        //pj_str_t subtype = { "DDCP", 4 };
        std::string xmlType = m_xmlType;
        if ( m_xmlType == "MANSCDP" )
            xmlType += "+xml";

        pj_str_t subtype = { (char *)xmlType.c_str(), (pj_ssize_t)xmlType.length() };
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
        //tdata->msg->body = pjsip_msg_body_create( pool, &type, &subtype, &text );
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, &type, &subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;

        /*
         * 插入dialog记录.
         */
        pjsip_cseq_hdr *cseq = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CSEQ, 0 );
        pjsip_cid_hdr  *cid  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CALL_ID, 0 );

        sid = createSid( cid, cseq );

        return PJ_SUCCESS == pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 );
    }

    bool CNotifySession::notify( const std::string &sid, const std::string &sipUri, const std::vector<std::string> &result )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        pjsip_tx_data *tdata = 0;
        std::vector<std::string> result2 = result;
        if( result[0] == "CatalogQueryCmad" )
        {
            result2.erase( result2.begin() );
            std::string resultone = result2[0];
            result2.erase( result2.begin() );

            if ( sipUri.empty() || m_from.empty() )
            {
                return false;
            }

            pj_str_t uri = { (char *)sipUri.c_str(), (pj_ssize_t)sipUri.length() };
            pj_str_t from = { (char *)m_from.c_str(), (pj_ssize_t)m_from.length() };

            if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, pjsip_get_notify_method(), &uri, &from, &uri, &from, 0, -1, 0, &tdata ) )
                return false;

            //添加Subscription-State头字段
            pj_str_t subscriptionheader = pj_str((char*)"Subscription-State");
            char subscription[64] = {0};
            sprintf(subscription,"active;expires=%d;retry-after=%d\n",m_iExpries,0);
            pj_str_t subscriptionValue = pj_str(subscription);
            pjsip_generic_string_hdr *subscriptionHdr = pjsip_generic_string_hdr_create( tdata->pool, &subscriptionheader, &subscriptionValue);
            pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)subscriptionHdr );

            //添加Event头字段
            pj_str_t eventheader = pj_str((char*)"Event");
            pj_str_t eventValue = pj_str((char*)"presence");
            pjsip_generic_string_hdr *eventHdr = pjsip_generic_string_hdr_create( tdata->pool, &eventheader, &eventValue);
            pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)eventHdr );

            pj_str_t type = { (char*)"Application", 11 };
            std::string xmlType = m_xmlType;
            if ( m_xmlType == "MANSCDP" )
                xmlType += "+xml";

            pj_str_t subtype = { (char *)xmlType.c_str(), (pj_ssize_t)xmlType.length() };
            pj_str_t text = { (char *)resultone.c_str(), (pj_ssize_t)resultone.length() };
            tdata->msg->body = pjsip_msg_body_create( tdata->pool, &type, &subtype, &text );
            if ( tdata->msg->body == NULL )
                return false;
            /*
             * 插入dialog记录.
             */
            //pjsip_cseq_hdr *cseq = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CSEQ, 0 );
            pjsip_cid_hdr  *cid  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CALL_ID, 0 );

//		sid = createSid( cid, cseq );


            std::string callid( cid->id.ptr, cid->id.slen );
            m_queryCatalog[callid] = result2;
        }

        return PJ_SUCCESS == pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 );
    }

    bool CNotifySession::notify( std::string &sid, const std::string &sipUri, const std::string &result, const std::string &callid )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        pjsip_tx_data *tdata = 0;

        if ( sipUri.empty() || m_from.empty() )
        {
            return false;
        }
        pj_str_t uri = { (char *)sipUri.c_str(), (pj_ssize_t)sipUri.length() };
        pj_str_t from = { (char *)m_from.c_str(), (pj_ssize_t)m_from.length() };
        pj_str_t pjcallid = { (char *)callid.c_str(), (pj_ssize_t)callid.length() };

        if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, pjsip_get_notify_method(), &uri, &from, &uri, &from, &pjcallid, -1, 0, &tdata ) )
            return false;

        //添加Subscription-State头字段
        pj_str_t subscriptionheader = pj_str((char*)"Subscription-State");
        char subscription[64] = {0};
        sprintf(subscription,"active;expires=%d;retry-after=%d\n",m_iExpries,0);
        pj_str_t subscriptionValue = pj_str(subscription);
        pjsip_generic_string_hdr *subscriptionHdr = pjsip_generic_string_hdr_create( tdata->pool, &subscriptionheader, &subscriptionValue);
        pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)subscriptionHdr );

        //添加Event头字段
        pj_str_t eventheader = pj_str((char*)"Event");
        pj_str_t eventValue = pj_str((char*)"presence");
        pjsip_generic_string_hdr *eventHdr = pjsip_generic_string_hdr_create( tdata->pool, &eventheader, &eventValue);
        pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)eventHdr );

        pj_str_t type = { (char*)"Application", 11 };
        //pj_str_t subtype = { "DDCP", 4 };
        std::string xmlType = m_xmlType;
        if ( m_xmlType == "MANSCDP" )
            xmlType += "+xml";

        pj_str_t subtype = { (char *)xmlType.c_str(), (pj_ssize_t)xmlType.length() };
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
        //tdata->msg->body = pjsip_msg_body_create( pool, &type, &subtype, &text );
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, &type, &subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;

        /*
         * 插入dialog记录.
         */
        pjsip_cseq_hdr *cseq = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CSEQ, 0 );
//	cseq->cseq = cseqid;
        pjsip_cid_hdr  *cid  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CALL_ID, 0 );

        sid = createSid( cid, cseq );

        return PJ_SUCCESS == pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 );
    }

    bool CNotifySession::answer( pjsip_rx_data *rdata, int status, const std::string &result )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;
        //CSipPoolGuard poolGuard;
        //pj_pool_t * pool = poolGuard.getPool();
        //if ( NULL == pool )
        //{
        //    return false;
        //}

        pjsip_tx_data *tdata = 0;
        if ( PJ_SUCCESS != pjsip_endpt_create_response( m_endPoint, rdata, status, 0, &tdata ) )
            return false;

        pj_str_t *type = &rdata->msg_info.ctype->media.type;
        pj_str_t *subtype = &rdata->msg_info.ctype->media.subtype;
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
        //tdata->msg->body = pjsip_msg_body_create( pool, type, subtype, &text );
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, type, subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;

        return PJ_SUCCESS == pjsip_endpt_send_response2( m_endPoint, rdata, tdata, 0, 0 );
    }

    bool CNotifySession::answer( pjsip_rx_data *rdata, int status, pj_int32_t expires,const std::string &result )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        pjsip_tx_data *tdata = 0;
        if ( PJ_SUCCESS != pjsip_endpt_create_response( m_endPoint, rdata, status, 0, &tdata ) )
            return false;

        //添加Expires头字段
        pjsip_expires_hdr* expriesHdr = pjsip_expires_hdr_create( tdata->pool, expires );
        if ( expriesHdr == NULL )
            return false;
        pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)expriesHdr );

        //添加Event头字段
        pj_str_t eventheader = pj_str((char*)"Event");
        pj_str_t eventValue = pj_str((char*)"presence");
        pjsip_generic_string_hdr *eventHdr = pjsip_generic_string_hdr_create( tdata->pool, &eventheader, &eventValue);
        pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)eventHdr );

        pj_str_t *type = &rdata->msg_info.ctype->media.type;
        pj_str_t *subtype = &rdata->msg_info.ctype->media.subtype;
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };

        tdata->msg->body = pjsip_msg_body_create( tdata->pool, type, subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;

        return PJ_SUCCESS == pjsip_endpt_send_response2( m_endPoint, rdata, tdata, 0, 0 );
    }

    bool CNotifySession::onSubscribe( pjsip_rx_data *rdata )
    {
        if ( !rdata->msg_info.msg || !rdata->msg_info.msg->body || !rdata->msg_info.cseq )
            return false;
        /*
         * 获取uri.
         */
        if ( !PJSIP_URI_SCHEME_IS_SIP( rdata->msg_info.msg->line.req.uri ) ) return false;

        pjsip_sip_uri *uri = (pjsip_sip_uri *)rdata->msg_info.msg->line.req.uri;
        std::string oid( uri->user.ptr, uri->user.slen );
        if ( oid.empty() ) return false;
        /*
         * 获取其他参数.
         */
        const char *sub = rdata->msg_info.msg->body->content_type.subtype.ptr;
        const char *ptr = (const char *)rdata->msg_info.msg->body->data;
        size_t      len = rdata->msg_info.msg->body->len;
        std::string sid = createSid( rdata->msg_info.cid, rdata->msg_info.cseq );

        pjsip_expires_hdr *expireshdr = (pjsip_expires_hdr *)pjsip_msg_find_hdr( rdata->msg_info.msg, PJSIP_H_EXPIRES, 0 );
        m_iExpries = 90;
        if (expireshdr != 0)
            m_iExpries = expireshdr->ivalue;

        /*
         * 判断正文是否是所需要的格式.
         */
        if ( 0 == ::strnicmp( sub, "DDCP", 4 ) ||
             0 == ::strnicmp( sub, "XML", 3 ) ||
             0 == ::strnicmp( sub, "IVS_XML", 7 ) ||
             0 == ::strnicmp( sub, "MANSCDP", 7 ))
        {
            if ( m_handler )
            {
                std::string result;
                if ( m_handler->commitSubscribe( sid, oid, ptr, len, result ) )
                {
                    return answer( rdata, 200, m_iExpries, result );
                }
                else
                {
                    return answer( rdata, 400, result );
                }
            }
            return false;
        }

        return true;
    }

    bool CNotifySession::onNotify( pjsip_rx_data *rdata )
    {
        if ( !rdata->msg_info.msg || !rdata->msg_info.msg->body || !rdata->msg_info.cseq )
            return false;
        /*
         * 获取uri.
         */
        if ( !PJSIP_URI_SCHEME_IS_SIP( rdata->msg_info.msg->line.req.uri ) ) return false;

        pjsip_sip_uri *uri = (pjsip_sip_uri *)rdata->msg_info.msg->line.req.uri;
        std::string oid( uri->user.ptr, uri->user.slen );
        if ( oid.empty() ) return false;
        /*
         * 获取其他参数.
         */
        //const char *sub = rdata->msg_info.msg->body->content_type.subtype.ptr;
        const char *ptr = (const char *)rdata->msg_info.msg->body->data;
        size_t      len = rdata->msg_info.msg->body->len;
        std::string sid = createSid( rdata->msg_info.cid, rdata->msg_info.cseq );
        /*
         * 判断正文是否是所需要的格式.
         */

        {
            if ( m_handler )
            {
                std::string result;
                if ( m_handler->commitNotify( sid, oid, ptr, len, result ) )
                {
                    return answer( rdata, 200, result );
                }
                else
                {
                    return answer( rdata, 400, result );
                }
            }
            return false;
        }

        return true;
    }

    bool CNotifySession::onAnswer( pjsip_rx_data *rdata )
    {
        if ( !m_handler || !rdata->msg_info.msg || !rdata->msg_info.cseq )
            return false;
        /*
         * 获取其他参数.
         */
        std::string sid = createSid( rdata->msg_info.cid, rdata->msg_info.cseq );

        int status = rdata->msg_info.msg->line.status.code / 100;
        switch ( status )
        {
            case 2:
            {
                char *ptr = 0;
                size_t len = 0;
                if ( rdata->msg_info.msg->body )
                {
                    ptr = (char *)rdata->msg_info.msg->body->data;
                    len = rdata->msg_info.msg->body->len;
                }

                //处理目录订阅发送
                std::string callid( rdata->msg_info.cid->id.ptr, rdata->msg_info.cid->id.slen );
                if( m_queryCatalog.find( callid ) != m_queryCatalog.end() )
                {
                    if( m_queryCatalog[callid].empty() )
                    {
                        m_queryCatalog.erase( callid );
                        return true;
                    }

                    std::string calalogstr = m_queryCatalog[callid][0];
                    m_queryCatalog[callid].erase( m_queryCatalog[callid].begin() );

                    return notify( sid, m_catalogSipUrl, calalogstr, callid );
                }

                return m_handler->commitAnswer( sid, ptr, len, true, INotifySession::SESSION_TYPE );
            }
            case 4:
            {
                //处理目录订阅发送
                std::string callid( rdata->msg_info.cid->id.ptr, rdata->msg_info.cid->id.slen );
                if( m_queryCatalog.find( callid ) != m_queryCatalog.end() )
                {
                    std::cerr << "ack 400 m_queryCatalog[" << callid << "] erase" << std::endl;

                    m_queryCatalog.erase( callid );
                }//end 目录订阅发送
            }
                return m_handler->commitAnswer( sid, 0, 0, false, INotifySession::SESSION_TYPE );

            default:
                break;
        }

        return true;
    }

    std::string CNotifySession::createSid( pjsip_cid_hdr *cid, pjsip_cseq_hdr *cseq )
    {
        if ( !cid || !cseq ) return std::string();

        std::string sid( cid->id.ptr, cid->id.slen );

        char buf[16] = { 0 };
        sprintf( buf, "%d", cseq->cseq );
        return sid += buf;
    }

    bool CNotifySession::setSipUrl( const std::string catalogSipUrl )
    {
        m_catalogSipUrl = catalogSipUrl;
        return true;
    }

    void CNotifySession::setExpries( uint32_t iExpries )
    {
        m_iExpries = iExpries;
    }

}
