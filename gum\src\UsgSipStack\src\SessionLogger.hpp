#ifndef SESSIONLOGGER_HPP_
#define SESSIONLOGGER_HPP_

#include <map>
#include <string>

#include <pjlib.h>
#include <pjsip.h>

namespace usg {

class CSessionLogger
{
public:
    CSessionLogger( pj_pool_t *pool );
    ~CSessionLogger();

public:
    void insertRx( const std::string &sid, pjsip_rx_data *data );
    void insertTx( const std::string &sid, pjsip_tx_data *data );

    bool removeRx( const std::string &sid, pjsip_rx_data **data = 0 );
    bool removeTx( const std::string &sid, pjsip_tx_data **data = 0 );

    bool pickupRx( const std::string &sid, pjsip_rx_data *&data );
    bool pickupTx( const std::string &sid, pjsip_tx_data *&data );

private:
    pj_pool_t *m_pool;

private:
    pj_mutex_t *m_rm, *m_tm;
    std::map<std::string, pjsip_rx_data *> m_rx;
    std::map<std::string, pjsip_tx_data *> m_tx;
};

}

#endif // SESSIONLOGGER_HPP_
