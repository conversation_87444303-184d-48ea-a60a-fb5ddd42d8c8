#pragma once

#include "ailog.h"
#include "md5.hpp"

namespace fvm{

    #define LICENSE_FILE  "/etc/fvm_license"

    int getdiskid_old(char *id, size_t max)
    {
        int fd;
        struct hd_driveid hid;
        FILE *fp;
        char line[0x100], *disk, *root, *p;
        fp = fopen("/etc/mtab", "r");
        if (fp == NULL)
        {
            fprintf(stderr, "No /etc/mtab file\n");
            return -1;
        }
        fd = -1;
        while (fgets(line, sizeof line, fp) != NULL)
        {
            disk = strtok(line, " ");
            if (disk == NULL)
            {
                continue;
            }
            root = strtok(NULL, " ");
            if (root == NULL)
            {
                continue;
            }

            if (strcmp(root, "/") == 0)
            {
                for (p = disk + strlen(disk) - 1; isdigit(*p); p--)
                {
                    *p = '\0';
                }
                fd = open(disk, O_RDONLY);
                break;
            }
        }
        fclose(fp);
        if (fd < 0)
        {
            fprintf(stderr, "open hard disk device failed\n");
            return -1;
        }

        if (ioctl(fd, HDIO_GET_IDENTITY, &hid) < 0)
        {
            fprintf(stderr, "ioctl error\n");

            return -1;

        }
        close(fd);
        snprintf(id, max, "%.20s", hid.serial_no);
        //fprintf(stdout, "get hard disk serial number: %s\n", id);
        return 0;
    }
    int getcpuid_old(char *id, size_t max)
    {
        FILE *fp;
        char line[0x100], *key, *value, *p;

        char mode[256] = {0};
        char num[10] = { 0 };

        fp = fopen("/proc/cpuinfo", "r");
        if (fp == NULL)
        {
            fprintf(stderr, "No /proc/cpuinfo\n");
            return -1;
        }
        while (fgets(line, sizeof line, fp) != NULL)
        {
            key = strtok(line, ":");
            if (key == NULL)
            {
                continue;
            }

            p = key + strlen(key) - 1;
            *p = '\0';

            value = strtok(NULL, ":");
            if (value == NULL)
            {
                continue;
            }

            if (strcmp(key, "model name") == 0)
            {
                strcpy(mode, value);
            }
            if (strcmp(key, "processor") == 0)
            {
                strcpy(num, value);
            }
        }
        fclose(fp);

        p = num + strlen(num) - 1;
        *p = '\0';

        p = mode + strlen(mode) - 1;
        *p = '\0';
        //fprintf(stdout, "get cpu info : %s:%s\n", num, mode);
        snprintf(id, max, "%s:%s", num, mode);

        return 0;
    }
    int getlocalmac_old(char *id, size_t max)
    {
        int sock_mac;

        struct ifreq ifr_mac;
        char mac_addr[30];

        sock_mac = socket(AF_INET, SOCK_STREAM, 0);
        if (sock_mac == -1)
        {
            perror("[getlocalmac] create socket failed/n");
            return -1;
        }

        memset(&ifr_mac, 0, sizeof(ifr_mac));
        strncpy(ifr_mac.ifr_name, "eth0", sizeof(ifr_mac.ifr_name) - 1);

        if ((ioctl(sock_mac, SIOCGIFHWADDR, &ifr_mac)) < 0)
        {
            //printf("mac ioctl error/n");
            return -1;
        }

        sprintf(mac_addr, "%02x:%02x:%02x:%02x:%02x:%02x",
                (unsigned char)ifr_mac.ifr_hwaddr.sa_data[0],
                (unsigned char)ifr_mac.ifr_hwaddr.sa_data[1],
                (unsigned char)ifr_mac.ifr_hwaddr.sa_data[2],
                (unsigned char)ifr_mac.ifr_hwaddr.sa_data[3],
                (unsigned char)ifr_mac.ifr_hwaddr.sa_data[4],
                (unsigned char)ifr_mac.ifr_hwaddr.sa_data[5]);
        //printf("local mac:%s\n", mac_addr);
        close(sock_mac);
        snprintf(id, max, "%s", mac_addr);
        return 0;
    }
    std::string getOldDevice()
    {
        std::string str;
        char id[1024] = { 0 };
        size_t max = 100;
        memset(id, 0x00, sizeof(id));
        getdiskid_old(id, max);

        str += id;

        memset(id, 0x00, sizeof(id));
        getcpuid_old(id, max);
        str += id;

        memset(id, 0x00, sizeof(id));
        getlocalmac_old(id, max);
        str += id;
        return str;
    }

    int checkLicense(const std::string& szProduct, time_t& tmBegin, time_t& tmEnd )
    {
        char buf[256] = { 0 };
        std::string str = szProduct;
        if (szProduct == "")
            str = "AI3.0";
        std::string szDevice = getdeviceinfo();
        unsigned char digest[16] = { 0 };
        std::string strMd5, md5File;
        FILE* pFile = NULL;
        time_t tm;
        std::string szInfo = "\nProduct: " + str + "\nDeviceInfo: " + szDevice;
        pFile = fopen(LICENSE_FILE, "rb");
        if (!pFile)
        {
            ai::LogError << "No License File" << szInfo;
            return 1;
        }
        fread(buf, 1, sizeof(buf), pFile);

        fclose(pFile);
        pFile = NULL;

        if (-1 == parse(buf, md5File, tmBegin, tmEnd))
        {
            ai::LogError << "License Parse Error" << szInfo;
            return 1;
        }
        std::string sOldStr = str;
        str += szDevice;
        genmd5(str, digest);
        strMd5 = hextostring(digest, sizeof(digest));

        if (strMd5 != md5File)
        {
            //存在license文件，但不匹配时，可能是以前的注册信息，如果能判断通过，也认为已授权
            sOldStr += getOldDevice();
            std::string strOldMd5;
            unsigned char oldDigest[16] = { 0 };
            genmd5( sOldStr, oldDigest );
            std::string oldstrMd5 = hextostring( oldDigest, sizeof( oldDigest ) );
            if ( oldstrMd5 != md5File )
            {
                bool bOk = false;
                //可能是cpu id不对，替换这个
                //std::cout << "old str: [" << str.c_str() << "]\n";
                size_t pos = str.find(':');
                if (pos != std::string::npos)
                {
                    int pos2 = str.find_last_of(' ', pos);
                    std::cout << "pos: " << pos << "  pos2: " << pos2 << std::endl;
                    std::string szLeft = str.substr(0, pos2 + 1);
                    std::string szRight = str.substr(pos, str.length());
                    std::cout << "left: [" << szLeft.c_str() << "] right: [" << szRight.c_str() << "]\n";
                    for (int i = 0; i < 16; i++)
                    {
                        std::string s1 = szLeft + std::to_string(i + 1) + szRight;
                        std::cout << "new str: [" << s1.c_str() << "]\n";
                        genmd5(s1, digest);
                        strMd5 = hextostring(digest, sizeof(digest));
                        if (strMd5 == md5File)
                        {
                            bOk = true;
                            break;
                        }
                    }
                }
                if (!bOk)
                {
                    ai::LogError <<"License is Invalid" << szInfo;
                    return 1;
                }
            }
        }

        tm = time(NULL);
        if (tm >= tmBegin && tm <= tmEnd)
        {
            return 0;
        }
        else
        {
            ai::LogError <<"License is Expired" << szInfo;
            return 1;
        }
    }

}