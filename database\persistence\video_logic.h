/**
 * @addtogroup odbDatabaseGroup
 * @brief 车道方向信息
 * @{
 */
#ifndef _VIDEOLOGIC_H
#define _VIDEOLOGIC_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
/**
 * @brief  车道方向信息（eg:十堰方向） 对应数据库aimonitorV3的表wn_video_logic
 */
#pragma db object table("wn_video_logic")
class VideoLogic {
public:

    VideoLogic(const std::string& name,
        unsigned long videoSrcId,
        int side
    )
        : name(name), videoSourceId(videoSrcId), side(side)
    {
    }

    unsigned long getId() const {
        return id;
    }

    const std::string& getName() const {
        return name;
    }

    void setName(const std::string& name) {
        this->name = name;
    }

    unsigned long getVideoSourceId()const {
        return videoSourceId;
    }

    void setVideoSourceId(unsigned long videoSrcId) {
        this->videoSourceId = videoSrcId;
    }

    int getSide()const {
        return side;
    }

    void setSide(int side) {
        this->side = side;
    }

private:

    friend class odb::access;
    VideoLogic() {}


private:

#pragma db id auto
    unsigned long id;                        //!< 表ID

#pragma db column("name") type("VARCHAR(255)")
    std::string name;                   //!< TODO:

#pragma db column("video_id")
    unsigned long videoSourceId;             //!< 视频资源id 对应表wn_video_source(VideoSource)的id

#pragma db column("side")
    int side;                      //!< TODO:
};
}
#endif //_VIDEOLOGIC_H
/**
 * @}
 */