/**
 * Project FVM
 */
#pragma once

#include <memory>
#include "data/video_source_info.h"
#include "aionvif.h"
#include "protocol/all.h"

/**
 * @brief onvif处理器，用于云台控制，解析授权信息，获取onvif位置等
 */
namespace fvm {
    class OnvifPtz {
    public:
        OnvifPtz( const VideoSourceInfoPtr source );

        //是否可控云台
        bool isPtzCapable();

        // 调用指定的预置位
        bool callPreset( int presetId );

        // 云台控制
        bool ptzControl( network::EPtzCommand command, int param1, int param2 );

        //获取当前位置
        bool getPosition( double& x, double& y, double& z );

        //获取指定预置位的默认位置
        bool getPresetPosition(int presetId, double& x, double& y, double& z);

    private:
        // 根据给定的字符串分析用户授权信息
        bool analysis( const std::string& sInfo );
        //获取访问信息
        bool getOnvifInfo( bool hasPtz );

    private:
        bool canOnvif = false;          //是否支持用onvif对云台进行操作
        bool canPtz = false;            //是否支持转动云台和调用预置位
        bool canGetPosition = false;    //是否支持获取摄像机的坐标

        std::string onvifIp = "";      //onvif 地址
        std::string onvifUser = "";    //用户名
        std::string onvifPass = "";    //密码

        std::string mediaUrl = "";      //当前设备的流地址
        std::string ptzUrl = "";        //控云台的地址
        std::string ptzProfile = "";    //控云台的配置文件

        int lastError = 0;              //上次读位置的错误信息

        ai::PtzRange ptzRange;              //x,y,z可取值的范围，在canPtz为true的情况下有效
    };
    typedef std::shared_ptr<OnvifPtz> OnvifPtzPtr;
}

