#ifndef _ROLLINFO_H
#define _ROLLINFO_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
/**
 * @brief  轮切方案详细信息: 对应数据库aimonitorV3的表wn_roll_info
 */
#pragma db object table("wn_roll_info")
class RollInfo {
public:

    RollInfo(unsigned long subRollId,
        unsigned long videoSrcId,
        unsigned long presetId,
        unsigned long rollTime,
        bool isDel,
        bool isEnable
    )
        : subRollProjId(subRollId), videoSourceId(videoSrcId), presetId(presetId),
        rollTime(rollTime), isDel(isDel), isEnable(isEnable)
    {
    }

    unsigned long getId() const {
        return id;
    }

    unsigned long getSubRollPorjId()const {
        return subRollProjId;
    }

    void setSubRollProjId(unsigned long id) {
        this->subRollProjId = id;
    }

    unsigned long getVideoSourceId()const {
        return videoSourceId;
    }

    void setVideoSourceId(unsigned long id) {
        this->videoSourceId = id;
    }

    unsigned long getPresetId()const {
        return presetId;
    }

    void setPresetId(unsigned long id) {
        this->presetId = id;
    }


    const unsigned long getRollTime() const {
        return rollTime;
    }

    void setRollTime(unsigned long time) {
        this->rollTime = time;
    }

    bool getIsDel() const {
        return isDel;
    }

    void setIsDel(bool del) {
        this->isDel = del;
    }

    bool getIsEnable() const {
        return isEnable;
    }

    void setIsEnable(bool enable) {
        this->isEnable = enable;
    }
private:

    friend class odb::access;
    RollInfo() {}


private:

#pragma db id auto
    unsigned long id;                        //!< 表ID

#pragma db column("sub_project_id")
    unsigned long subRollProjId;             //!< 子轮切方案id 对应表wn_roll_project(RollProject)的id

#pragma db column("video_id")
    unsigned long videoSourceId;             //!< 视频资源id 对应表wn_video_source(VideoSource)的id

#pragma db column("preset_id")
    unsigned long presetId;                  //!< 预置位id 对应表wn_preset(preset) id TODO:

#pragma db column("roll_time")
    unsigned long rollTime;                  //!< 轮切时间   TODO:分

#pragma db column("is_del") type("INT")
    bool isDel;                              //!< 是否删除

#pragma db column("is_enable") type("INT")
    bool isEnable;                           //!< 是否使能


};
}
#endif //_ROLLINFO_H
/**
 * @}
 */