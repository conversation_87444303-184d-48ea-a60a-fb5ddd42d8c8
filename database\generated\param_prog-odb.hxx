// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#ifndef PARAM_PROG_ODB_HXX
#define PARAM_PROG_ODB_HXX

#include <odb/version.hxx>


#include <odb/pre.hxx>

#include "param_prog.h"

#include <memory>
#include <cstddef>
#include <utility>

#include <odb/core.hxx>
#include <odb/traits.hxx>
#include <odb/callback.hxx>
#include <odb/wrapper-traits.hxx>
#include <odb/pointer-traits.hxx>
#include <odb/container-traits.hxx>
#include <odb/no-op-cache-traits.hxx>
#include <odb/result.hxx>
#include <odb/simple-object-result.hxx>
#include <odb/view-image.hxx>
#include <odb/view-result.hxx>

#include <odb/details/unused.hxx>
#include <odb/details/shared-ptr.hxx>

namespace odb
{
  // ParamProg
  //
  template <>
  struct class_traits< ::db::ParamProg >
  {
    static const class_kind kind = class_object;
  };

  template <>
  class access::object_traits< ::db::ParamProg >
  {
    public:
    typedef ::db::ParamProg object_type;
    typedef ::db::ParamProg* pointer_type;
    typedef odb::pointer_traits<pointer_type> pointer_traits;

    static const bool polymorphic = false;

    typedef long unsigned int id_type;

    static const bool auto_id = true;

    static const bool abstract = false;

    static id_type
    id (const object_type&);

    typedef
    no_op_pointer_cache_traits<pointer_type>
    pointer_cache_traits;

    typedef
    no_op_reference_cache_traits<object_type>
    reference_cache_traits;

    static void
    callback (database&, object_type&, callback_event);

    static void
    callback (database&, const object_type&, callback_event);
  };

  // ParamProgData
  //
  template <>
  struct class_traits< ::db::ParamProgData >
  {
    static const class_kind kind = class_view;
  };

  template <>
  class access::view_traits< ::db::ParamProgData >
  {
    public:
    typedef ::db::ParamProgData view_type;
    typedef ::db::ParamProgData* pointer_type;

    static void
    callback (database&, view_type&, callback_event);
  };
}

#include <odb/details/buffer.hxx>

#include <odb/mysql/version.hxx>
#include <odb/mysql/forward.hxx>
#include <odb/mysql/binding.hxx>
#include <odb/mysql/mysql-types.hxx>
#include <odb/mysql/query.hxx>

namespace odb
{
  // ParamProg
  //
  template <typename A>
  struct query_columns< ::db::ParamProg, id_mysql, A >
  {
    // id
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    id_type_;

    static const id_type_ id;

    // paramName
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    paramName_type_;

    static const paramName_type_ paramName;

    // paramKey
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    paramKey_type_;

    static const paramKey_type_ paramKey;

    // paramValue
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    paramValue_type_;

    static const paramValue_type_ paramValue;

    // program
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    program_type_;

    static const program_type_ program;

    // description
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    description_type_;

    static const description_type_ description;

    // isShow
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        bool,
        mysql::id_long >::query_type,
      mysql::id_long >
    isShow_type_;

    static const isShow_type_ isShow;

    // isDel
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        bool,
        mysql::id_long >::query_type,
      mysql::id_long >
    isDel_type_;

    static const isDel_type_ isDel;
  };

  template <typename A>
  const typename query_columns< ::db::ParamProg, id_mysql, A >::id_type_
  query_columns< ::db::ParamProg, id_mysql, A >::
  id (A::table_name, "`id`", 0);

  template <typename A>
  const typename query_columns< ::db::ParamProg, id_mysql, A >::paramName_type_
  query_columns< ::db::ParamProg, id_mysql, A >::
  paramName (A::table_name, "`alias`", 0);

  template <typename A>
  const typename query_columns< ::db::ParamProg, id_mysql, A >::paramKey_type_
  query_columns< ::db::ParamProg, id_mysql, A >::
  paramKey (A::table_name, "`param_key`", 0);

  template <typename A>
  const typename query_columns< ::db::ParamProg, id_mysql, A >::paramValue_type_
  query_columns< ::db::ParamProg, id_mysql, A >::
  paramValue (A::table_name, "`param_value`", 0);

  template <typename A>
  const typename query_columns< ::db::ParamProg, id_mysql, A >::program_type_
  query_columns< ::db::ParamProg, id_mysql, A >::
  program (A::table_name, "`program`", 0);

  template <typename A>
  const typename query_columns< ::db::ParamProg, id_mysql, A >::description_type_
  query_columns< ::db::ParamProg, id_mysql, A >::
  description (A::table_name, "`description`", 0);

  template <typename A>
  const typename query_columns< ::db::ParamProg, id_mysql, A >::isShow_type_
  query_columns< ::db::ParamProg, id_mysql, A >::
  isShow (A::table_name, "`is_show`", 0);

  template <typename A>
  const typename query_columns< ::db::ParamProg, id_mysql, A >::isDel_type_
  query_columns< ::db::ParamProg, id_mysql, A >::
  isDel (A::table_name, "`is_del`", 0);

  template <typename A>
  struct pointer_query_columns< ::db::ParamProg, id_mysql, A >:
    query_columns< ::db::ParamProg, id_mysql, A >
  {
  };

  template <>
  class access::object_traits_impl< ::db::ParamProg, id_mysql >:
    public access::object_traits< ::db::ParamProg >
  {
    public:
    struct id_image_type
    {
      unsigned long long id_value;
      my_bool id_null;

      std::size_t version;
    };

    struct image_type
    {
      // id
      //
      unsigned long long id_value;
      my_bool id_null;

      // paramName
      //
      details::buffer paramName_value;
      unsigned long paramName_size;
      my_bool paramName_null;

      // paramKey
      //
      details::buffer paramKey_value;
      unsigned long paramKey_size;
      my_bool paramKey_null;

      // paramValue
      //
      details::buffer paramValue_value;
      unsigned long paramValue_size;
      my_bool paramValue_null;

      // program
      //
      details::buffer program_value;
      unsigned long program_size;
      my_bool program_null;

      // description
      //
      details::buffer description_value;
      unsigned long description_size;
      my_bool description_null;

      // isShow
      //
      int isShow_value;
      my_bool isShow_null;

      // isDel
      //
      int isDel_value;
      my_bool isDel_null;

      std::size_t version;
    };

    struct extra_statement_cache_type;

    using object_traits<object_type>::id;

    static id_type
    id (const id_image_type&);

    static id_type
    id (const image_type&);

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&,
          mysql::statement_kind);

    static void
    bind (MYSQL_BIND*, id_image_type&);

    static bool
    init (image_type&,
          const object_type&,
          mysql::statement_kind);

    static void
    init (object_type&,
          const image_type&,
          database*);

    static void
    init (id_image_type&, const id_type&);

    typedef mysql::object_statements<object_type> statements_type;

    typedef mysql::query_base query_base_type;

    static const std::size_t column_count = 8UL;
    static const std::size_t id_column_count = 1UL;
    static const std::size_t inverse_column_count = 0UL;
    static const std::size_t readonly_column_count = 0UL;
    static const std::size_t managed_optimistic_column_count = 0UL;

    static const std::size_t separate_load_column_count = 0UL;
    static const std::size_t separate_update_column_count = 0UL;

    static const bool versioned = false;

    static const char persist_statement[];
    static const char find_statement[];
    static const char update_statement[];
    static const char erase_statement[];
    static const char query_statement[];
    static const char erase_query_statement[];

    static const char table_name[];

    static void
    persist (database&, object_type&);

    static pointer_type
    find (database&, const id_type&);

    static bool
    find (database&, const id_type&, object_type&);

    static bool
    reload (database&, object_type&);

    static void
    update (database&, const object_type&);

    static void
    erase (database&, const id_type&);

    static void
    erase (database&, const object_type&);

    static result<object_type>
    query (database&, const query_base_type&);

    static unsigned long long
    erase_query (database&, const query_base_type&);

    public:
    static bool
    find_ (statements_type&,
           const id_type*);

    static void
    load_ (statements_type&,
           object_type&,
           bool reload);
  };

  template <>
  class access::object_traits_impl< ::db::ParamProg, id_common >:
    public access::object_traits_impl< ::db::ParamProg, id_mysql >
  {
  };

  // ParamProgData
  //
  template <>
  class access::view_traits_impl< ::db::ParamProgData, id_mysql >:
    public access::view_traits< ::db::ParamProgData >
  {
    public:
    struct image_type
    {
      // paramKey
      //
      details::buffer paramKey_value;
      unsigned long paramKey_size;
      my_bool paramKey_null;

      // paramValue
      //
      details::buffer paramValue_value;
      unsigned long paramValue_size;
      my_bool paramValue_null;

      std::size_t version;
    };

    typedef mysql::view_statements<view_type> statements_type;

    typedef mysql::query_base query_base_type;
    struct query_columns;

    static const bool versioned = false;

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&);

    static void
    init (view_type&,
          const image_type&,
          database*);

    static const std::size_t column_count = 2UL;

    static query_base_type
    query_statement (const query_base_type&);

    static result<view_type>
    query (database&, const query_base_type&);
  };

  template <>
  class access::view_traits_impl< ::db::ParamProgData, id_common >:
    public access::view_traits_impl< ::db::ParamProgData, id_mysql >
  {
  };

  // ParamProg
  //
  // ParamProgData
  //
  struct access::view_traits_impl< ::db::ParamProgData, id_mysql >::query_columns:
    odb::pointer_query_columns<
      ::db::ParamProg,
      id_mysql,
      odb::access::object_traits_impl< ::db::ParamProg, id_mysql > >
  {
  };
}

#include "param_prog-odb.ixx"

#include <odb/post.hxx>

#endif // PARAM_PROG_ODB_HXX
