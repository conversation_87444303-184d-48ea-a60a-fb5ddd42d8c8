
#ifndef GB28181XMLTAG_HPP_
#define GB28181XMLTAG_HPP_

#include <string>

namespace gb28181
{
    static const std::string TAG_QUERY("Query");
    static const std::string TAG_CONTROL("Control");
    static const std::string TAG_NOTIFY("Notify");
    static const std::string TAG_RESPONSE("Response");
    static const std::string TAG_CONTROL_CMDTYPE("CmdType");
    static const std::string TAG_CONTROL_PTZCMD("PTZCmd");
    static const std::string TAG_CONTROL_TELEBOOT("TeleBoot");
    static const std::string TAG_CONTROL_RECORDCMD("RecordCmd");
    static const std::string TAG_CONTROL_GUARDCMD("GuardCmd");
    static const std::string TAG_CONTROL_ALARMCMD("AlarmCmd");
    static const std::string TAG_CONTROL_ALARM("Alarm");
    static const std::string TAG_CONTROL_RECORD("Record");
    static const std::string TAG_CONTROL_STOPRECORD("StopRecord");
    static const std::string TAG_CONTROL_SETGUARD("SetGuard");
    static const std::string TAG_CONTROL_RESETGUARD("ResetGuard");
    static const std::string TAG_CONTROL_RESETALARM("ResetAlarm");
    static const std::string TAG_CONTROL_MEDIASTAUTS("MediaStatus");
    static const std::string TAG_CONTROL_SN("SN");


    static const std::string FUNC_CONTROL_CMDTYPE_DEVICECONTROL("DeviceControl");
    static const std::string FUNC_CONTROL_CMDTYPE_DEVICEREBOOT("Boot");
    static const std::string FUNC_CONTROL_CMDTYPE_CATALOG("Catalog");
    static const std::string FUNC_CONTROL_CMDTYPE_DEVICEINFO("DeviceInfo");
    static const std::string FUNC_CONTROL_CMDTYPE_DEVICESTATUS("DeviceStatus");
    static const std::string FUNC_CONTROL_CMDTYPE_KEEPALIVE("Keepalive");
    static const std::string FUNC_CONTROL_CMDTYPE_RECORDINFO("RecordInfo");
    static const std::string FUNC_CONTROL_CMDTYPE_ALARMINFO("Info");
    static const std::string FUNC_CONTROL_CMDTYPE_ALARMMETHOD("AlarmMethod");
    static const std::string FUNC_QUERY_CMDTYPE_CONFIGDOWNLOAD("ConfigDownload");
    static const std::string FUNC_QUERY_CMDTYPE_DEVICECONFIG("DeviceConfig");

    static const std::string TAG_RTP_OPT_REALPLAY("Play");
    static const std::string TAG_RTP_OPT_PLAYBACK("Playback");
    static const std::string TAG_RTP_OPT_DOWNLOAD("Download");

    static const std::string TAG_SDP_VIDEOTYPE_H264("H264");
    static const std::string TAG_SDP_VIDEOTYPE_MPEG4("MPEG4");

    static const std::string TAG_FILETYPE("Type");
    static const std::string TAG_FILEPATH("FilePath");
    static const std::string TAG_SECRECY("Secrecy");
    static const std::string TAG_RECORDERID("RecorderID");
    static const std::string TAG_STARTTIME("StartTime");
    static const std::string TAG_ENDTIME("EndTime");
    static const std::string TAG_BROADCAST("Broadcast");

    static const std::string TAG_NOTIFYTYPE("NotifyType");
    static const std::string TAG_SUMNUM("SumNum");
    static const std::string TAG_DEVICEID("DeviceID");
    static const std::string TAG_SOURCEID("SourceID");
    static const std::string TAG_TARGETID("TargetID");
    static const std::string TAG_SIPSERVERID("SIPServerID");
    static const std::string TAG_SIPSERVERIP("SIPServerIP");
    static const std::string TAG_SIPSERVERPORT("SIPServerPort");
    static const std::string TAG_DOMAINNAME("DomainName");
    static const std::string TAG_EXPIRATION("Expiration");
    static const std::string TAG_SN("SN");
    static const std::string TAG_CONFIGTYPE("ConfigType");
    static const std::string TAG_BASICPARAM("BasicParam");
    static const std::string TAG_VIDEOPARAMCONFIG("VideoParamConfig");
    static const std::string TAG_AUDIOPARAMCONFIG("AudioParamConfig");
    static const std::string TAG_SVACENCODECONFIG("SVACEncodeConfig");
    static const std::string TAG_SVACDECODECONFIG("SVACDecodeConfig");
    static const std::string TAG_RESULT("Result");
    static const std::string TAG_DEVICETYPE("DeviceType");
    static const std::string TAG_NAME("Name");
    static const std::string TAG_MANUFACTURER("Manufacturer");
    static const std::string TAG_MODEL("Model");
    static const std::string TAG_FIRMWARE("Firmware");
    static const std::string TAG_MAXCAMERA("MaxCamera");
    static const std::string TAG_MAXALARM("MaxAlarm");
    static const std::string TAG_STATUS("Status");
    static const std::string TAG_REASON("Reason");
    static const std::string TAG_ONLINE("Online");
    static const std::string TAG_ENCODE	("Encode");
    static const std::string TAG_RECORD	("Record");
    static const std::string TAG_DEVICETIME	("DeviceTime");
    static const std::string TAG_ATTR_CAMERAL("Cameral");
    static const std::string TAG_RECORDLIST("RecordList");
    static const std::string TAG_HEARTBEATINTERVAL("HeartBeatInterval");
    static const std::string TAG_HEARTBEATCOUNT("HeartBeatCount");
    static const std::string TAG_VIDEOPARAMATTRIBUTETYPE("VideoParamAttributeType");
    static const std::string TAG_AUDIOPARAMATTRIBUTETYPE("AudioParamAttributeType");
    static const std::string TAG_STREAMNAME("StreamName");
    static const std::string TAG_VIDEOFORMAT("VideoFormat");
    static const std::string TAG_AUDIOFORMAT("AudioFormat");
    static const std::string TAG_RESOLUTION("Resolution");
    static const std::string TAG_FRAMERATE("FrameRate");
    static const std::string TAG_AUDIOBITRATE("AudioBitRate");
    static const std::string TAG_BITRATETYPE("BitRateType");
    static const std::string TAG_VIDEOBITRATE("VideoBitRate");
    static const std::string TAG_SAMPLINGRATE("SamplingRate");

    static const std::string TAG_ITEM_OWNER("Owner");
    static const std::string TAG_ITEM_CIVILCODE("CivilCode");
    static const std::string TAG_ITEM_BLOCK("Block");
    static const std::string TAG_ITEM_PARENTAL("Parental");
    static const std::string TAG_ITEM_PARENTID("ParentID");
    static const std::string TAG_ITEM_SAFTETYWAY("SafetyWay");
    static const std::string TAG_ITEM_CERTNUM("CertNum");
    static const std::string TAG_ITEM_CERTIFIABLE("Certifiable");
    static const std::string TAG_ITEM_ERRCODE("ErrCode");
    static const std::string TAG_ITEM_ENDTIME("EndTime");
    static const std::string TAG_ITEM_SECRECY("Secrecy");
    static const std::string TAG_ITEM_IPADDRESS("IPAddress");
    static const std::string TAG_ITEM_PORT("Port");
    static const std::string TAG_ITEM_PASSWORD("Password");

    static const std::string TAG_ACTION("Action");
    static const std::string TAG_ACTION_CONTROL("Control");
    static const std::string TAG_ACTION_QUERY("Query");
    static const std::string TAG_ACTION_NOTIFY("Notify");


    static const std::string TAG_VARIABLE("Variable"); // 功能名.


    static const std::string TAG_RESPONSE_CONTROL("ControlResponse");
    static const std::string TAG_RESPONSE_QUERY("QueryResponse");

    static const std::string FUNC_VARIABLE_KEEPALIVE("KeepAlive");//===+++---

    static const std::string FUNC_VARIABLE_CATALOG("Catalog");//===+++---
    static const std::string TAG_PARENT("Parent");
    static const std::string TAG_SUBLIST("SubList");
    static const std::string TAG_DEVICELIST("DeviceList");
    static const std::string TAG_ITEM("Item");
    static const std::string TAG_ITEM_NAME("Name");
    static const std::string TAG_ITEM_ADDRESS("Address");
    static const std::string TAG_ITEM_RESTYPE("ResType");
    static const std::string TAG_ITEM_RESSUBTYPE("ResSubType");
    static const std::string TAG_ITEM_PRIVILEGE("Privilege");
    static const std::string TAG_ITEM_STATUS("Status");
    static const std::string TAG_ITEM_LONGITUDE("Longitude");
    static const std::string TAG_ITEM_LATITUDE("Latitude");
    static const std::string TAG_ITEM_ELEVATION("Elevation");
    static const std::string TAG_ITEM_DECODERTAG("DecoderTag");
    static const std::string TAG_ITEM_OPERATETYPE("OperateType");
    static const std::string TAG_ITEM_PILENO( "PileNo");
    static const std::string TAG_ITEM_ROADWAY("Roadway");
    static const std::string TAG_IITEM_CODING("Coding");

    static const std::string FUNC_VARIABLE_REALMEDIA("RealMedia");//===+++---
    static const std::string TAG_PRIVILEGE("Privilege");
    static const std::string TAG_FORMAT("Format");
    static const std::string TAG_VIDEO("Video");
    static const std::string TAG_AUDIO("Audio");
    static const std::string TAG_MAXBITRATE("MaxBitrate");
    static const std::string TAG_BITRATE("Bitrate");
    static const std::string TAG_SOCKET("ReceiveSocket");
    static const std::string TAG_DECODERTAG("DecoderTag");

    static const std::string FUNC_VARIABLE_FILELIST("FileList");//===+++---

    static const std::string TAG_REALFILENUM("RealFileNum");
    static const std::string TAG_SENDFILENUM("SendFileNum");
    static const std::string TAG_FILEINFOLIST("FileInfolist");
    static const std::string TAG_CREATIONTIME("CreationTime");
    static const std::string TAG_LASTWRITETIME("LastWriteTime");
    static const std::string TAG_ITEM_FILESIZE("FileSize");
    static const std::string TAG_MAXFILENUM("MaxFileNum");

    static const std::string FUNC_VARIABLE_VODBYRTSP("VODByRTSP");//===+++---
    static const std::string TAG_PLAYURL("Playurl");

    static const std::string FUNC_VARIABLE_PTZCOMMAND("PTZCommand");//===+++---
    static const std::string TAG_COMMAND("Command");

    static const std::string FUNC_VARIABLE_PRESETLIST("PresetList");//===+++---
    static const std::string TAG_REALNUM("RealNum");
    static const std::string TAG_PRESETINFOLIST("PresetInfoList");
    static const std::string TAG_ITEM_VALUE("Value");
    static const std::string TAG_ITEM_DESCRIPTION("Description");
    static const std::string TAG_FROMINDEX("FromIndex");
    static const std::string TAG_TOINDEX("ToIndex");

    static const std::string FUNC_VARIABLE_ALARMSUBSCRIBE("AlarmSubscribe");//===+++---
    static const std::string TAG_ADDRESS("Address");
    static const std::string TAG_LEVEL("Level");
    static const std::string TAG_ALARMTYPE("AlarmType");

    static const std::string FUNC_VARIABLE_ALARMNOTIFY("AlarmNotify");//===+++---
    static const std::string TAG_DATA("Data");

    static const std::string XML_RESULT_OK("OK");
    static const std::string XML_RESULT_ERROR("ERROR");

    static const std::string TAG_START_ALARM_PRIORITY("StartAlarmPriority");
    static const std::string TAG_END_ALARM_PRIORITY("EndAlarmPriority");
    static const std::string TAG_ALARM_METHOD("AlarmMethod");

    static const std::string XML_PROLOG_HEAD( "<?xml version=\"1.0\" encoding=\"GB2312\" ?>\n" );
}

#endif
