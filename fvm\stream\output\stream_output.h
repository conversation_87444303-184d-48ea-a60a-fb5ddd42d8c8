/**
 * Project FVM
 */
#pragma once

#include <string>
#include <queue>
#include <list>
#include "stream/element/stream_element.h"
#include "stream/element/ffmpeg_element.h"
#include "stream/element/packet_data.h"
#include "util/worker_pool.h"

/**
 * @brief: 视频输出
 *          派生文件录像、RTMP流等
 */
namespace fvm {
    namespace stream {

        constexpr auto MAX_OUTPUT_BUFFER_SIZE = 200u;
        constexpr auto MIN_OUTPUT_BUFFER_SIZE = 20u;

        /**
        * 视频输出类型
        */
        enum class StreamOuputType {
            Base,
            File,
            UDP,
            RTP,
            RTMP
        };

        /**
         * @brief 输出目标类型
         */
        enum class OutputTargetType
        {
            Common,
            TempRequest,        //!< web端 视频查看
            TempSetArea         //!< web端 设置检测区
        };

        class StreamOutput : public FFMPEGElement {
        public:
            StreamOutput(StreamOuputType streamType);
            /**
            *  填入视频输出地址
            * @param const addr
            */
            void initAddress(const std::string addr);

            /**
            *  设置输出端口
            */
            void setPort(int port) { outputPort = port; }
            /**
            *  获取输出端口
            */
            int getPort() { return outputPort; }
            
            /**
             *  填入视频流数据包
             * @param const PacketDataPtr
             */
            virtual void pushData(const PacketDataPtr packet);

            /**
             *  填入视频流信息
             * @param const CodecInfoPtr
             */
            void setCodecInfo(const CodecInfoPtr codec);

            /**
             *  设置输出目标类型
             * @param const targetType
             */
            void setOutputTarget(OutputTargetType type){this->targetType = type;};

            /**
             *  获取输出目标类型
             */
            OutputTargetType getOutputTarget(){return this->targetType;};

            /**
             *  获取输出类型
             */
            StreamOuputType  getStreamOuputType() { return this->outputStreamType; };
        protected:
            /**
             * 打开输出流
             */
            virtual bool open();

            /**
             * 关闭输出流
             */
            virtual void close();

            virtual unsigned int maxBufferSize() {return MAX_OUTPUT_BUFFER_SIZE;}
            virtual unsigned int minBufferSize() {return MIN_OUTPUT_BUFFER_SIZE;}

            /*
             * 任务类别
             */
            worker::WorkerType workerType() override {return worker::WorkerType::StreamOutput;}

        protected:
            // 数据条件
            worker::FiberDone newData;
            boost::fibers::mutex mutexOutput;

            // 视频流头信息是否已写入
            std::atomic_bool headerHasWritten = false;

            // 流输出类型
            StreamOuputType outputStreamType;
            // 视频流数据包
            std::list<PacketDataPtr> packetDatas;
            // 码流信息
            CodecInfoPtr codecInfo = NULL;
            // 输出目标类型
            OutputTargetType targetType = OutputTargetType::Common;

            // 输出端口
            int outputPort = 0;
        };

        typedef std::shared_ptr<StreamOutput> StreamOutputPtr;
        typedef std::list<StreamOutputPtr> StreamOutputList;
    }
}