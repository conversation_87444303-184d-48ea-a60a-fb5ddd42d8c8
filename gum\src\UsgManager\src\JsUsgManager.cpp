#include <ace/INET_Addr.h>
#include <boost/uuid/string_generator.hpp>
#include <map>
#include "UsgManager/include/UsgManagerPkg.hpp"

#include "UsgManager/src/JsUsgManager.hpp"
#include "UsgManager/src/UsgManager.hpp"

namespace wtoe {

    template<>
    JSClass CJavaScriptAdapter<CJsUsgManager>::s_jsClass =
            {
                    "usg",
                    JSCLASS_HAS_PRIVATE,
                    JS_PropertyStub, // add
                    JS_PropertyStub, // del
                    JS_PropertyStub, // get
                    JS_PropertyStub, // vector
                    JS_EnumerateStub,
                    JS_ResolveStub,
                    JS_ConvertStub,
                    WTOE_JS_FINALIZER( CJsUsgManager , finalize ),
                    JSCLASS_NO_OPTIONAL_MEMBERS
            };

    template<>
    JSPropertySpec CJavaScriptAdapter<CJsUsgManager>::s_jsClassProperties[] =
            {
                    { 0, 0, 0, 0, 0 }
            };

    template<>
    JSFunctionSpec CJavaScriptAdapter<CJsUsgManager>::s_jsClassMethords[] =
            {
                    { "setLoacalAddr", WTOE_JS_METHORD( CJsUsgManager, setLoacalAddr ), 0, 0, 0 },
//    { "setLoadLibName", WTOE_JS_METHORD( CJsUsgManager, setLoadLibName ), 0, 0, 0 },
//    { "setXmlType", WTOE_JS_METHORD( CJsUsgManager, setXmlType ), 0, 0, 0 },
                    { "setPackerType", WTOE_JS_METHORD( CJsUsgManager, setPackerType ), 0, 0, 0 },
                    { "start", WTOE_JS_METHORD( CJsUsgManager, start ), 0, 0, 0 },
                    { "stop", WTOE_JS_METHORD( CJsUsgManager, stop ), 0, 0, 0 },
                    { "playHistoryPrepare", WTOE_JS_METHORD( CJsUsgManager, playHistoryPrepare ), 0, 0, 0 },
                    { "getMediaInfoByMedia", WTOE_JS_METHORD( CJsUsgManager, getMediaInfoByMedia ), 0, 0, 0 },
                    { "getAllResInfo", WTOE_JS_METHORD( CJsUsgManager, getAllResInfo ), 0, 0, 0 },
                    { "getPtzController", WTOE_JS_METHORD( CJsUsgManager, getPtzController ), 0, 0, 0 },
                    { "onReceivePtzCommand", WTOE_JS_METHORD( CJsUsgManager, onReceivePtzCommand ), 0, 0, 0 },
                    { "onqueryPreposition", WTOE_JS_METHORD( CJsUsgManager, onqueryPreposition ), 0, 0, 0 },
                    { "onReceiveCatalog", WTOE_JS_METHORD( CJsUsgManager, onReceiveCatalog ), 0, 0, 0 },
                    { "onReceiveDeviceReboot", WTOE_JS_METHORD( CJsUsgManager, onReceiveDeviceReboot ), 0, 0, 0 },
                    { "onReceiveRecordStart", WTOE_JS_METHORD( CJsUsgManager, onReceiveRecordStart ), 0, 0, 0 },
                    { "onReceiveRecordStop", WTOE_JS_METHORD( CJsUsgManager, onReceiveRecordStop ), 0, 0, 0 },
                    { "onReceiveGuardSet", WTOE_JS_METHORD( CJsUsgManager, onReceiveGuardSet ), 0, 0, 0 },
                    { "onReceiveGuardReset", WTOE_JS_METHORD( CJsUsgManager, onReceiveGuardReset ), 0, 0, 0 },
                    { "onReceiveAlarmReset", WTOE_JS_METHORD( CJsUsgManager, onReceiveAlarmReset ), 0, 0, 0 },
                    { "onReceiveQueryDeviceCatalog", WTOE_JS_METHORD( CJsUsgManager, onReceiveQueryDeviceCatalog ), 0, 0, 0 },
                    { "onReceiveQueryDeviceInfo", WTOE_JS_METHORD( CJsUsgManager, onReceiveQueryDeviceInfo ), 0, 0, 0 },
                    { "onReceiveQueryDeviceStatus", WTOE_JS_METHORD( CJsUsgManager, onReceiveQueryDeviceStatus ), 0, 0, 0 },
                    { 0, 0, 0, 0, 0 }
            };

    template<>
    JSPropertySpec CJavaScriptAdapter<CJsUsgManager>::s_jsObjectProperties[] =
            {
                    { 0, 0, 0, 0, 0 }
            };

    template<>
    JSFunctionSpec CJavaScriptAdapter<CJsUsgManager>::s_jsObjectMethords[] =
            {
                    { 0, 0, 0, 0, 0 }
            };

    CJsUsgManager::CJsUsgManager()
    {
    }

    CJsUsgManager::~CJsUsgManager()
    {
    }

    CJsUsgManager *CJsUsgManager::createObject()
    {
        return 0;
    }

    JSBool CJsUsgManager::setLoacalAddr( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string addr;
        if ( JS_FALSE == JType2CType( addr, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        sgc->setLoacalAddr( addr );

        return JS_TRUE;
    }

    JSBool CJsUsgManager::start( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string clientAddr, srcAddr;
        std::string id;
        if ( JS_FALSE == JType2CType( id, clientAddr, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;
// 	bool bRet = sgc->startPlay( id, clientAddr, srcAddr );
// 	if ( !bRet )  return JS_FALSE;
        return JS_TRUE;
    }

    JSBool CJsUsgManager::stop( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string clientAddr, srcAddr;
        std::string id;
        if ( JS_FALSE == JType2CType( id, clientAddr, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

//     if ( !sgc->stopPlay( id, clientAddr ) )
//     {
//         return JS_FALSE;
//     }
        return JS_TRUE;
    }

    JSBool CJsUsgManager::getMediaInfoByMedia( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string uuid;
        uint32_t uBegin,uEnd;
        if ( JS_FALSE == JType2CType( uuid,uBegin,uEnd, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IGb28181> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        usg::TimePeriodUnit unit;
        unit.first = uBegin;
        unit.second = uEnd;
        boost::uuids::uuid resUuid = boost::uuids::string_generator()( uuid.c_str() );
        std::vector< std::pair< std::string, usg::TimePeriodUnit >  >info;
        if ( !sgc->getMediaInfoByMedia( resUuid,unit, info ) )
        {
            return JS_FALSE;
        }
        std::cout << " CJsUsgManager getMediaInfoByMedia OK , info.size = " << info.size() << std::endl;
        return JS_TRUE;
    }

    JSBool CJsUsgManager::playHistoryPrepare( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string uuid;
        std::string fileName;
        uint32_t uBegin,uEnd;
        if ( JS_FALSE == JType2CType( uuid,fileName,uBegin,uEnd, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        usg::TimePeriodUnit unit;
        unit.first = uBegin;
        unit.second = uEnd;
//        boost::uuids::uuid resUuid = boost::uuids::string_generator()( uuid.c_str() );
//        std::string vodUrl;
//     if ( !sg->playHistoryPrepare( resUuid,fileName,unit, vodUrl ) )
//     {
//         return JS_FALSE;
//     }
//        std::cout << " CJsUsgManager playHistoryPrepare OK , vodUrl = " << vodUrl << std::endl;
        return JS_TRUE;
    }

    JSBool CJsUsgManager::getAllResInfo( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        std::map<boost::uuids::uuid, usg::SResInfo > mapRes;
        uint32_t timeStamp;
        if ( !sgc->getAllResInfo( mapRes,timeStamp ) )
        {
            return JS_FALSE;
        }
        std::cout << " CJsUsgManager getAllResInfo OK , mapRes.size = " << mapRes.size() << std::endl;
        return JS_TRUE;
    }

    JSBool CJsUsgManager::getPtzController( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        boost::shared_ptr<usg::IGb28181> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return JS_FALSE;

        usg::CSpIPtzController SipPtzController = sg->getPtzControllerMgr();
        if ( !SipPtzController )
        {
            std::cout << " CJsUsgManager getPtzController fail. " << std::endl;
            return JS_FALSE;
        }
        std::cout << " CJsUsgManager getPtzController OK. " << std::endl;
        return JS_TRUE;
    }

    JSBool CJsUsgManager::onReceivePtzCommand( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string struuid;
        uint32_t uptzCommand;
        uint16_t uarg1,uarg2;
        if ( JS_FALSE == JType2CType( uptzCommand,struuid,uarg1, uarg2, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        boost::uuids::uuid resUuid = boost::uuids::string_generator()( struuid.c_str() );
        if ( uptzCommand > 25 )
        {
            std::cout << " uptzCommand error." << uptzCommand << std::endl;
            return JS_FALSE;
        }
        usg::EPtzCommand eCommand = (usg::EPtzCommand)uptzCommand;
        std::string vodUrl;
        if ( !sgc->onReceivePtzCommand( eCommand,resUuid,uarg1, uarg2 ) )
        {
            return JS_FALSE;
        }
        std::cout << " CJsUsgManager onReceivePtzCommand OK "  << std::endl;
        return JS_TRUE;
    }

    JSBool CJsUsgManager::onqueryPreposition( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string struuid;
        if ( JS_FALSE == JType2CType( struuid, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        boost::uuids::uuid resUuid = boost::uuids::string_generator()( struuid.c_str() );

        std::map< uint8_t, std::string > map;
        if ( !sgc->onqueryPreposition( resUuid,map ) )
        {
            return JS_FALSE;
        }
        std::cout << " CJsUsgManager onqueryPreposition OK "  << std::endl;
        return JS_TRUE;
    }

    JSBool CJsUsgManager::onReceiveCatalog( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
//     std::string struuid;
//     uint16_t action;
//     if ( JS_FALSE == JType2CType( action, struuid, ctx, argv ) ) return JS_FALSE;
//     if ( action > 3 )
//     {
//         std::cout << " action input error. action = " << action << std::endl;
//         return JS_FALSE;
//     }
//
//     boost::uuids::uuid resUuid = boost::uuids::string_generator()( struuid.c_str() );
//
//     boost::shared_ptr<usg::IUsgManager> sg;
//     if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return JS_FALSE;
//
// 	boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
// 	if( sgc == 0 ) return JS_FALSE;
//
//     usg::SResInfo info;
//     std::map<boost::uuids::uuid, usg::SResInfo > mapRes;
//     uint32_t tem;
//     if ( !sgc->getAllResInfo( mapRes,tem ) )
//     {
//         std::cout << " onReceiveCatalog fail. getAllResInfo -----" << std::endl;
//         return JS_FALSE;
//     }
//     if ( action != 0 ) //修改和删除
//     {
//         std::map<boost::uuids::uuid, usg::SResInfo >::iterator it = mapRes.find( resUuid );
//         if ( it == mapRes.end() )
//         {
//             std::cout << " uuid input error. uuid =" << struuid << " not find." << std::endl;
//             return JS_FALSE;
//         }
//         info = (*it).second;
//         info.resStatus = (!info.resStatus);
//     }
//     else 
//     {
//         info.resName = "test___";
//         info.sipResCode = "1234567890123456";
//         info.resStatus = true;
//     }
//     
//     usg::ESgResNotifyAction eAction = (usg::ESgResNotifyAction)action;
//     if ( !sgc->onReceiveCatalog( eAction,info ) )
//     {
//         std::cout << " onReceiveCatalog fail. " << std::endl;
//         return JS_FALSE;
//     }
//     std::cout << " CJsUsgManager onReceiveCatalog OK "  << std::endl;
        return JS_TRUE;
    }

    JSBool CJsUsgManager::setPackerType( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        uint8_t packType;
        if ( JS_FALSE == JType2CType( packType, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        if ( !sgc->setPackerType( packType ) )
        {
            return JS_FALSE;
        }

        return JS_TRUE;
    }

    JSBool CJsUsgManager::onReceiveDeviceReboot( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string struuid;
        if ( JS_FALSE == JType2CType( struuid, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) )
            return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        boost::uuids::uuid resUuid = boost::uuids::string_generator()( struuid.c_str() );
        if ( !sgc->onReceiveDeviceReboot( resUuid ) )
        {
            return JS_FALSE;
        }
        std::cout << " CJsUsgManager onReceiveDeviceReboot OK "  << std::endl;
        return JS_TRUE;
    }

    JSBool CJsUsgManager::onReceiveRecordStart( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {

        std::string struuid;
        if ( JS_FALSE == JType2CType( struuid, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) )
            return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        boost::uuids::uuid resUuid = boost::uuids::string_generator()( struuid.c_str() );
        if ( !sgc->onReceiveRecordContronl( resUuid, true ) )
        {
            return JS_FALSE;
        }
        std::cout << " CJsUsgManager onReceiveRecordStart OK "  << std::endl;
        return JS_TRUE;
    }

    JSBool CJsUsgManager::onReceiveRecordStop( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {

        std::string struuid;
        if ( JS_FALSE == JType2CType( struuid, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) )
            return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        boost::uuids::uuid resUuid = boost::uuids::string_generator()( struuid.c_str() );
        if ( !sgc->onReceiveRecordContronl( resUuid, false ) )
        {
            return JS_FALSE;
        }
        std::cout << " CJsUsgManager onReceiveRecordStop OK "  << std::endl;
        return JS_TRUE;
    }

    JSBool CJsUsgManager::onReceiveGuardSet( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string struuid;
        if ( JS_FALSE == JType2CType( struuid, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) )
            return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        boost::uuids::uuid resUuid = boost::uuids::string_generator()( struuid.c_str() );
        if ( !sgc->onReceiveGuardContronl( resUuid, true ) )
        {
            return JS_FALSE;
        }
        std::cout << " CJsUsgManager onReceiveGuardSet OK "  << std::endl;
        return JS_TRUE;

    }

    JSBool CJsUsgManager::onReceiveGuardReset( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string struuid;
        if ( JS_FALSE == JType2CType( struuid, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) )
            return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        boost::uuids::uuid resUuid = boost::uuids::string_generator()( struuid.c_str() );
        if ( !sgc->onReceiveGuardContronl( resUuid, false ) )
        {
            return JS_FALSE;
        }
        std::cout << " CJsUsgManager onReceiveGuardReset OK "  << std::endl;
        return JS_TRUE;

    }


    JSBool CJsUsgManager::onReceiveAlarmReset( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string struuid;
        if ( JS_FALSE == JType2CType( struuid, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) )
            return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        boost::uuids::uuid resUuid = boost::uuids::string_generator()( struuid.c_str() );
        if ( !sgc->onReceiveAlarmReset( resUuid ) )
        {
            return JS_FALSE;
        }
        std::cout << " CJsUsgManager onReceiveAlarmReset OK "  << std::endl;
        return JS_TRUE;

    }

    JSBool CJsUsgManager::onReceiveQueryDeviceCatalog( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string devid;
        if ( JS_FALSE == JType2CType( devid, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) )
            return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

// 	usg::SCatalog info;
//     if ( !sgc->onReceiveQueryDeviceCatalog( devid, info ) )
//     {
//         return JS_FALSE;
//     }
        std::cout << " CJsUsgManager onReceiveQueryDeviceCatalog OK "  << std::endl;
        return JS_TRUE;

    }

    JSBool CJsUsgManager::onReceiveQueryDeviceInfo( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string struuid;
        if ( JS_FALSE == JType2CType( struuid, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) )
            return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        boost::uuids::uuid resUuid = boost::uuids::string_generator()( struuid.c_str() );
        usg::SDeviceInfoResponse info;
        if ( !sgc->onReceiveQueryDeviceInfo( resUuid, info ) )
        {
            return JS_FALSE;
        }
        std::cout << " CJsUsgManager onReceiveQueryDeviceInfo OK "  << std::endl;
        return JS_TRUE;

    }

    JSBool CJsUsgManager::onReceiveQueryDeviceStatus( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
    {
        std::string struuid;
        if ( JS_FALSE == JType2CType( struuid, ctx, argv ) ) return JS_FALSE;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) )
            return JS_FALSE;

        boost::shared_ptr< usg::CUsgManager > sgc = boost::dynamic_pointer_cast< usg::CUsgManager >( sg );
        if( sgc == 0 ) return JS_FALSE;

        boost::uuids::uuid resUuid = boost::uuids::string_generator()( struuid.c_str() );
        usg::SDeviceStatusResponse info;
        if ( !sgc->onReceiveQueryDeviceStatus( resUuid, info ) )
        {
            return JS_FALSE;
        }
        std::cout << " CJsUsgManager onReceiveQueryDeviceStatus OK "  << std::endl;
        return JS_TRUE;

    }

}

/******************************************************************************/
/* javaScriptRegister                                                         */
/******************************************************************************/
bool usg::CUsgManager::javaScriptRegister( JSContext *jsCtx, JSObject *jsObj )
{
    return wtoe::CJavaScriptAdapter<wtoe::CJsUsgManager>::registerJavaScriptClass( jsCtx, jsObj );
}
