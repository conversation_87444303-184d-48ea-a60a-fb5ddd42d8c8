/**
 * @file    database_process.h
 * @brief   基于ODB的数据库通用操作API接口
 * <AUTHOR>
 * @version 0.0.1
 * @date    18 Oct 2021
 */

/**
 * @defgroup odbDatabaseGroup odbDatabase
 * @brief odb database modules
 * @{
 */
#ifndef _DATABASE_PROCESS_H
#define _DATABASE_PROCESS_H

#include <iostream>
#include <string>
#include <odb/core.hxx>
#include <memory>     // std::auto_ptr

#include <odb/database.hxx>
#include <odb/transaction.hxx>
#include <odb/mysql/database.hxx>
#include "database_process.tcc"


/**
 * @brief: 基于ODB的数据库通用操作API接口
 */
namespace db{

	/**
	 * @brief      数据库初始化、连接
	 * @param[in]  user: 数据库用户名 passwd: 数据库密码 dbName:数据库名 host:数据库域名  port:数据库端口号
	 */
	bool init(const std::string& user,const std::string& passwd, const std::string& dbName, const std::string& host = "", unsigned int port = 0);

	/**
	 * @brief      数据库反初始化
	 */
	bool deinit();

	/**
	 * @brief      根据条件查询数据库表或者视图
	 * @param[in]  query 查询条件，T: 数据库表对应的持久化类或视图结构
	 * @param[out] results: 返回的对应条件的数据库对象或者视图对象
	 */
	template<typename T>
	bool queryData(std::vector<T>& results, odb::query<T>& query);

	/**
	 * @brief      往数据库表中新增一条记录（持久化对象)
	 * @param[in]  tableObject 需要新增表对应的对象
	 * @param[out]  return 返回值-1 即失败 >-1即返回插入的自动ID
	 */
	template <typename T>
	int insertData(T& tableObject);

	/**
	 * @brief      更新数据库表中记录（持久化对象)
	 * @param[in]  tableObject 需要更新的表对应的对象 T: 数据库表对应的持久化类
	 */
	template <typename T>
	bool updateData(T& tableObject);

	/**
	 * @brief      擦除数据库表中记录（持久化对象)
	 * @param[in]  tableObject 需要更新的表对应的对象 T: 数据库表对应的持久化类
	 */
	template <typename T>
	bool eraseData(T& tableObject);

	/**
	 * @brief      擦除给定条件的数据库表记录（持久化对象)
	 * @param[in]  query 删除条件
	 */
	template <typename T>
	bool eraseData(odb::query<T>& query);
}

/**
 * @}
 */

#endif //_DATABASE_PROCESS_H