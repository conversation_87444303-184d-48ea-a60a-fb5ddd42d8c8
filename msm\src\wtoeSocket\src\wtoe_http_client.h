﻿#ifndef _WTOE_HTTP_CLIENT_H
#define _WTOE_HTTP_CLIENT_H

#include <string>
#include "wtoeSocketComm.h"


// http 工具类
class WTOESOCKET_API WtoeAIHttpClient
{
private:
    static size_t OnWriteData(void* buffer, size_t size, size_t nmemb, void* lpVoid);
	int sendMsg( std::string url, std::string msg);
public:
    WtoeAIHttpClient();
	~WtoeAIHttpClient();

    bool PrintLog;
    int ChannelIndex;

    int do_get(std::string url, std::string key_values, std::string & response);
    int do_post(std::string url, std::string body_values, std::string & response);

	//3.2.1录入事件
	int eventRecord(std::string url, EventInfo& eventInfo);
	//3.2.2事件录像完成
	int eventVideoFinished(std::string url, std::string urlEventVideo);
	//3.2.3FVM心跳保活
	int heartInfo(std::string url, int status, int localPort );
	//3.2.4通道流状态上报
	int streamStatus(std::string url, int channelId, int videoId,int status);
	//3.4.1视频质量告警
	int videoQuaAlarm(std::string url, VideoQuaAlarmConf& videoQuaAlarmConf);
	//3.4.2视频质量恢复
	int videoQuaRecovery(std::string url, int iVideoId, std::vector<int> vtType,int iPresetId,std::string ip);
	//3.4.3录入通道检测数据
	int saveDetectInfo(std::string url, DetectInfo& detectInfo);
	int saveDetectInfo(std::string url, std::vector<DetectInfo>& vtDetectInfo);

	//3.4.4事件解除通知
	int eventRemove(std::string url, EventRemoveInfo & eventRemoveInfo);

	//3.4.5事件撤回通知
	int eventWithdraw(std::string url, std::string eventId, int eventTypeId);
	//IVA心跳保活 接口同 3.2.3


	//3.4.6通道检测状态上报
	int detectStatus(std::string url, DetectStatusInfo& detectStatusInfo);
	/*播放临时流状态上报*/
	int playTempStatus(std::string url, int videoId, int streamId, int status);

	void RegisterMsgReceived(CBHandleMsgReceived cb, void*pUserData);

private:
	CBHandleMsgReceived m_cbhandleMsgReceived;
};
#endif
