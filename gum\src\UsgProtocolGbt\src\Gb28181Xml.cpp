
#include <string>
#include <pjlib.h>
#include <pjlib-util.h>

#include "UsgManager/include/UsgManagerExp.hpp"

#include "Gb28181Xml.hpp"
#include "Gb28181XmlTag.hpp"
#include "../include/Gb28181Type.hpp"
#include "StructExchange.hpp"

#define ENCODERNUM  1 //不知道编码器的个数，暂时取两个编码器

namespace gb28181
{

    static const std::string s_notifyType("121");

    CGb28181Xml::CGb28181Xml( pj_pool_t *pool ) : m_pool( pool ), m_isOk( false ), m_isPoolExtra( false )
    {
        if( m_pool == 0 )
        {
            boost::shared_ptr<usg::IUsgManager> sg;
            if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return;

            long *ptrdes = 0;
            if( PJ_TRUE != pj_thread_is_registered() )
            {
                ptrdes = new long[PJ_THREAD_DESC_SIZE];
                pj_thread_desc *tmp = ( pj_thread_desc* )ptrdes;
                pj_thread_t *this_thread = 0;
                ::memset( tmp, 0, sizeof( pj_thread_desc ) );
                if( PJ_SUCCESS != pj_thread_register( 0, *tmp, &this_thread ) )
                {
                    std::cerr << "CGb28181Xml::CGb28181Xml pj_thread_register false" << std::endl;
                    return;
                }

                sg->addPJThreadDes( ptrdes );
            }

            pj_caching_pool_init( &m_caching_pool, &pj_pool_factory_default_policy, 0 );
            pj_pool_factory *mem = 0;
            mem = &m_caching_pool.factory;
            m_pool = pj_pool_create( mem, "CGb28181Xml", 4096, 1024, 0 );
            if( m_pool == 0 )
            {
                return;
            }
        }
        else
        {
            m_isPoolExtra = true;
        }
        m_isOk = true;
    }
    CGb28181Xml::~CGb28181Xml()
    {
        if( m_isPoolExtra )
            return;
        if( m_pool )
        {
            pj_pool_release( m_pool );
            m_pool = 0;
        }

        pj_caching_pool_destroy( &m_caching_pool );
    }

    bool CGb28181Xml::isOk()
    {
        return m_isOk;
    }

    pj_str_t* CGb28181Xml::doConv( pj_str_t *dst, const std::string &str )
    {
        if( !m_isOk ) return 0;
        if( m_pool == 0 ) return 0;
        return pj_strdup2( m_pool, dst, str.c_str() );
    }

    bool CGb28181Xml::getString( const SCatalog &gbtInfo, std::vector<std::string> &outStringVec )
    {
        outStringVec.clear();
        outStringVec.push_back( std::string( "CatalogQueryCmad" ) );
        if( gbtInfo.subNum == 0 )
        {
            std::string outStr = "";
            bool ret = getString( gbtInfo.coding, gbtInfo.sn, gbtInfo.name, outStr, ENULLTYPE_CATALOGFILE );
            if( !ret )
                return false;

            if( !outStr.empty() )
                outStringVec.push_back(outStr);
            return true;
        }

        for( int16_t i=0;i<gbtInfo.subNum;i++)
        {
            std::string outStr = "";
            bool ret = getString(gbtInfo, i, outStr);
            if( !ret )
                return false;
            outStringVec.push_back(outStr);
        }

        return true;
    }

    bool CGb28181Xml::getString( const SCatalog &gbtInfo, size_t index, std::string &outString )
    {
        const std::string wtoeName = "WTOE-PLATEFORM";
        if( !m_isOk ) return false;

        //if( gbtInfo.subList.empty() )
        //	return false;
        if( gbtInfo.subList.size() <= index )
            return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 4000;//+gbtInfo.subList.size()*1000; // 头计200,一个item计500

        //Notify
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //CmdType
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "Catalog" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //SN
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_SN ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //DeviceID
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.coding ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //SumNum
        if( 0 == doConv( &pjStr, gb28181::TAG_SUMNUM ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( gbtInfo.subNum, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //DeviceList
        pj_xml_node *subRoot1=0;
        if( 0 == doConv( &pjStr, "DeviceList" ) )
            return false;
        if( !( subRoot1 = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        if( 0 == doConv( &pjStr, "Num" ) )
            return false;
        pj_str_t pjStr1;
        if( 0 == doConv( &pjStr1, "1" ) )
            return false;
        pj_xml_attr *temp  = pj_xml_attr_new(m_pool, &pjStr, &pjStr1);
        pj_xml_add_attr(subRoot1, temp);
        pj_xml_add_node( root, subRoot1 );

        const std::string encoderName = "EncoderName";
        for(int i= 0; i< ENCODERNUM ; i++)
        {
            gb28181::SCatalog::SItem item = gbtInfo.subList[index];
            //SubItem
            pj_xml_node *subRoot=0;
            if( 0 == doConv( &pjStr, gb28181::TAG_ITEM ) )
                return false;
            if( !( subRoot = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            pj_xml_add_node( subRoot1, subRoot );

            //DeviceID
            if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, item.itemAddr ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            // name
            if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_NAME ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, item.itemName ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            //Manufacturer
            if( 0 == doConv( &pjStr, gb28181::TAG_MANUFACTURER ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, item.manufacturer ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            //Model
            if( 0 == doConv( &pjStr, gb28181::TAG_MODEL ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, item.model ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            //Owner
            if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_OWNER ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, item.owner ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            //CivilCode
            if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_CIVILCODE ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, item.civilcode ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            //Address
            if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_ADDRESS ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, item.address) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            // Parental
            if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_PARENTAL ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, item.parental ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            // ParentID
            if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_PARENTID ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, gbtInfo.coding ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            // RegisterWay
            if( 0 == doConv( &pjStr, "RegisterWay" ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, item.registerway ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            // Secrecy
            if( 0 == doConv( &pjStr, "Secrecy" ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, item.secrecy ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            //IPAddress
            if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_IPADDRESS ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, item.address) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            // Status
            if( 0 == doConv( &pjStr, "Status" ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, (item.itemStatus == "0")?"ON":"OFF" ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            // Longitude
            if( 0 == doConv( &pjStr, "Longitude" ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, item.itemLongitude ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );

            // Latitude
            if( 0 == doConv( &pjStr, "Latitude" ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, item.itemLatitude ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( subRoot, next );
        }

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;
        return true;
    }

    bool CGb28181Xml::getString( const SCatalog &gbtInfo, std::string &outString )
    {
        const std::string wtoeName = "WTOE-PLATEFORM";
        if( !m_isOk ) return false;

        //if( gbtInfo.subList.empty() )
        //	return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 4000;//+gbtInfo.subList.size()*1000; // 头计200,一个item计500

        //Notify
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //CmdType
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "Catalog" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //SN
        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //DeviceID
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "64010000001110000001" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //SumNum
        if( 0 == doConv( &pjStr, gb28181::TAG_SUMNUM ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "1" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //DeviceList
        pj_xml_node *subRoot1=0;
        if( 0 == doConv( &pjStr, "DeviceList" ) )
            return false;
        if( !( subRoot1 = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        if( 0 == doConv( &pjStr, "Num" ) )
            return false;
        pj_str_t pjStr1;
        if( 0 == doConv( &pjStr1, "1" ) )
            return false;
        pj_xml_attr *temp  = pj_xml_attr_new(m_pool, &pjStr, &pjStr1);
        pj_xml_add_attr(subRoot1, temp);
        pj_xml_add_node( root, subRoot1 );

        const std::string encoderName = "EncoderName";
        for(int i= 0; i< ENCODERNUM ; i++)
        {
            std::vector< gb28181::SCatalog::SItem >::const_iterator it( gbtInfo.subList.begin() );
            std::vector< gb28181::SCatalog::SItem >::const_iterator end( gbtInfo.subList.end() );
            for( ; it != end; ++it )
            {
                //SubItem
                pj_xml_node *subRoot=0;
                if( 0 == doConv( &pjStr, gb28181::TAG_ITEM ) )
                    return false;
                if( !( subRoot = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                pj_xml_add_node( subRoot1, subRoot );

                //DeviceID
                if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, gbtInfo.parentAddr ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // name
                if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_NAME ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, (*it).itemName ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                //Manufacturer
                if( 0 == doConv( &pjStr, gb28181::TAG_MANUFACTURER ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "MANUFACTURER" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                //Model
                if( 0 == doConv( &pjStr, gb28181::TAG_MODEL ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "WTDC-NC1262RC-ADKSI" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                //Owner
                if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_OWNER ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "Owner" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                //CivilCode
                if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_CIVILCODE ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "CivilCode" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                //Block
                if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_BLOCK ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "Block1" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                //Address
                if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_ADDRESS ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, (*it).itemAddr) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // Parental
                if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_PARENTAL ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "1" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // ParentID
                if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_PARENTID ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "64010000001110000001" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // SafetyWay
                if( 0 == doConv( &pjStr, "SafetyWay" ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "0" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // RegisterWay
                if( 0 == doConv( &pjStr, "RegisterWay" ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "1" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // CertNum
                if( 0 == doConv( &pjStr, "CertNum" ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "CertNum1" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // Certifiable
                if( 0 == doConv( &pjStr, "Certifiable" ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "0" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // ErrCode
                if( 0 == doConv( &pjStr, "ErrCode" ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "400" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // ErrCode
                if( 0 == doConv( &pjStr, "EndTime" ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "2020-11-11T24:00:00" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // Secrecy
                if( 0 == doConv( &pjStr, "Secrecy" ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "0" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // IPAddress
                if( 0 == doConv( &pjStr, "IPAddress" ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "************" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // Port
                if( 0 == doConv( &pjStr, "Port" ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "6060" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // Password
                if( 0 == doConv( &pjStr, "Password" ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "12345678" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // Status
                if( 0 == doConv( &pjStr, "Status" ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "ON" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // Longitude
                if( 0 == doConv( &pjStr, "Longitude" ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "0.000" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );

                // Latitude
                if( 0 == doConv( &pjStr, "Latitude" ) )
                    return false;
                if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;
                if( 0 == doConv( &pjStr, "0.000" ) )
                    return false;
                next->content = pjStr;
                pj_xml_add_node( subRoot, next );
            }

        }

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;
        return true;
    }

    bool CGb28181Xml::getString( const SRealMediaResponse &gbtInfo, std::string &outString )
    {
        if( !m_isOk ) return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 400;

        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        if( 0 == doConv( &pjStr, gb28181::TAG_VARIABLE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_VARIABLE_REALMEDIA ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_FORMAT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !strFromFormatType( gbtInfo.formatType, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_VIDEO ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !strFromVideoType( gbtInfo.videoType, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_AUDIO ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !strFromAudioType( gbtInfo.audioType, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_BITRATE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( gbtInfo.bitrate, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

//     if( 0 == doConv( &pjStr, gb28181::TAG_SOCKET ) )
//         return false;
//     if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
//         return false;
//     if( !strFromSocketUrl( gbtInfo.socketType, gbtInfo.sockAddr, gbtInfo.sockPort, stdStr ) )
//         return false;
//     if( 0 == doConv( &pjStr, stdStr ) )
//         return false;
//     next->content = pjStr;
//     pj_xml_add_node( root, next );
//
//     if( 0 == doConv( &pjStr, gb28181::TAG_DECODERTAG ) )
//         return false;
//     if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
//         return false;
// //     if( 0 == doConv( &pjStr, gbtInfo.decoderTag ) )
// //         return false;
//     next->content = pjStr;
//     pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        //std::cout << outString << std::endl;

        return true;
    }

    bool CGb28181Xml::getString( const std::string &recordId, const std::string &sn, const std::string &resName, std::string &outString, ENULLTYPE type )
    {
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 300+1*500; // 头计300,一个item计500

        //Response
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //CmdType
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        if( type == ENULLTYPE_HISTORYFILE )
        {
            if( 0 == doConv( &pjStr, FUNC_CONTROL_CMDTYPE_RECORDINFO ) )
                return false;
        }
        else if( type == ENULLTYPE_CATALOGFILE )
        {
            if( 0 == doConv( &pjStr, FUNC_CONTROL_CMDTYPE_CATALOG ) )
                return false;
        }

        next->content = pjStr;
        pj_xml_add_node( root, next );

        //SN
        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //DeviceID
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, recordId ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( type == ENULLTYPE_HISTORYFILE )
        {
            //Name
            if( 0 == doConv( &pjStr, gb28181::TAG_NAME ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, resName ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( root, next );
        }

        //SumNum
        if( 0 == doConv( &pjStr, gb28181::TAG_SUMNUM ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( 0, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0 )
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        return true;
    }

    bool CGb28181Xml::getString( const SFileListResponse &gbtInfo, std::vector<std::string> &outStringVec )
    {
        outStringVec.clear();
        if( gbtInfo.fileInfolist.size() == 0 )
        {
            std::string outStr = "";
            bool ret = getString( gbtInfo.resAddr, gbtInfo.sn, gbtInfo.resName, outStr, ENULLTYPE_HISTORYFILE );
            if( !ret )
                return false;

            if( !outStr.empty() )
                outStringVec.push_back(outStr);
            return true;
        }

        for(size_t i=0;i<gbtInfo.fileInfolist.size();i++)
        {
            std::string outStr = "";
            bool ret = getString( gbtInfo, i, outStr );
            if( !ret )
                return false;

            if( !outStr.empty() )
                outStringVec.push_back(outStr);
        }

        return true;
    }

    bool CGb28181Xml::getString( const SFileListResponse &gbtInfo, size_t index, std::string &outString )
    {
        if( !m_isOk ) return false;

        if( gbtInfo.fileInfolist.size() <= index )
            return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 300+1*500; // 头计300,一个item计500

        //Response
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //CmdType
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, FUNC_CONTROL_CMDTYPE_RECORDINFO ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //SN
        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //DeviceID
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.resAddr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //Name
        //if( 0 == doConv( &pjStr, gb28181::TAG_NAME ) )
        //	return false;
        //if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
        //	return false;
        //if( 0 == doConv( &pjStr, TAG_ATTR_CAMERAL ) )
        //	return false;
        //next->content = pjStr;
        //pj_xml_add_node( root, next );

        //SumNum
        if( 0 == doConv( &pjStr, gb28181::TAG_SUMNUM ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( gbtInfo.realFileNum, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //RecordList
        pj_xml_node *subRoot1=0;
        if( 0 == doConv( &pjStr, gb28181::TAG_RECORDLIST ) )
            return false;
        if( !( subRoot1 = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        if( 0 == doConv( &pjStr, "Num" ) )
            return false;
        if( !valueToLexical< int, std::string >( 1, stdStr ) )
            return false;
        pj_str_t pjStr1;
        if( 0 == doConv( &pjStr1, stdStr ) )
            return false;
        pj_xml_attr *temp  = pj_xml_attr_new(m_pool, &pjStr, &pjStr1);
        pj_xml_add_attr(subRoot1, temp);
        pj_xml_add_node( root, subRoot1 );

        //if ( gbtInfo.fileInfolist.size() > 0 )
        //{
        //	std::vector< gb28181::SFileListResponse::SItem >::const_iterator it( gbtInfo.fileInfolist.begin() );
        //	std::vector< gb28181::SFileListResponse::SItem >::const_iterator end( gbtInfo.fileInfolist.end() );
        //	for( ; it != end; ++it )
        //	{
        gb28181::SFileListResponse::SItem item = gbtInfo.fileInfolist[index];
        // item root
        pj_xml_node *itemRoot=0;
        if( 0 == doConv( &pjStr, gb28181::TAG_ITEM ) )
            return false;
        if( !( itemRoot = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        pj_xml_add_node( subRoot1, itemRoot );

        //DeviceID
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.resAddr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( itemRoot, next );

        // name
        if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_NAME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::TAG_ATTR_CAMERAL ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( itemRoot, next );

        // filepath
        //if( 0 == doConv( &pjStr, gb28181::TAG_FILEPATH ) )
        //	return false;
        //if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
        //	return false;
        //if( 0 == doConv( &pjStr, gbtInfo.filepath ) )
        //	return false;
        //next->content = pjStr;
        //pj_xml_add_node( itemRoot, next );

        // address
        //if( 0 == doConv( &pjStr, gb28181::TAG_ADDRESS ) )
        //	return false;
        //if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
        //	return false;
        //if( 0 == doConv( &pjStr, gbtInfo.address ) )
        //	return false;
        //next->content = pjStr;
        //pj_xml_add_node( itemRoot, next );

        // startTime
        if( 0 == doConv( &pjStr, gb28181::TAG_STARTTIME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, item.itemBeginTime ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( itemRoot, next );

        // endtime
        if( 0 == doConv( &pjStr, gb28181::TAG_ENDTIME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, item.itemEndTime ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( itemRoot, next );

        //Secrecy
        if( 0 == doConv( &pjStr, gb28181::TAG_SECRECY ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.secrecy ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( itemRoot, next );

        //type
        if( 0 == doConv( &pjStr, gb28181::TAG_FILETYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "time" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( itemRoot, next );

        //recordId
        //if( 0 == doConv( &pjStr, gb28181::TAG_RECORDERID ) )
        //	return false;
        //if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
        //	return false;
        //if( 0 == doConv( &pjStr, gbtInfo.recordId ) )
        //	return false;
        //next->content = pjStr;
        //pj_xml_add_node( itemRoot, next );
        //	}
        //}

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0 )
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        return true;
    }
    bool CGb28181Xml::getString( const SHistoryMediaResponse &gbtInfo, std::string &outString )
    {
        if( !m_isOk ) return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 600;

        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        pj_xml_node *responseroot=0;
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE_QUERY ) )
            return false;
        if( !( responseroot = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        pj_xml_add_node( root, responseroot );

        if( 0 == doConv( &pjStr, gb28181::TAG_VARIABLE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_VARIABLE_VODBYRTSP ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( responseroot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_RESULT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        strFromBoolResult( gbtInfo.result, stdStr );
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( responseroot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_BITRATE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( gbtInfo.bitrate, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( responseroot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_PLAYURL ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.playUrl ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( responseroot, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;



        return true;
    }
    bool CGb28181Xml::getString( const SPtzCommandResponse &gbtInfo, std::string &outString )
    {
        if( !m_isOk ) return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 300;

        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        pj_xml_node *responseroot=0;
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE_CONTROL ) )
            return false;
        if( !( responseroot = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        pj_xml_add_node( root, responseroot );

        if( 0 == doConv( &pjStr, gb28181::TAG_VARIABLE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_VARIABLE_PTZCOMMAND ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( responseroot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_RESULT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        strFromBoolResult( gbtInfo.result, stdStr );
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( responseroot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_COMMAND ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !strFromControlCode( gbtInfo.command, gbtInfo.commandParam1, gbtInfo.commandParam2, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( responseroot, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        //std::cout << outString << std::endl;

        return true;
    }

    bool CGb28181Xml::getString( const SPresetListResponse &gbtInfo, std::string &outString )
    {
        if( !m_isOk ) return false;

        if( gbtInfo.presetInfoList.empty() )
            return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 300+gbtInfo.presetInfoList.size()*200; // 头计300,一个item计200

        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        pj_xml_node *responseroot=0;
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE_QUERY ) )
            return false;
        if( !( responseroot = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        pj_xml_add_node( root, responseroot );

        if( 0 == doConv( &pjStr, gb28181::TAG_VARIABLE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_VARIABLE_PRESETLIST ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( responseroot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_RESULT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        strFromBoolResult( gbtInfo.result, stdStr );
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( responseroot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_REALNUM ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( gbtInfo.realNum, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( responseroot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_FROMINDEX ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( gbtInfo.fromIndex, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( responseroot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_TOINDEX ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( gbtInfo.toIndex, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( responseroot, next );

        pj_xml_node *infolistroot=0;
        if( 0 == doConv( &pjStr, gb28181::TAG_PRESETINFOLIST ) )
            return false;
        if( !( infolistroot = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        pj_xml_add_node( responseroot, infolistroot );

        std::vector< gb28181::SPresetListResponse::SItem >::const_iterator it( gbtInfo.presetInfoList.begin() );
        std::vector< gb28181::SPresetListResponse::SItem >::const_iterator end( gbtInfo.presetInfoList.end() );
        for( ; it != end; ++it )
        {
            // item root
            pj_xml_node *itemRoot=0;
            if( 0 == doConv( &pjStr, gb28181::TAG_ITEM ) )
                return false;
            if( !( itemRoot = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            pj_xml_add_node( infolistroot, itemRoot );

            if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_VALUE ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( !valueToLexical< int, std::string >( (*it).itemValue, stdStr ) )
                return false;
            if( 0 == doConv( &pjStr, stdStr ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( itemRoot, next );

            if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_DESCRIPTION ) )
                return false;
            if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;
            if( 0 == doConv( &pjStr, (*it).itemDescription ) )
                return false;
            next->content = pjStr;
            pj_xml_add_node( itemRoot, next );
        }

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        return true;


    }

    bool CGb28181Xml::getString( const SDeviceInfoResponse &gbtInfo, std::vector<std::string> &outStringVec )
    {
        outStringVec.clear();

        std::string outStr = "";
        bool ret = getString(gbtInfo, outStr);
        if( !ret )
            return false;
        outStringVec.push_back(outStr);

        return true;
    }

    bool CGb28181Xml::getString( const SDeviceInfoResponse &gbtInfo, std::string &outString )
    {
        if( !m_isOk ) return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 400;

        //response
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //cmdType
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICEINFO ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //SN
        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //DeviceID
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.resAddr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //Result
        if( 0 == doConv( &pjStr, gb28181::TAG_RESULT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "OK" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //DeviceType
// 	if( 0 == doConv( &pjStr, gb28181::TAG_DEVICETYPE ) )
// 		return false;
// 	if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
// 		return false;
// 	if( 0 == doConv( &pjStr, gbtInfo.deviceType ) )
// 		return false;
// 	next->content = pjStr;
// 	pj_xml_add_node( root, next );

        //Manufacturer
        if( 0 == doConv( &pjStr, gb28181::TAG_MANUFACTURER ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.manufacturer ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //Model
        if( 0 == doConv( &pjStr, gb28181::TAG_MODEL ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.model ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //Firmware
// 	if( 0 == doConv( &pjStr, gb28181::TAG_FIRMWARE ) )
// 		return false;
// 	if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
// 		return false;
// 	if( 0 == doConv( &pjStr, gbtInfo.firmware ) )
// 		return false;
// 	next->content = pjStr;
// 	pj_xml_add_node( root, next );

        //MaxCamera
        if( 0 == doConv( &pjStr, "Channel" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, boost::lexical_cast< std::string >( gbtInfo.maxCamera ) ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //MaxAlarm
// 	if( 0 == doConv( &pjStr, gb28181::TAG_MAXALARM ) )
// 		return false;
// 	if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
// 		return false;
// 	if( 0 == doConv( &pjStr, "1" ) )
// 		return false;
// 	next->content = pjStr;
// 	pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;


        return true;
    }

    bool CGb28181Xml::getString( const SDeviceStatusResponse &gbtInfo, std::vector<std::string> &outStringVec )
    {
        outStringVec.clear();

        std::string outStr = "";
        bool ret = getString(gbtInfo, outStr);
        if( !ret )
            return false;
        outStringVec.push_back(outStr);

        return true;
    }

    bool CGb28181Xml::getString( const SDeviceStatusResponse &gbtInfo, std::string &outString )
    {
        if( !m_isOk ) return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 400;

        //response
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //cmdType
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICESTATUS ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //SN
        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //DeviceID
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.resAddr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //Result
        if( 0 == doConv( &pjStr, gb28181::TAG_RESULT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "OK" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //Online
        if( 0 == doConv( &pjStr, gb28181::TAG_ONLINE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.isOnline ? "ONLINE" : "OFFLINE" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //Status
        if( 0 == doConv( &pjStr, gb28181::TAG_STATUS ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.isNormal ? "OK" : "FAILE" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //Encoder
        if( 0 == doConv( &pjStr, gb28181::TAG_ENCODE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.isEncoder ? "ON" : "OFF" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //Record
// 	if( 0 == doConv( &pjStr, gb28181::TAG_RECORD ) )
// 		return false;
// 	if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
// 		return false;
// 	if( 0 == doConv( &pjStr, gbtInfo.isRecord ? "ON" : "OFF" ) )
// 		return false;
// 	next->content = pjStr;
// 	pj_xml_add_node( root, next );

        //DeviceTime
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICETIME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        boost::posix_time::ptime timer = boost::posix_time::second_clock::local_time();
        tm tm_localTime = boost::posix_time::to_tm( timer ); // 本地时间
        char szTmp[128];
        memset( szTmp, 0, 128 );
        sprintf( szTmp, "%d-%d-%dT%d:%d:%d", tm_localTime.tm_year + 1900, tm_localTime.tm_mon, tm_localTime.tm_mday, tm_localTime.tm_hour, tm_localTime.tm_min, tm_localTime.tm_sec );
        if( 0 == doConv( &pjStr, szTmp ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //告警暂时不处理

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;


        return true;
    }

    bool CGb28181Xml::getString( const SDeviceAlarmStatusResponse &gbtInfo, std::string &outString )
    {
        if( !m_isOk ) return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 400;

        //response
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //cmdType
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICESTATUS ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //SN
        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //DeviceID
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.resAddr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //Result
        if( 0 == doConv( &pjStr, gb28181::TAG_RESULT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "OK" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //Online
        if( 0 == doConv( &pjStr, gb28181::TAG_ONLINE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.isOnline ? "ONLINE" : "OFFLINE" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //Status
        if( 0 == doConv( &pjStr, gb28181::TAG_STATUS ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.isNormal ? "OK" : "FAILE" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );


        //DeviceTime
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICETIME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        boost::posix_time::ptime timer = boost::posix_time::second_clock::local_time();
        tm tm_localTime = boost::posix_time::to_tm( timer ); // 本地时间
        char szTmp[128];
        memset( szTmp, 0, 128 );
        sprintf( szTmp, "%d-%d-%dT%d:%d:%d", tm_localTime.tm_year + 1900, tm_localTime.tm_mon, tm_localTime.tm_mday, tm_localTime.tm_hour, tm_localTime.tm_min, tm_localTime.tm_sec );
        if( 0 == doConv( &pjStr, szTmp ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //告警暂时不处理
        if( 0 == doConv( &pjStr, "Alarmstatus" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "Num" ) )
            return false;
        pj_str_t pjStr1;
        if( 0 == doConv( &pjStr1, "1" ) )
            return false;
        pj_xml_attr *temp  = pj_xml_attr_new(m_pool, &pjStr, &pjStr1);
        pj_xml_add_attr(next, temp);
        pj_xml_add_node( root, next );

        pj_xml_node *alarmItem=0;
        if( 0 == doConv( &pjStr, "Item" ) )
            return false;
        if( !( alarmItem = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        pj_xml_add_node( next, alarmItem );

        pj_xml_node *alarmItemVar = 0;
        if( 0 == doConv( &pjStr, "DeviceID" ) )
            return false;
        if( !( alarmItemVar = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.resAddr ) )
            return false;
        alarmItemVar->content = pjStr;
        pj_xml_add_node( alarmItem, alarmItemVar );
        if( 0 == doConv( &pjStr, "Status" ) )
            return false;
        if( !( alarmItemVar = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.isDuty ? "ONDUTY" : "OFFDUTY" ) )
            return false;
        alarmItemVar->content = pjStr;
        pj_xml_add_node( alarmItem, alarmItemVar );


        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;


        return true;
    }

    bool CGb28181Xml::getString( const SRecordContronlResponse &gbtInfo, std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICECONTROL ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_SN ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.devid) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_RESULT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        const std::string tmp = (gbtInfo.result) ? XML_RESULT_OK : XML_RESULT_ERROR;
        if( 0 == doConv( &pjStr, tmp ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;

        outString.clear();
        outString.assign( output, output_len );
        outString = gb28181::XML_PROLOG_HEAD + outString;
        return true;
    }

    bool CGb28181Xml::getString( const SGuardContronlResponse &gbtInfo, std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICECONTROL ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_SN ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.devid ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_RESULT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        const std::string tmp = (gbtInfo.result) ? XML_RESULT_OK : XML_RESULT_ERROR;
        if( 0 == doConv( &pjStr, tmp ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;

        outString.clear();
        outString.assign( output, output_len );
        outString = gb28181::XML_PROLOG_HEAD + outString;
        return true;
    }

    bool CGb28181Xml::getString( const SAlarmResetResponse &gbtInfo, std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICECONTROL ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_SN ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.devid ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_RESULT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        const std::string tmp = (gbtInfo.result) ? XML_RESULT_OK : XML_RESULT_ERROR;
        if( 0 == doConv( &pjStr, tmp ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;

        outString.clear();
        outString.assign( output, output_len );
        outString = gb28181::XML_PROLOG_HEAD + outString;
        return true;
    }

    bool CGb28181Xml::getKeepAliveString( const std::string &deviceId, std::string &outString )
    {
        if( !m_isOk ) return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 400;

        //Notify
        if( 0 == doConv( &pjStr, gb28181::TAG_NOTIFY ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //cmdType
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, FUNC_CONTROL_CMDTYPE_KEEPALIVE ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //SN
        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "43" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //DeviceID
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, deviceId ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );
        //Status
        if( 0 == doConv( &pjStr, gb28181::TAG_STATUS ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "OK" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;


        return true;
    }

    bool CGb28181Xml::makeString( const SCatalogResponse&gbtInfo, std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        if ( !gbtInfo.result )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }

        //cmdType
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, FUNC_CONTROL_CMDTYPE_CATALOG ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //SN
        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //DeviceID
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, outString ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );
        //Status
        if( 0 == doConv( &pjStr, gb28181::TAG_RESULT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "OK" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        return true;

    }

    bool CGb28181Xml::makeString( const SRealMedia &gbtInfo, std::string &outString )
    {

        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_ACTION ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }
        if( 0 == doConv( &pjStr, gb28181::TAG_VARIABLE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_VARIABLE_REALMEDIA ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_PRIVILEGE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.privilege ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_FORMAT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !fromatType_sToStr( gbtInfo.supportFormatTypes, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_VIDEO ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !videoType_sToStr( gbtInfo.supportVideoTypes, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_AUDIO ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !audioType_sToStr( gbtInfo.supportAudioTypes, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_MAXBITRATE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( gbtInfo.maxBitrate, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_SOCKET ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !strFromSocketUrl( gbtInfo.socketType, gbtInfo.sockAddr, gbtInfo.sockPort, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;
        return true;

    }

    bool CGb28181Xml::makeString( const SHistoryMedia &gbtInfo, std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_ACTION ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }

        pj_xml_node *requestRoot=0;
        if( 0 == doConv( &pjStr, gb28181::TAG_ACTION_QUERY ) )
            return false;
        if( !( requestRoot = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        pj_xml_add_node( root, requestRoot );

        if( 0 == doConv( &pjStr, gb28181::TAG_VARIABLE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_VARIABLE_VODBYRTSP ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( requestRoot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_PRIVILEGE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.privilege ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( requestRoot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_FILETYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( gbtInfo.fileType, stdStr ) )
        {
            return false;
        }
        if( 0 == doConv( &pjStr,stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( requestRoot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_NAME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr,gbtInfo.name ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( requestRoot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_STARTTIME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr,gbtInfo.beginTime ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( requestRoot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_ENDTIME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr,gbtInfo.endTime ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( requestRoot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_MAXBITRATE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( gbtInfo.maxBitrate, stdStr ) )
        {
            return false;
        }
        if( 0 == doConv( &pjStr,stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( requestRoot, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        return true;
    }

    bool CGb28181Xml::makeString( const SPtzCommand &ptzCmd, std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICECONTROL ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_SN ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, ptzCmd.sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, outString ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        std::string tmpstr;
        if ( !ptzCommandTypeToString( ptzCmd.command, ptzCmd.commandParam1, ptzCmd.commandParam2, tmpstr) )
        {
            return false;
        }
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_PTZCMD ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, tmpstr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;

        outString.clear();
        outString.assign( output, output_len );
        outString = gb28181::XML_PROLOG_HEAD + outString;
        return true;
    }

    bool CGb28181Xml::makeString( const SFileList &gbtInfo, std::string &outString, std::string &sn )
    {
        if ( !m_isOk )
        {
            return false;
        }

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 500;

        if( 0 == doConv( &pjStr, gb28181::TAG_ACTION_QUERY ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;


        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_RECORDINFO ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        sn = boost::lexical_cast< std::string >( rand() );
        if( 0 == doConv( &pjStr, sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, outString ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );


        if( 0 == doConv( &pjStr, gb28181::TAG_STARTTIME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !gbtInfo.beginTime.empty() )
        {
            if( 0 == doConv( &pjStr, gbtInfo.beginTime ) )
                return false;
            next->content = pjStr;
        }
        pj_xml_add_node( root, next );


        if( 0 == doConv( &pjStr, gb28181::TAG_ENDTIME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !gbtInfo.endTime.empty() )
        {
            if( 0 == doConv( &pjStr, gbtInfo.endTime ) )
                return false;
            next->content = pjStr;
        }

        pj_xml_add_node( root, next );


        if( 0 == doConv( &pjStr, gb28181::TAG_FILEPATH ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.filepath ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_ADDRESS ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.address ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_SECRECY ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.secrecy ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_FILETYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "time" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_RECORDERID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.recordId ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        return true;
    }

    bool CGb28181Xml::makeString( const SKeepalive &gbtInfo,std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_ACTION ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }
        pj_xml_node *requestRoot=0;
        if( 0 == doConv( &pjStr, gb28181::TAG_ACTION_NOTIFY ) )
            return false;
        if( !( requestRoot = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        pj_xml_add_node( root, requestRoot );

        if( 0 == doConv( &pjStr, gb28181::TAG_VARIABLE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_VARIABLE_KEEPALIVE ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( requestRoot, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;
        return true;

    }

    bool CGb28181Xml::makeString( const SPresetList &gbtInfo, std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_ACTION ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }
        pj_xml_node *requestRoot=0;
        if( 0 == doConv( &pjStr, gb28181::TAG_ACTION_QUERY ) )
            return false;
        if( !( requestRoot = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        pj_xml_add_node( root, requestRoot );

        if( 0 == doConv( &pjStr, gb28181::TAG_VARIABLE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_VARIABLE_PRESETLIST ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( requestRoot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_PRIVILEGE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.privilege ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( requestRoot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_FROMINDEX ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( gbtInfo.fromIndex, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( requestRoot, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_TOINDEX ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( gbtInfo.toIndex, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( requestRoot, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;
        return true;

    }

    bool CGb28181Xml::makeString( const SDeviceReboot &gbtInfo, std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICECONTROL ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_SN ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, outString ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_TELEBOOT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICEREBOOT ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;

        outString.clear();
        outString.assign( output, output_len );
        outString = gb28181::XML_PROLOG_HEAD + outString;
        return true;
    }

    bool CGb28181Xml::makeString( const SRecordContronl &gbtInfo, std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICECONTROL ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_SN ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, outString ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_RECORDCMD ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        const std::string tmp = (gbtInfo.flag)? TAG_CONTROL_RECORD : TAG_CONTROL_STOPRECORD;
        if( 0 == doConv( &pjStr, tmp ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;

        outString.clear();
        outString.assign( output, output_len );
        outString = gb28181::XML_PROLOG_HEAD + outString;
        return true;
    }

    bool CGb28181Xml::makeString( const SGuardContronl &gbtInfo, std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICECONTROL ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_SN ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, outString ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_GUARDCMD ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        const std::string tmp = (gbtInfo.flag)? TAG_CONTROL_SETGUARD : TAG_CONTROL_RESETGUARD;
        if( 0 == doConv( &pjStr, tmp ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;

        outString.clear();
        outString.assign( output, output_len );
        outString = gb28181::XML_PROLOG_HEAD + outString;
        return true;
    }

    bool CGb28181Xml::makeString( const SAlarmReset&gbtInfo, std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_ACTION_CONTROL ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICECONTROL ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_SN ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, outString ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_ALARMCMD ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, TAG_CONTROL_RESETALARM ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_ALARMINFO ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_ALARMMETHOD ) )
            return false;
        pj_xml_node * tmp = pj_xml_node_new( m_pool, &pjStr ) ;
        if( !tmp )   return false;
        if( 0 == doConv( &pjStr, std::string("2") ) )
            return false;
        tmp->content = pjStr;

        pj_xml_add_node( next, tmp );
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;

        outString.clear();
        outString.assign( output, output_len );
        outString = gb28181::XML_PROLOG_HEAD + outString;
        return true;

    }

    bool CGb28181Xml::makeString( const SDeviceCatalog &gbtInfo, std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_ACTION_QUERY ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_CATALOG ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_SN ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, outString ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;
        outString.clear();
        outString.assign( output, output_len );
        outString = gb28181::XML_PROLOG_HEAD + outString;
        return true;
    }

    bool CGb28181Xml::makeString( const SDeviceInfo &gbtInfo, std::string &outString, std::string &sn )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_ACTION_QUERY ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICEINFO ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_SN ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;
        sn = gbtInfo.sn;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, outString ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;
        outString.clear();
        outString.assign( output, output_len );
        outString = gb28181::XML_PROLOG_HEAD + outString;
        return true;
    }

    bool CGb28181Xml::makeString( const SDeviceStatus &gbtInfo, std::string &outString, std::string &sn )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_ACTION_QUERY ) )
            return false;
        if ( ! ( root = pj_xml_node_new( m_pool,&pjStr ) ))
        {
            return false;
        }

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::FUNC_CONTROL_CMDTYPE_DEVICESTATUS ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_SN ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gbtInfo.sn ) )
            return false;

        sn = gbtInfo.sn;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, outString ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0)
            return false;
        outString.clear();
        outString.assign( output, output_len );
        outString = gb28181::XML_PROLOG_HEAD + outString;
        return true;
    }


    bool CGb28181Xml::getDeviceConfigQuery( const std::string &sn, const std::string &sid, const SDeviceConfigQueryResult &result, std::string &outString )
    {
        if( !m_isOk ) return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 600;

        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //命令类型
        pj_xml_node *flower=0;
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, FUNC_QUERY_CMDTYPE_CONFIGDOWNLOAD ) )
            return false;
        flower->content = pjStr;
        pj_xml_add_node( root, flower );

        //sn
        if( 0 == doConv( &pjStr, gb28181::TAG_SN ) )
            return false;
        if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, sn.c_str() ) )
            return false;
        flower->content = pjStr;
        pj_xml_add_node( root, flower );

        //deviceid
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, sid.c_str() ) )
            return false;
        flower->content = pjStr;
        pj_xml_add_node( root, flower );

        //查询结果标志（必选）
        if( 0 == doConv( &pjStr, gb28181::TAG_RESULT ) )
            return false;
        if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( result.isOk )
        {
            if( 0 == doConv( &pjStr, "OK" ) )
                return false;
            flower->content = pjStr;
        }
        else
        {
            if( 0 == doConv( &pjStr, "ERROR" ) )
                return false;
            flower->content = pjStr;
        }
        pj_xml_add_node( root, flower );

        //基本参数配置（可选）
        if( result.basicParam != 0 )
        {
            pj_xml_node *basicParamXml = 0;
            if( 0 == doConv( &pjStr, gb28181::TAG_BASICPARAM ) )
                return false;
            if( !( basicParamXml = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;

            pj_xml_add_node( root, basicParamXml );

            { //基本配置的子节点
                //设备名称（必选）
                if( 0 == doConv( &pjStr, gb28181::TAG_NAME ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.basicParam->name.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( basicParamXml, flower );

                //设备ID（必选）
                if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.basicParam->deviceid.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( basicParamXml, flower );

                //SIP服务器ID（必选）
                if( 0 == doConv( &pjStr, gb28181::TAG_SIPSERVERID ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.basicParam->sipserverid.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( basicParamXml, flower );

                //SIP服务器IP（必选）
                if( 0 == doConv( &pjStr, gb28181::TAG_SIPSERVERIP ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.basicParam->sipserverip.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( basicParamXml, flower );

                //SIP服务器端口（必选）
                if( 0 == doConv( &pjStr, gb28181::TAG_SIPSERVERPORT ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.basicParam->sipserverport.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( basicParamXml, flower );

                //SIP服务器域名（必选）
                if( 0 == doConv( &pjStr, gb28181::TAG_DOMAINNAME ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.basicParam->domainName.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( basicParamXml, flower );

                //注册过期时间（必选）
                if( 0 == doConv( &pjStr, gb28181::TAG_EXPIRATION ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.basicParam->expiration.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( basicParamXml, flower );

                //注册口令（必选）
                if( 0 == doConv( &pjStr, gb28181::TAG_ITEM_PASSWORD ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.basicParam->password.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( basicParamXml, flower );

                //心跳间隔时间（必选）
                if( 0 == doConv( &pjStr, gb28181::TAG_HEARTBEATINTERVAL ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.basicParam->heartBeatInterval.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( basicParamXml, flower );

                //心跳超时次数（必选）
                if( 0 == doConv( &pjStr, gb28181::TAG_HEARTBEATCOUNT ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.basicParam->heartBeatCount.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( basicParamXml, flower );
            }
        }

        //视频参数配置范围（可选），各可选参数以“/”分隔
        if( result.videoParamOpt != 0 )
        {
            pj_xml_node *videoParamOptXml = 0;
            if( 0 == doConv( &pjStr, "VideoParamOpt" ) )
                return false;
            if( !( videoParamOptXml = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;

            pj_xml_add_node( root, videoParamOptXml );

            { //子节点
                //视频编码格式可选范围（必选）
                if( 0 == doConv( &pjStr, "VideoFormatOpt" ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.videoParamOpt->videoFormatOpt.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( videoParamOptXml, flower );

                //分辨率可选范围（必选）
                if( 0 == doConv( &pjStr, "ResolutionOpt" ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.videoParamOpt->resolutionOpt.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( videoParamOptXml, flower );

                //帧率可选范围（必选）
                if( 0 == doConv( &pjStr, "FrameRateOpt" ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.videoParamOpt->frameRateOpt.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( videoParamOptXml, flower );

                //码率类型范围（必选）
                if( 0 == doConv( &pjStr, "BitRateTypeOpt" ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.videoParamOpt->bitRateTypeOpt.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( videoParamOptXml, flower );

                //视频码率范围（必选）
                if( 0 == doConv( &pjStr, "VideoBitRateOpt" ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.videoParamOpt->videoBitRateOpt.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( videoParamOptXml, flower );

                //视频下载速度可选范围（必选）
                if( 0 == doConv( &pjStr, "DownloadSpeedOpt" ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.videoParamOpt->downloadSpeedOpt.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( videoParamOptXml, flower );
            }
        }

        //视频参数当前配置（可选）
        if( !result.videoParamConfigs.empty() )
        {
            pj_xml_node *videoParamConfigXml = 0;
            if( 0 == doConv( &pjStr, "VideoParamConfig" ) )
                return false;
            if( !( videoParamConfigXml = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;

            pj_xml_add_node( root, videoParamConfigXml );
            pj_str_t pjattrname;
            if( 0 == doConv( &pjattrname, "Num" ) )
                return false;

            pj_str_t pjattrvalue;
            std::string strval = boost::lexical_cast< std::string >( result.videoParamConfigs.size() );
            if( 0 == doConv( &pjattrvalue, strval.c_str() ) )
                return false;


            pj_xml_attr *num = pj_xml_attr_new( m_pool, &pjattrname, &pjattrvalue );
            pj_xml_add_attr( videoParamConfigXml, num );

            { //子节点
                for( std::vector< SVideoParamAttribute >::const_iterator iter = result.videoParamConfigs.begin(); iter != result.videoParamConfigs.end(); ++iter )
                {
                    //视频编码格式可选范围（必选）
                    if( 0 == doConv( &pjStr, TAG_ITEM ) )
                        return false;
                    if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                        return false;

                    pj_xml_add_node( videoParamConfigXml, flower );

                    {
                        pj_xml_node *next = 0;
                        //流名称(必选) 如第一个流 Stream1,第二个流 Stream2
                        if( 0 == doConv( &pjStr, TAG_STREAMNAME ) )
                            return false;
                        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                            return false;

                        if( 0 == doConv( &pjStr, ( *iter ).streamName.c_str() ) )
                            return false;
                        next->content = pjStr;
                        pj_xml_add_node( flower, next );

                        //视频编码格式当前配置值(必选)
                        if( 0 == doConv( &pjStr, TAG_VIDEOFORMAT ) )
                            return false;
                        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                            return false;

                        if( 0 == doConv( &pjStr, ( *iter ).videoFormat.c_str() ) )
                            return false;
                        next->content = pjStr;
                        pj_xml_add_node( flower, next );

                        //分辨率当前配置值(必选)
                        if( 0 == doConv( &pjStr, TAG_RESOLUTION ) )
                            return false;
                        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                            return false;

                        if( 0 == doConv( &pjStr, ( *iter ).resolution.c_str() ) )
                            return false;
                        next->content = pjStr;
                        pj_xml_add_node( flower, next );

                        //帧率当前配置值(必选)
                        if( 0 == doConv( &pjStr, TAG_FRAMERATE ) )
                            return false;
                        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                            return false;

                        if( 0 == doConv( &pjStr, ( *iter ).frameRate.c_str() ) )
                            return false;
                        next->content = pjStr;
                        pj_xml_add_node( flower, next );

                        //码率类型配置值(必选)
                        if( 0 == doConv( &pjStr, TAG_BITRATETYPE ) )
                            return false;
                        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                            return false;

                        if( 0 == doConv( &pjStr, ( *iter ).bitRateType.c_str() ) )
                            return false;
                        next->content = pjStr;
                        pj_xml_add_node( flower, next );

                        //视频码率配置值(必选)
                        if( 0 == doConv( &pjStr, TAG_VIDEOBITRATE ) )
                            return false;
                        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                            return false;

                        if( 0 == doConv( &pjStr, ( *iter ).videoBitRate.c_str() ) )
                            return false;
                        next->content = pjStr;
                        pj_xml_add_node( flower, next );
                    }
                }
            }
        }

        //音频参数配置范围（可选），各可选参数以“/”分隔
        if( result.audioParamOpt != 0 )
        {
            pj_xml_node *audioParamOptXml = 0;
            if( 0 == doConv( &pjStr, "AudioParamOpt" ) )
                return false;
            if( !( audioParamOptXml = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;

            pj_xml_add_node( root, audioParamOptXml );

            { //子节点
                //音频编码格式可选范围（必选）
                if( 0 == doConv( &pjStr, "AudioFormatOpt" ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.audioParamOpt->audioFormatOpt.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( audioParamOptXml, flower );

                //音频码率可选范围（必选）
                if( 0 == doConv( &pjStr, "AudioBitRateOpt" ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.audioParamOpt->audioBitRateOpt.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( audioParamOptXml, flower );

                //采样率可选范围（必选）
                if( 0 == doConv( &pjStr, "SamplingRateOpt" ) )
                    return false;
                if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                    return false;

                if( 0 == doConv( &pjStr, result.audioParamOpt->samplingRateOpt.c_str() ) )
                    return false;
                flower->content = pjStr;
                pj_xml_add_node( audioParamOptXml, flower );
            }
        }

        //音频参数当前配置（可选）
        if( !result.audioParamConfigs.empty() )
        {
            pj_xml_node *audioParamConfigXml = 0;
            if( 0 == doConv( &pjStr, "AudioParamConfig" ) )
                return false;
            if( !( audioParamConfigXml = pj_xml_node_new( m_pool, &pjStr ) ) )
                return false;

            pj_xml_add_node( root, audioParamConfigXml );
            pj_str_t pjattrname;
            if( 0 == doConv( &pjattrname, "Num" ) )
                return false;

            pj_str_t pjattrvalue;
            std::string strval = boost::lexical_cast< std::string >( result.audioParamConfigs.size() );
            if( 0 == doConv( &pjattrvalue, strval.c_str() ) )
                return false;


            pj_xml_attr *num = pj_xml_attr_new( m_pool, &pjattrname, &pjattrvalue );
            pj_xml_add_attr( audioParamConfigXml, num );

            { //子节点
                for( std::vector< SAideoParamAttribute >::const_iterator iter = result.audioParamConfigs.begin(); iter != result.audioParamConfigs.end(); ++iter )
                {
                    //视频编码格式可选范围（必选）
                    if( 0 == doConv( &pjStr, TAG_ITEM ) )
                        return false;
                    if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
                        return false;

                    pj_xml_add_node( audioParamConfigXml, flower );

                    {
                        pj_xml_node *next = 0;
                        //流名称(必选) 如第一个流 Stream1,第二个流 Stream2
                        if( 0 == doConv( &pjStr, TAG_STREAMNAME ) )
                            return false;
                        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                            return false;

                        if( 0 == doConv( &pjStr, ( *iter ).streamName.c_str() ) )
                            return false;
                        next->content = pjStr;
                        pj_xml_add_node( flower, next );

                        //音频编码格式当前配置值(必选)
                        if( 0 == doConv( &pjStr, TAG_AUDIOFORMAT ) )
                            return false;
                        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                            return false;

                        if( 0 == doConv( &pjStr, ( *iter ).audioFormat.c_str() ) )
                            return false;
                        next->content = pjStr;
                        pj_xml_add_node( flower, next );

                        //音频码率当前配置值(必选)
                        if( 0 == doConv( &pjStr, TAG_AUDIOBITRATE ) )
                            return false;
                        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                            return false;

                        if( 0 == doConv( &pjStr, ( *iter ).audioBitRate.c_str() ) )
                            return false;
                        next->content = pjStr;
                        pj_xml_add_node( flower, next );

                        //采样率当前配置值(必选)
                        if( 0 == doConv( &pjStr, TAG_SAMPLINGRATE ) )
                            return false;
                        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
                            return false;

                        if( 0 == doConv( &pjStr, ( *iter ).samplingRate.c_str() ) )
                            return false;
                        next->content = pjStr;
                        pj_xml_add_node( flower, next );
                    }
                }
            }
        }

// 	if( 0 == doConv( &pjStr, gb28181::FUNC_VARIABLE_VODBYRTSP ) )
// 		return false;
// 	next->content = pjStr;
// 	pj_xml_add_node( responseroot, next );
//
// 	if( 0 == doConv( &pjStr, gb28181::TAG_RESULT ) )
// 		return false;
// 	if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
// 		return false;
// 	strFromBoolResult( gbtInfo.result, stdStr );
// 	if( 0 == doConv( &pjStr, stdStr ) )
// 		return false;
// 	next->content = pjStr;
// 	pj_xml_add_node( responseroot, next );
//
// 	if( 0 == doConv( &pjStr, gb28181::TAG_BITRATE ) )
// 		return false;
// 	if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
// 		return false;
// 	if( !valueToLexical< int, std::string >( gbtInfo.bitrate, stdStr ) )
// 		return false;
// 	if( 0 == doConv( &pjStr, stdStr ) )
// 		return false;
// 	next->content = pjStr;
// 	pj_xml_add_node( responseroot, next );
//
// 	if( 0 == doConv( &pjStr, gb28181::TAG_PLAYURL ) )
// 		return false;
// 	if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
// 		return false;
// 	if( 0 == doConv( &pjStr, gbtInfo.playUrl ) )
// 		return false;
// 	next->content = pjStr;
// 	pj_xml_add_node( responseroot, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        return true;
    }

    bool CGb28181Xml::getAlarmNotify( const std::string &sn, const std::string &sid, const SAlarmInfo &info, std::string &outString )
    {
        if( !m_isOk ) return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 600;

        if( 0 == doConv( &pjStr, gb28181::TAG_NOTIFY ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //命令类型
        pj_xml_node *flower=0;
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, TAG_CONTROL_ALARM ) )
            return false;
        flower->content = pjStr;
        pj_xml_add_node( root, flower );

        //sn
        if( 0 == doConv( &pjStr, gb28181::TAG_SN ) )
            return false;
        if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, sn.c_str() ) )
            return false;
        flower->content = pjStr;
        pj_xml_add_node( root, flower );

        //deviceid
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, sid.c_str() ) )
            return false;
        flower->content = pjStr;
        pj_xml_add_node( root, flower );

        //级别(必选)，l为～级警情，2为二级警情，3为三级警情，4为四级警情
        if( 0 == doConv( &pjStr, "AlarmPriority" ) )
            return false;
        if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, info.alarmPriority.c_str() ) )
            return false;
        flower->content = pjStr;
        pj_xml_add_node( root, flower );

        //警方式(必选)，取值1为电话报警，2为设备报警，3为短信报警，4为GPS报警，5为视频报警，6为设备故障报警，7其他报警
        if( 0 == doConv( &pjStr, "AlarmMethod" ) )
            return false;
        if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, info.alarmMethod.c_str() ) )
            return false;
        flower->content = pjStr;
        pj_xml_add_node( root, flower );

        //报警时间(必选)
        if( 0 == doConv( &pjStr, "AlarmTime" ) )
            return false;
        if( !( flower = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, info.alarmTime.c_str() ) )
            return false;
        flower->content = pjStr;
        pj_xml_add_node( root, flower );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        return true;
    }

    bool CGb28181Xml::getSubscribeCatalogString(const uint32_t startTime, const uint32_t endTime, std::string &outString )
    {
        if( !m_isOk ) return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 600;

        if( 0 == doConv( &pjStr, gb28181::TAG_QUERY ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //命令类型
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, FUNC_CONTROL_CMDTYPE_CATALOG ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //sn
        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, boost::lexical_cast< std::string >( rand() ) ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //deviceid
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, outString ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_STARTTIME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, utcTime_t2UtcString(startTime) ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_ENDTIME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, utcTime_t2UtcString(endTime) ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        return true;
    }

    bool CGb28181Xml::getSubscribeAlarmString(const SAlarmSubscribeParam &param,std::string &outString)
    {
        if( !m_isOk ) return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 600;

        if( 0 == doConv( &pjStr, gb28181::TAG_QUERY ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //命令类型
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, TAG_CONTROL_ALARM ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //sn
        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "17431" ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //deviceid
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, outString ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_START_ALARM_PRIORITY ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( param.startAlarmPriority, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_END_ALARM_PRIORITY ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( !valueToLexical< int, std::string >( param.endAlarmPriority, stdStr ) )
            return false;
        if( 0 == doConv( &pjStr, stdStr ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_ALARM_METHOD ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, param.alarmMethod ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_STARTTIME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, utcTime_t2UtcString( param.startTime )) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_ENDTIME ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, utcTime_t2UtcString( param.endTime )) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        return true;
    }

    bool CGb28181Xml::getAudioBroadcastString(const std::string &sourceId, const std::string &targetId, std::string &outString)
    {
        if( !m_isOk ) return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 600;

        if( 0 == doConv( &pjStr, gb28181::TAG_NOTIFY ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //命令类型
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::TAG_BROADCAST ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //sn
        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, "171" ) ) //todo:语音广播SN不确定
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //sourceId
        if( 0 == doConv( &pjStr, "SourceID" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, sourceId ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //targetId
        if( 0 == doConv( &pjStr, "TargetID" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, targetId ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        return true;
    }

    bool CGb28181Xml::getReceiveBroadcastResponseString( const std::string &sn, const std::string deviceId, bool isOk, std::string &outString )
    {
        if( !m_isOk ) return false;

        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root=0;
        pj_xml_node *next=0;
        char *output=0;
        int output_len;
        const size_t MYSIZE= 600;

        if( 0 == doConv( &pjStr, gb28181::TAG_RESPONSE ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        //命令类型
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, gb28181::TAG_BROADCAST ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //sn
        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, sn.c_str() ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //deviceid
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, deviceId.c_str() ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        if( 0 == doConv( &pjStr, gb28181::TAG_RESULT ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;

        if( isOk)
        {
            if( 0 == doConv( &pjStr, "OK" ) )
                return false;
        }
        else
        {
            if( 0 == doConv( &pjStr, "ERROR" ) )
                return false;
        }

        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if ( output_len < 0)
        {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );

        outString = gb28181::XML_PROLOG_HEAD+outString;

        return true;
    }

    bool CGb28181Xml::getReplayEndString( const std::string &sn, const std::string &sid, std::string &outString )
    {
        if ( !m_isOk )
        {
            return false;
        }
        pj_str_t pjStr;
        std::string stdStr;
        pj_xml_node *root = 0;
        pj_xml_node *next = 0;
        char *output = 0;
        int output_len =0;
        const size_t MYSIZE = 300;
        if( 0 == doConv( &pjStr, gb28181::TAG_NOTIFY ) )
            return false;
        if( !( root = pj_xml_node_new( m_pool,&pjStr ) ) )
        {
            return false;
        }

        //命令类型
        if( 0 == doConv( &pjStr, gb28181::TAG_CONTROL_CMDTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, TAG_CONTROL_MEDIASTAUTS ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //sn
        if( 0 == doConv( &pjStr, "SN" ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, sn.c_str() ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //deviceid
        if( 0 == doConv( &pjStr, gb28181::TAG_DEVICEID ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, sid.c_str() ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        //notifyType
        if( 0 == doConv( &pjStr, gb28181::TAG_NOTIFYTYPE ) )
            return false;
        if( !( next = pj_xml_node_new( m_pool, &pjStr ) ) )
            return false;
        if( 0 == doConv( &pjStr, s_notifyType ) )
            return false;
        next->content = pjStr;
        pj_xml_add_node( root, next );

        output = (char*)pj_pool_zalloc( m_pool, MYSIZE );
        output_len = pj_xml_print(root, output, MYSIZE, PJ_FALSE);
        if( output_len < 0) {
            return false;
        }
        outString.clear();
        outString.assign( output, output_len );
        outString = gb28181::XML_PROLOG_HEAD + outString;
        return true;
    }

}
