/**
 * Project FVM
 */

#pragma once
#include "stream_pipe.h"
#include "util/timer/timer_manager.h"
/**
 * @brief: 检测通道
 *          主要控制预置位、轮切、时间方案等
 */
namespace fvm::stream
{
    enum class ChannelDetectState
    {
        NotInited,
        Paused,
        OffSetPaused,
        Detecting
    };

    /**
     * 流暂停原因
     */
    enum class StreamPauseReason
    {
        Init,
        Manual,          //!<  手工暂停
        SetDetectArea,   //!<  设置检测区
        OffsetEvent      //!<  发生偏移事件
    };

    /**
     * 流恢复原因
     */
    enum class StreamRestoreReason
    {
        Init,
        Manual,         // 手工恢复
        OffsetRestore   // 偏移恢复
    };

    typedef boost::signals2::signal<void(StreamRestoreReason)> OnChannelStreamRestored;
    typedef boost::signals2::signal<void(StreamPauseReason)> OnChannelStreamPaused;
    typedef boost::signals2::signal<void()> OnNotifyPtzCheck;
    typedef boost::signals2::signal<void(boost::posix_time::ptime time, bool isPresetChange)> OnPTZPresetChecked;
    class DetectChannel : public StreamPipe {
    public:
        /**
         * 管道恢复检测
         * @param reason
         */
        void restoreDetect(StreamRestoreReason reason);

        /**
         * 管道暂停检测
         * @param reason
         */
        void pauseDetect(StreamPauseReason reason, bool notify = true);

        /**
         * 初始化输入器
         * @param StreamInput
         */
        void initInput(StreamInputPtr input) override;

        /**
         * 获取视频编码基本信息
         */
        CodecInfoPtr getCodecInfo();

        /**
         * 获取通道检测状态
         */
        inline ChannelDetectState getDetectStatus(){return detectState;}

        /**
         * 通过onvif获取输入流ptz的偏移状态，如果onvif没有能力判断偏移则返回None
         */
        PTZPresetStatus getPtzPresetStatus();

        /**
         * 配置偏移自动归位定时器
         * @param init 是否第一次初始化
         */
        void configAutoPtzTimer(bool init= false);

        /**
         * 偏移自动归位
         */
        void autoRestorePtz();

        /**
         * 停止管道
         * @param waitFinish 是否等待停止
         * @param dispose 是否销毁管道的输入输出
         */
        void stopPipe(bool waitFinish, bool dispose) override;
        /**
         * 检查管道的输入通道资源是否发生变化
         */
        bool isInputChanged();

    public: // SIGNALs
        OnChannelStreamRestored onChannelStreamRestored;
        OnChannelStreamPaused onChannelStreamPaused;
        OnStreamCodecInfoRetrieved onStreamCodecInfoRetrieved;
        OnStreamInputLost onStreamInputLost;
        OnNotifyPtzCheck  onNotifyPtzCheck;                     //!< 立即唤醒ptz偏移判断线程
        OnPTZPresetChecked  onPTZPresetChecked;                 //!< ptz偏移判断线程判断结果
    private:
        /*
         * 视频流 恢复（解复用成功 准备通知消息）
         */
        void streamRestored(StreamRestoreReason reason);

        /*
         * 通道ID
         */
        int channelID;

        /*
         * 通道检测状态
         */
        ChannelDetectState detectState = ChannelDetectState::NotInited;

        timer::TimerPtr restorePtzTimer = nullptr;  //!< 偏移自动归位定时器

        int autoPtzTime = 0;  //!< 偏移自动归位时间间隔,单位为分钟
        //void *program;
        //void *rollInfo;
    };

    typedef std::shared_ptr<DetectChannel> DetectChannelPtr;
}