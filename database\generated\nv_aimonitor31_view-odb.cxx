// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#include <odb/pre.hxx>

#include "nv_aimonitor31_view-odb.hxx"

#include <cassert>
#include <cstring>  // std::memcpy


#include <odb/mysql/traits.hxx>
#include <odb/mysql/database.hxx>
#include <odb/mysql/transaction.hxx>
#include <odb/mysql/connection.hxx>
#include <odb/mysql/statement.hxx>
#include <odb/mysql/statement-cache.hxx>
#include <odb/mysql/simple-object-statements.hxx>
#include <odb/mysql/view-statements.hxx>
#include <odb/mysql/container-statements.hxx>
#include <odb/mysql/exceptions.hxx>
#include <odb/mysql/simple-object-result.hxx>
#include <odb/mysql/view-result.hxx>
#include <odb/mysql/enum.hxx>

namespace odb
{
  // MonitorDetectPointData
  //

  bool access::view_traits_impl< ::db::MonitorDetectPointData, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // monitorPtr
    //
    if (object_traits_impl< ::db::Monitor, id_mysql >::grow (
          i.monitorPtr_value, t + 0UL))
      grew = true;

    // detectPointPtr
    //
    if (object_traits_impl< ::db::DetectionPoint, id_mysql >::grow (
          i.detectPointPtr_value, t + 9UL))
      grew = true;

    return grew;
  }

  void access::view_traits_impl< ::db::MonitorDetectPointData, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i)
  {
    using namespace mysql;

    mysql::statement_kind sk (statement_select);
    ODB_POTENTIALLY_UNUSED (sk);

    std::size_t n (0);

    // monitorPtr
    //
    object_traits_impl< ::db::Monitor, id_mysql >::bind (
      b + n, i.monitorPtr_value, sk);
    n += 9UL;

    // detectPointPtr
    //
    object_traits_impl< ::db::DetectionPoint, id_mysql >::bind (
      b + n, i.detectPointPtr_value, sk);
    n += 10UL;
  }

  void access::view_traits_impl< ::db::MonitorDetectPointData, id_mysql >::
  init (view_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    mysql::connection& conn (
      mysql::transaction::current ().connection (*db));

    // monitorPtr pre
    //
    typedef ::db::Monitor monitorPtr_object_type;
    typedef object_traits_impl<monitorPtr_object_type, id_mysql> monitorPtr_object_traits;
    typedef monitorPtr_object_traits::pointer_type monitorPtr_pointer_type;
    typedef monitorPtr_object_traits::pointer_traits monitorPtr_pointer_traits;
    typedef monitorPtr_object_traits::pointer_cache_traits monitorPtr_cache_traits;

    monitorPtr_object_traits::id_type monitorPtr_id;
    monitorPtr_pointer_type monitorPtr_p = 0;
    monitorPtr_pointer_traits::guard monitorPtr_pg;
    monitorPtr_cache_traits::insert_guard monitorPtr_ig;
    monitorPtr_object_type* monitorPtr_o (0);

    {
      if (!(i.monitorPtr_value.id_null))
      {
        monitorPtr_id = monitorPtr_object_traits::id (i.monitorPtr_value);
        monitorPtr_p = monitorPtr_cache_traits::find (*db, monitorPtr_id);

        if (monitorPtr_pointer_traits::null_ptr (monitorPtr_p))
        {
          monitorPtr_p = object_factory<monitorPtr_object_type, monitorPtr_pointer_type>::create ();
          monitorPtr_pg.reset (monitorPtr_p);
          monitorPtr_ig.reset (monitorPtr_cache_traits::insert (*db, monitorPtr_id, monitorPtr_p));
          monitorPtr_o = monitorPtr_pointer_traits::get_ptr (monitorPtr_p);
        }
      }
    }

    // detectPointPtr pre
    //
    typedef ::db::DetectionPoint detectPointPtr_object_type;
    typedef object_traits_impl<detectPointPtr_object_type, id_mysql> detectPointPtr_object_traits;
    typedef detectPointPtr_object_traits::pointer_type detectPointPtr_pointer_type;
    typedef detectPointPtr_object_traits::pointer_traits detectPointPtr_pointer_traits;
    typedef detectPointPtr_object_traits::pointer_cache_traits detectPointPtr_cache_traits;

    detectPointPtr_object_traits::id_type detectPointPtr_id;
    detectPointPtr_pointer_type detectPointPtr_p = 0;
    detectPointPtr_pointer_traits::guard detectPointPtr_pg;
    detectPointPtr_cache_traits::insert_guard detectPointPtr_ig;
    detectPointPtr_object_type* detectPointPtr_o (0);

    {
      if (!(i.detectPointPtr_value.id_null))
      {
        detectPointPtr_id = detectPointPtr_object_traits::id (i.detectPointPtr_value);
        detectPointPtr_p = detectPointPtr_cache_traits::find (*db, detectPointPtr_id);

        if (detectPointPtr_pointer_traits::null_ptr (detectPointPtr_p))
        {
          detectPointPtr_p = object_factory<detectPointPtr_object_type, detectPointPtr_pointer_type>::create ();
          detectPointPtr_pg.reset (detectPointPtr_p);
          detectPointPtr_ig.reset (detectPointPtr_cache_traits::insert (*db, detectPointPtr_id, detectPointPtr_p));
          detectPointPtr_o = detectPointPtr_pointer_traits::get_ptr (detectPointPtr_p);
        }
      }
    }

    // monitorPtr
    //
    {
      if (monitorPtr_o != 0)
      {
        monitorPtr_object_traits::callback (*db, *monitorPtr_o, callback_event::pre_load);
        monitorPtr_object_traits::init (*monitorPtr_o, i.monitorPtr_value, db);
        monitorPtr_object_traits::statements_type& sts (
          conn.statement_cache ().find_object<monitorPtr_object_type> ());
        monitorPtr_object_traits::load_ (sts, *monitorPtr_o, false);
      }
    }

    // detectPointPtr
    //
    {
      if (detectPointPtr_o != 0)
      {
        detectPointPtr_object_traits::callback (*db, *detectPointPtr_o, callback_event::pre_load);
        detectPointPtr_object_traits::init (*detectPointPtr_o, i.detectPointPtr_value, db);
        detectPointPtr_object_traits::statements_type& sts (
          conn.statement_cache ().find_object<detectPointPtr_object_type> ());
        detectPointPtr_object_traits::load_ (sts, *detectPointPtr_o, false);
      }
    }

    // monitorPtr post
    //
    {
      if (monitorPtr_o != 0)
      {
        monitorPtr_object_traits::callback (*db, *monitorPtr_o, callback_event::post_load);
        monitorPtr_cache_traits::load (monitorPtr_ig.position ());
        monitorPtr_ig.release ();
        monitorPtr_pg.release ();
      }

      // If a compiler error points to the line below, then
      // it most likely means that a pointer used in view
      // member cannot be initialized from an object pointer.
      //
      o.monitorPtr = ::std::shared_ptr< ::db::Monitor > (
        std::move (monitorPtr_p));
    }

    // detectPointPtr post
    //
    {
      if (detectPointPtr_o != 0)
      {
        detectPointPtr_object_traits::callback (*db, *detectPointPtr_o, callback_event::post_load);
        detectPointPtr_cache_traits::load (detectPointPtr_ig.position ());
        detectPointPtr_ig.release ();
        detectPointPtr_pg.release ();
      }

      // If a compiler error points to the line below, then
      // it most likely means that a pointer used in view
      // member cannot be initialized from an object pointer.
      //
      o.detectPointPtr = ::std::shared_ptr< ::db::DetectionPoint > (
        std::move (detectPointPtr_p));
    }
  }

  access::view_traits_impl< ::db::MonitorDetectPointData, id_mysql >::query_base_type
  access::view_traits_impl< ::db::MonitorDetectPointData, id_mysql >::
  query_statement (const query_base_type& q)
  {
    query_base_type r (
      "SELECT "
      "`wn_monitor`.`id`, "
      "`wn_monitor`.`name`, "
      "`wn_monitor`.`ip`, "
      "`wn_monitor`.`detection_point_count`, "
      "`wn_monitor`.`is_del`, "
      "`wn_monitor`.`is_enable`, "
      "`wn_monitor`.`ntp_server`, "
      "`wn_monitor`.`is_main`, "
      "`wn_monitor`.`status`, "
      "`wn_detection_point`.`id`, "
      "`wn_detection_point`.`monitor_id`, "
      "`wn_detection_point`.`detection_point_name`, "
      "`wn_detection_point`.`video_id`, "
      "`wn_detection_point`.`project_id`, "
      "`wn_detection_point`.`stream_id`, "
      "`wn_detection_point`.`is_roll`, "
      "`wn_detection_point`.`is_del`, "
      "`wn_detection_point`.`is_enable`, "
      "`wn_detection_point`.`bind_video_id` ");

    r += "FROM `wn_monitor`";

    r += " INNER JOIN `wn_detection_point` ON";
    // From nv_aimonitor31_view.h:34:34
    r += query_columns::DetectionPoint::monitorId == query_columns::Monitor::id;

    if (!q.empty ())
    {
      r += " ";
      r += q.clause_prefix ();
      r += q;
    }

    return r;
  }

  result< access::view_traits_impl< ::db::MonitorDetectPointData, id_mysql >::view_type >
  access::view_traits_impl< ::db::MonitorDetectPointData, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_view<view_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.image_binding ());

    if (im.version != sts.image_version () || imb.version == 0)
    {
      bind (imb.bind, im);
      sts.image_version (im.version);
      imb.version++;
    }

    const query_base_type& qs (query_statement (q));
    qs.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        qs.clause (),
        false,
        true,
        qs.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::view_result_impl<view_type> > r (
      new (shared) mysql::view_result_impl<view_type> (
        qs, st, sts, 0));

    return result<view_type> (r);
  }

  // PresetProgramData
  //

  bool access::view_traits_impl< ::db::PresetProgramData, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // programInfoPtr
    //
    if (object_traits_impl< ::db::ProgramInfo, id_mysql >::grow (
          i.programInfoPtr_value, t + 0UL))
      grew = true;

    // programPtr
    //
    if (object_traits_impl< ::db::Program, id_mysql >::grow (
          i.programPtr_value, t + 6UL))
      grew = true;

    return grew;
  }

  void access::view_traits_impl< ::db::PresetProgramData, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i)
  {
    using namespace mysql;

    mysql::statement_kind sk (statement_select);
    ODB_POTENTIALLY_UNUSED (sk);

    std::size_t n (0);

    // programInfoPtr
    //
    object_traits_impl< ::db::ProgramInfo, id_mysql >::bind (
      b + n, i.programInfoPtr_value, sk);
    n += 6UL;

    // programPtr
    //
    object_traits_impl< ::db::Program, id_mysql >::bind (
      b + n, i.programPtr_value, sk);
    n += 5UL;
  }

  void access::view_traits_impl< ::db::PresetProgramData, id_mysql >::
  init (view_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    mysql::connection& conn (
      mysql::transaction::current ().connection (*db));

    // programInfoPtr pre
    //
    typedef ::db::ProgramInfo programInfoPtr_object_type;
    typedef object_traits_impl<programInfoPtr_object_type, id_mysql> programInfoPtr_object_traits;
    typedef programInfoPtr_object_traits::pointer_type programInfoPtr_pointer_type;
    typedef programInfoPtr_object_traits::pointer_traits programInfoPtr_pointer_traits;
    typedef programInfoPtr_object_traits::pointer_cache_traits programInfoPtr_cache_traits;

    programInfoPtr_object_traits::id_type programInfoPtr_id;
    programInfoPtr_pointer_type programInfoPtr_p = 0;
    programInfoPtr_pointer_traits::guard programInfoPtr_pg;
    programInfoPtr_cache_traits::insert_guard programInfoPtr_ig;
    programInfoPtr_object_type* programInfoPtr_o (0);

    {
      if (!(i.programInfoPtr_value.id_null))
      {
        programInfoPtr_id = programInfoPtr_object_traits::id (i.programInfoPtr_value);
        programInfoPtr_p = programInfoPtr_cache_traits::find (*db, programInfoPtr_id);

        if (programInfoPtr_pointer_traits::null_ptr (programInfoPtr_p))
        {
          programInfoPtr_p = object_factory<programInfoPtr_object_type, programInfoPtr_pointer_type>::create ();
          programInfoPtr_pg.reset (programInfoPtr_p);
          programInfoPtr_ig.reset (programInfoPtr_cache_traits::insert (*db, programInfoPtr_id, programInfoPtr_p));
          programInfoPtr_o = programInfoPtr_pointer_traits::get_ptr (programInfoPtr_p);
        }
      }
    }

    // programPtr pre
    //
    typedef ::db::Program programPtr_object_type;
    typedef object_traits_impl<programPtr_object_type, id_mysql> programPtr_object_traits;
    typedef programPtr_object_traits::pointer_type programPtr_pointer_type;
    typedef programPtr_object_traits::pointer_traits programPtr_pointer_traits;
    typedef programPtr_object_traits::pointer_cache_traits programPtr_cache_traits;

    programPtr_object_traits::id_type programPtr_id;
    programPtr_pointer_type programPtr_p = 0;
    programPtr_pointer_traits::guard programPtr_pg;
    programPtr_cache_traits::insert_guard programPtr_ig;
    programPtr_object_type* programPtr_o (0);

    {
      if (!(i.programPtr_value.id_null))
      {
        programPtr_id = programPtr_object_traits::id (i.programPtr_value);
        programPtr_p = programPtr_cache_traits::find (*db, programPtr_id);

        if (programPtr_pointer_traits::null_ptr (programPtr_p))
        {
          programPtr_p = object_factory<programPtr_object_type, programPtr_pointer_type>::create ();
          programPtr_pg.reset (programPtr_p);
          programPtr_ig.reset (programPtr_cache_traits::insert (*db, programPtr_id, programPtr_p));
          programPtr_o = programPtr_pointer_traits::get_ptr (programPtr_p);
        }
      }
    }

    // programInfoPtr
    //
    {
      if (programInfoPtr_o != 0)
      {
        programInfoPtr_object_traits::callback (*db, *programInfoPtr_o, callback_event::pre_load);
        programInfoPtr_object_traits::init (*programInfoPtr_o, i.programInfoPtr_value, db);
        programInfoPtr_object_traits::statements_type& sts (
          conn.statement_cache ().find_object<programInfoPtr_object_type> ());
        programInfoPtr_object_traits::load_ (sts, *programInfoPtr_o, false);
      }
    }

    // programPtr
    //
    {
      if (programPtr_o != 0)
      {
        programPtr_object_traits::callback (*db, *programPtr_o, callback_event::pre_load);
        programPtr_object_traits::init (*programPtr_o, i.programPtr_value, db);
        programPtr_object_traits::statements_type& sts (
          conn.statement_cache ().find_object<programPtr_object_type> ());
        programPtr_object_traits::load_ (sts, *programPtr_o, false);
      }
    }

    // programInfoPtr post
    //
    {
      if (programInfoPtr_o != 0)
      {
        programInfoPtr_object_traits::callback (*db, *programInfoPtr_o, callback_event::post_load);
        programInfoPtr_cache_traits::load (programInfoPtr_ig.position ());
        programInfoPtr_ig.release ();
        programInfoPtr_pg.release ();
      }

      // If a compiler error points to the line below, then
      // it most likely means that a pointer used in view
      // member cannot be initialized from an object pointer.
      //
      o.programInfoPtr = ::std::shared_ptr< ::db::ProgramInfo > (
        std::move (programInfoPtr_p));
    }

    // programPtr post
    //
    {
      if (programPtr_o != 0)
      {
        programPtr_object_traits::callback (*db, *programPtr_o, callback_event::post_load);
        programPtr_cache_traits::load (programPtr_ig.position ());
        programPtr_ig.release ();
        programPtr_pg.release ();
      }

      // If a compiler error points to the line below, then
      // it most likely means that a pointer used in view
      // member cannot be initialized from an object pointer.
      //
      o.programPtr = ::std::shared_ptr< ::db::Program > (
        std::move (programPtr_p));
    }
  }

  access::view_traits_impl< ::db::PresetProgramData, id_mysql >::query_base_type
  access::view_traits_impl< ::db::PresetProgramData, id_mysql >::
  query_statement (const query_base_type& q)
  {
    query_base_type r (
      "SELECT "
      "`wn_program_info`.`id`, "
      "`wn_program_info`.`switch_time`, "
      "`wn_program_info`.`preset_id`, "
      "`wn_program_info`.`program_id`, "
      "`wn_program_info`.`is_del`, "
      "`wn_program_info`.`is_enable`, "
      "`wn_program`.`id`, "
      "`wn_program`.`start_date`, "
      "`wn_program`.`morning_time`, "
      "`wn_program`.`evening_time`, "
      "`wn_program`.`is_del` ");

    r += "FROM `wn_program_info`";

    r += " INNER JOIN `wn_program` ON";
    // From nv_aimonitor31_view.h:45:2
    r += query_columns::Program::id == query_columns::ProgramInfo::programId;

    r += " INNER JOIN `wn_preset` ON";
    // From nv_aimonitor31_view.h:46:2
    r += query_columns::Preset::id == query_columns::ProgramInfo::presetId;

    if (!q.empty ())
    {
      r += " ";
      r += q.clause_prefix ();
      r += q;
    }

    return r;
  }

  result< access::view_traits_impl< ::db::PresetProgramData, id_mysql >::view_type >
  access::view_traits_impl< ::db::PresetProgramData, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_view<view_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.image_binding ());

    if (im.version != sts.image_version () || imb.version == 0)
    {
      bind (imb.bind, im);
      sts.image_version (im.version);
      imb.version++;
    }

    const query_base_type& qs (query_statement (q));
    qs.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        qs.clause (),
        false,
        true,
        qs.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::view_result_impl<view_type> > r (
      new (shared) mysql::view_result_impl<view_type> (
        qs, st, sts, 0));

    return result<view_type> (r);
  }

  // PresetAreasData
  //

  bool access::view_traits_impl< ::db::PresetAreasData, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // presetOffsetPtr
    //
    if (object_traits_impl< ::db::PresetOffset, id_mysql >::grow (
          i.presetOffsetPtr_value, t + 0UL))
      grew = true;

    // presetRoiPtr
    //
    if (object_traits_impl< ::db::PresetRoi, id_mysql >::grow (
          i.presetRoiPtr_value, t + 5UL))
      grew = true;

    return grew;
  }

  void access::view_traits_impl< ::db::PresetAreasData, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i)
  {
    using namespace mysql;

    mysql::statement_kind sk (statement_select);
    ODB_POTENTIALLY_UNUSED (sk);

    std::size_t n (0);

    // presetOffsetPtr
    //
    object_traits_impl< ::db::PresetOffset, id_mysql >::bind (
      b + n, i.presetOffsetPtr_value, sk);
    n += 5UL;

    // presetRoiPtr
    //
    object_traits_impl< ::db::PresetRoi, id_mysql >::bind (
      b + n, i.presetRoiPtr_value, sk);
    n += 16UL;
  }

  void access::view_traits_impl< ::db::PresetAreasData, id_mysql >::
  init (view_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    mysql::connection& conn (
      mysql::transaction::current ().connection (*db));

    // presetOffsetPtr pre
    //
    typedef ::db::PresetOffset presetOffsetPtr_object_type;
    typedef object_traits_impl<presetOffsetPtr_object_type, id_mysql> presetOffsetPtr_object_traits;
    typedef presetOffsetPtr_object_traits::pointer_type presetOffsetPtr_pointer_type;
    typedef presetOffsetPtr_object_traits::pointer_traits presetOffsetPtr_pointer_traits;
    typedef presetOffsetPtr_object_traits::pointer_cache_traits presetOffsetPtr_cache_traits;

    presetOffsetPtr_object_traits::id_type presetOffsetPtr_id;
    presetOffsetPtr_pointer_type presetOffsetPtr_p = 0;
    presetOffsetPtr_pointer_traits::guard presetOffsetPtr_pg;
    presetOffsetPtr_cache_traits::insert_guard presetOffsetPtr_ig;
    presetOffsetPtr_object_type* presetOffsetPtr_o (0);

    {
      if (!(i.presetOffsetPtr_value.id_null))
      {
        presetOffsetPtr_id = presetOffsetPtr_object_traits::id (i.presetOffsetPtr_value);
        presetOffsetPtr_p = presetOffsetPtr_cache_traits::find (*db, presetOffsetPtr_id);

        if (presetOffsetPtr_pointer_traits::null_ptr (presetOffsetPtr_p))
        {
          presetOffsetPtr_p = object_factory<presetOffsetPtr_object_type, presetOffsetPtr_pointer_type>::create ();
          presetOffsetPtr_pg.reset (presetOffsetPtr_p);
          presetOffsetPtr_ig.reset (presetOffsetPtr_cache_traits::insert (*db, presetOffsetPtr_id, presetOffsetPtr_p));
          presetOffsetPtr_o = presetOffsetPtr_pointer_traits::get_ptr (presetOffsetPtr_p);
        }
      }
    }

    // presetRoiPtr pre
    //
    typedef ::db::PresetRoi presetRoiPtr_object_type;
    typedef object_traits_impl<presetRoiPtr_object_type, id_mysql> presetRoiPtr_object_traits;
    typedef presetRoiPtr_object_traits::pointer_type presetRoiPtr_pointer_type;
    typedef presetRoiPtr_object_traits::pointer_traits presetRoiPtr_pointer_traits;
    typedef presetRoiPtr_object_traits::pointer_cache_traits presetRoiPtr_cache_traits;

    presetRoiPtr_object_traits::id_type presetRoiPtr_id;
    presetRoiPtr_pointer_type presetRoiPtr_p = 0;
    presetRoiPtr_pointer_traits::guard presetRoiPtr_pg;
    presetRoiPtr_cache_traits::insert_guard presetRoiPtr_ig;
    presetRoiPtr_object_type* presetRoiPtr_o (0);

    {
      if (!(i.presetRoiPtr_value.id_null))
      {
        presetRoiPtr_id = presetRoiPtr_object_traits::id (i.presetRoiPtr_value);
        presetRoiPtr_p = presetRoiPtr_cache_traits::find (*db, presetRoiPtr_id);

        if (presetRoiPtr_pointer_traits::null_ptr (presetRoiPtr_p))
        {
          presetRoiPtr_p = object_factory<presetRoiPtr_object_type, presetRoiPtr_pointer_type>::create ();
          presetRoiPtr_pg.reset (presetRoiPtr_p);
          presetRoiPtr_ig.reset (presetRoiPtr_cache_traits::insert (*db, presetRoiPtr_id, presetRoiPtr_p));
          presetRoiPtr_o = presetRoiPtr_pointer_traits::get_ptr (presetRoiPtr_p);
        }
      }
    }

    // presetOffsetPtr
    //
    {
      if (presetOffsetPtr_o != 0)
      {
        presetOffsetPtr_object_traits::callback (*db, *presetOffsetPtr_o, callback_event::pre_load);
        presetOffsetPtr_object_traits::init (*presetOffsetPtr_o, i.presetOffsetPtr_value, db);
        presetOffsetPtr_object_traits::statements_type& sts (
          conn.statement_cache ().find_object<presetOffsetPtr_object_type> ());
        presetOffsetPtr_object_traits::load_ (sts, *presetOffsetPtr_o, false);
      }
    }

    // presetRoiPtr
    //
    {
      if (presetRoiPtr_o != 0)
      {
        presetRoiPtr_object_traits::callback (*db, *presetRoiPtr_o, callback_event::pre_load);
        presetRoiPtr_object_traits::init (*presetRoiPtr_o, i.presetRoiPtr_value, db);
        presetRoiPtr_object_traits::statements_type& sts (
          conn.statement_cache ().find_object<presetRoiPtr_object_type> ());
        presetRoiPtr_object_traits::load_ (sts, *presetRoiPtr_o, false);
      }
    }

    // presetOffsetPtr post
    //
    {
      if (presetOffsetPtr_o != 0)
      {
        presetOffsetPtr_object_traits::callback (*db, *presetOffsetPtr_o, callback_event::post_load);
        presetOffsetPtr_cache_traits::load (presetOffsetPtr_ig.position ());
        presetOffsetPtr_ig.release ();
        presetOffsetPtr_pg.release ();
      }

      // If a compiler error points to the line below, then
      // it most likely means that a pointer used in view
      // member cannot be initialized from an object pointer.
      //
      o.presetOffsetPtr = ::std::shared_ptr< ::db::PresetOffset > (
        std::move (presetOffsetPtr_p));
    }

    // presetRoiPtr post
    //
    {
      if (presetRoiPtr_o != 0)
      {
        presetRoiPtr_object_traits::callback (*db, *presetRoiPtr_o, callback_event::post_load);
        presetRoiPtr_cache_traits::load (presetRoiPtr_ig.position ());
        presetRoiPtr_ig.release ();
        presetRoiPtr_pg.release ();
      }

      // If a compiler error points to the line below, then
      // it most likely means that a pointer used in view
      // member cannot be initialized from an object pointer.
      //
      o.presetRoiPtr = ::std::shared_ptr< ::db::PresetRoi > (
        std::move (presetRoiPtr_p));
    }
  }

  access::view_traits_impl< ::db::PresetAreasData, id_mysql >::query_base_type
  access::view_traits_impl< ::db::PresetAreasData, id_mysql >::
  query_statement (const query_base_type& q)
  {
    query_base_type r (
      "SELECT "
      "`wn_preset_offset`.`id`, "
      "`wn_preset_offset`.`preset_id`, "
      "`wn_preset_offset`.`offset_area`, "
      "`wn_preset_offset`.`is_del`, "
      "`wn_preset_offset`.`is_enable`, "
      "`wn_preset_roi`.`id`, "
      "`wn_preset_roi`.`preset_id`, "
      "`wn_preset_roi`.`check_area`, "
      "`wn_preset_roi`.`direction`, "
      "`wn_preset_roi`.`logic_id`, "
      "`wn_preset_roi`.`p0`, "
      "`wn_preset_roi`.`p1`, "
      "`wn_preset_roi`.`p2`, "
      "`wn_preset_roi`.`l12`, "
      "`wn_preset_roi`.`count_line`, "
      "`wn_preset_roi`.`event_property`, "
      "`wn_preset_roi`.`label_property`, "
      "`wn_preset_roi`.`param_plan_id`, "
      "`wn_preset_roi`.`param`, "
      "`wn_preset_roi`.`is_del`, "
      "`wn_preset_roi`.`is_enable` ");

    r += "FROM `wn_preset`";

    r += " INNER JOIN `wn_preset_offset` ON";
    // From nv_aimonitor31_view.h:57:2
    r += query_columns::PresetOffset::presetId == query_columns::Preset::id;

    r += " INNER JOIN `wn_preset_roi` ON";
    // From nv_aimonitor31_view.h:58:2
    r += query_columns::PresetRoi::presetId == query_columns::Preset::id;

    if (!q.empty ())
    {
      r += " ";
      r += q.clause_prefix ();
      r += q;
    }

    return r;
  }

  result< access::view_traits_impl< ::db::PresetAreasData, id_mysql >::view_type >
  access::view_traits_impl< ::db::PresetAreasData, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_view<view_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.image_binding ());

    if (im.version != sts.image_version () || imb.version == 0)
    {
      bind (imb.bind, im);
      sts.image_version (im.version);
      imb.version++;
    }

    const query_base_type& qs (query_statement (q));
    qs.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        qs.clause (),
        false,
        true,
        qs.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::view_result_impl<view_type> > r (
      new (shared) mysql::view_result_impl<view_type> (
        qs, st, sts, 0));

    return result<view_type> (r);
  }

  // PresetOffsetData
  //

  bool access::view_traits_impl< ::db::PresetOffsetData, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // presetOffsetPtr
    //
    if (object_traits_impl< ::db::PresetOffset, id_mysql >::grow (
          i.presetOffsetPtr_value, t + 0UL))
      grew = true;

    return grew;
  }

  void access::view_traits_impl< ::db::PresetOffsetData, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i)
  {
    using namespace mysql;

    mysql::statement_kind sk (statement_select);
    ODB_POTENTIALLY_UNUSED (sk);

    std::size_t n (0);

    // presetOffsetPtr
    //
    object_traits_impl< ::db::PresetOffset, id_mysql >::bind (
      b + n, i.presetOffsetPtr_value, sk);
    n += 5UL;
  }

  void access::view_traits_impl< ::db::PresetOffsetData, id_mysql >::
  init (view_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    mysql::connection& conn (
      mysql::transaction::current ().connection (*db));

    // presetOffsetPtr pre
    //
    typedef ::db::PresetOffset presetOffsetPtr_object_type;
    typedef object_traits_impl<presetOffsetPtr_object_type, id_mysql> presetOffsetPtr_object_traits;
    typedef presetOffsetPtr_object_traits::pointer_type presetOffsetPtr_pointer_type;
    typedef presetOffsetPtr_object_traits::pointer_traits presetOffsetPtr_pointer_traits;
    typedef presetOffsetPtr_object_traits::pointer_cache_traits presetOffsetPtr_cache_traits;

    presetOffsetPtr_object_traits::id_type presetOffsetPtr_id;
    presetOffsetPtr_pointer_type presetOffsetPtr_p = 0;
    presetOffsetPtr_pointer_traits::guard presetOffsetPtr_pg;
    presetOffsetPtr_cache_traits::insert_guard presetOffsetPtr_ig;
    presetOffsetPtr_object_type* presetOffsetPtr_o (0);

    {
      if (!(i.presetOffsetPtr_value.id_null))
      {
        presetOffsetPtr_id = presetOffsetPtr_object_traits::id (i.presetOffsetPtr_value);
        presetOffsetPtr_p = presetOffsetPtr_cache_traits::find (*db, presetOffsetPtr_id);

        if (presetOffsetPtr_pointer_traits::null_ptr (presetOffsetPtr_p))
        {
          presetOffsetPtr_p = object_factory<presetOffsetPtr_object_type, presetOffsetPtr_pointer_type>::create ();
          presetOffsetPtr_pg.reset (presetOffsetPtr_p);
          presetOffsetPtr_ig.reset (presetOffsetPtr_cache_traits::insert (*db, presetOffsetPtr_id, presetOffsetPtr_p));
          presetOffsetPtr_o = presetOffsetPtr_pointer_traits::get_ptr (presetOffsetPtr_p);
        }
      }
    }

    // presetOffsetPtr
    //
    {
      if (presetOffsetPtr_o != 0)
      {
        presetOffsetPtr_object_traits::callback (*db, *presetOffsetPtr_o, callback_event::pre_load);
        presetOffsetPtr_object_traits::init (*presetOffsetPtr_o, i.presetOffsetPtr_value, db);
        presetOffsetPtr_object_traits::statements_type& sts (
          conn.statement_cache ().find_object<presetOffsetPtr_object_type> ());
        presetOffsetPtr_object_traits::load_ (sts, *presetOffsetPtr_o, false);
      }
    }

    // presetOffsetPtr post
    //
    {
      if (presetOffsetPtr_o != 0)
      {
        presetOffsetPtr_object_traits::callback (*db, *presetOffsetPtr_o, callback_event::post_load);
        presetOffsetPtr_cache_traits::load (presetOffsetPtr_ig.position ());
        presetOffsetPtr_ig.release ();
        presetOffsetPtr_pg.release ();
      }

      // If a compiler error points to the line below, then
      // it most likely means that a pointer used in view
      // member cannot be initialized from an object pointer.
      //
      o.presetOffsetPtr = ::std::shared_ptr< ::db::PresetOffset > (
        std::move (presetOffsetPtr_p));
    }
  }

  access::view_traits_impl< ::db::PresetOffsetData, id_mysql >::query_base_type
  access::view_traits_impl< ::db::PresetOffsetData, id_mysql >::
  query_statement (const query_base_type& q)
  {
    query_base_type r (
      "SELECT "
      "`wn_preset_offset`.`id`, "
      "`wn_preset_offset`.`preset_id`, "
      "`wn_preset_offset`.`offset_area`, "
      "`wn_preset_offset`.`is_del`, "
      "`wn_preset_offset`.`is_enable` ");

    r += "FROM `wn_preset`";

    r += " INNER JOIN `wn_preset_offset` ON";
    // From nv_aimonitor31_view.h:67:2
    r += query_columns::PresetOffset::presetId == query_columns::Preset::id;

    if (!q.empty ())
    {
      r += " ";
      r += q.clause_prefix ();
      r += q;
    }

    return r;
  }

  result< access::view_traits_impl< ::db::PresetOffsetData, id_mysql >::view_type >
  access::view_traits_impl< ::db::PresetOffsetData, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_view<view_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.image_binding ());

    if (im.version != sts.image_version () || imb.version == 0)
    {
      bind (imb.bind, im);
      sts.image_version (im.version);
      imb.version++;
    }

    const query_base_type& qs (query_statement (q));
    qs.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        qs.clause (),
        false,
        true,
        qs.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::view_result_impl<view_type> > r (
      new (shared) mysql::view_result_impl<view_type> (
        qs, st, sts, 0));

    return result<view_type> (r);
  }

  // PresetRoiData
  //

  bool access::view_traits_impl< ::db::PresetRoiData, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // presetRoiPtr
    //
    if (object_traits_impl< ::db::PresetRoi, id_mysql >::grow (
          i.presetRoiPtr_value, t + 0UL))
      grew = true;

    return grew;
  }

  void access::view_traits_impl< ::db::PresetRoiData, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i)
  {
    using namespace mysql;

    mysql::statement_kind sk (statement_select);
    ODB_POTENTIALLY_UNUSED (sk);

    std::size_t n (0);

    // presetRoiPtr
    //
    object_traits_impl< ::db::PresetRoi, id_mysql >::bind (
      b + n, i.presetRoiPtr_value, sk);
    n += 16UL;
  }

  void access::view_traits_impl< ::db::PresetRoiData, id_mysql >::
  init (view_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    mysql::connection& conn (
      mysql::transaction::current ().connection (*db));

    // presetRoiPtr pre
    //
    typedef ::db::PresetRoi presetRoiPtr_object_type;
    typedef object_traits_impl<presetRoiPtr_object_type, id_mysql> presetRoiPtr_object_traits;
    typedef presetRoiPtr_object_traits::pointer_type presetRoiPtr_pointer_type;
    typedef presetRoiPtr_object_traits::pointer_traits presetRoiPtr_pointer_traits;
    typedef presetRoiPtr_object_traits::pointer_cache_traits presetRoiPtr_cache_traits;

    presetRoiPtr_object_traits::id_type presetRoiPtr_id;
    presetRoiPtr_pointer_type presetRoiPtr_p = 0;
    presetRoiPtr_pointer_traits::guard presetRoiPtr_pg;
    presetRoiPtr_cache_traits::insert_guard presetRoiPtr_ig;
    presetRoiPtr_object_type* presetRoiPtr_o (0);

    {
      if (!(i.presetRoiPtr_value.id_null))
      {
        presetRoiPtr_id = presetRoiPtr_object_traits::id (i.presetRoiPtr_value);
        presetRoiPtr_p = presetRoiPtr_cache_traits::find (*db, presetRoiPtr_id);

        if (presetRoiPtr_pointer_traits::null_ptr (presetRoiPtr_p))
        {
          presetRoiPtr_p = object_factory<presetRoiPtr_object_type, presetRoiPtr_pointer_type>::create ();
          presetRoiPtr_pg.reset (presetRoiPtr_p);
          presetRoiPtr_ig.reset (presetRoiPtr_cache_traits::insert (*db, presetRoiPtr_id, presetRoiPtr_p));
          presetRoiPtr_o = presetRoiPtr_pointer_traits::get_ptr (presetRoiPtr_p);
        }
      }
    }

    // presetRoiPtr
    //
    {
      if (presetRoiPtr_o != 0)
      {
        presetRoiPtr_object_traits::callback (*db, *presetRoiPtr_o, callback_event::pre_load);
        presetRoiPtr_object_traits::init (*presetRoiPtr_o, i.presetRoiPtr_value, db);
        presetRoiPtr_object_traits::statements_type& sts (
          conn.statement_cache ().find_object<presetRoiPtr_object_type> ());
        presetRoiPtr_object_traits::load_ (sts, *presetRoiPtr_o, false);
      }
    }

    // presetRoiPtr post
    //
    {
      if (presetRoiPtr_o != 0)
      {
        presetRoiPtr_object_traits::callback (*db, *presetRoiPtr_o, callback_event::post_load);
        presetRoiPtr_cache_traits::load (presetRoiPtr_ig.position ());
        presetRoiPtr_ig.release ();
        presetRoiPtr_pg.release ();
      }

      // If a compiler error points to the line below, then
      // it most likely means that a pointer used in view
      // member cannot be initialized from an object pointer.
      //
      o.presetRoiPtr = ::std::shared_ptr< ::db::PresetRoi > (
        std::move (presetRoiPtr_p));
    }
  }

  access::view_traits_impl< ::db::PresetRoiData, id_mysql >::query_base_type
  access::view_traits_impl< ::db::PresetRoiData, id_mysql >::
  query_statement (const query_base_type& q)
  {
    query_base_type r (
      "SELECT "
      "`wn_preset_roi`.`id`, "
      "`wn_preset_roi`.`preset_id`, "
      "`wn_preset_roi`.`check_area`, "
      "`wn_preset_roi`.`direction`, "
      "`wn_preset_roi`.`logic_id`, "
      "`wn_preset_roi`.`p0`, "
      "`wn_preset_roi`.`p1`, "
      "`wn_preset_roi`.`p2`, "
      "`wn_preset_roi`.`l12`, "
      "`wn_preset_roi`.`count_line`, "
      "`wn_preset_roi`.`event_property`, "
      "`wn_preset_roi`.`label_property`, "
      "`wn_preset_roi`.`param_plan_id`, "
      "`wn_preset_roi`.`param`, "
      "`wn_preset_roi`.`is_del`, "
      "`wn_preset_roi`.`is_enable` ");

    r += "FROM `wn_preset`";

    r += " INNER JOIN `wn_preset_roi` ON";
    // From nv_aimonitor31_view.h:74:2
    r += query_columns::PresetRoi::presetId == query_columns::Preset::id;

    if (!q.empty ())
    {
      r += " ";
      r += q.clause_prefix ();
      r += q;
    }

    return r;
  }

  result< access::view_traits_impl< ::db::PresetRoiData, id_mysql >::view_type >
  access::view_traits_impl< ::db::PresetRoiData, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_view<view_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.image_binding ());

    if (im.version != sts.image_version () || imb.version == 0)
    {
      bind (imb.bind, im);
      sts.image_version (im.version);
      imb.version++;
    }

    const query_base_type& qs (query_statement (q));
    qs.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        qs.clause (),
        false,
        true,
        qs.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::view_result_impl<view_type> > r (
      new (shared) mysql::view_result_impl<view_type> (
        qs, st, sts, 0));

    return result<view_type> (r);
  }

  // ROICheckAreaData
  //

  bool access::view_traits_impl< ::db::ROICheckAreaData, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // presetCheckAreaPtr
    //
    if (object_traits_impl< ::db::PresetCheckArea, id_mysql >::grow (
          i.presetCheckAreaPtr_value, t + 0UL))
      grew = true;

    return grew;
  }

  void access::view_traits_impl< ::db::ROICheckAreaData, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i)
  {
    using namespace mysql;

    mysql::statement_kind sk (statement_select);
    ODB_POTENTIALLY_UNUSED (sk);

    std::size_t n (0);

    // presetCheckAreaPtr
    //
    object_traits_impl< ::db::PresetCheckArea, id_mysql >::bind (
      b + n, i.presetCheckAreaPtr_value, sk);
    n += 8UL;
  }

  void access::view_traits_impl< ::db::ROICheckAreaData, id_mysql >::
  init (view_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    mysql::connection& conn (
      mysql::transaction::current ().connection (*db));

    // presetCheckAreaPtr pre
    //
    typedef ::db::PresetCheckArea presetCheckAreaPtr_object_type;
    typedef object_traits_impl<presetCheckAreaPtr_object_type, id_mysql> presetCheckAreaPtr_object_traits;
    typedef presetCheckAreaPtr_object_traits::pointer_type presetCheckAreaPtr_pointer_type;
    typedef presetCheckAreaPtr_object_traits::pointer_traits presetCheckAreaPtr_pointer_traits;
    typedef presetCheckAreaPtr_object_traits::pointer_cache_traits presetCheckAreaPtr_cache_traits;

    presetCheckAreaPtr_object_traits::id_type presetCheckAreaPtr_id;
    presetCheckAreaPtr_pointer_type presetCheckAreaPtr_p = 0;
    presetCheckAreaPtr_pointer_traits::guard presetCheckAreaPtr_pg;
    presetCheckAreaPtr_cache_traits::insert_guard presetCheckAreaPtr_ig;
    presetCheckAreaPtr_object_type* presetCheckAreaPtr_o (0);

    {
      if (!(i.presetCheckAreaPtr_value.id_null))
      {
        presetCheckAreaPtr_id = presetCheckAreaPtr_object_traits::id (i.presetCheckAreaPtr_value);
        presetCheckAreaPtr_p = presetCheckAreaPtr_cache_traits::find (*db, presetCheckAreaPtr_id);

        if (presetCheckAreaPtr_pointer_traits::null_ptr (presetCheckAreaPtr_p))
        {
          presetCheckAreaPtr_p = object_factory<presetCheckAreaPtr_object_type, presetCheckAreaPtr_pointer_type>::create ();
          presetCheckAreaPtr_pg.reset (presetCheckAreaPtr_p);
          presetCheckAreaPtr_ig.reset (presetCheckAreaPtr_cache_traits::insert (*db, presetCheckAreaPtr_id, presetCheckAreaPtr_p));
          presetCheckAreaPtr_o = presetCheckAreaPtr_pointer_traits::get_ptr (presetCheckAreaPtr_p);
        }
      }
    }

    // presetCheckAreaPtr
    //
    {
      if (presetCheckAreaPtr_o != 0)
      {
        presetCheckAreaPtr_object_traits::callback (*db, *presetCheckAreaPtr_o, callback_event::pre_load);
        presetCheckAreaPtr_object_traits::init (*presetCheckAreaPtr_o, i.presetCheckAreaPtr_value, db);
        presetCheckAreaPtr_object_traits::statements_type& sts (
          conn.statement_cache ().find_object<presetCheckAreaPtr_object_type> ());
        presetCheckAreaPtr_object_traits::load_ (sts, *presetCheckAreaPtr_o, false);
      }
    }

    // presetCheckAreaPtr post
    //
    {
      if (presetCheckAreaPtr_o != 0)
      {
        presetCheckAreaPtr_object_traits::callback (*db, *presetCheckAreaPtr_o, callback_event::post_load);
        presetCheckAreaPtr_cache_traits::load (presetCheckAreaPtr_ig.position ());
        presetCheckAreaPtr_ig.release ();
        presetCheckAreaPtr_pg.release ();
      }

      // If a compiler error points to the line below, then
      // it most likely means that a pointer used in view
      // member cannot be initialized from an object pointer.
      //
      o.presetCheckAreaPtr = ::std::shared_ptr< ::db::PresetCheckArea > (
        std::move (presetCheckAreaPtr_p));
    }
  }

  access::view_traits_impl< ::db::ROICheckAreaData, id_mysql >::query_base_type
  access::view_traits_impl< ::db::ROICheckAreaData, id_mysql >::
  query_statement (const query_base_type& q)
  {
    query_base_type r (
      "SELECT "
      "`wn_preset_check_area`.`id`, "
      "`wn_preset_check_area`.`roi_id`, "
      "`wn_preset_check_area`.`check_area`, "
      "`wn_preset_check_area`.`event_property`, "
      "`wn_preset_check_area`.`label_property`, "
      "`wn_preset_check_area`.`param`, "
      "`wn_preset_check_area`.`area_type`, "
      "`wn_preset_check_area`.`param_plan_id` ");

    r += "FROM `wn_preset_roi`";

    r += " INNER JOIN `wn_preset_check_area` ON";
    // From nv_aimonitor31_view.h:82:5
    r += query_columns::PresetCheckArea::roiId == query_columns::PresetRoi::id;

    if (!q.empty ())
    {
      r += " ";
      r += q.clause_prefix ();
      r += q;
    }

    return r;
  }

  result< access::view_traits_impl< ::db::ROICheckAreaData, id_mysql >::view_type >
  access::view_traits_impl< ::db::ROICheckAreaData, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_view<view_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.image_binding ());

    if (im.version != sts.image_version () || imb.version == 0)
    {
      bind (imb.bind, im);
      sts.image_version (im.version);
      imb.version++;
    }

    const query_base_type& qs (query_statement (q));
    qs.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        qs.clause (),
        false,
        true,
        qs.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::view_result_impl<view_type> > r (
      new (shared) mysql::view_result_impl<view_type> (
        qs, st, sts, 0));

    return result<view_type> (r);
  }

  // ROILaneData
  //

  bool access::view_traits_impl< ::db::ROILaneData, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // presetLanePtr
    //
    if (object_traits_impl< ::db::PresetLane, id_mysql >::grow (
          i.presetLanePtr_value, t + 0UL))
      grew = true;

    return grew;
  }

  void access::view_traits_impl< ::db::ROILaneData, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i)
  {
    using namespace mysql;

    mysql::statement_kind sk (statement_select);
    ODB_POTENTIALLY_UNUSED (sk);

    std::size_t n (0);

    // presetLanePtr
    //
    object_traits_impl< ::db::PresetLane, id_mysql >::bind (
      b + n, i.presetLanePtr_value, sk);
    n += 13UL;
  }

  void access::view_traits_impl< ::db::ROILaneData, id_mysql >::
  init (view_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    mysql::connection& conn (
      mysql::transaction::current ().connection (*db));

    // presetLanePtr pre
    //
    typedef ::db::PresetLane presetLanePtr_object_type;
    typedef object_traits_impl<presetLanePtr_object_type, id_mysql> presetLanePtr_object_traits;
    typedef presetLanePtr_object_traits::pointer_type presetLanePtr_pointer_type;
    typedef presetLanePtr_object_traits::pointer_traits presetLanePtr_pointer_traits;
    typedef presetLanePtr_object_traits::pointer_cache_traits presetLanePtr_cache_traits;

    presetLanePtr_object_traits::id_type presetLanePtr_id;
    presetLanePtr_pointer_type presetLanePtr_p = 0;
    presetLanePtr_pointer_traits::guard presetLanePtr_pg;
    presetLanePtr_cache_traits::insert_guard presetLanePtr_ig;
    presetLanePtr_object_type* presetLanePtr_o (0);

    {
      if (!(i.presetLanePtr_value.id_null))
      {
        presetLanePtr_id = presetLanePtr_object_traits::id (i.presetLanePtr_value);
        presetLanePtr_p = presetLanePtr_cache_traits::find (*db, presetLanePtr_id);

        if (presetLanePtr_pointer_traits::null_ptr (presetLanePtr_p))
        {
          presetLanePtr_p = object_factory<presetLanePtr_object_type, presetLanePtr_pointer_type>::create ();
          presetLanePtr_pg.reset (presetLanePtr_p);
          presetLanePtr_ig.reset (presetLanePtr_cache_traits::insert (*db, presetLanePtr_id, presetLanePtr_p));
          presetLanePtr_o = presetLanePtr_pointer_traits::get_ptr (presetLanePtr_p);
        }
      }
    }

    // presetLanePtr
    //
    {
      if (presetLanePtr_o != 0)
      {
        presetLanePtr_object_traits::callback (*db, *presetLanePtr_o, callback_event::pre_load);
        presetLanePtr_object_traits::init (*presetLanePtr_o, i.presetLanePtr_value, db);
        presetLanePtr_object_traits::statements_type& sts (
          conn.statement_cache ().find_object<presetLanePtr_object_type> ());
        presetLanePtr_object_traits::load_ (sts, *presetLanePtr_o, false);
      }
    }

    // presetLanePtr post
    //
    {
      if (presetLanePtr_o != 0)
      {
        presetLanePtr_object_traits::callback (*db, *presetLanePtr_o, callback_event::post_load);
        presetLanePtr_cache_traits::load (presetLanePtr_ig.position ());
        presetLanePtr_ig.release ();
        presetLanePtr_pg.release ();
      }

      // If a compiler error points to the line below, then
      // it most likely means that a pointer used in view
      // member cannot be initialized from an object pointer.
      //
      o.presetLanePtr = ::std::shared_ptr< ::db::PresetLane > (
        std::move (presetLanePtr_p));
    }
  }

  access::view_traits_impl< ::db::ROILaneData, id_mysql >::query_base_type
  access::view_traits_impl< ::db::ROILaneData, id_mysql >::
  query_statement (const query_base_type& q)
  {
    query_base_type r (
      "SELECT "
      "`wn_preset_lane`.`id`, "
      "`wn_preset_lane`.`roi_id`, "
      "`wn_preset_lane`.`lane_area`, "
      "`wn_preset_lane`.`lane_type`, "
      "`wn_preset_lane`.`min_speed`, "
      "`wn_preset_lane`.`max_speed`, "
      "`wn_preset_lane`.`event_property`, "
      "`wn_preset_lane`.`label_property`, "
      "`wn_preset_lane`.`param_plan_id`, "
      "`wn_preset_lane`.`param`, "
      "`wn_preset_lane`.`direction`, "
      "`wn_preset_lane`.`is_del`, "
      "`wn_preset_lane`.`is_enable` ");

    r += "FROM `wn_preset_roi`";

    r += " INNER JOIN `wn_preset_lane` ON";
    // From nv_aimonitor31_view.h:89:5
    r += query_columns::PresetLane::roiId == query_columns::PresetRoi::id;

    if (!q.empty ())
    {
      r += " ";
      r += q.clause_prefix ();
      r += q;
    }

    return r;
  }

  result< access::view_traits_impl< ::db::ROILaneData, id_mysql >::view_type >
  access::view_traits_impl< ::db::ROILaneData, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_view<view_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.image_binding ());

    if (im.version != sts.image_version () || imb.version == 0)
    {
      bind (imb.bind, im);
      sts.image_version (im.version);
      imb.version++;
    }

    const query_base_type& qs (query_statement (q));
    qs.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        qs.clause (),
        false,
        true,
        qs.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::view_result_impl<view_type> > r (
      new (shared) mysql::view_result_impl<view_type> (
        qs, st, sts, 0));

    return result<view_type> (r);
  }

  // LaneLineData
  //

  bool access::view_traits_impl< ::db::LaneLineData, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // presetLaneLinePtr
    //
    if (object_traits_impl< ::db::PresetLaneLine, id_mysql >::grow (
          i.presetLaneLinePtr_value, t + 0UL))
      grew = true;

    return grew;
  }

  void access::view_traits_impl< ::db::LaneLineData, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i)
  {
    using namespace mysql;

    mysql::statement_kind sk (statement_select);
    ODB_POTENTIALLY_UNUSED (sk);

    std::size_t n (0);

    // presetLaneLinePtr
    //
    object_traits_impl< ::db::PresetLaneLine, id_mysql >::bind (
      b + n, i.presetLaneLinePtr_value, sk);
    n += 18UL;
  }

  void access::view_traits_impl< ::db::LaneLineData, id_mysql >::
  init (view_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    mysql::connection& conn (
      mysql::transaction::current ().connection (*db));

    // presetLaneLinePtr pre
    //
    typedef ::db::PresetLaneLine presetLaneLinePtr_object_type;
    typedef object_traits_impl<presetLaneLinePtr_object_type, id_mysql> presetLaneLinePtr_object_traits;
    typedef presetLaneLinePtr_object_traits::pointer_type presetLaneLinePtr_pointer_type;
    typedef presetLaneLinePtr_object_traits::pointer_traits presetLaneLinePtr_pointer_traits;
    typedef presetLaneLinePtr_object_traits::pointer_cache_traits presetLaneLinePtr_cache_traits;

    presetLaneLinePtr_object_traits::id_type presetLaneLinePtr_id;
    presetLaneLinePtr_pointer_type presetLaneLinePtr_p = 0;
    presetLaneLinePtr_pointer_traits::guard presetLaneLinePtr_pg;
    presetLaneLinePtr_cache_traits::insert_guard presetLaneLinePtr_ig;
    presetLaneLinePtr_object_type* presetLaneLinePtr_o (0);

    {
      if (!(i.presetLaneLinePtr_value.id_null))
      {
        presetLaneLinePtr_id = presetLaneLinePtr_object_traits::id (i.presetLaneLinePtr_value);
        presetLaneLinePtr_p = presetLaneLinePtr_cache_traits::find (*db, presetLaneLinePtr_id);

        if (presetLaneLinePtr_pointer_traits::null_ptr (presetLaneLinePtr_p))
        {
          presetLaneLinePtr_p = object_factory<presetLaneLinePtr_object_type, presetLaneLinePtr_pointer_type>::create ();
          presetLaneLinePtr_pg.reset (presetLaneLinePtr_p);
          presetLaneLinePtr_ig.reset (presetLaneLinePtr_cache_traits::insert (*db, presetLaneLinePtr_id, presetLaneLinePtr_p));
          presetLaneLinePtr_o = presetLaneLinePtr_pointer_traits::get_ptr (presetLaneLinePtr_p);
        }
      }
    }

    // presetLaneLinePtr
    //
    {
      if (presetLaneLinePtr_o != 0)
      {
        presetLaneLinePtr_object_traits::callback (*db, *presetLaneLinePtr_o, callback_event::pre_load);
        presetLaneLinePtr_object_traits::init (*presetLaneLinePtr_o, i.presetLaneLinePtr_value, db);
        presetLaneLinePtr_object_traits::statements_type& sts (
          conn.statement_cache ().find_object<presetLaneLinePtr_object_type> ());
        presetLaneLinePtr_object_traits::load_ (sts, *presetLaneLinePtr_o, false);
      }
    }

    // presetLaneLinePtr post
    //
    {
      if (presetLaneLinePtr_o != 0)
      {
        presetLaneLinePtr_object_traits::callback (*db, *presetLaneLinePtr_o, callback_event::post_load);
        presetLaneLinePtr_cache_traits::load (presetLaneLinePtr_ig.position ());
        presetLaneLinePtr_ig.release ();
        presetLaneLinePtr_pg.release ();
      }

      // If a compiler error points to the line below, then
      // it most likely means that a pointer used in view
      // member cannot be initialized from an object pointer.
      //
      o.presetLaneLinePtr = ::std::shared_ptr< ::db::PresetLaneLine > (
        std::move (presetLaneLinePtr_p));
    }
  }

  access::view_traits_impl< ::db::LaneLineData, id_mysql >::query_base_type
  access::view_traits_impl< ::db::LaneLineData, id_mysql >::
  query_statement (const query_base_type& q)
  {
    query_base_type r (
      "SELECT "
      "`wn_preset_lane_line`.`id`, "
      "`wn_preset_lane_line`.`preset_id`, "
      "`wn_preset_lane_line`.`line0`, "
      "`wn_preset_lane_line`.`line1`, "
      "`wn_preset_lane_line`.`line2`, "
      "`wn_preset_lane_line`.`line3`, "
      "`wn_preset_lane_line`.`line4`, "
      "`wn_preset_lane_line`.`line5`, "
      "`wn_preset_lane_line`.`line6`, "
      "`wn_preset_lane_line`.`line7`, "
      "`wn_preset_lane_line`.`line8`, "
      "`wn_preset_lane_line`.`line9`, "
      "`wn_preset_lane_line`.`line10`, "
      "`wn_preset_lane_line`.`line11`, "
      "`wn_preset_lane_line`.`line12`, "
      "`wn_preset_lane_line`.`line13`, "
      "`wn_preset_lane_line`.`line14`, "
      "`wn_preset_lane_line`.`line15` ");

    r += "FROM `wn_preset`";

    r += " INNER JOIN `wn_preset_lane_line` ON";
    // From nv_aimonitor31_view.h:96:5
    r += query_columns::PresetLaneLine::presetId == query_columns::Preset::id;

    if (!q.empty ())
    {
      r += " ";
      r += q.clause_prefix ();
      r += q;
    }

    return r;
  }

  result< access::view_traits_impl< ::db::LaneLineData, id_mysql >::view_type >
  access::view_traits_impl< ::db::LaneLineData, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_view<view_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.image_binding ());

    if (im.version != sts.image_version () || imb.version == 0)
    {
      bind (imb.bind, im);
      sts.image_version (im.version);
      imb.version++;
    }

    const query_base_type& qs (query_statement (q));
    qs.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        qs.clause (),
        false,
        true,
        qs.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::view_result_impl<view_type> > r (
      new (shared) mysql::view_result_impl<view_type> (
        qs, st, sts, 0));

    return result<view_type> (r);
  }

  // AlgoParamsData
  //

  bool access::view_traits_impl< ::db::AlgoParamsData, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // planId
    //
    t[0UL] = 0;

    // paramKey
    //
    if (t[1UL])
    {
      i.paramKey_value.capacity (i.paramKey_size);
      grew = true;
    }

    // paramValue
    //
    if (t[2UL])
    {
      i.paramValue_value.capacity (i.paramValue_size);
      grew = true;
    }

    return grew;
  }

  void access::view_traits_impl< ::db::AlgoParamsData, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i)
  {
    using namespace mysql;

    mysql::statement_kind sk (statement_select);
    ODB_POTENTIALLY_UNUSED (sk);

    std::size_t n (0);

    // planId
    //
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.planId_value;
    b[n].is_null = &i.planId_null;
    n++;

    // paramKey
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.paramKey_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.paramKey_value.capacity ());
    b[n].length = &i.paramKey_size;
    b[n].is_null = &i.paramKey_null;
    n++;

    // paramValue
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.paramValue_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.paramValue_value.capacity ());
    b[n].length = &i.paramValue_size;
    b[n].is_null = &i.paramValue_null;
    n++;
  }

  void access::view_traits_impl< ::db::AlgoParamsData, id_mysql >::
  init (view_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    // planId
    //
    {
      long unsigned int& v =
        o.planId;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.planId_value,
        i.planId_null);
    }

    // paramKey
    //
    {
      ::std::string& v =
        o.paramKey;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.paramKey_value,
        i.paramKey_size,
        i.paramKey_null);
    }

    // paramValue
    //
    {
      ::std::string& v =
        o.paramValue;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.paramValue_value,
        i.paramValue_size,
        i.paramValue_null);
    }
  }

  access::view_traits_impl< ::db::AlgoParamsData, id_mysql >::query_base_type
  access::view_traits_impl< ::db::AlgoParamsData, id_mysql >::
  query_statement (const query_base_type& q)
  {
    query_base_type r (
      "SELECT "
      "`plan_Id`, "
      "`key`, "
      "`param_value` ");

    r += "FROM `wn_algorithm_param`";

    r += " INNER JOIN `wn_alg_param_plan` ON";
    // From nv_aimonitor31_view.h:106:2
    r += query_columns::AlgParamPlan::paramId == query_columns::AlgorithmParam::id;

    if (!q.empty ())
    {
      r += " ";
      r += q.clause_prefix ();
      r += q;
    }

    return r;
  }

  result< access::view_traits_impl< ::db::AlgoParamsData, id_mysql >::view_type >
  access::view_traits_impl< ::db::AlgoParamsData, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_view<view_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.image_binding ());

    if (im.version != sts.image_version () || imb.version == 0)
    {
      bind (imb.bind, im);
      sts.image_version (im.version);
      imb.version++;
    }

    const query_base_type& qs (query_statement (q));
    qs.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        qs.clause (),
        false,
        true,
        qs.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::view_result_impl<view_type> > r (
      new (shared) mysql::view_result_impl<view_type> (
        qs, st, sts, 0));

    return result<view_type> (r);
  }

  // DetectPointVideoSourceData
  //

  bool access::view_traits_impl< ::db::DetectPointVideoSourceData, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // videoSourcePtr
    //
    if (object_traits_impl< ::db::VideoSource, id_mysql >::grow (
          i.videoSourcePtr_value, t + 0UL))
      grew = true;

    // detectPointPtr
    //
    if (object_traits_impl< ::db::DetectionPoint, id_mysql >::grow (
          i.detectPointPtr_value, t + 13UL))
      grew = true;

    return grew;
  }

  void access::view_traits_impl< ::db::DetectPointVideoSourceData, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i)
  {
    using namespace mysql;

    mysql::statement_kind sk (statement_select);
    ODB_POTENTIALLY_UNUSED (sk);

    std::size_t n (0);

    // videoSourcePtr
    //
    object_traits_impl< ::db::VideoSource, id_mysql >::bind (
      b + n, i.videoSourcePtr_value, sk);
    n += 13UL;

    // detectPointPtr
    //
    object_traits_impl< ::db::DetectionPoint, id_mysql >::bind (
      b + n, i.detectPointPtr_value, sk);
    n += 10UL;
  }

  void access::view_traits_impl< ::db::DetectPointVideoSourceData, id_mysql >::
  init (view_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    mysql::connection& conn (
      mysql::transaction::current ().connection (*db));

    // videoSourcePtr pre
    //
    typedef ::db::VideoSource videoSourcePtr_object_type;
    typedef object_traits_impl<videoSourcePtr_object_type, id_mysql> videoSourcePtr_object_traits;
    typedef videoSourcePtr_object_traits::pointer_type videoSourcePtr_pointer_type;
    typedef videoSourcePtr_object_traits::pointer_traits videoSourcePtr_pointer_traits;
    typedef videoSourcePtr_object_traits::pointer_cache_traits videoSourcePtr_cache_traits;

    videoSourcePtr_object_traits::id_type videoSourcePtr_id;
    videoSourcePtr_pointer_type videoSourcePtr_p = 0;
    videoSourcePtr_pointer_traits::guard videoSourcePtr_pg;
    videoSourcePtr_cache_traits::insert_guard videoSourcePtr_ig;
    videoSourcePtr_object_type* videoSourcePtr_o (0);

    {
      if (!(i.videoSourcePtr_value.id_null))
      {
        videoSourcePtr_id = videoSourcePtr_object_traits::id (i.videoSourcePtr_value);
        videoSourcePtr_p = videoSourcePtr_cache_traits::find (*db, videoSourcePtr_id);

        if (videoSourcePtr_pointer_traits::null_ptr (videoSourcePtr_p))
        {
          videoSourcePtr_p = object_factory<videoSourcePtr_object_type, videoSourcePtr_pointer_type>::create ();
          videoSourcePtr_pg.reset (videoSourcePtr_p);
          videoSourcePtr_ig.reset (videoSourcePtr_cache_traits::insert (*db, videoSourcePtr_id, videoSourcePtr_p));
          videoSourcePtr_o = videoSourcePtr_pointer_traits::get_ptr (videoSourcePtr_p);
        }
      }
    }

    // detectPointPtr pre
    //
    typedef ::db::DetectionPoint detectPointPtr_object_type;
    typedef object_traits_impl<detectPointPtr_object_type, id_mysql> detectPointPtr_object_traits;
    typedef detectPointPtr_object_traits::pointer_type detectPointPtr_pointer_type;
    typedef detectPointPtr_object_traits::pointer_traits detectPointPtr_pointer_traits;
    typedef detectPointPtr_object_traits::pointer_cache_traits detectPointPtr_cache_traits;

    detectPointPtr_object_traits::id_type detectPointPtr_id;
    detectPointPtr_pointer_type detectPointPtr_p = 0;
    detectPointPtr_pointer_traits::guard detectPointPtr_pg;
    detectPointPtr_cache_traits::insert_guard detectPointPtr_ig;
    detectPointPtr_object_type* detectPointPtr_o (0);

    {
      if (!(i.detectPointPtr_value.id_null))
      {
        detectPointPtr_id = detectPointPtr_object_traits::id (i.detectPointPtr_value);
        detectPointPtr_p = detectPointPtr_cache_traits::find (*db, detectPointPtr_id);

        if (detectPointPtr_pointer_traits::null_ptr (detectPointPtr_p))
        {
          detectPointPtr_p = object_factory<detectPointPtr_object_type, detectPointPtr_pointer_type>::create ();
          detectPointPtr_pg.reset (detectPointPtr_p);
          detectPointPtr_ig.reset (detectPointPtr_cache_traits::insert (*db, detectPointPtr_id, detectPointPtr_p));
          detectPointPtr_o = detectPointPtr_pointer_traits::get_ptr (detectPointPtr_p);
        }
      }
    }

    // videoSourcePtr
    //
    {
      if (videoSourcePtr_o != 0)
      {
        videoSourcePtr_object_traits::callback (*db, *videoSourcePtr_o, callback_event::pre_load);
        videoSourcePtr_object_traits::init (*videoSourcePtr_o, i.videoSourcePtr_value, db);
        videoSourcePtr_object_traits::statements_type& sts (
          conn.statement_cache ().find_object<videoSourcePtr_object_type> ());
        videoSourcePtr_object_traits::load_ (sts, *videoSourcePtr_o, false);
      }
    }

    // detectPointPtr
    //
    {
      if (detectPointPtr_o != 0)
      {
        detectPointPtr_object_traits::callback (*db, *detectPointPtr_o, callback_event::pre_load);
        detectPointPtr_object_traits::init (*detectPointPtr_o, i.detectPointPtr_value, db);
        detectPointPtr_object_traits::statements_type& sts (
          conn.statement_cache ().find_object<detectPointPtr_object_type> ());
        detectPointPtr_object_traits::load_ (sts, *detectPointPtr_o, false);
      }
    }

    // videoSourcePtr post
    //
    {
      if (videoSourcePtr_o != 0)
      {
        videoSourcePtr_object_traits::callback (*db, *videoSourcePtr_o, callback_event::post_load);
        videoSourcePtr_cache_traits::load (videoSourcePtr_ig.position ());
        videoSourcePtr_ig.release ();
        videoSourcePtr_pg.release ();
      }

      // If a compiler error points to the line below, then
      // it most likely means that a pointer used in view
      // member cannot be initialized from an object pointer.
      //
      o.videoSourcePtr = ::std::shared_ptr< ::db::VideoSource > (
        std::move (videoSourcePtr_p));
    }

    // detectPointPtr post
    //
    {
      if (detectPointPtr_o != 0)
      {
        detectPointPtr_object_traits::callback (*db, *detectPointPtr_o, callback_event::post_load);
        detectPointPtr_cache_traits::load (detectPointPtr_ig.position ());
        detectPointPtr_ig.release ();
        detectPointPtr_pg.release ();
      }

      // If a compiler error points to the line below, then
      // it most likely means that a pointer used in view
      // member cannot be initialized from an object pointer.
      //
      o.detectPointPtr = ::std::shared_ptr< ::db::DetectionPoint > (
        std::move (detectPointPtr_p));
    }
  }

  access::view_traits_impl< ::db::DetectPointVideoSourceData, id_mysql >::query_base_type
  access::view_traits_impl< ::db::DetectPointVideoSourceData, id_mysql >::
  query_statement (const query_base_type& q)
  {
    query_base_type r (
      "SELECT "
      "`wn_video_source`.`id`, "
      "`wn_video_source`.`name`, "
      "`wn_video_source`.`address`, "
      "`wn_video_source`.`access_front_end_id`, "
      "`wn_video_source`.`location`, "
      "`wn_video_source`.`has_ptz`, "
      "`wn_video_source`.`is_detectable`, "
      "`wn_video_source`.`detect_point_id`, "
      "`wn_video_source`.`ptz_control`, "
      "`wn_video_source`.`video_status`, "
      "`wn_video_source`.`road_id`, "
      "`wn_video_source`.`is_enable`, "
      "`wn_video_source`.`is_change`, "
      "`wn_detection_point`.`id`, "
      "`wn_detection_point`.`monitor_id`, "
      "`wn_detection_point`.`detection_point_name`, "
      "`wn_detection_point`.`video_id`, "
      "`wn_detection_point`.`project_id`, "
      "`wn_detection_point`.`stream_id`, "
      "`wn_detection_point`.`is_roll`, "
      "`wn_detection_point`.`is_del`, "
      "`wn_detection_point`.`is_enable`, "
      "`wn_detection_point`.`bind_video_id` ");

    r += "FROM `wn_detection_point`";

    r += " INNER JOIN `wn_video_source` ON";
    // From nv_aimonitor31_view.h:122:44
    r += query_columns::DetectionPoint::id == query_columns::VideoSource::detectPointId;

    if (!q.empty ())
    {
      r += " ";
      r += q.clause_prefix ();
      r += q;
    }

    return r;
  }

  result< access::view_traits_impl< ::db::DetectPointVideoSourceData, id_mysql >::view_type >
  access::view_traits_impl< ::db::DetectPointVideoSourceData, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_view<view_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.image_binding ());

    if (im.version != sts.image_version () || imb.version == 0)
    {
      bind (imb.bind, im);
      sts.image_version (im.version);
      imb.version++;
    }

    const query_base_type& qs (query_statement (q));
    qs.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        qs.clause (),
        false,
        true,
        qs.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::view_result_impl<view_type> > r (
      new (shared) mysql::view_result_impl<view_type> (
        qs, st, sts, 0));

    return result<view_type> (r);
  }
}

#include <odb/post.hxx>
