// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#ifndef PRESET_ROI_FEATURE_ODB_HXX
#define PRESET_ROI_FEATURE_ODB_HXX

#include <odb/version.hxx>


#include <odb/pre.hxx>

#include "preset_roi_feature.h"

#include <memory>
#include <cstddef>
#include <utility>

#include <odb/core.hxx>
#include <odb/traits.hxx>
#include <odb/callback.hxx>
#include <odb/wrapper-traits.hxx>
#include <odb/pointer-traits.hxx>
#include <odb/container-traits.hxx>
#include <odb/no-op-cache-traits.hxx>
#include <odb/result.hxx>
#include <odb/simple-object-result.hxx>

#include <odb/details/unused.hxx>
#include <odb/details/shared-ptr.hxx>

namespace odb
{
  // PresetRoiFeature
  //
  template <>
  struct class_traits< ::db::PresetRoiFeature >
  {
    static const class_kind kind = class_object;
  };

  template <>
  class access::object_traits< ::db::PresetRoiFeature >
  {
    public:
    typedef ::db::PresetRoiFeature object_type;
    typedef ::db::PresetRoiFeature* pointer_type;
    typedef odb::pointer_traits<pointer_type> pointer_traits;

    static const bool polymorphic = false;

    typedef long unsigned int id_type;

    static const bool auto_id = true;

    static const bool abstract = false;

    static id_type
    id (const object_type&);

    typedef
    no_op_pointer_cache_traits<pointer_type>
    pointer_cache_traits;

    typedef
    no_op_reference_cache_traits<object_type>
    reference_cache_traits;

    static void
    callback (database&, object_type&, callback_event);

    static void
    callback (database&, const object_type&, callback_event);
  };
}

#include <odb/details/buffer.hxx>

#include <odb/mysql/version.hxx>
#include <odb/mysql/forward.hxx>
#include <odb/mysql/binding.hxx>
#include <odb/mysql/mysql-types.hxx>
#include <odb/mysql/query.hxx>

namespace odb
{
  // PresetRoiFeature
  //
  template <typename A>
  struct query_columns< ::db::PresetRoiFeature, id_mysql, A >
  {
    // id
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    id_type_;

    static const id_type_ id;

    // roiId
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    roiId_type_;

    static const roiId_type_ roiId;

    // area
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    area_type_;

    static const area_type_ area;

    // beginTime
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    beginTime_type_;

    static const beginTime_type_ beginTime;

    // endTime
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    endTime_type_;

    static const endTime_type_ endTime;
  };

  template <typename A>
  const typename query_columns< ::db::PresetRoiFeature, id_mysql, A >::id_type_
  query_columns< ::db::PresetRoiFeature, id_mysql, A >::
  id (A::table_name, "`id`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetRoiFeature, id_mysql, A >::roiId_type_
  query_columns< ::db::PresetRoiFeature, id_mysql, A >::
  roiId (A::table_name, "`roi_id`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetRoiFeature, id_mysql, A >::area_type_
  query_columns< ::db::PresetRoiFeature, id_mysql, A >::
  area (A::table_name, "`area`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetRoiFeature, id_mysql, A >::beginTime_type_
  query_columns< ::db::PresetRoiFeature, id_mysql, A >::
  beginTime (A::table_name, "`begin_time`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetRoiFeature, id_mysql, A >::endTime_type_
  query_columns< ::db::PresetRoiFeature, id_mysql, A >::
  endTime (A::table_name, "`end_time`", 0);

  template <typename A>
  struct pointer_query_columns< ::db::PresetRoiFeature, id_mysql, A >:
    query_columns< ::db::PresetRoiFeature, id_mysql, A >
  {
  };

  template <>
  class access::object_traits_impl< ::db::PresetRoiFeature, id_mysql >:
    public access::object_traits< ::db::PresetRoiFeature >
  {
    public:
    struct id_image_type
    {
      unsigned long long id_value;
      my_bool id_null;

      std::size_t version;
    };

    struct image_type
    {
      // id
      //
      unsigned long long id_value;
      my_bool id_null;

      // roiId
      //
      unsigned long long roiId_value;
      my_bool roiId_null;

      // area
      //
      details::buffer area_value;
      unsigned long area_size;
      my_bool area_null;

      // beginTime
      //
      details::buffer beginTime_value;
      unsigned long beginTime_size;
      my_bool beginTime_null;

      // endTime
      //
      details::buffer endTime_value;
      unsigned long endTime_size;
      my_bool endTime_null;

      std::size_t version;
    };

    struct extra_statement_cache_type;

    using object_traits<object_type>::id;

    static id_type
    id (const id_image_type&);

    static id_type
    id (const image_type&);

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&,
          mysql::statement_kind);

    static void
    bind (MYSQL_BIND*, id_image_type&);

    static bool
    init (image_type&,
          const object_type&,
          mysql::statement_kind);

    static void
    init (object_type&,
          const image_type&,
          database*);

    static void
    init (id_image_type&, const id_type&);

    typedef mysql::object_statements<object_type> statements_type;

    typedef mysql::query_base query_base_type;

    static const std::size_t column_count = 5UL;
    static const std::size_t id_column_count = 1UL;
    static const std::size_t inverse_column_count = 0UL;
    static const std::size_t readonly_column_count = 0UL;
    static const std::size_t managed_optimistic_column_count = 0UL;

    static const std::size_t separate_load_column_count = 0UL;
    static const std::size_t separate_update_column_count = 0UL;

    static const bool versioned = false;

    static const char persist_statement[];
    static const char find_statement[];
    static const char update_statement[];
    static const char erase_statement[];
    static const char query_statement[];
    static const char erase_query_statement[];

    static const char table_name[];

    static void
    persist (database&, object_type&);

    static pointer_type
    find (database&, const id_type&);

    static bool
    find (database&, const id_type&, object_type&);

    static bool
    reload (database&, object_type&);

    static void
    update (database&, const object_type&);

    static void
    erase (database&, const id_type&);

    static void
    erase (database&, const object_type&);

    static result<object_type>
    query (database&, const query_base_type&);

    static unsigned long long
    erase_query (database&, const query_base_type&);

    public:
    static bool
    find_ (statements_type&,
           const id_type*);

    static void
    load_ (statements_type&,
           object_type&,
           bool reload);
  };

  template <>
  class access::object_traits_impl< ::db::PresetRoiFeature, id_common >:
    public access::object_traits_impl< ::db::PresetRoiFeature, id_mysql >
  {
  };

  // PresetRoiFeature
  //
}

#include "preset_roi_feature-odb.ixx"

#include <odb/post.hxx>

#endif // PRESET_ROI_FEATURE_ODB_HXX
