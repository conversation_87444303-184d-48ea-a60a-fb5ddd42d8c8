/**
 * Project FVM
 */

#pragma once
#include "stream_output.h"
#include <chrono>

/**
 * @brief: 视频录像器
 */
namespace fvm::stream 
{
    //录像类型
    enum class RecorderType 
    {
        Event,          //事件型录像
        Task,           //后台记录型录像
        Cmd             //命令行保存录像
    };

    //录像扩展名
    enum class ExtensionType
    {
        MP4,            //MP4
        FLV             //FLV
    };

    //录像抽象基类
    class Recorder
    {
    public:
        Recorder(RecorderType myType);

        virtual ~Recorder(void);

        //设置ffmpeg一些参数
        void setParameters(CodecInfoPtr pCodecInfo);

        //发数据
        void pushData(const PacketDataPtr packet);

        //设置缓存数据
        void setListData(std::list<PacketDataPtr> datas);

        //录像是否完成
        virtual bool isFinished(void);

        //创建录像过程 基类实现
        virtual bool createRecord(void) = 0;

        //检查录像结束过程 基类实现
        virtual void checkFinished(void) = 0;

        //创建录像文件
        bool createFile(std::string szFile, ExtensionType myType = ExtensionType::FLV);

        //关闭释放文件
        void closeFile(void);

        //录像主逻辑过程
        void process(void);

        //返回录像类型
        inline RecorderType getRecorderType(void)
        {
            return recordType;
        }

        //获取帧率
        inline int getFrameRateNum(void)
        {
            return frameRateNum;
        }
    private:
        //安全检查获取pkt checkIFrame是否检查I帧
        AVPacket* getPacketData(bool checkIFrame);

        //保存写数据
        void writePacket(AVPacket* pkt);
    protected:
        int frameIndex = 0;                             //当前帧数号
        int writeErrorNum = 0;                          //连续错误次数
        int64_t recordLength = 0;                       //录像文件大小 单位B
    private:
        RecorderType recordType;                        //录像类型
        bool firstIFrameFound = false;                  //判断第一帧写I帧
        std::atomic_bool bFileCreated = false;          //文件是否创建
        std::atomic_bool bFinishState = false;          //录像是否结束

        //ffmmpeg参数相关
        AVCodecParameters* codecpar = nullptr;
        AVRational timebase, frameRate, dstTb;
        int frameRateNum = 25;                          //计算的帧率 默认25
        AVFormatContext* formatCtx = nullptr;
        AVStream* outStream = nullptr;
        int64_t ptsStart = 0;
        int64_t dtsStart = 0;

        //录像缓存数据相关
        std::list<PacketDataPtr> packets;               //录像缓存数据
        boost::fibers::mutex packetsMutex;
    };

    //事件型录像
    class EventRecorder :public Recorder
    {
    public:
        /**
        * @brief                        构造函数
        * @param[in] localPath          本地录像路径
        * @param[in] webUrl             完成录像提交web的url
        * @param[in] iBeforeTime        预录像时长
        * @param[in] iAfterTime         发生后录像时长
        * @param[in] pCodecInfo         解码相关参数
        */
        EventRecorder(std::string localPath, std::string webUrl, int iBeforeTime, int iAfterTime, CodecInfoPtr pCodecInfo);

        bool createRecord(void) override;

        void checkFinished(void) override;

        /**
        * @brief                        是否可追加
        * @param[in] seconds            可追加时长 单位秒
        */
        bool isAddible(int seconds);

        /**
        * @brief                        追加多少秒录像
        * @param[in] seconds            可追加时长 单位秒
        */
        void addTime(int seconds);

        //录像文件最大时间 (单位 秒)
        void setMaxRecordTime(int time = 100);

        /**
        * @brief                        获取录像地址
        */
        inline std::string urlPath(void)
        {
            return szWeburl;
        }
    private:
        /**
        * @brief                        处理文件夹的创建
        * @param[in] address            实际录像系统的前缀路径
        * @param[in] szWeburl           web传来的路径
        * @param[out]                   实际录像文件的绝对地址
        */
        std::string createDir(const std::string& address, const std::string& szWeburl);

    private:
        std::string szWeburl;                           //web的url路径
        std::string szLocalFile;                        //本地写入路径
        int beforeTime = 0;                             //预录像时长
        int afterTime = 0;                              //发生后录像时长
        int videoLength = 0;                            //录像总帧数 (帧率x秒得到)
        int maxLength = 0;                              //录像最大帧数
    };

    //后台记录型录像 类似nvr录像
    class TaskRecorder :public Recorder
    {
    public:
        /**
        * @brief                       构造函数
        * @param[in] iChannelId        通道id
        * @param[in] begintime         开始录像时刻 类似 xx:xx 如 00:00
        * @param[in] endtime           结束录像时刻 类似 xx:xx 如 23:59
        * @param[in] iDays             保留天数
        * @param[in] iSection          录像文件分段间隔(单位 分钟)
        */
        TaskRecorder(int iChannelId, std::string begintime, std::string endtime, int iDays, int iSection);

        bool isFinished(void) override;

        bool createRecord(void) override;

        void checkFinished(void) override;
    private:
        //检查录像时间范围条件 注意类似NVR的设置 开始时刻<结束时刻
        bool checkTime(void);

        //创建录像名称
        std::string createFileName(void);

        //清理过期视频
        void cleanOldVideo(std::string recordPath);
    private:
        int begintimeSec = 0;                           //开始录像时刻
        int endtimeSec = 0;                             //结束录像时刻
        int days = 0;                                   //保留录像天数
        int section = 0;                                //间隔自动分段 单位分钟
        int filesize = 0;                               //录像文件大小
        int channelId = 0;                              //通道id用来创建文件夹
        std::string recordFileName;                     //录像文件名
        std::chrono::steady_clock::time_point startPoint;
    };

    //命令行保存录像
    class CmdRecorder :public Recorder
    {
    public:
        /**
        * @brief                        构造函数
        * @param[in] iChannelId         通道号
        * @param[in] iSeconds           单位秒
        * @param[in] pCodecInfo         解码相关参数
        */
        CmdRecorder(int iChannelId, int iSeconds, CodecInfoPtr pCodecInfo);

        bool createRecord(void) override;

        void checkFinished(void) override;

        //设置录像时长
        void setSeconds(int iSeconds);

    private:
        //创建录像名称
        std::string createFileName(void);

    private:
        int videoLength = 0;                            //录像总帧数 (帧率x秒得到)
        int channelId = 0;                              //通道id用来创建文件夹
        int seconds = 0;                                //录像秒用来创建文件夹
    };

    //录像功能模块
    class VideoRecorder : public StreamOutput
    {
    public:
        //构造函数
        VideoRecorder(StreamOuputType streamType);

        //发数据
        void pushData(const PacketDataPtr packet) override;

        //设置通道ID
        void setChannelId(int iChannelId);

        //设置预录像和事件录像时长
        void setBeforeAfterTime(int iBeforeTime, int iAfterTime);

        //事件开始录像
        std::string eventStart(std::string szWebUrl);

        //命令行保存录像
        bool cmdSaveStart(int second = 30);

        //命令行录像手动停止
        bool cmdSaveStop(void);

        //后台记录录像(通道ID设置好后 创建)
        void bgRecordStart(void);

    private:
        //预录像时长
        int beforeTime = 10;

        //事件发生后录像时长
        int afterTime = 30;

        //处理函数
        void process() override;

        unsigned int maxBufferSize()override  {return 2 *beforeBufNum;}
        unsigned int minBufferSize()override {return beforeBufNum; }
        /*
         * 任务类别
         */
        worker::WorkerType workerType() override { return worker::WorkerType::Record; }
    private:
        //日志型录像
        std::shared_ptr<TaskRecorder> taskRecorder = nullptr;

        //事件型录像对象
        std::vector<std::shared_ptr<Recorder>> recorders;
        boost::fibers::mutex recorderMutex;

        // 预录像 缓存帧数
        unsigned int  beforeBufNum = 500;
        int channelId = 0;
    };
}


