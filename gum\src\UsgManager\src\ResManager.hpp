#ifndef RESMANAGER_HPP_
#define RESMANAGER_HPP_

#include <boost/utility.hpp>
#include <boost/multi_index_container.hpp>
#include <boost/multi_index/member.hpp>
#include <boost/multi_index/ordered_index.hpp>
#include <boost/filesystem.hpp>
#include <boost/thread/mutex.hpp>
#include <boost/thread/condition.hpp>

#include "UsgManager/include/UsgManagerCfg.hpp"
#include "UsgManager/include/UsgManagerItf.hpp"
#include "UsgManager/src/CfgConfigFile.hpp"

using boost::multi_index_container;
using namespace boost::multi_index;

namespace usg
{

//资源多索引容器，这里简单的使用了boost::multi_index
    struct RESID{}; //资源UUID索引
    struct RESSIPCODE{}; //资源SIPCODE索引
    struct RESCHANNEL{};//资源通道号索引

    typedef
    boost::multi_index_container<
            SResInfo,
            indexed_by<
            ordered_unique<
            tag<RESID>,  BOOST_MULTI_INDEX_MEMBER(SResInfo,boost::uuids::uuid,resId)>,
    ordered_unique<
            tag<RESSIPCODE>, BOOST_MULTI_INDEX_MEMBER(SResInfo,std::string,sipResCode)>,
    ordered_unique<
            tag<RESCHANNEL>, BOOST_MULTI_INDEX_MEMBER(SResInfo,uint32_t,channelIndex)>
    >
    > ResContainer;


    class USGMANAGER_PRIVATE CResManager : private boost::noncopyable
    {
    public:
        CResManager( uint32_t dbId,  const std::string& szSipId, const std::string& szAddr, EPackerType type );
        ~CResManager();

    public:
        bool init(FUNC_INSERTVIDEO_CALLBACK func1,
                  FUNC_UPDATEREMOTESTATUS_CALLBACK func2,
                  FUNC_UPDATEVIDEONAME_CALLBACK func3 );
        bool fini();

        bool addVideo( const std::string& szResCode, const std::string& szName, uint32_t id );
        bool getVideoStatus( const std::string& szResCode );
        void onRegist(  int expires );
        int getExpires();
        void keepAlive();
        bool isAlive();
        //接受SgService的资源信息后判断处理函数
        bool onReceiveCatalog( const SCatalog& info );

        bool getResInfoByUuid( const boost::uuids::uuid& id, SResInfo& info );
        bool getResInfoByChannel( const uint16_t channel, SResInfo& info );
        bool getResInfoBySipCode( const std::string& code, SResInfo& info );
        bool getAllResInfo( std::map<boost::uuids::uuid, SResInfo >& mapRes, uint32_t& timeStamp );
        bool onLinkDown();

        bool updateLowerStatus( bool online );
        bool isRegist();
        void setRegist( bool bValue );
        void waitRegist();
        std::string getSipId();
        std::string getSipUrl(std::string res = "");
        std::string getRemoteAddr();
        bool isSubscribeCatalog();
        void setSubscribeCatalog( bool bValue );
        //测试用接口
//    bool onReceiveCatalog( const ESgResNotifyAction action, const SResInfo& info );

    private:
        bool onAddRes( const SResInfo& resInfo );
        bool onDelRes( const SResInfo& resInfo );
        bool onModifyRes( const SResInfo& resInfo );

        bool OnSgResNotify( const std::map<boost::uuids::uuid, SResInfo >& mapRes );

        uint32_t getTimeStamp( uint32_t& timeStamp );

//    void onReceiveCatalogThread();
        bool onReceiveCatalogInner( const SCatalog& info );
        bool updatePtzInfo();

        void keepAliveThread();
    private:
        bool m_bKeepExit;
        boost::thread m_keepThread;
        std::string   m_resFileName;
        ResContainer  m_resContainer;
        CCfgConfigFile m_cfgConfigFile;
        rwmutex  /*m_sipResFileLock, */m_sipResInfoLock;
        uint16_t m_currentChannel; //记录通道号，通道号小于65535则每次新增通道加1
        uint32_t  m_iDbId;			//前端所在数据库的索引
        std::string m_szSipId;			//平台的SIP编号
        std::string m_szRemoteAddr;	//平台的地址端口
        std::set<uint16_t> m_currentUseChannnels;//
        uint32_t   m_timeStamp;
        //   rwmutex    m_timeStampLock;
//    rwmutex    m_currentChannelLock;
//    std::list< SCatalog > m_receiveCatalog;
        typedef boost::unique_lock< boost::mutex > lock_type;
//    boost::mutex  m_mutexList;
//    boost::condition m_cond;
//    volatile bool m_threadForceStop;
//    boost::thread *m_thread;
        EPackerType    m_packerType;

        boost::mutex  m_mutexReg,m_mutexKeepalive;
        boost::condition m_keepaliveCond,m_registerCond;

        volatile bool m_isRegist;
        volatile bool m_KeepaliveOk;

        boost::posix_time::ptime m_keepAliveTime;///<记录保活上报的时间
        int m_expries;		//保活时间
        bool m_isSubscribeCatalog; //是否需要订阅目录

        usg::IInviteSession *m_inviteSession;
        usg::INotifySession *m_notifySession;
        usg::IRegistSession *m_registSession;
        usg::IDdcpDoSession *m_ddcpDoSession;

        FUNC_INSERTVIDEO_CALLBACK m_funcInsertVideo;
        FUNC_UPDATEREMOTESTATUS_CALLBACK m_funcUpdateRemoteStatus;
        FUNC_UPDATEVIDEONAME_CALLBACK m_funcUpdateVideoName;

        class CModifyResInfo
        {
        public:
            CModifyResInfo( const SResInfo& info ):m_info( info ){}
            ~CModifyResInfo(){}

            void operator()( CModifyResInfo& stu )
            {
                stu = m_info;
            }

        private:
            SResInfo m_info;
        };
    };

}

#endif
