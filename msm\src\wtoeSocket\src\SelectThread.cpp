#include "Utility.h"
#include "SelectThread.h"
#include <stdio.h>


SelectThread::SelectThread(void):Thread(false)
{
	SetDeleteOnExit(false);
}

SelectThread::~SelectThread(void)
{
	printf("woteSocketUdp SelectThread::~ woteSocketUdp SelectThread(void)\n");
}

void SelectThread::Run()
{
	
	while (IsRunning())
	{
		if(m_h.GetCount())
		{
			m_h.Select(1,0);
		}
		else{

			Utility::Sleep(100);
		}
	}
}
