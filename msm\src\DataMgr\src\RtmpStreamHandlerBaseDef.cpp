#include "RtmpStreamHandlerBaseDef.hpp"

namespace msm
{
    #define ERR_INFO_SIZE   64

    std::string err2str( int32_t err )
    {
        char szErrInfo[ERR_INFO_SIZE]={0};
        av_make_error_string( szErrInfo, ERR_INFO_SIZE, err);
        return std::string(szErrInfo);
    }

    int32_t lockmgr( void **mtx, enum AVLockOp op )
    {
        switch( op ) 
        {
        case AV_LOCK_CREATE:
            (*mtx) = new (std::nothrow) boost::mutex;
            if(!*mtx)
                return 1;
            return 0;
        case AV_LOCK_OBTAIN:
            ((boost::mutex*)(*mtx))->lock();
            return 0;
        case AV_LOCK_RELEASE:
            ((boost::mutex*)(*mtx))->unlock();
            return 0;
        case AV_LOCK_DESTROY:
            delete (*mtx);
            (*mtx) = 0;
            return 0;
        }
        return 1;
    }

}
