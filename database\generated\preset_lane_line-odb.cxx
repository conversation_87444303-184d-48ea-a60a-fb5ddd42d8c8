// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#include <odb/pre.hxx>

#include "preset_lane_line-odb.hxx"

#include <cassert>
#include <cstring>  // std::memcpy


#include <odb/mysql/traits.hxx>
#include <odb/mysql/database.hxx>
#include <odb/mysql/transaction.hxx>
#include <odb/mysql/connection.hxx>
#include <odb/mysql/statement.hxx>
#include <odb/mysql/statement-cache.hxx>
#include <odb/mysql/simple-object-statements.hxx>
#include <odb/mysql/container-statements.hxx>
#include <odb/mysql/exceptions.hxx>
#include <odb/mysql/simple-object-result.hxx>
#include <odb/mysql/enum.hxx>

namespace odb
{
  // PresetLaneLine
  //

  struct access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::extra_statement_cache_type
  {
    extra_statement_cache_type (
      mysql::connection&,
      image_type&,
      id_image_type&,
      mysql::binding&,
      mysql::binding&)
    {
    }
  };

  access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::id_type
  access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  id (const id_image_type& i)
  {
    mysql::database* db (0);
    ODB_POTENTIALLY_UNUSED (db);

    id_type id;
    {
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        id,
        i.id_value,
        i.id_null);
    }

    return id;
  }

  access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::id_type
  access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  id (const image_type& i)
  {
    mysql::database* db (0);
    ODB_POTENTIALLY_UNUSED (db);

    id_type id;
    {
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        id,
        i.id_value,
        i.id_null);
    }

    return id;
  }

  bool access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // id
    //
    t[0UL] = 0;

    // presetId
    //
    t[1UL] = 0;

    // line0
    //
    if (t[2UL])
    {
      i.line0_value.capacity (i.line0_size);
      grew = true;
    }

    // line1
    //
    if (t[3UL])
    {
      i.line1_value.capacity (i.line1_size);
      grew = true;
    }

    // line2
    //
    if (t[4UL])
    {
      i.line2_value.capacity (i.line2_size);
      grew = true;
    }

    // line3
    //
    if (t[5UL])
    {
      i.line3_value.capacity (i.line3_size);
      grew = true;
    }

    // line4
    //
    if (t[6UL])
    {
      i.line4_value.capacity (i.line4_size);
      grew = true;
    }

    // line5
    //
    if (t[7UL])
    {
      i.line5_value.capacity (i.line5_size);
      grew = true;
    }

    // line6
    //
    if (t[8UL])
    {
      i.line6_value.capacity (i.line6_size);
      grew = true;
    }

    // line7
    //
    if (t[9UL])
    {
      i.line7_value.capacity (i.line7_size);
      grew = true;
    }

    // line8
    //
    if (t[10UL])
    {
      i.line8_value.capacity (i.line8_size);
      grew = true;
    }

    // line9
    //
    if (t[11UL])
    {
      i.line9_value.capacity (i.line9_size);
      grew = true;
    }

    // line10
    //
    if (t[12UL])
    {
      i.line10_value.capacity (i.line10_size);
      grew = true;
    }

    // line11
    //
    if (t[13UL])
    {
      i.line11_value.capacity (i.line11_size);
      grew = true;
    }

    // line12
    //
    if (t[14UL])
    {
      i.line12_value.capacity (i.line12_size);
      grew = true;
    }

    // line13
    //
    if (t[15UL])
    {
      i.line13_value.capacity (i.line13_size);
      grew = true;
    }

    // line14
    //
    if (t[16UL])
    {
      i.line14_value.capacity (i.line14_size);
      grew = true;
    }

    // line15
    //
    if (t[17UL])
    {
      i.line15_value.capacity (i.line15_size);
      grew = true;
    }

    return grew;
  }

  void access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i,
        mysql::statement_kind sk)
  {
    ODB_POTENTIALLY_UNUSED (sk);

    using namespace mysql;

    std::size_t n (0);

    // id
    //
    if (sk != statement_update)
    {
      b[n].buffer_type = MYSQL_TYPE_LONGLONG;
      b[n].is_unsigned = 1;
      b[n].buffer = &i.id_value;
      b[n].is_null = &i.id_null;
      n++;
    }

    // presetId
    //
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.presetId_value;
    b[n].is_null = &i.presetId_null;
    n++;

    // line0
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line0_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line0_value.capacity ());
    b[n].length = &i.line0_size;
    b[n].is_null = &i.line0_null;
    n++;

    // line1
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line1_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line1_value.capacity ());
    b[n].length = &i.line1_size;
    b[n].is_null = &i.line1_null;
    n++;

    // line2
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line2_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line2_value.capacity ());
    b[n].length = &i.line2_size;
    b[n].is_null = &i.line2_null;
    n++;

    // line3
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line3_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line3_value.capacity ());
    b[n].length = &i.line3_size;
    b[n].is_null = &i.line3_null;
    n++;

    // line4
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line4_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line4_value.capacity ());
    b[n].length = &i.line4_size;
    b[n].is_null = &i.line4_null;
    n++;

    // line5
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line5_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line5_value.capacity ());
    b[n].length = &i.line5_size;
    b[n].is_null = &i.line5_null;
    n++;

    // line6
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line6_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line6_value.capacity ());
    b[n].length = &i.line6_size;
    b[n].is_null = &i.line6_null;
    n++;

    // line7
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line7_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line7_value.capacity ());
    b[n].length = &i.line7_size;
    b[n].is_null = &i.line7_null;
    n++;

    // line8
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line8_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line8_value.capacity ());
    b[n].length = &i.line8_size;
    b[n].is_null = &i.line8_null;
    n++;

    // line9
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line9_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line9_value.capacity ());
    b[n].length = &i.line9_size;
    b[n].is_null = &i.line9_null;
    n++;

    // line10
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line10_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line10_value.capacity ());
    b[n].length = &i.line10_size;
    b[n].is_null = &i.line10_null;
    n++;

    // line11
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line11_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line11_value.capacity ());
    b[n].length = &i.line11_size;
    b[n].is_null = &i.line11_null;
    n++;

    // line12
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line12_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line12_value.capacity ());
    b[n].length = &i.line12_size;
    b[n].is_null = &i.line12_null;
    n++;

    // line13
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line13_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line13_value.capacity ());
    b[n].length = &i.line13_size;
    b[n].is_null = &i.line13_null;
    n++;

    // line14
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line14_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line14_value.capacity ());
    b[n].length = &i.line14_size;
    b[n].is_null = &i.line14_null;
    n++;

    // line15
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.line15_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.line15_value.capacity ());
    b[n].length = &i.line15_size;
    b[n].is_null = &i.line15_null;
    n++;
  }

  void access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  bind (MYSQL_BIND* b, id_image_type& i)
  {
    std::size_t n (0);
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.id_value;
    b[n].is_null = &i.id_null;
  }

  bool access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  init (image_type& i,
        const object_type& o,
        mysql::statement_kind sk)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (sk);

    using namespace mysql;

    bool grew (false);

    // id
    //
    if (sk == statement_insert)
    {
      long unsigned int const& v =
        o.id;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.id_value, is_null, v);
      i.id_null = is_null;
    }

    // presetId
    //
    {
      long unsigned int const& v =
        o.presetId;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.presetId_value, is_null, v);
      i.presetId_null = is_null;
    }

    // line0
    //
    {
      ::std::string const& v =
        o.line0;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line0_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line0_value,
        size,
        is_null,
        v);
      i.line0_null = is_null;
      i.line0_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line0_value.capacity ());
    }

    // line1
    //
    {
      ::std::string const& v =
        o.line1;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line1_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line1_value,
        size,
        is_null,
        v);
      i.line1_null = is_null;
      i.line1_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line1_value.capacity ());
    }

    // line2
    //
    {
      ::std::string const& v =
        o.line2;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line2_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line2_value,
        size,
        is_null,
        v);
      i.line2_null = is_null;
      i.line2_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line2_value.capacity ());
    }

    // line3
    //
    {
      ::std::string const& v =
        o.line3;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line3_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line3_value,
        size,
        is_null,
        v);
      i.line3_null = is_null;
      i.line3_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line3_value.capacity ());
    }

    // line4
    //
    {
      ::std::string const& v =
        o.line4;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line4_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line4_value,
        size,
        is_null,
        v);
      i.line4_null = is_null;
      i.line4_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line4_value.capacity ());
    }

    // line5
    //
    {
      ::std::string const& v =
        o.line5;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line5_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line5_value,
        size,
        is_null,
        v);
      i.line5_null = is_null;
      i.line5_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line5_value.capacity ());
    }

    // line6
    //
    {
      ::std::string const& v =
        o.line6;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line6_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line6_value,
        size,
        is_null,
        v);
      i.line6_null = is_null;
      i.line6_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line6_value.capacity ());
    }

    // line7
    //
    {
      ::std::string const& v =
        o.line7;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line7_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line7_value,
        size,
        is_null,
        v);
      i.line7_null = is_null;
      i.line7_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line7_value.capacity ());
    }

    // line8
    //
    {
      ::std::string const& v =
        o.line8;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line8_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line8_value,
        size,
        is_null,
        v);
      i.line8_null = is_null;
      i.line8_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line8_value.capacity ());
    }

    // line9
    //
    {
      ::std::string const& v =
        o.line9;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line9_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line9_value,
        size,
        is_null,
        v);
      i.line9_null = is_null;
      i.line9_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line9_value.capacity ());
    }

    // line10
    //
    {
      ::std::string const& v =
        o.line10;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line10_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line10_value,
        size,
        is_null,
        v);
      i.line10_null = is_null;
      i.line10_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line10_value.capacity ());
    }

    // line11
    //
    {
      ::std::string const& v =
        o.line11;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line11_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line11_value,
        size,
        is_null,
        v);
      i.line11_null = is_null;
      i.line11_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line11_value.capacity ());
    }

    // line12
    //
    {
      ::std::string const& v =
        o.line12;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line12_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line12_value,
        size,
        is_null,
        v);
      i.line12_null = is_null;
      i.line12_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line12_value.capacity ());
    }

    // line13
    //
    {
      ::std::string const& v =
        o.line13;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line13_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line13_value,
        size,
        is_null,
        v);
      i.line13_null = is_null;
      i.line13_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line13_value.capacity ());
    }

    // line14
    //
    {
      ::std::string const& v =
        o.line14;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line14_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line14_value,
        size,
        is_null,
        v);
      i.line14_null = is_null;
      i.line14_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line14_value.capacity ());
    }

    // line15
    //
    {
      ::std::string const& v =
        o.line15;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.line15_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.line15_value,
        size,
        is_null,
        v);
      i.line15_null = is_null;
      i.line15_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.line15_value.capacity ());
    }

    return grew;
  }

  void access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  init (object_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    // id
    //
    {
      long unsigned int& v =
        o.id;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.id_value,
        i.id_null);
    }

    // presetId
    //
    {
      long unsigned int& v =
        o.presetId;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.presetId_value,
        i.presetId_null);
    }

    // line0
    //
    {
      ::std::string& v =
        o.line0;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line0_value,
        i.line0_size,
        i.line0_null);
    }

    // line1
    //
    {
      ::std::string& v =
        o.line1;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line1_value,
        i.line1_size,
        i.line1_null);
    }

    // line2
    //
    {
      ::std::string& v =
        o.line2;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line2_value,
        i.line2_size,
        i.line2_null);
    }

    // line3
    //
    {
      ::std::string& v =
        o.line3;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line3_value,
        i.line3_size,
        i.line3_null);
    }

    // line4
    //
    {
      ::std::string& v =
        o.line4;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line4_value,
        i.line4_size,
        i.line4_null);
    }

    // line5
    //
    {
      ::std::string& v =
        o.line5;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line5_value,
        i.line5_size,
        i.line5_null);
    }

    // line6
    //
    {
      ::std::string& v =
        o.line6;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line6_value,
        i.line6_size,
        i.line6_null);
    }

    // line7
    //
    {
      ::std::string& v =
        o.line7;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line7_value,
        i.line7_size,
        i.line7_null);
    }

    // line8
    //
    {
      ::std::string& v =
        o.line8;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line8_value,
        i.line8_size,
        i.line8_null);
    }

    // line9
    //
    {
      ::std::string& v =
        o.line9;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line9_value,
        i.line9_size,
        i.line9_null);
    }

    // line10
    //
    {
      ::std::string& v =
        o.line10;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line10_value,
        i.line10_size,
        i.line10_null);
    }

    // line11
    //
    {
      ::std::string& v =
        o.line11;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line11_value,
        i.line11_size,
        i.line11_null);
    }

    // line12
    //
    {
      ::std::string& v =
        o.line12;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line12_value,
        i.line12_size,
        i.line12_null);
    }

    // line13
    //
    {
      ::std::string& v =
        o.line13;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line13_value,
        i.line13_size,
        i.line13_null);
    }

    // line14
    //
    {
      ::std::string& v =
        o.line14;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line14_value,
        i.line14_size,
        i.line14_null);
    }

    // line15
    //
    {
      ::std::string& v =
        o.line15;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.line15_value,
        i.line15_size,
        i.line15_null);
    }
  }

  void access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  init (id_image_type& i, const id_type& id)
  {
    {
      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.id_value, is_null, id);
      i.id_null = is_null;
    }
  }

  const char access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::persist_statement[] =
  "INSERT INTO `wn_preset_lane_line` "
  "(`id`, "
  "`preset_id`, "
  "`line0`, "
  "`line1`, "
  "`line2`, "
  "`line3`, "
  "`line4`, "
  "`line5`, "
  "`line6`, "
  "`line7`, "
  "`line8`, "
  "`line9`, "
  "`line10`, "
  "`line11`, "
  "`line12`, "
  "`line13`, "
  "`line14`, "
  "`line15`) "
  "VALUES "
  "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

  const char access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::find_statement[] =
  "SELECT "
  "`wn_preset_lane_line`.`id`, "
  "`wn_preset_lane_line`.`preset_id`, "
  "`wn_preset_lane_line`.`line0`, "
  "`wn_preset_lane_line`.`line1`, "
  "`wn_preset_lane_line`.`line2`, "
  "`wn_preset_lane_line`.`line3`, "
  "`wn_preset_lane_line`.`line4`, "
  "`wn_preset_lane_line`.`line5`, "
  "`wn_preset_lane_line`.`line6`, "
  "`wn_preset_lane_line`.`line7`, "
  "`wn_preset_lane_line`.`line8`, "
  "`wn_preset_lane_line`.`line9`, "
  "`wn_preset_lane_line`.`line10`, "
  "`wn_preset_lane_line`.`line11`, "
  "`wn_preset_lane_line`.`line12`, "
  "`wn_preset_lane_line`.`line13`, "
  "`wn_preset_lane_line`.`line14`, "
  "`wn_preset_lane_line`.`line15` "
  "FROM `wn_preset_lane_line` "
  "WHERE `wn_preset_lane_line`.`id`=?";

  const char access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::update_statement[] =
  "UPDATE `wn_preset_lane_line` "
  "SET "
  "`preset_id`=?, "
  "`line0`=?, "
  "`line1`=?, "
  "`line2`=?, "
  "`line3`=?, "
  "`line4`=?, "
  "`line5`=?, "
  "`line6`=?, "
  "`line7`=?, "
  "`line8`=?, "
  "`line9`=?, "
  "`line10`=?, "
  "`line11`=?, "
  "`line12`=?, "
  "`line13`=?, "
  "`line14`=?, "
  "`line15`=? "
  "WHERE `id`=?";

  const char access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::erase_statement[] =
  "DELETE FROM `wn_preset_lane_line` "
  "WHERE `id`=?";

  const char access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::query_statement[] =
  "SELECT "
  "`wn_preset_lane_line`.`id`, "
  "`wn_preset_lane_line`.`preset_id`, "
  "`wn_preset_lane_line`.`line0`, "
  "`wn_preset_lane_line`.`line1`, "
  "`wn_preset_lane_line`.`line2`, "
  "`wn_preset_lane_line`.`line3`, "
  "`wn_preset_lane_line`.`line4`, "
  "`wn_preset_lane_line`.`line5`, "
  "`wn_preset_lane_line`.`line6`, "
  "`wn_preset_lane_line`.`line7`, "
  "`wn_preset_lane_line`.`line8`, "
  "`wn_preset_lane_line`.`line9`, "
  "`wn_preset_lane_line`.`line10`, "
  "`wn_preset_lane_line`.`line11`, "
  "`wn_preset_lane_line`.`line12`, "
  "`wn_preset_lane_line`.`line13`, "
  "`wn_preset_lane_line`.`line14`, "
  "`wn_preset_lane_line`.`line15` "
  "FROM `wn_preset_lane_line`";

  const char access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::erase_query_statement[] =
  "DELETE FROM `wn_preset_lane_line`";

  const char access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::table_name[] =
  "`wn_preset_lane_line`";

  void access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  persist (database& db, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    callback (db,
              static_cast<const object_type&> (obj),
              callback_event::pre_persist);

    image_type& im (sts.image ());
    binding& imb (sts.insert_image_binding ());

    if (init (im, obj, statement_insert))
      im.version++;

    im.id_value = 0;

    if (im.version != sts.insert_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_insert);
      sts.insert_image_version (im.version);
      imb.version++;
    }

    {
      id_image_type& i (sts.id_image ());
      binding& b (sts.id_image_binding ());
      if (i.version != sts.id_image_version () || b.version == 0)
      {
        bind (b.bind, i);
        sts.id_image_version (i.version);
        b.version++;
      }
    }

    insert_statement& st (sts.persist_statement ());
    if (!st.execute ())
      throw object_already_persistent ();

    obj.id = id (sts.id_image ());

    callback (db,
              static_cast<const object_type&> (obj),
              callback_event::post_persist);
  }

  void access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  update (database& db, const object_type& obj)
  {
    ODB_POTENTIALLY_UNUSED (db);

    using namespace mysql;
    using mysql::update_statement;

    callback (db, obj, callback_event::pre_update);

    mysql::transaction& tr (mysql::transaction::current ());
    mysql::connection& conn (tr.connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    id_image_type& idi (sts.id_image ());
    init (idi, id (obj));

    image_type& im (sts.image ());
    if (init (im, obj, statement_update))
      im.version++;

    bool u (false);
    binding& imb (sts.update_image_binding ());
    if (im.version != sts.update_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_update);
      sts.update_image_version (im.version);
      imb.version++;
      u = true;
    }

    binding& idb (sts.id_image_binding ());
    if (idi.version != sts.update_id_image_version () ||
        idb.version == 0)
    {
      if (idi.version != sts.id_image_version () ||
          idb.version == 0)
      {
        bind (idb.bind, idi);
        sts.id_image_version (idi.version);
        idb.version++;
      }

      sts.update_id_image_version (idi.version);

      if (!u)
        imb.version++;
    }

    update_statement& st (sts.update_statement ());
    if (st.execute () == 0)
      throw object_not_persistent ();

    callback (db, obj, callback_event::post_update);
    pointer_cache_traits::update (db, obj);
  }

  void access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  erase (database& db, const id_type& id)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    id_image_type& i (sts.id_image ());
    init (i, id);

    binding& idb (sts.id_image_binding ());
    if (i.version != sts.id_image_version () || idb.version == 0)
    {
      bind (idb.bind, i);
      sts.id_image_version (i.version);
      idb.version++;
    }

    if (sts.erase_statement ().execute () != 1)
      throw object_not_persistent ();

    pointer_cache_traits::erase (db, id);
  }

  access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::pointer_type
  access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  find (database& db, const id_type& id)
  {
    using namespace mysql;

    {
      pointer_type p (pointer_cache_traits::find (db, id));

      if (!pointer_traits::null_ptr (p))
        return p;
    }

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);

    if (l.locked ())
    {
      if (!find_ (sts, &id))
        return pointer_type ();
    }

    pointer_type p (
      access::object_factory<object_type, pointer_type>::create ());
    pointer_traits::guard pg (p);

    pointer_cache_traits::insert_guard ig (
      pointer_cache_traits::insert (db, id, p));

    object_type& obj (pointer_traits::get_ref (p));

    if (l.locked ())
    {
      select_statement& st (sts.find_statement ());
      ODB_POTENTIALLY_UNUSED (st);

      callback (db, obj, callback_event::pre_load);
      init (obj, sts.image (), &db);
      load_ (sts, obj, false);
      sts.load_delayed (0);
      l.unlock ();
      callback (db, obj, callback_event::post_load);
      pointer_cache_traits::load (ig.position ());
    }
    else
      sts.delay_load (id, obj, ig.position ());

    ig.release ();
    pg.release ();
    return p;
  }

  bool access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  find (database& db, const id_type& id, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);
    assert (l.locked ()) /* Must be a top-level call. */;

    if (!find_ (sts, &id))
      return false;

    select_statement& st (sts.find_statement ());
    ODB_POTENTIALLY_UNUSED (st);

    reference_cache_traits::position_type pos (
      reference_cache_traits::insert (db, id, obj));
    reference_cache_traits::insert_guard ig (pos);

    callback (db, obj, callback_event::pre_load);
    init (obj, sts.image (), &db);
    load_ (sts, obj, false);
    sts.load_delayed (0);
    l.unlock ();
    callback (db, obj, callback_event::post_load);
    reference_cache_traits::load (pos);
    ig.release ();
    return true;
  }

  bool access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  reload (database& db, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);
    assert (l.locked ()) /* Must be a top-level call. */;

    const id_type& id (object_traits_impl::id (obj));
    if (!find_ (sts, &id))
      return false;

    select_statement& st (sts.find_statement ());
    ODB_POTENTIALLY_UNUSED (st);

    callback (db, obj, callback_event::pre_load);
    init (obj, sts.image (), &db);
    load_ (sts, obj, true);
    sts.load_delayed (0);
    l.unlock ();
    callback (db, obj, callback_event::post_load);
    return true;
  }

  bool access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  find_ (statements_type& sts,
         const id_type* id)
  {
    using namespace mysql;

    id_image_type& i (sts.id_image ());
    init (i, *id);

    binding& idb (sts.id_image_binding ());
    if (i.version != sts.id_image_version () || idb.version == 0)
    {
      bind (idb.bind, i);
      sts.id_image_version (i.version);
      idb.version++;
    }

    image_type& im (sts.image ());
    binding& imb (sts.select_image_binding ());

    if (im.version != sts.select_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_select);
      sts.select_image_version (im.version);
      imb.version++;
    }

    select_statement& st (sts.find_statement ());

    st.execute ();
    auto_result ar (st);
    select_statement::result r (st.fetch ());

    if (r == select_statement::truncated)
    {
      if (grow (im, sts.select_image_truncated ()))
        im.version++;

      if (im.version != sts.select_image_version ())
      {
        bind (imb.bind, im, statement_select);
        sts.select_image_version (im.version);
        imb.version++;
        st.refetch ();
      }
    }

    return r != select_statement::no_data;
  }

  result< access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::object_type >
  access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));

    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.select_image_binding ());

    if (im.version != sts.select_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_select);
      sts.select_image_version (im.version);
      imb.version++;
    }

    std::string text (query_statement);
    if (!q.empty ())
    {
      text += " ";
      text += q.clause ();
    }

    q.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        text,
        false,
        true,
        q.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::object_result_impl<object_type> > r (
      new (shared) mysql::object_result_impl<object_type> (
        q, st, sts, 0));

    return result<object_type> (r);
  }

  unsigned long long access::object_traits_impl< ::db::PresetLaneLine, id_mysql >::
  erase_query (database& db, const query_base_type& q)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));

    std::string text (erase_query_statement);
    if (!q.empty ())
    {
      text += ' ';
      text += q.clause ();
    }

    q.init_parameters ();
    delete_statement st (
      conn,
      text,
      q.parameters_binding ());

    return st.execute ();
  }
}

#include <odb/post.hxx>
