#ifndef CLI_CONSOLE_H
#define CLI_CONSOLE_H

/**
 * @brief       终端通用操作接口
 */


namespace cli{

    /**
     * @brief    检查键盘是否有输入
     */
    bool hasInput();

    /**
     * @brief    设置是否显示光标
     */
    bool setCursorEnable(bool enable);

    /**
     * @brief    设置是否为规范模式输入
     * @see      https://www.cnblogs.com/xiayong123/archive/2011/07/19/3717258.html
     */
    bool setCanonEnable(bool enable);

    /**
     * @brief    清空光标所在行所有输出内容
     */
    void clearLine();

    /**
     * @brief    清空光标所在行的光标后的所有输出内容
     */
    void clearLineAfter();

    /**
     * @brief    清空屏幕
     */
    void clearScreen();

    /**
     * @brief    清空光标后的所有屏幕内容
     */
    void clearScreenAfter();

    /**
     * @brief      向上移动游标
     * @param[in]  lines: 向上移动的行数，默认移动一行
     */
    void moveCursorUp(int lines = 1);

    /**
     * @brief      向下移动游标
     * @param[in]  lines: 向下移动的行数，默认移动一行
     */
    void moveCursorDown(int lines = 1);

    /**
     * @brief      向左移动光标
     * @param[in]  columns: 向左移动的列数，默认移动一列
     */
    void moveCursorRight(int columns = 1);

    /**
     * @brief      向右移动光标
     * @param[in]  columns: 向右移动的列数，默认移动一列
     */
    void moveCursorLeft(int columns = 1);

    /**
     * @brief      将光标移动到光标所在行的对应列上
     * @param[in]  columns: 列
     */
    void moveCursorToColumn(int column);

    /**
     * @brief      将光标移动到指定位置
     * @param[in]  column: 需要移动到的列 row: 需要移动到的行
     */
    void moveCursorToPosition(int column, int row);

    /**
     * @brief      将光标移动到指定位置
     * @param[out] row: 光标所在行 column: 光标所在列
     */
    bool getCursorPosition(int& row, int& column);

    /**
     * @brief      获取单个字符，不需要通过enter键确认
     */
    int getChar();
}


#endif //CLI_CONSOLE_H
