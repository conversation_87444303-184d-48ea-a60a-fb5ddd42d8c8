#ifndef SGWTOESIPFUNC_HPP_
#define SGWTOESIPFUNC_HPP_

#include <boost/thread.hpp>
#include <ace/INET_Addr.h>
#include "UsgManager/include/WtoeSipFuncItf.hpp"
#include "UsgManager/include/UsgManagerItf.hpp"

namespace usg
{

//class CCommandWtoe;
    class CUsgManager;

    class CSgWtoeSipFunc : public IWtoeSipFunc
    {
    public:
        CSgWtoeSipFunc();
        ~CSgWtoeSipFunc();

    public:
        bool init( CUsgManager* sg );
        bool fini();

        void regOnNotifyCallBack( const std::string& sid, onNotifyCallBack_t& cb );
        void regOnNotifyCallBack( const std::string& sid );
        void unRegOnNotifyCallBack( const std::string& sid );

        void regOnNotifyCBbyPresetList( const std::string & sipResCode, onNotifyCallBack_t& cb );
        void unRegOnNotifyCBbyPresetList( const std::string & sipResCode );


    public:
        virtual bool onReceiveCatalogResponse( const std::string &sid, const bool isOk );
        virtual bool onReceiveCatalog( const std::string &sid, const SCatalog& info, SCatalogResponse& out );
        virtual bool onReceiveQueryCatalog( const std::string &sid, const SCatalog& info );
        virtual bool onReceiveKeepaliveResponse( const std::string &sid, const bool isOk );
        virtual bool onReceiveKeeplive( const std::string &sid );

        virtual bool onReceiveRealMedia(
                const std::string &sid,
                const boost::uuids::uuid &resId,
                const uint8_t subImageSize,
                const uint8_t masterImageSize,
                const SRealMedia &r,
                SRealMediaResponse &out );
        virtual bool onReceiveRealMediaCancel( const std::string &sid );
        virtual bool onReceiveRealMediaAck( const std::string &sid );
        virtual bool onReceiveRealMediaBye( const std::string &sid );

        virtual bool onReceiveHistoryList( const std::string &sid, const boost::uuids::uuid &resId, const SHistoryList &r, SHistoryListResponse &out );
        virtual bool onReceiveHistoryMedia( const std::string &sid, const boost::uuids::uuid &resId, const SHistoryMedia &r, SHistoryMediaResponse &out );
        virtual bool onReceivePresetList( const std::string &sid, const boost::uuids::uuid &resId, const SPresetList &r, SPresetListResponse &out );
        virtual bool onReceivePtzCommand( const std::string &sid, const boost::uuids::uuid &resId, const SPtzCommand &r, SPtzCommandResponse &out );

        virtual bool onReceiveRealMediaResponse( const std::string &sid, SRealMediaResponse& out);
        virtual bool onReceiveHistoryListResponse( const std::string& sid, SHistoryListResponse& out);
        virtual bool onReceiveHistoryMediaResponse( const std::string& sid, SHistoryMediaResponse& out );
        virtual bool onReceivePresetListResponse( const std::string& sid,SPresetListResponse& out );
        virtual bool onReceiveError( const std::string& sid );
        virtual bool onReceive200Ok( const std::string& sid );

        virtual bool onReceivePresetListNotify( const std::string& sipCode,SPresetListResponse& out );

        //gb28181添加
//	virtual bool onReceiveDeviceCatalog(const std::string &sid, SCatalog &out);
        virtual bool onReceiveDeviceInfo( const std::string &sid, const boost::uuids::uuid &resId, SDeviceInfoResponse &out );
        virtual bool onReceiveDeviceStatus( const std::string &sid, const boost::uuids::uuid &resId, SDeviceStatusResponse &out );
//    virtual bool onReceiveReboot( const std::string &sid, const boost::uuids::uuid &resId, SRebootCommandResponse &out );

        virtual bool onReceiveSubscribeAlarmResponse( const std::string& sid, bool result);
        virtual bool onReceiveBroadcastResponse(const std::string& sid ,const bool result);

        virtual bool onReceiveFiletoEnd(const std::string &sid);

        virtual bool onReceiveAlarmNotify(const SAlarmParam &alarm);

    private:
        CUsgManager* m_sg;
        std::map< std::string, onNotifyCallBack_t > m_onNotifyCallBack;
        std::map< std::string, std::vector< onNotifyCallBack_t > > m_presetListCallBack;
        rwmutex m_lock,m_presetLock;

        std::set< std::string > m_responseOpers;
    };
}
#endif
