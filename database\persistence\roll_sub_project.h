/**
 * @addtogroup odbDatabaseGroup
 * @brief 轮切子方案
 * @{
 */
#ifndef _ROLLSUBPROJECT_H
#define _ROLLSUBPROJECT_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {

/**
 * @brief  轮切子方案: 对应数据库aimonitorV3的表wn_roll_sub_project
 */
#pragma db object table("wn_roll_sub_project")
class RollSubProject {
public:

    RollSubProject(unsigned long rollId,
        const std::string& startTime,
        unsigned long programId,
        bool isDel,
        bool isEnable

    )
        : rollProjId(rollId), startTime(startTime), programId(programId),
          isDel(isDel), isEnable(isEnable)
    {
    }

    unsigned long getId() const {
        return id;
    }

    unsigned long getRollPorjId()const {
        return rollProjId;
    }

    void setRollProjId(unsigned long id) {
        this->rollProjId = id;
    }

    const std::string& getStartTime() const {
        return startTime;
    }

    void setStartTime(const std::string& time) {
        this->startTime = time;
    }

    unsigned long getProgramId()const {
        return programId;
    }

    void setProgramId(unsigned long id) {
        this->programId = id;
    }


    bool getIsDel() const {
        return isDel;
    }

    void setIsDel(bool del) {
        this->isDel = del;
    }

    bool getIsEnable() const {
        return isEnable;
    }

    void setIsEnable(bool enable) {
        this->isEnable = enable;
    }

private:

    friend class odb::access;
    RollSubProject() {}

private:
#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("project_id")
    unsigned long rollProjId;           //!< 轮切方案id 对应表wn_roll_project(RollProject)的id

#pragma db column("start_time")  type("VARCHAR(255)")
    std::string startTime;              //!< 轮切起始时间 TODO:

#pragma db column("program_id")
    unsigned long programId;            //!< 夏天冬天外场时间信息 对应表wn_program(program)的id

#pragma db column("is_del") type("INT")
    bool isDel;                         //!< 是否删除

#pragma db column("is_enable") type("INT")
    bool isEnable;                      //!< 是否使能
};
}
#endif //_ROLLSUBPROJECT_H
/**
 * @}
 */