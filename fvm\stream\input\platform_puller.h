/**
 * Project FVM
 */

#pragma once
#include "stream_input.h"
#include "platform/front_platform.h"
#include "protocol/protocol_manager.h"

/**
 * @brief: 平台接流输入
 */
namespace fvm::stream
{
    class PlatformPuller : public StreamInput{
    public:
        PlatformPuller(FrontPlatformPtr platform);

        /**
         * 初始化视频资源信息
         */
        void initSource(const VideoSourceInfoPtr source) override;

        // （新增）3D定位
        virtual bool focusRect(int xTop, int yTop, int xBottom, int yBottom) { return false; };

        virtual FrontPlatformPtr platform() { return frontPlatform; }

    protected:
       /**
       * 向GUM/MSM请求流
       */
        bool remoteRequest();
        /**
         * 向GUM/MSM请求停止流
         */
        void remoteStop();

        /**
         * @brief 控制云台 
         * @param[in] action:  云台动作 1：向左 2：向右 3：向上 4：向下 5：变倍短 6：变倍长 7：聚焦近 8：聚焦远 9：光圈小 10：光圈大 11：灯光关 12：灯光开
         * @param[in] step:  步长  主要用于控制云台转动方向的操作，1-8为步长值，1为最小步长，8为最大步长
         */
        bool controlPtz(int action, int step);

    private:
        void process() override;
        void dispose() override;

        /**
        * 调用相机预置位
        */
        bool callCameraPreset(int presetId) override;

        /**
         * 输入流是否能调用预置位
         */
        bool isPtzCapable() override;

    protected:
        FrontPlatformPtr frontPlatform = nullptr;
        std::string destAddr = "";    //请求流的目的地址
        std::string sourceAddr = "";  //发流的源地址
        boost::fibers::mutex mutexRequest;     //请求流使用的锁和条件变量
        boost::fibers::condition_variable condRequest;
    };
}