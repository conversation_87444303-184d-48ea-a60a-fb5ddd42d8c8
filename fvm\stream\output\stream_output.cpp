/**
 * Project FVM
 */
#include "stream_output.h"
#include "ailog.h"

/**
 * StreamOutput implementation
 * @brief: 视频输出
 *          派生文件录像、RTMP流等
 */

namespace fvm {
    namespace stream {

        StreamOutput::StreamOutput(StreamOuputType streamType)
        {
            this->outputStreamType = streamType;
        }

        /**
        *  填入视频输出地址
        * @param const addr
        */
        void StreamOutput::initAddress(const std::string addr) {
            this->address = addr;
            this->inited();
        }

        /**
         * 输入视频流数据
         * @param const PacketDataPtr
         */
        void StreamOutput::pushData(const PacketDataPtr data)
        {
            std::lock_guard<boost::fibers::mutex> lock(mutexOutput);

			// 缓存堆积检查
            if(this->packetDatas.size() > maxBufferSize())
            {
                ai::LogInfo << this->address << " streamOutput with buffer size " << this->packetDatas.size();
                this->packetDatas.resize(minBufferSize());
            }
            this->packetDatas.push_front(data);
            this->newData.notify();
        }

        /**
         *  填入码流信息等
         * @param const CodecInfoPtr
         */
        void StreamOutput::setCodecInfo(const CodecInfoPtr codec) {
            this->codecInfo = codec;
        }

        /**
         * 打开输出流
         */
        bool StreamOutput::open(void)
        {
            if (headerHasWritten)
                return true;            //已经成功打开 跳过
            if (this->codecInfo == nullptr)
                return false;
            if (this->codecInfo->getWidth() <= 0 || this->codecInfo->getHeight() <= 0)
            {
                setLastError("open context: no size info!");
                return false;
            }

            codecpar = codecInfo->getAVCodecParam();
            timebase = codecInfo->getTimebase();
            frameRate = codecInfo->getFrameRate();
            char formatName[16]{ 0 };
            switch (this->outputStreamType)
            {
            case StreamOuputType::RTMP:
                sprintf(formatName, "flv");
                av_dict_set(&options, "flvflags", "no_duration_filesize", 0);
                break;
            case StreamOuputType::RTP:
                sprintf(formatName, "rtp");
                break;
            case StreamOuputType::UDP:
                sprintf(formatName, "h264");
                break;
            default:
                setLastError("output stream type not supported!");
                return false;
            }
            int ret;
            if ((ret = avformat_alloc_output_context2(&formatCtx, NULL, formatName, this->address.c_str())) < 0)
            {
                setLastError(str(boost::format("avformat_alloc_output_context2 FAILED: %s") % getErrorString(ret)), ret);
                close();
                return false;
            }
            outStream = avformat_new_stream(formatCtx, NULL);
            if (outStream == nullptr)
            {
                setLastError("avformat_new_stream FAILED!");
                close();
                return false;
            }
            if ((ret = avcodec_parameters_copy(outStream->codecpar, codecpar)) < 0)
            {
                setLastError(str(boost::format("avcodec_parameters_copy FAILED: %s") % getErrorString(ret)), ret);
                close();
                return false;
            }
            outStream->codecpar->codec_tag = 0;
            av_dump_format(formatCtx, 0, address.c_str(), 1);
            if (!(formatCtx->oformat->flags & AVFMT_NOFILE))
            {
                if ((ret = avio_open(&formatCtx->pb, this->address.c_str(), AVIO_FLAG_WRITE)) < 0)
                {
                    setLastError(str(boost::format("avio_open %s FAILED: %s") % this->address % getErrorString(ret)), ret);
                    close();
                    return false;
                }
            }
            //Write file header
            if ((ret = avformat_write_header(formatCtx, NULL)) < 0)
            {
                setLastError(str(boost::format("avformat_write_header FAILED: %s") % getErrorString(ret)), ret);
                close();
                return false;
            }
            frameIndex = 0;
            headerHasWritten = true;
            return true;
        }

        /**
         * 关闭输出流
         */
        void StreamOutput::close(void)
        {
            if (formatCtx)
            {
                if (headerHasWritten)
                    av_write_trailer(formatCtx);
                av_freep(&formatCtx->priv_data);
                if (!(formatCtx->oformat->flags & AVFMT_NOFILE))
                    avio_closep(&formatCtx->pb);
                FFMPEGElement::disposeContext();
                headerHasWritten = false;
            }
        }

    }
}