
#ifndef GB28181XML_HPP_
#define GB28181XML_HPP_

/**
 * 将db33的结构体转换为xml字符串.
 */
#include <string>
#include <pjlib.h>
#include <pjlib-util.h>

#include "../include/Gb28181Struct.hpp"

namespace gb28181
{
    enum ENULLTYPE
    {
        ENULLTYPE_HISTORYFILE,
        ENULLTYPE_CATALOGFILE,
    };

    class CGb28181Xml
    {
    public:
        /**
         * 构造时可以给一个pool,则此类用给入的pool.
         * 如果给入为0,则会自己创建一个pool.
         */
        CGb28181Xml( pj_pool_t *pool );
        ~CGb28181Xml();

        bool isOk();

    private:
        pj_str_t* doConv( pj_str_t *dst, const std::string &str );

    public:
        // 本软件会向外发的包,全部有转换.
        bool getString( const SCatalog &gbtInfo, std::string &outString );
        bool getString( const SCatalog &gbtInfo, size_t index, std::string &outString );
        bool getString( const SCatalog &gbtInfo, std::vector<std::string> &outStringVec );

        //当搜索的列表为空时（如：检索历史文件、目录查询），用此方法
        //ENULLTYPE:0 -- 历史文件；1 -- 目录查询
        bool getString( const std::string &recordId, const std::string &sn, const std::string &resName, std::string &outString, ENULLTYPE type );

        bool getString( const SRealMediaResponse &gbtInfo, std::string &outString );
        bool getString( const SFileListResponse &gbtInfo, size_t index, std::string &outString );
        bool getString( const SFileListResponse &gbtInfo, std::vector<std::string> &outStringVec );
        bool getString( const SHistoryMediaResponse &gbtInfo, std::string &outString );
        bool getString( const SPtzCommandResponse &gbtInfo, std::string &outString );
        bool getString( const SPresetListResponse &gbtInfo, std::string &outString );
        bool getString( const SDeviceInfoResponse &gbtInfo, std::vector<std::string> &outStringVec );
        bool getString( const SDeviceInfoResponse &gbtInfo, std::string &outString );
        bool getString( const SDeviceStatusResponse &gbtInfo, std::vector<std::string> &outStringVec );
        bool getString( const SDeviceStatusResponse &gbtInfo, std::string &outString );
        bool getString( const SDeviceAlarmStatusResponse &gbtInfo, std::string &outString );
        bool getString( const SRecordContronlResponse &gbtInfo, std::string &outString );
        bool getString( const SGuardContronlResponse &gbtInfo, std::string &outString );
        bool getString( const SAlarmResetResponse &gbtInfo, std::string &outString );
        // 添加请求包
        bool makeString( const SCatalogResponse&gbtInfo, std::string &outString );
        bool makeString( const SRealMedia &gbtInfo, std::string &outString );
        bool makeString( const SFileList &gbtInfo, std::string &outString, std::string &sn );
        bool makeString( const SHistoryMedia &gbtInfo, std::string &outString );
        bool makeString( const SPtzCommand &gbtInfo, std::string &outString );
        bool makeString( const SKeepalive &gbtInfo,std::string &outString );
        bool makeString( const SPresetList &gbtInfo,std::string &outString );
        bool makeString( const SDeviceReboot &gbtInfo, std::string &outString );
        bool makeString( const SRecordContronl &gbtInfo, std::string &outString );
        bool makeString( const SGuardContronl &gbtInfo, std::string &outString );
        bool makeString( const SAlarmReset&gbtInfo, std::string &outString );
        bool makeString( const SDeviceCatalog &gbtInfo, std::string &outString );
        bool makeString( const SDeviceInfo &gbtInfo, std::string &outString, std::string &sn );
        bool makeString( const SDeviceStatus&gbtInfo, std::string &outString, std::string &sn );

        //设备配置查询
        bool getDeviceConfigQuery( const std::string &sn, const std::string &sid, const SDeviceConfigQueryResult &result, std::string &outString );
        bool getAlarmNotify(  const std::string &sn, const std::string &sid, const SAlarmInfo &info, std::string &outString );
        bool getReplayEndString( const std::string &sn, const std::string &sid, std::string &outString );
        bool getKeepAliveString( const std::string &deviceId, std::string &outString );

        bool getSubscribeCatalogString(const uint32_t startTime, const uint32_t endTime, std::string &outString );
        bool getSubscribeAlarmString(const SAlarmSubscribeParam &param,std::string &outString);
        bool getReceiveBroadcastResponseString( const std::string &sn, const std::string deviceId, bool isOk, std::string &outString );

        bool getAudioBroadcastString(const std::string &sourceId, const std::string &targetId, std::string &outString);

    private:
        pj_caching_pool m_caching_pool;
        pj_pool_t *m_pool;

        bool m_isOk;
        bool m_isPoolExtra;
    };

}

#endif
