/**
 * @addtogroup odbDatabaseGroup
 * @brief 预置位对应的车道信息
 * @{
 */
#ifndef _PRESETLANE_H
#define _PRESETLANE_H


#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
 /**
  * @brief  预置位对应的车道信息 对应数据库aimonitorV3的表wn_preset_lane
  */
#pragma db object table("wn_preset_lane")
class PresetLane {
public:

    PresetLane(unsigned long roiId,
        const std::string& laneArea,
        int laneType,
        float minSpeed,
        float maxSpeed,
        const std::string& eventProperty,
        const std::string& labelProperty,
        unsigned long paramPlanId,
        const std::string& param,
        const std::string& direction,
        bool isDel,
        bool isEnable

    )
        : roiId(roiId), laneArea(laneArea), laneType(laneType),
        minSpeed(minSpeed), maxSpeed(maxSpeed), eventProperty(eventProperty),
        labelProperty(labelProperty), paramPlanId(paramPlanId), param(param),
        direction(direction), isDel(isDel), isEnable(isEnable)
    {
    }

    unsigned long getId() const {
        return id;
    }

    unsigned long getRoiId()const {
        return roiId;
    }

    void setRoiId(unsigned long id) {
        this->roiId = id;
    }

    const std::string& getLaneArea() const {
        return laneArea;
    }

    void setLaneArea(const std::string& area) {
        this->laneArea = area;
    }

    int getLaneType()const {
        return laneType;
    }

    void setLaneType(int type) {
        this->laneType = type;
    }

    float getMinSpeed()const {
        return minSpeed;
    }

    void setMinSpeed(float speed) {
        this->minSpeed = speed;
    }

    float getMaxSpeed()const {
        return maxSpeed;
    }

    void setMaxSpeed(float speed) {
        this->maxSpeed = speed;
    }

    const std::string& getEventProperty() const {
        return eventProperty;
    }

    void setEventProperty(const std::string& property) {
        this->eventProperty = property;
    }

    const std::string& getLabelProperty() const {
        return labelProperty;
    }

    void setLabelProperty(const std::string& property) {
        this->labelProperty = property;
    }

    uint64_t getParamPlanId()const {
        return paramPlanId;
    }

    void setParamPlanId(unsigned long id) {
        this->paramPlanId = id;
    }

    const std::string& getParam() const {
        return param;
    }

    void setParam(const std::string& param) {
        this->param = param;
    }

    const std::string& getDirection() const {
        return direction;
    }

    void setDirection(const std::string& direction) {
        this->direction = direction;
    }

    bool getIsDel() const {
        return isDel;
    }

    void setIsDel(bool del) {
        this->isDel = del;
    }

    bool getIsEnable() const {
        return isEnable;
    }

    void setIsEnable(bool enable) {
        this->isEnable = enable;
    }

private:

    friend class odb::access;
    PresetLane() {}

private:
#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("roi_id")
    unsigned long roiId;                //!< roi id 对应表wn_video_source(VideoSource) id

#pragma db column("lane_area")  type("VARCHAR(255)")
    std::string laneArea;               //!< 车道区域

#pragma db column("lane_type")
    int laneType;                       //!< 车道类型

#pragma db column("min_speed")
    float minSpeed;                       //!< 车道最小速度

#pragma db column("max_speed")
    float maxSpeed;                       //!< 车道最大速度

#pragma db column("event_property") type("VARCHAR(255)")
    std::string eventProperty;          //!< 事件属性值

#pragma db column("label_property") type("VARCHAR(255)")
    std::string labelProperty;          //!< 目标标签属性值

#pragma db column("param_plan_id")
    unsigned long paramPlanId;          //!< 灵敏度预案 对应表wn_alg_param_plan(AlgParamPlan) id

#pragma db column("param")  type("VARCHAR(255)")
    std::string param;                  //!< 用户配置灵敏度参数，不配置就用默认

#pragma db column("direction")  type("VARCHAR(255)")
    std::string direction;              //!< 车道行驶方向

#pragma db column("is_del") type("INT")
    bool isDel;                         //!< 是否删除

#pragma db column("is_enable") type("INT")
    bool isEnable;                      //!< 是否使能

};
}
#endif //_PRESETLANE_H
/**
 * @}
 */
