/**
 * @addtogroup odbDatabaseGroup
 * @brief 检测通道信息
 * @{
 */
#ifndef _DETECTIONPOINT_H
#define _DETECTIONPOINT_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
/**
 * @brief: 检测通道信息 对应数据库aimonitorV3的表wn_detection_point
 */
#pragma db object table("wn_detection_point")
class DetectionPoint {
public:

    DetectionPoint(unsigned long monitorId,
        const std::string& detectionPointName,
        unsigned long videoId,
        unsigned long projectId,
        unsigned long streamId,
        bool isRoll,
        bool isDel,
        bool isEnable,
        unsigned long bindVideoId
    )
        : monitorId(monitorId), detectionPointName(detectionPointName), videoId(videoId),
        projectId(projectId), streamId(streamId), isRoll(isRoll),
        isDel(isDel), isEnable(isEnable), bindVideoId(bindVideoId)
    {
    }
    unsigned long getId() const {
        return id;
    }


    unsigned long getMonitorId() const {
        return monitorId;
    }

    void setMonitorId(unsigned long id) {
        this->monitorId = id;
    }

    const std::string& getDetectPointName() const {
        return detectionPointName;
    }

    void setDetectPointName(const std::string& name) {
        this->detectionPointName = name;
    }


    unsigned long getVideoId() const {
        return monitorId;
    }

    void setVideoId(unsigned long id) {
        this->monitorId = id;
    }

    unsigned long getProjectId() const {
        return projectId;
    }

    void setProjectId(unsigned long id) {
        this->projectId = id;
    }


    unsigned long getStreamId() const {
        return streamId;
    }

    void setStreamId(unsigned long id) {
        this->streamId = id;
    }


    bool getIsRoll() const {
        return isRoll;
    }

    void setIsRoll(bool del) {
        this->isRoll = del;
    }

    bool getIsDel() const {
        return isDel;
    }

    void setIsDel(bool del) {
        this->isDel = del;
    }

    bool getIsEnable() const {
        return isEnable;
    }

    void setIsEnable(bool enable) {
        this->isEnable = enable;
    }

    unsigned long getBindVideoId() const {
        return bindVideoId;
    }

    void setBindVideoId(unsigned long id) {
        this->bindVideoId = id;
    }


private:

    friend class odb::access;
    DetectionPoint() {}

private:

#pragma db id auto
    unsigned long id;                   //!< 表ID 对应通道号

#pragma db column("monitor_id")
    unsigned long monitorId;            //!< 检测仪ID  对应表wn_monitor(monitor) id

#pragma db column("detection_point_name")  type("VARCHAR(255)")
    std::string detectionPointName;     //!< 检测通道name

#pragma db column("video_id")
    unsigned long videoId;              //!< 视频资源ID

#pragma db column("project_id")
    unsigned long projectId;            //!< 轮切方案ID

#pragma db column("stream_id")
    unsigned long streamId;             //!< 视频流ID TODO:

#pragma db column("is_roll") type("INT")
    bool isRoll;                        //!< 是否轮切

#pragma db column("is_del") type("INT")
    bool isDel;                         //!< 是否删除

#pragma db column("is_enable") type("INT")
    bool isEnable;                      //!< 是否使能

#pragma db column("bind_video_id")
    unsigned long bindVideoId;          //!<

};
}
#endif //_DETECTIONPOINT_H

/**
 * @}
 */