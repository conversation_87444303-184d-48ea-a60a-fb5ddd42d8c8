// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#ifndef PRESET_LANE_LINE_ODB_HXX
#define PRESET_LANE_LINE_ODB_HXX

#include <odb/version.hxx>

#include <odb/pre.hxx>

#include "preset_lane_line.h"

#include <memory>
#include <cstddef>
#include <utility>

#include <odb/core.hxx>
#include <odb/traits.hxx>
#include <odb/callback.hxx>
#include <odb/wrapper-traits.hxx>
#include <odb/pointer-traits.hxx>
#include <odb/container-traits.hxx>
#include <odb/no-op-cache-traits.hxx>
#include <odb/result.hxx>
#include <odb/simple-object-result.hxx>

#include <odb/details/unused.hxx>
#include <odb/details/shared-ptr.hxx>

namespace odb
{
  // PresetLaneLine
  //
  template <>
  struct class_traits< ::db::PresetLaneLine >
  {
    static const class_kind kind = class_object;
  };

  template <>
  class access::object_traits< ::db::PresetLaneLine >
  {
    public:
    typedef ::db::PresetLaneLine object_type;
    typedef ::db::PresetLaneLine* pointer_type;
    typedef odb::pointer_traits<pointer_type> pointer_traits;

    static const bool polymorphic = false;

    typedef long unsigned int id_type;

    static const bool auto_id = true;

    static const bool abstract = false;

    static id_type
    id (const object_type&);

    typedef
    no_op_pointer_cache_traits<pointer_type>
    pointer_cache_traits;

    typedef
    no_op_reference_cache_traits<object_type>
    reference_cache_traits;

    static void
    callback (database&, object_type&, callback_event);

    static void
    callback (database&, const object_type&, callback_event);
  };
}

#include <odb/details/buffer.hxx>

#include <odb/mysql/version.hxx>
#include <odb/mysql/forward.hxx>
#include <odb/mysql/binding.hxx>
#include <odb/mysql/mysql-types.hxx>
#include <odb/mysql/query.hxx>

namespace odb
{
  // PresetLaneLine
  //
  template <typename A>
  struct query_columns< ::db::PresetLaneLine, id_mysql, A >
  {
    // id
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    id_type_;

    static const id_type_ id;

    // presetId
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    presetId_type_;

    static const presetId_type_ presetId;

    // line0
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line0_type_;

    static const line0_type_ line0;

    // line1
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line1_type_;

    static const line1_type_ line1;

    // line2
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line2_type_;

    static const line2_type_ line2;

    // line3
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line3_type_;

    static const line3_type_ line3;

    // line4
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line4_type_;

    static const line4_type_ line4;

    // line5
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line5_type_;

    static const line5_type_ line5;

    // line6
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line6_type_;

    static const line6_type_ line6;

    // line7
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line7_type_;

    static const line7_type_ line7;

    // line8
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line8_type_;

    static const line8_type_ line8;

    // line9
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line9_type_;

    static const line9_type_ line9;

    // line10
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line10_type_;

    static const line10_type_ line10;

    // line11
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line11_type_;

    static const line11_type_ line11;

    // line12
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line12_type_;

    static const line12_type_ line12;

    // line13
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line13_type_;

    static const line13_type_ line13;

    // line14
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line14_type_;

    static const line14_type_ line14;

    // line15
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    line15_type_;

    static const line15_type_ line15;
  };

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::id_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  id (A::table_name, "`id`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::presetId_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  presetId (A::table_name, "`preset_id`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line0_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line0 (A::table_name, "`line0`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line1_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line1 (A::table_name, "`line1`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line2_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line2 (A::table_name, "`line2`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line3_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line3 (A::table_name, "`line3`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line4_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line4 (A::table_name, "`line4`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line5_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line5 (A::table_name, "`line5`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line6_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line6 (A::table_name, "`line6`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line7_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line7 (A::table_name, "`line7`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line8_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line8 (A::table_name, "`line8`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line9_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line9 (A::table_name, "`line9`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line10_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line10 (A::table_name, "`line10`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line11_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line11 (A::table_name, "`line11`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line12_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line12 (A::table_name, "`line12`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line13_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line13 (A::table_name, "`line13`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line14_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line14 (A::table_name, "`line14`", 0);

  template <typename A>
  const typename query_columns< ::db::PresetLaneLine, id_mysql, A >::line15_type_
  query_columns< ::db::PresetLaneLine, id_mysql, A >::
  line15 (A::table_name, "`line15`", 0);

  template <typename A>
  struct pointer_query_columns< ::db::PresetLaneLine, id_mysql, A >:
    query_columns< ::db::PresetLaneLine, id_mysql, A >
  {
  };

  template <>
  class access::object_traits_impl< ::db::PresetLaneLine, id_mysql >:
    public access::object_traits< ::db::PresetLaneLine >
  {
    public:
    struct id_image_type
    {
      unsigned long long id_value;
      my_bool id_null;

      std::size_t version;
    };

    struct image_type
    {
      // id
      //
      unsigned long long id_value;
      my_bool id_null;

      // presetId
      //
      unsigned long long presetId_value;
      my_bool presetId_null;

      // line0
      //
      details::buffer line0_value;
      unsigned long line0_size;
      my_bool line0_null;

      // line1
      //
      details::buffer line1_value;
      unsigned long line1_size;
      my_bool line1_null;

      // line2
      //
      details::buffer line2_value;
      unsigned long line2_size;
      my_bool line2_null;

      // line3
      //
      details::buffer line3_value;
      unsigned long line3_size;
      my_bool line3_null;

      // line4
      //
      details::buffer line4_value;
      unsigned long line4_size;
      my_bool line4_null;

      // line5
      //
      details::buffer line5_value;
      unsigned long line5_size;
      my_bool line5_null;

      // line6
      //
      details::buffer line6_value;
      unsigned long line6_size;
      my_bool line6_null;

      // line7
      //
      details::buffer line7_value;
      unsigned long line7_size;
      my_bool line7_null;

      // line8
      //
      details::buffer line8_value;
      unsigned long line8_size;
      my_bool line8_null;

      // line9
      //
      details::buffer line9_value;
      unsigned long line9_size;
      my_bool line9_null;

      // line10
      //
      details::buffer line10_value;
      unsigned long line10_size;
      my_bool line10_null;

      // line11
      //
      details::buffer line11_value;
      unsigned long line11_size;
      my_bool line11_null;

      // line12
      //
      details::buffer line12_value;
      unsigned long line12_size;
      my_bool line12_null;

      // line13
      //
      details::buffer line13_value;
      unsigned long line13_size;
      my_bool line13_null;

      // line14
      //
      details::buffer line14_value;
      unsigned long line14_size;
      my_bool line14_null;

      // line15
      //
      details::buffer line15_value;
      unsigned long line15_size;
      my_bool line15_null;

      std::size_t version;
    };

    struct extra_statement_cache_type;

    using object_traits<object_type>::id;

    static id_type
    id (const id_image_type&);

    static id_type
    id (const image_type&);

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&,
          mysql::statement_kind);

    static void
    bind (MYSQL_BIND*, id_image_type&);

    static bool
    init (image_type&,
          const object_type&,
          mysql::statement_kind);

    static void
    init (object_type&,
          const image_type&,
          database*);

    static void
    init (id_image_type&, const id_type&);

    typedef mysql::object_statements<object_type> statements_type;

    typedef mysql::query_base query_base_type;

    static const std::size_t column_count = 18UL;
    static const std::size_t id_column_count = 1UL;
    static const std::size_t inverse_column_count = 0UL;
    static const std::size_t readonly_column_count = 0UL;
    static const std::size_t managed_optimistic_column_count = 0UL;

    static const std::size_t separate_load_column_count = 0UL;
    static const std::size_t separate_update_column_count = 0UL;

    static const bool versioned = false;

    static const char persist_statement[];
    static const char find_statement[];
    static const char update_statement[];
    static const char erase_statement[];
    static const char query_statement[];
    static const char erase_query_statement[];

    static const char table_name[];

    static void
    persist (database&, object_type&);

    static pointer_type
    find (database&, const id_type&);

    static bool
    find (database&, const id_type&, object_type&);

    static bool
    reload (database&, object_type&);

    static void
    update (database&, const object_type&);

    static void
    erase (database&, const id_type&);

    static void
    erase (database&, const object_type&);

    static result<object_type>
    query (database&, const query_base_type&);

    static unsigned long long
    erase_query (database&, const query_base_type&);

    public:
    static bool
    find_ (statements_type&,
           const id_type*);

    static void
    load_ (statements_type&,
           object_type&,
           bool reload);
  };

  template <>
  class access::object_traits_impl< ::db::PresetLaneLine, id_common >:
    public access::object_traits_impl< ::db::PresetLaneLine, id_mysql >
  {
  };

  // PresetLaneLine
  //
}

#include "preset_lane_line-odb.ixx"

#include <odb/post.hxx>

#endif // PRESET_LANE_LINE_ODB_HXX
