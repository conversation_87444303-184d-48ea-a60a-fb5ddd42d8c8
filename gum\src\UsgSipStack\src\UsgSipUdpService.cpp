
#include <map>
#include <iostream>
#include <boost/lexical_cast.hpp>

#include "SipPrinter.hpp"
#include "InviteSession.hpp"
#include "DdcpDoSession.hpp"
#include "RegistSession.hpp"
#include "NotifySession.hpp"

#include "UsgSipUdpService.hpp"
#include "ailog.h"

namespace usg {

    namespace {

        std::map<void *, CUsgSipUdpService *> g_services;

        CUsgSipUdpService *getSipUdpService( void *key )
        {
            std::map<void *, CUsgSipUdpService *>::iterator it = g_services.find( key );
            if ( it == g_services.end() ) return 0;

            return (*it).second;
        }

    }

// CUsgSipUdpService::CUsgSipUdpService( const std::string &lAddr, uint16_t lPort, const std::string &lCode )
// : m_inviteSession( 0 ), m_notifySession( 0 ), m_registSession( 0 ), m_ddcpDoSession( 0 ),
//   m_addr( lAddr ), m_port( lPort ), m_code( lCode ),
//   m_pool( 0 ), m_endPoint( 0 ), m_thread( 0 ),
//   m_isExit( false ),m_xmlType("DDCP"),m_mutex( NULL )
// {
//     /*
//      * 模块名称.
//      */
//     m_modStr = "SipUdp@";
//     char buf[16] = { 0 };
//     sprintf( buf, "%d", lPort );
//     m_modStr += buf;
//
//     /*
//      * 本地串.
//      */
//     m_from = "sip:";
//     m_from = m_from + lCode + "@";
//     m_from = m_from + lAddr + ":";
//     m_from = m_from + buf;
//
//     /*
//      * 创建内存池.
//      */
//     pj_caching_pool_init( &m_cache, 0, 0 );
//     m_pool = pj_pool_create( &m_cache.factory, m_modStr.c_str(), 4096, 1024, 0 );
//     /*
//      * 设置本地监听地址.
//      */
//     pj_str_t ipStr;
//     ipStr.ptr = (char *)m_addr.c_str();
//     ipStr.slen = m_addr.size();
//     pj_sockaddr_init( pj_AF_INET(), &m_lSock, &ipStr, lPort );
    /*
//      * 初始化模块.
//      */
//     pj_bzero( &m_module, sizeof( m_module ) );
//     m_module.name.ptr       = (char *)m_modStr.c_str();
//     m_module.name.slen      = strlen( m_module.name.ptr );
//     m_module.id             = -1;
//     m_module.priority       = PJSIP_MOD_PRIORITY_TSX_LAYER;
//     m_module.on_rx_request  = &onRxRequest;
//     m_module.on_rx_response = &onRxResponse;
// }

    CUsgSipUdpService::CUsgSipUdpService( const std::string &lAddr, uint16_t lPort, const std::string &lCode, const std::string& xmlType )
            : m_inviteSession( 0 ), m_notifySession( 0 ), m_registSession( 0 ), m_ddcpDoSession( 0 ),
              m_addr( lAddr ), m_port( lPort ), m_code( lCode ),
              m_pool( 0 ), m_endPoint( 0 ), m_thread( 0 ),
              m_isExit( false ),m_xmlType(xmlType),m_mutex( NULL )
    {
        std::string temp;
        try
        {
            temp = boost::lexical_cast< std::string >( lPort );
        }
        catch ( boost::bad_lexical_cast & )
        {
            std::cout << " boost::lexical_cast fail. " << std::endl;
        }
        catch ( ... )
        {
            ;
        }
        /*
        * 模块名称.
        */
        m_modStr = "SipUdp@";
        //char buf[16] = { 0 };
        //sprintf( buf, "%d", lPort );
        m_modStr += temp;
        /*
        * 本地串.
        */
        m_from = "sip:";
        m_from = m_from + lCode + "@";
        m_from = m_from + lAddr + ":";
        m_from = m_from + temp;
        ai::LogInfo << "Usg lAddr : " << m_from;

        /*
        * 创建内存池.
        */
        pj_caching_pool_init( &m_cache, 0, 0 );
        m_pool = pj_pool_create( &m_cache.factory, m_modStr.c_str(), 4096, 1024, 0 );
        /*
        * 设置本地监听地址.
        */
        pj_str_t ipStr;
        if ( m_addr != "127.0.0.1" && m_addr != "0.0.0.0" )
        {
            ipStr.ptr = (char *)m_addr.c_str();
            ipStr.slen = m_addr.size();
        }
        else
        {
            ipStr.ptr = 0;
            ipStr.slen = 0;
        }
        pj_status_t ret = pj_sockaddr_init( pj_AF_INET(), &m_lSock, &ipStr, lPort );
        if ( ret == 0 )
        {
            //std::cout << "pj_sockaddr_init ret " << ret << std::endl;
        }
        /*
        * 初始化模块.
        */
        pj_bzero( &m_module, sizeof( m_module ) );
        m_module.name.ptr       = (char *)m_modStr.c_str();
        m_module.name.slen      = strlen( m_module.name.ptr );
        m_module.id             = -1;
        m_module.priority       = PJSIP_MOD_PRIORITY_TSX_LAYER;
        m_module.on_rx_request  = &onRxRequest;
        m_module.on_rx_response = &onRxResponse;
    }

    CUsgSipUdpService::~CUsgSipUdpService()
    {
        pj_pool_release( m_pool );
        pj_caching_pool_destroy( &m_cache );
    }

    bool CUsgSipUdpService::init()
    {
        pjsip_cfg()->endpt.disable_tcp_switch = 1;
        /*
         * 创建传输端点.
         */
        if ( PJ_SUCCESS != pjsip_endpt_create( &m_cache.factory, "SipService", &m_endPoint ) )
            return false;

        if ( PJ_SUCCESS != pjsip_udp_transport_start( m_endPoint, &m_lSock.ipv4, 0, 1, 0 ) )
        {
            ai::LogError << "listen addr=" << m_addr << ":" << m_port << " FAIL!" << " STOP USG Manager";
            return false;
        }

        /*
         * 注册服务模块.
         */
        if ( PJ_SUCCESS != pjsip_endpt_register_module( m_endPoint, &m_module ) )
            return false;
        /*
         * 注册日志打印模块.
         */
        CSipPrinter printer;
        if ( !printer.registerModule( m_endPoint ) )
            return false;

        if ( PJ_SUCCESS != pj_mutex_create_recursive( m_pool, "@SipMutex@", &m_mutex ) )
            return false;

        /*
         * 创建消息处理线程.
         */
        if ( PJ_SUCCESS != pj_thread_create( m_pool, "@SipUdp@", &handleEvent, this, 1024 * 1024, 0, &m_thread ) )
            return false;

        /*
         * 创建会话对象.
         */
        m_inviteSession = new CInviteSession( m_pool, m_endPoint, m_from, m_xmlType );
        m_ddcpDoSession = new CDdcpDoSession( m_pool, m_endPoint, m_from, m_xmlType );
        m_registSession = new CRegistSession( m_pool, m_endPoint, m_from, m_xmlType );
        m_notifySession = new CNotifySession( m_pool, m_endPoint, m_from, m_xmlType );

        g_services[m_endPoint] = this;

        return true;
    }

    bool CUsgSipUdpService::fini()
    {
        /*
         * 关闭消息处理线程.
         */
        if ( m_thread )
        {
            m_isExit = true;
            pj_thread_join( m_thread );
            m_thread = 0;
        }

        if ( m_mutex )
        {
            pj_mutex_destroy( m_mutex );
            m_mutex = 0;
        }

        delete m_inviteSession; m_inviteSession = 0;
        delete m_ddcpDoSession; m_ddcpDoSession = 0;
        delete m_registSession; m_registSession = 0;
        delete m_notifySession; m_notifySession = 0;

        g_services.erase( m_endPoint );
        /*
         * 析构终端点.
         */
        if ( m_endPoint )
            pjsip_endpt_destroy( m_endPoint );

        return true;
    }

    bool CUsgSipUdpService::okey()
    {
        return m_thread != 0;
    }

    pj_bool_t CUsgSipUdpService::onRxRequest( pjsip_rx_data *rdata )
    {
        if ( rdata->msg_info.msg->type != PJSIP_REQUEST_MSG )
            return PJ_FALSE;
        /*
         * 通过终端点查找服务对象.
         */
        CUsgSipUdpService *service = getSipUdpService( rdata->tp_info.transport->endpt );
        if ( !service ) return PJ_FALSE;

        pj_mutex_lock( service->m_mutex );

        switch ( rdata->msg_info.msg->line.req.method.id )
        {
            case PJSIP_INVITE_METHOD:
                service->m_inviteSession->onInvite( rdata );
                break;

            case PJSIP_CANCEL_METHOD:
                service->m_inviteSession->onCancel( rdata );
                break;

            case PJSIP_ACK_METHOD:
                service->m_inviteSession->onAck( rdata );
                break;

            case PJSIP_BYE_METHOD:
                service->m_inviteSession->onBye( rdata );
                break;

            case PJSIP_REGISTER_METHOD:
                service->m_registSession->onRegist( rdata );
                break;

            case PJSIP_OTHER_METHOD:
                if ( 0 == strncmp( rdata->msg_info.msg->line.req.method.name.ptr, "DO", 2 ) ||
                     0 == strncmp( rdata->msg_info.msg->line.req.method.name.ptr, "INFO", 4 ) ||
                     0 == strncmp( rdata->msg_info.msg->line.req.method.name.ptr, "MESSAGE", 7 ) )
                    service->m_ddcpDoSession->onDdcpDo( rdata );
                if ( 0 == strncmp( rdata->msg_info.msg->line.req.method.name.ptr, "SUBSCRIBE", 9 ) )
                    service->m_notifySession->onSubscribe( rdata );
                if ( 0 == strncmp( rdata->msg_info.msg->line.req.method.name.ptr, "NOTIFY", 6 ) )
                    service->m_notifySession->onNotify( rdata );
                break;

            default:
                break;
        }
        pj_mutex_unlock( service->m_mutex );

        return PJ_TRUE;
    }

    pj_bool_t CUsgSipUdpService::onRxResponse( pjsip_rx_data *rdata )
    {
        /*
         * 通过终端点查找服务对象.
         */
        CUsgSipUdpService *service = getSipUdpService( rdata->tp_info.transport->endpt );
        if ( !service ) return PJ_FALSE;

        pj_mutex_lock( service->m_mutex );

        switch ( rdata->msg_info.cseq->method.id )
        {
            case PJSIP_INVITE_METHOD:
                service->m_inviteSession->onAnswer( rdata );
                break;

            case PJSIP_BYE_METHOD:
                //service->m_inviteSession->onAnswer( rdata );
                break;

            case PJSIP_REGISTER_METHOD:
                service->m_registSession->onAnswer( rdata );
                break;

            case PJSIP_OTHER_METHOD:
                if ( 0 == strncmp( rdata->msg_info.cseq->method.name.ptr, "DO", 2 ) ||
                     0 == strncmp( rdata->msg_info.cseq->method.name.ptr, "INFO", 4 ) ||
                     0 == strncmp( rdata->msg_info.cseq->method.name.ptr, "MESSAGE", 7 ) )
                    service->m_ddcpDoSession->onAnswer( rdata );
                if ( 0 == strncmp( rdata->msg_info.cseq->method.name.ptr, "SUBSCRIBE", 9 ) )
                    service->m_notifySession->onAnswer( rdata );
                if ( 0 == strncmp( rdata->msg_info.cseq->method.name.ptr, "NOTIFY", 6 ) )
                    service->m_notifySession->onAnswer( rdata );
                break;

            default:
                break;
        }
        pj_mutex_unlock( service->m_mutex );

        return PJ_TRUE;
    }

    int CUsgSipUdpService::handleEvent( void *arg )
    {
//    TRY_REGISTER_THIS_THREAD(0);
        pj_thread_t *this_thread = 0;
        pj_thread_desc desc = {0};
        if (PJ_SUCCESS != pj_thread_register(0, desc, &this_thread))
            return 0;

        CUsgSipUdpService *This = (CUsgSipUdpService *)arg;

        while ( !This->m_isExit )
        {
            pj_time_val interval = { 0, 200 };
            pjsip_endpt_handle_events( This->m_endPoint, &interval );
        }

        return 0;
    }

    IInviteSession *CUsgSipUdpService::inviteSession( ICtxHandler *handler )
    {
        return m_inviteSession->setHandler( handler );
    }

    INotifySession *CUsgSipUdpService::notifySession( ICtxHandler *handler )
    {
        return m_notifySession->setHandler( handler );
    }

    IRegistSession *CUsgSipUdpService::registSession( ICtxHandler *handler )
    {
        return m_registSession->setHandler( handler );
    }

    IDdcpDoSession *CUsgSipUdpService::ddcpDoSession( ICtxHandler *handler )
    {
        return m_ddcpDoSession->setHandler( handler );
    }

}
