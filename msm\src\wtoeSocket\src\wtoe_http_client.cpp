#include "wtoe_http_client.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/document.h"
#include <iostream>
#include <sstream>
#include <curl/curl.h>

using namespace rapidjson;
using namespace std;


WtoeAIHttpClient::WtoeAIHttpClient()
: ChannelIndex(0), PrintLog(false),m_cbhandleMsgReceived(0)
{
	curl_global_init(CURL_GLOBAL_ALL);
}

WtoeAIHttpClient::~WtoeAIHttpClient()
{
	curl_global_cleanup();
}

int WtoeAIHttpClient::do_get(std::string url, std::string key_values, std::string & response)
{
    CURLcode res;
    CURL* curl = curl_easy_init();
    if (NULL == curl)
    {
        return CURLE_FAILED_INIT;
    }

    curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
    curl_easy_setopt(curl, CURLOPT_READFUNCTION, NULL);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, OnWriteData);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *)&response);
    /**
    * ������̶߳�ʹ�ó�ʱ�����ʱ��ͬʱ���߳�����sleep����wait�Ȳ�����
    * ������������ѡ�libcurl���ᷢ�źŴ�����wait�Ӷ����³����˳���
    */
    curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1);
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 3);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 3);
    res = curl_easy_perform(curl);
    curl_easy_cleanup(curl);
    return res;
}

int WtoeAIHttpClient::do_post(std::string url, std::string body_values, std::string & response)
{
    CURLcode res;
    CURL* curl = curl_easy_init();
    if (NULL == curl)
    {
        return CURLE_FAILED_INIT;
    }

	struct curl_slist *headers = NULL;

	//����HTTP header
	headers = curl_slist_append(headers, "Accept:application/json");
	headers = curl_slist_append(headers, "Content-Type:application/json");
	headers = curl_slist_append(headers, "charset:utf-8");
	curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

    curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
    curl_easy_setopt(curl, CURLOPT_POST, 1);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body_values.c_str());
    curl_easy_setopt(curl, CURLOPT_READFUNCTION, NULL);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, OnWriteData);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *)&response);
    curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1);
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 3);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 3);
    res = curl_easy_perform(curl);
    curl_easy_cleanup(curl);
	/* free the custom headers */
	curl_slist_free_all(headers);
    return res;
}


size_t WtoeAIHttpClient::OnWriteData(void* buffer, size_t size, size_t nmemb, void* lpVoid)
{
    std::string* str = dynamic_cast<std::string*>((std::string *)lpVoid);
    if (NULL == str || NULL == buffer)
    {
        return -1;
    }

    char* pData = (char*)buffer;
    str->append(pData, size * nmemb);
    return nmemb;
}

int WtoeAIHttpClient::videoQuaAlarm(std::string url, VideoQuaAlarmConf& videoQuaAlarmConf)
{
	Document d;
	d.SetObject();
	d.AddMember("videoId",videoQuaAlarmConf.iVideoId, d.GetAllocator());
	d.AddMember("ip",rapidjson::StringRef(videoQuaAlarmConf.ip.c_str()), d.GetAllocator());

	Value vtTypes(rapidjson::kArrayType);
	for (size_t i = 0; i< videoQuaAlarmConf.vtAlarmType.size(); i++)
	{
		vtTypes.PushBack(videoQuaAlarmConf.vtAlarmType[i], d.GetAllocator());
	}
	d.AddMember("type", vtTypes, d.GetAllocator());

	
	d.AddMember("pauseDetect",videoQuaAlarmConf.isPauseDetect, d.GetAllocator());

    StringBuffer buffer;
    Writer<StringBuffer> writer(buffer);
    d.Accept(writer);

	return sendMsg(url, buffer.GetString());
}


int WtoeAIHttpClient::eventRecord(std::string url, EventInfo& eventInfo)
{
	Document d;
	d.SetObject();
	d.AddMember("roiId",eventInfo.iRoiId, d.GetAllocator());
	d.AddMember("laneId", eventInfo.iLaneId, d.GetAllocator());
	d.AddMember("checkAreaId", eventInfo.iCheckAreaId, d.GetAllocator());
	d.AddMember("eventId", rapidjson::StringRef(eventInfo.strEventId.c_str()), d.GetAllocator());
	d.AddMember("eventTypeId",eventInfo.iEventTypeId, d.GetAllocator());
	d.AddMember("occurTime",rapidjson::StringRef(eventInfo.occurTime.c_str()), d.GetAllocator());
	d.AddMember("eventVideo",rapidjson::StringRef(eventInfo.eventVideo.c_str()), d.GetAllocator());
	d.AddMember("eventImg",rapidjson::StringRef(eventInfo.eventImg.c_str()), d.GetAllocator());
	d.AddMember("isAlarm",eventInfo.isAlarm, d.GetAllocator());
	d.AddMember("areaTypeId",eventInfo.iAreaTypeId, d.GetAllocator());
	
	Value objectArea(rapidjson::kArrayType);
	for (size_t i = 0; i< eventInfo.objectArea.vtPoint.size(); i++)
	{
		Value point(rapidjson::kObjectType);
		point.AddMember("x",eventInfo.objectArea.vtPoint[i].x, d.GetAllocator());
		point.AddMember("y",eventInfo.objectArea.vtPoint[i].y, d.GetAllocator());
		objectArea.PushBack(point, d.GetAllocator());
	}
	d.AddMember("objectArea", objectArea, d.GetAllocator());

    StringBuffer buffer;
    Writer<StringBuffer> writer(buffer);
    d.Accept(writer);

	return sendMsg(url, buffer.GetString());
}

int WtoeAIHttpClient::playTempStatus(std::string url, int videoId, int streamId, int status )
{
	Document d;
	d.SetObject();
	d.AddMember("videoId", videoId, d.GetAllocator());
	d.AddMember("streamId", streamId, d.GetAllocator());
	d.AddMember("status", status, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return sendMsg(url, buffer.GetString());
}
int WtoeAIHttpClient::eventVideoFinished(std::string url, std::string urlEventVideo)
{
	Document d;
	d.SetObject();
	d.AddMember("eventVideo",rapidjson::StringRef(urlEventVideo.c_str()), d.GetAllocator());

    StringBuffer buffer;
    Writer<StringBuffer> writer(buffer);
    d.Accept(writer);

	return sendMsg(url, buffer.GetString());
}

int WtoeAIHttpClient::heartInfo(std::string url, int status, int localPort )
{
	Document d;
	d.SetObject();
	d.AddMember("status", status, d.GetAllocator());
    d.AddMember("port", localPort, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return sendMsg(url, buffer.GetString());
}

int WtoeAIHttpClient::streamStatus(std::string url, int channelId, int videoId, int status)
{
	Document d;
	d.SetObject();
	d.AddMember("channelId", channelId, d.GetAllocator());
	d.AddMember("videoId", videoId, d.GetAllocator());
	d.AddMember("status", status, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return sendMsg(url, buffer.GetString());
}

int WtoeAIHttpClient::videoQuaRecovery(std::string url, int iVideoId, std::vector<int> vtType, int iPresetId,std::string ip)
{
	Document d;
	d.SetObject();
	d.AddMember("videoId",iVideoId, d.GetAllocator());
	
	Value vtTypes(rapidjson::kArrayType);
	for (size_t i = 0; i< vtType.size(); i++)
	{
		vtTypes.PushBack(vtType[i], d.GetAllocator());
	}
	d.AddMember("type", vtTypes, d.GetAllocator());

	d.AddMember("presetId", iPresetId, d.GetAllocator());

	d.AddMember("ip",rapidjson::StringRef(ip.c_str()), d.GetAllocator());

    StringBuffer buffer;
    Writer<StringBuffer> writer(buffer);
    d.Accept(writer);

	return sendMsg(url, buffer.GetString());
}


int WtoeAIHttpClient::saveDetectInfo(std::string url, DetectInfo& detectInfo)
{
	Document d;
	d.SetObject();

	Value vtTrackinfo(rapidjson::kArrayType);
	for (size_t i = 0; i< detectInfo.vtTrackinfo.size(); i++)
	{
		Value trackinfo(rapidjson::kObjectType);
		trackinfo.AddMember("roiId", detectInfo.vtTrackinfo[i].iRoiId, d.GetAllocator());
		trackinfo.AddMember("laneId", detectInfo.vtTrackinfo[i].iLaneId, d.GetAllocator());
		trackinfo.AddMember("trackId", detectInfo.vtTrackinfo[i].iTrackId, d.GetAllocator());
		trackinfo.AddMember("mainLabel", detectInfo.vtTrackinfo[i].iMainLabel, d.GetAllocator());

//		trackinfo.AddMember("timeBegin", rapidjson::StringRef(detectInfo.vtTrackinfo[i].timeBegin.c_str()), d.GetAllocator());
		trackinfo.AddMember("timeBegin", rapidjson::Value().SetString(detectInfo.vtTrackinfo[i].timeBegin.c_str(), d.GetAllocator()).Move(), d.GetAllocator());
//		trackinfo.AddMember("timeEnd", rapidjson::StringRef(detectInfo.vtTrackinfo[i].timeEnd.c_str()), d.GetAllocator());
		trackinfo.AddMember("timeEnd", rapidjson::Value().SetString(detectInfo.vtTrackinfo[i].timeEnd.c_str(), d.GetAllocator()).Move(), d.GetAllocator());
		trackinfo.AddMember("trackSpeed", detectInfo.vtTrackinfo[i].trackSpeed, d.GetAllocator());
		vtTrackinfo.PushBack(trackinfo, d.GetAllocator());
	}
	d.AddMember("tracks", vtTrackinfo, d.GetAllocator());

#if 0
	Value status(rapidjson::kObjectType);
	status.AddMember("channelID", detectInfo.detectPointStatus.iChannelID, d.GetAllocator());
	status.AddMember("curVideoId", detectInfo.detectPointStatus.iCurVideoId, d.GetAllocator());
	status.AddMember("detectStatus", detectInfo.detectPointStatus.detectStatus, d.GetAllocator());
	d.AddMember("status", status, d.GetAllocator());

	//frameInfo
	Value vtFrameInfo(rapidjson::kArrayType);
	for (size_t i = 0; i < detectInfo.vtCarInfo.size(); i++)
	{
		Value frameInfo(rapidjson::kObjectType);
		frameInfo.AddMember("logicId", detectInfo.vtCarInfo[i].iLogicId, d.GetAllocator());
		frameInfo.AddMember("carNum", detectInfo.vtCarInfo[i].iCarNum, d.GetAllocator());
		vtFrameInfo.PushBack(frameInfo, d.GetAllocator());
	}
	d.AddMember("frameInfos", vtFrameInfo, d.GetAllocator());
#endif

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return sendMsg(url, buffer.GetString());
}

int WtoeAIHttpClient::saveDetectInfo(std::string url, std::vector<DetectInfo>& vtDetectInfo)
{
	Document d;
	d.SetObject();

	Value js_vtDetectInfo(rapidjson::kArrayType);

	for (size_t k = 0; k < vtDetectInfo.size(); k++)
	{
		DetectInfo detectInfo = vtDetectInfo[k];
		Value js_detectInfo(rapidjson::kObjectType);

		Value vtTrackinfo(rapidjson::kArrayType);
		for (size_t i = 0; i < detectInfo.vtTrackinfo.size(); i++)
		{
			Value trackinfo(rapidjson::kObjectType);
			trackinfo.AddMember("roiId", detectInfo.vtTrackinfo[i].iRoiId, d.GetAllocator());
			trackinfo.AddMember("laneId", detectInfo.vtTrackinfo[i].iLaneId, d.GetAllocator());
			trackinfo.AddMember("trackId", detectInfo.vtTrackinfo[i].iTrackId, d.GetAllocator());
			trackinfo.AddMember("mainLabel", detectInfo.vtTrackinfo[i].iMainLabel, d.GetAllocator());

//			trackinfo.AddMember("timeBegin", rapidjson::StringRef(detectInfo.vtTrackinfo[i].timeBegin.c_str()), d.GetAllocator());
			trackinfo.AddMember("timeBegin", rapidjson::Value().SetString(detectInfo.vtTrackinfo[i].timeBegin.c_str(), d.GetAllocator()).Move(), d.GetAllocator());
//			trackinfo.AddMember("timeEnd", rapidjson::StringRef(detectInfo.vtTrackinfo[i].timeEnd.c_str()), d.GetAllocator());
			trackinfo.AddMember("timeEnd", rapidjson::Value().SetString(detectInfo.vtTrackinfo[i].timeEnd.c_str(), d.GetAllocator()).Move(), d.GetAllocator());
			trackinfo.AddMember("trackSpeed", detectInfo.vtTrackinfo[i].trackSpeed, d.GetAllocator());

			vtTrackinfo.PushBack(trackinfo, d.GetAllocator());
		}
		js_detectInfo.AddMember("tracks", vtTrackinfo, d.GetAllocator());

		js_vtDetectInfo.PushBack(js_detectInfo, d.GetAllocator());
	}

	d.AddMember("detectInfos", js_vtDetectInfo, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return sendMsg(url, buffer.GetString());
}

int WtoeAIHttpClient::eventRemove(std::string url, EventRemoveInfo& eventRemoveInfo)
{
	Document d;
	d.SetObject();
	d.AddMember("eventId", rapidjson::StringRef(eventRemoveInfo.eventId.c_str()), d.GetAllocator());
	d.AddMember("finishTime", rapidjson::StringRef(eventRemoveInfo.finishTime.c_str()), d.GetAllocator());
	d.AddMember("finishImg", rapidjson::StringRef(eventRemoveInfo.finishImg.c_str()), d.GetAllocator());
	d.AddMember("eventTypeId", eventRemoveInfo.eventTypeId, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return sendMsg(url, buffer.GetString());
}

int WtoeAIHttpClient::eventWithdraw(std::string url, std::string eventId, int eventTypeId )
{
	Document d;
	d.SetObject();
	d.AddMember("eventId", rapidjson::StringRef( eventId.c_str()), d.GetAllocator());
	d.AddMember("eventTypeId", eventTypeId, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return sendMsg(url, buffer.GetString());
}
int WtoeAIHttpClient::detectStatus(std::string url, DetectStatusInfo& detectStatusInfo)
{
	Document d;
	d.SetObject();
	d.AddMember("channelId", detectStatusInfo.iChannelID, d.GetAllocator());
	d.AddMember("videoId", detectStatusInfo.iVideoID, d.GetAllocator());
	d.AddMember("status", detectStatusInfo.iStatus, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return sendMsg( url, buffer.GetString() );
}

void WtoeAIHttpClient::RegisterMsgReceived( CBHandleMsgReceived cb, void*pUserData )
{
	m_cbhandleMsgReceived = cb;
}

int WtoeAIHttpClient::sendMsg( std::string url, std::string msg )
{
	std::string response;
	int ret = do_post(url, msg, response);
	int code = 0;
	if (CURLE_OK == ret)
	{
		Document doc;
		doc.Parse(response.c_str());
		if (!doc.HasParseError())
		{
			if (doc.HasMember("code") && doc["code"].IsInt())
			{
				code = doc["code"].GetInt();
			}
		}
	}
	if (PrintLog)
	{
		std::stringstream ss;
		ss << "Channel[" << ChannelIndex << "][http]==" << url.c_str() << " msg: " << msg.c_str() << " ret: " << code
			<< " with: " << response.c_str();

		if (m_cbhandleMsgReceived)
			m_cbhandleMsgReceived(ss.str(), NULL);
		else
			std::cout << ss.str() << endl;
	}
	return (code == 200 || code == 0 )? WTOESOCKET_TRUE : WTOESOCKET_FALSE;
}
