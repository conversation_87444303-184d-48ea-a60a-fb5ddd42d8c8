/**
 * Project FVM
 */
#pragma once
#include "front_platform.h"
#include <atomic>
#include "util/timer/timer_manager.h"

namespace fvm::platform
{
    //回调函数指针
    using RecvCallback = std::function<void(uint8_t*, int)>;

    //单路海康通道管理
    struct HKPlay
    {
        int iUserId = -1;                               //海康用户关联ID
        std::string szChanName;                         //通道名称
        int iChanIndex = 0;                             //监控通道号=数组索引+iStartChan  播放ID
        int iEnable = 0;                                //是否有效
        int iRealPlayHandle = -1;                       //播放句柄
        RecvCallback fRecvCallback = nullptr;           //对应回调函数

        HKPlay(void)
        {};

        /**
         * @brief 构造函数
         * @param[in] iId 海康ID
         * @param[in] szName 通道名称
         * @param[in] iIndex 播放ID
         * @param[in] state 是否有效
         */
        HKPlay(int iId, std::string szName, int iIndex, int state = 1)
            :iUserId(iId), szChanName(szName), iChanIndex(iIndex), iEnable(state)
        {};

        //包装设置回调
        void setCallback(RecvCallback pCallback);

        //海康回调的处理函数
        void dealData(uint8_t* buf, int bufSize);

        //开始播放
        bool startPlay(RecvCallback pCallback);

        //停止播放
        void stopPlay(void);

        //切换设置到预置点
        bool ptzControl(int iActPreset);

        //保存预置点
        bool savePreset(int iActPreset);

        //获取当前ptz坐标
        bool getPtzPosition(double& x, double& y, double& z);
    };

    //海康登录播放管理
    class SDKHKPlatform : public FrontPlatform
    {
    public:
        //初始化SDK，只调用一次
        static void initSDK();

        //清理SDK
        static void cleanupSDK();
    public:
        //操作海康初始化
        SDKHKPlatform();

        //操作释放
        virtual ~SDKHKPlatform();

        //初始化时登录
        void init(data::VideoServerPtr svrPtr) override;

        //注销登录
        void fini(void);

        //请求播放海康通道
        bool startPlay(const std::string& addr, RecvCallback pCallback);

        //停止播放海康通道
        void stopPlay(const std::string& addr);

        //切换设置到预置点
        bool callPreset(VideoSourceInfoPtr videoSourceInfo, int ptzId) { return callPreset(ptzId); }

        bool callPreset(int presetId) override{ return ptzControl(presetId); }
        bool ptzControl(int iPreset);
        bool ptzControl(const std::string& addr, int iPreset);

        //获取当前ptz坐标
        bool getPtzPosition(const std::string& addr, double& x, double& y, double& z);

        // （新增）3D定位
        bool focusRect(VideoSourceInfoPtr videoSourceInfo, int xTop, int yTop, int xBottom, int yBottom) override;

        // 云台保存预置位
        bool savePreset(int presetId);

        // 云台控制ptz
        bool controlPtz(int action, int step);

        //页面修改时请求处理重登录
        void onChanged() override;

        //获取错误信息
        std::string getLastError(void);

        // 设备登录
        bool login(void) override;

    private:
        //登录过程策略
        bool doLogin(void);

        //刷新获取资源列表
        void getResource(void);


        //内部使用 添加维护设备表(登录ID,名称,通道ID)
        void insertVecRes(int iId, std::string szName, int iChanelId);

        /* 根据url获取通道ID addr必须为: ip:通道号
        * @param[in]  addr 为传过来的url       addr = ***********:33
        * @param[out] iRet 为分离出的通道号    iRet = 33
        * @return     解析执行是否成功
        */
        bool getChanelId(const std::string& addr, int& iRet);
    private:
        std::atomic_bool bOnline = false;           //设备是否成功登录
        std::atomic_bool bFirstLogin = true;        //登录器是否第一次打开
        std::atomic_bool bExit = false;             //登录器退出保护
        // timer::TimerPtr timerPtr = nullptr;         //登录计时器
        // timer::TimerPtr getResTimerPtr = nullptr;   //扫描资源计时器
        std::mutex playMtx;
        std::mutex loginMtx;                        // 设备登录锁

        //海康设备管理数据
        int iLoginID = -1;
        uint16_t iDeviceChanNum = 0;                //模拟通道数
        uint16_t iStartChan = 0;                    //起始通道号
        std::vector<HKPlay> vecRes;                 //当前设备资源管理

        int m_iLastError = 0;                       //上次的错误码
        std::string      lastErrorString;           //!< 错误信息
        int loginIgnoreCount = 0;                   //!< 登录间隔时间
        std::atomic_bool bNeedGetResource = true;             // 是否需要获取资源
        int lastPTZcmd = 0;
    };

    typedef std::shared_ptr<platform::SDKHKPlatform> SDKHKPlatformPtr;
}



