
#ifndef DB33PARSER_HPP_
#define DB33PARSER_HPP_

#include <string>
#include <pjlib.h>
#include <pjlib-util.h>

#include "StructExchange.hpp"

#include "../../UsgManager/include/ProtocolWtoeItf.hpp"


namespace usg
{
    struct IWtoeSipFunc;
}

namespace gb28181
{

    class CGb28181Xml;
    class CConfigFile;

/**
 * @brief 此类将网络发来的SIP串,变成wtoe的InfoObject.
 */
    class CGb28181Parser
    {
    public:
        CGb28181Parser(
                usg::IWtoeSipFunc* wsf,
                CConfigFile *configFile,
                const std::string &sid,
                const std::string &resAddr,
                const std::string &gbtXmlStr,
                const usg::ESESSTION_TYPE type );
        ~CGb28181Parser();
    public:
        // 是否构造成功.
        bool isOk();

        std::string getResultStr();
        void getResultVec(std::vector<std::string> &vec);

    private:
        std::string getName( pj_xml_node *p );
        std::string getContent( pj_xml_node *p );
        pj_str_t*   doConv( pj_str_t *dst, const std::string &str );

//    bool request_func_catalog(); // 暂未用到==仅用来进行测试.
        bool response_func_catalog();

        bool request_query_func_devicecatalog();
        bool response_query_func_devicecatalog();
        bool request_notify_func_keepalive(); // 暂未用到==仅用来进行测试.
        bool response_func_keepalive();

        bool request_func_realmedia();
        bool response_func_realmedia();

//    bool request_query_func_filelist();
        bool response_query_func_filelist(); // 暂未用到.

//	bool request_query_func_deviceinfo();
        bool response_query_func_deviceInfo(); // 暂未用到.

//	bool request_query_func_deviceStatus();
        bool response_query_func_deviceStatus(); // 暂未用到.

        bool request_func_vodbyrstp();
        bool response_func_vodbyrstp(); // 暂未用到.

        bool request_control_func_ptzcommand();
        bool response_control_func_ptzcommand(); // 暂未用到.

        bool request_func_reboot(); //远程启动

        bool request_query_func_presetlist();
        bool response_query_func_presetlist(); // 暂未用到.

        bool request_func_alarmsubcribe(); // 暂未用到.==仅用来进行测试.
        bool response_func_alarmsubcribe(); // 暂未用到.==仅用来进行测试.

        bool request_func_alarmnotify(); // 暂未用到.==仅用来进行测试.
        bool response_func_alarmnotify(); // 暂未用到.==仅用来进行测试.

        bool request_control_func_startRecord(); //开始录像
        bool request_control_func_stopRecord(); //停止录像
        bool request_control_func_setGuard(); //布防
        bool request_control_func_resetGuard(); //撤防
        bool request_control_func_resetAlarm(); //复位告警

        bool request_func_subscribe_catalog();//订阅目录
        bool request_func_subscribe_alarm();//事件订阅
//	bool request_query_func_configDownload();//设备配置查询
//	bool request_query_func_deviceConfig(); //配置设备
        bool response_notify_broadcast();//语音广播
        bool response_func_broadcast(); //语音广播响应

        bool request_func_filetoend(); //录像播放到结束

    public:
        bool parserReceivePkg();
        bool parserSubscribeReceivePkg();

    private:
        bool parserAction();
        bool parserResponse();

    private:
        usg::IWtoeSipFunc* m_wsf;
        const std::string m_sid;
        const std::string m_resAddr;
        std::string m_snContent;

        pj_caching_pool m_caching_pool;
        pj_pool_t *m_pool;
        pj_str_t m_msg;
        pj_xml_node *m_root;

        std::string m_result;
        std::vector<std::string> m_resultVec;

        bool m_isOk;
        bool m_isInitCachingPool;
        usg::ESESSTION_TYPE m_type;

        CGb28181Xml *m_gbtXml;
        CConfigFile *m_configFile;
        CStructExchange m_structExchange;
    };

};

#endif
