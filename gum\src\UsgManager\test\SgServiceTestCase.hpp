#ifndef SGSERVICETESTCASE_HPP_
#define SGSERVICETESTCASE_HPP_

#include <cppunit/extensions/HelperMacros.h>

namespace msg
{

class CSgServiceTestCase : public CPPUNIT_NS::TestFixture
{
    CPPUNIT_TEST_SUITE( CSgServiceTestCase );

    //CPPUNIT_TEST( test_onReceiveCatalog );
    //CPPUNIT_TEST( test_getResInfo );
    //CPPUNIT_TEST( test_getAllResInfo );


    CPPUNIT_TEST_SUITE_END();

public:
    CSgServiceTestCase();
    ~CSgServiceTestCase();

public:
    virtual void setUp();
    virtual void tearDown();

public:
    //void test_onReceiveCatalog();
    //void test_getResInfo();
    //void test_getAllResInfo();
};

}

#endif