#include <iostream>
#include <string>
#include <odb/core.hxx>
#include <memory>     // std::auto_ptr

#include <odb/database.hxx>
#include <odb/transaction.hxx>
#include <odb/mysql/database.hxx>

#include "database_process.h"


namespace db{

    using namespace std;
    using namespace odb::core;

    std::shared_ptr<odb::database> databasePtr = nullptr;
    constexpr int MAX_CONNECTIONS = 10;
    constexpr int MIN_CONNECTIONS = 3;

    bool init(const std::string& user, const std::string& passwd, const std::string& dbName, const std::string& host, unsigned int port)
	{
        for (unsigned short retryCount (0); ; retryCount++)
        {
            try
            {
                unique_ptr<odb::mysql::connection_factory> connectionFactory (new odb::mysql::connection_pool_factory (MAX_CONNECTIONS,MIN_CONNECTIONS));
                cout << "try connecting " << host << " database ..." << endl;
                auto pMysql = new(std::nothrow) odb::mysql::database(user.c_str(), passwd.c_str(), dbName.c_str(),
                                                                     host.c_str(), port, nullptr, "utf8",0, std::move(connectionFactory));
                if (nullptr == pMysql)
                {
                    if (retryCount > MAX_RETRIES)
                    {
                        std::cerr << " retry connect limit exceeded "<< std::endl;
                        return false;
                    }
                    cout << "connected failed, try again "<< endl;
                    continue;
                }
                databasePtr.reset(pMysql);
                cout << "connected success" << endl;
                return true;
            }
            catch (const odb::recoverable &e)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr << "retry limit exceeded : " << e.what() << std::endl;
                    return false;
                }
                continue;
            }
            catch (const odb::exception &e)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr << e.what() << std::endl;
                    return false;
                }
                continue;
            }
            catch (...)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr << " caught an anonymous exception " << std::endl;
                    return false;
                }
                continue;
            }
        }
	}

    std::shared_ptr<odb::database> getDatabase()
    {
        return databasePtr;
    }

	bool deinit()
	{
		databasePtr.reset();
		return true;
	}

}
