/**
 * Project FVM
 */
#include "stream_pusher.h"
#include "boost/timer.hpp"

/**
 * StreamPusher implementation
 *
 * ffmpeg 流输出 （RTP、RTMP、UDP等）
 */
namespace fvm::stream
{
    void StreamPusher::process() 
    {
        printInfo(str(boost::format("Start pushing %s") % this->address ));

        while (jobIsRunning())
        {
            //1.取出缓存数据
            PacketDataPtr packetData;
            if (!this->packetDatas.empty())
            {
                std::lock_guard<boost::fibers::mutex> lock(mutexOutput);
                packetData = this->packetDatas.back();
                this->packetDatas.pop_back();
            }
            else
            {
                this->newData.waitFor(std::chrono::milliseconds(10));
                continue;
            }

            boost::timer debugTimer;
            //2.打开连接处理
            if (!open())
            {
                int millisecondPassed = debugTimer.elapsed() * 1000;
                if(millisecondPassed > 100)
                    std::cerr << this->address<< " openContext "<<  millisecondPassed << std::endl;

                boost::this_fiber::sleep_for(std::chrono::milliseconds(50));
                continue;
            }

            //3.下面处理推流
            auto pkt = packetData->getData(true);                       //取clone数据
            if (pkt == nullptr)
            {
                boost::this_fiber::sleep_for(std::chrono::milliseconds(2));
                continue;
            }
            
            // 检查H.264 NAL单元类型，过滤掉未定义的类型
            if (pkt->data && pkt->size > 4 && formatCtx->streams[0]->codecpar->codec_id == AV_CODEC_ID_H264)
            {
                // 检查NAL单元起始码
                if ((pkt->data[0] == 0x00 && pkt->data[1] == 0x00 && pkt->data[2] == 0x00 && pkt->data[3] == 0x01) || 
                    (pkt->data[0] == 0x00 && pkt->data[1] == 0x00 && pkt->data[2] == 0x01))
                {
                    int offset = (pkt->data[2] == 0x01) ? 3 : 4;
                    uint8_t nal_type = pkt->data[offset] & 0x1F;
                    if (nal_type == 0) // 未定义的NAL类型
                    {
                        freeAVPacket(pkt);
                        continue; // 跳过此包
                    }
                }
            }
            
            calcPts(pkt);                                               //计算pts
            int ret = av_interleaved_write_frame(formatCtx, pkt);       //推rtmp
            freeAVPacket(pkt);                                          //释放Packet
            if (ret < 0)
            {
                setLastError(str(boost::format("%s frameIndex:%d write_frame FAILED: %s") % streamUrl() % frameIndex % getErrorString(ret)), ret);
                close();                                          //释放Context
                boost::this_fiber::sleep_for(std::chrono::milliseconds(20));
                headerHasWritten = false;                                       //处理重连
                continue;
            }
            else
            {
                boost::this_fiber::yield();
            }
        }
        close();

        printInfo(str(boost::format("Stop pushing %s") % this->address ));
    }

    /*
    * 重新计算 packet的pts/dts等信息
    */
    void StreamPusher::calcPts(AVPacket* pkt)
    {
        AVRational srcTb = timebase;
        AVRational dstTb = formatCtx->streams[0]->time_base;
        if (pkt->duration == 0)
        {
            int64_t m_duration = (int64_t)((double)AV_TIME_BASE / av_q2d(frameRate)) / 1000;
            int64_t duration = av_rescale(m_duration, timebase.den, outStream->time_base.den);
            pkt->duration = duration;
        }
        if (pkt->pts == AV_NOPTS_VALUE || pkt->pts < lastPts)
        {
            pkt->pts = lastPts + pkt->duration;
        }
        if (pkt->dts == AV_NOPTS_VALUE || pkt->dts < lastDts)
        {
            pkt->dts = lastDts + pkt->duration;
        }
        lastPts = pkt->pts;
        lastDts = pkt->dts;

        pkt->pts = av_rescale_q_rnd(pkt->pts, srcTb, dstTb, (AVRounding)(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));
        // pkt->dts = av_rescale_q_rnd(pkt->dts, srcTb, dstTb, (AVRounding)(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));
        pkt->dts = pkt->pts;
        pkt->duration = av_rescale_q(pkt->duration, srcTb, dstTb);
        pkt->pos = -1;
        pkt->stream_index = 0;
        frameIndex++;
    }


}

