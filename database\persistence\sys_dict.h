/**
 * @addtogroup odbDatabaseGroup
 * @brief 系统相关的字典类型定义
 * @{
 */
#ifndef _SYSDICT_H
#define _SYSDICT_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
/**
 * @brief  系统相关的字典类型定义: 对应数据库aimonitorV3的表sys_dict
 */
#pragma db object table("sys_dict")
class SysDict {
public:

    SysDict(const std::string& name,
        const std::string& type,
        int code,
        const std::string& cnValue,
        const std::string& enValue
    )
        : dictName(name), dictType(type), dictCode(code),
        dictCodeCnValue(cnValue), dictCodeEnValue(enValue)
    {
    }

    unsigned long getId() const {
        return id;
    }

    const std::string& getDictName() const {
        return dictName;
    }

    void getDictType(const std::string& name) {
        this->dictName = name;
    }

    const std::string& getDictType() const {
        return dictType;
    }

    void setDictType(const std::string& type) {
        this->dictType = type;
    }

    int getDictCode()const {
        return dictCode;
    }

    void setDictCode(int code) {
        this->dictCode = code;
    }

    const std::string& getDictCodeCnValue() const {
        return dictCodeCnValue;
    }

    void setDictCodeCnValue(const std::string& cnValue) {
        this->dictCodeCnValue = cnValue;
    }

    const std::string& getDictCodeEnValue() const {
        return dictCodeEnValue;
    }

    void setDictCodeEnValue(const std::string& enValue) {
        this->dictCodeEnValue = enValue;
    }

private:

    friend class odb::access;
    SysDict() {}


private:
#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("name") type("VARCHAR(255)")
    std::string dictName;              //!< 字典类型名

#pragma db column("type") type("VARCHAR(255)")
    std::string dictType;              //!< 字典类型

#pragma db column("code")
    int dictCode;                      //!< 字典类性对应的值定义

#pragma db column("cn_value") type("VARCHAR(255)")
    std::string dictCodeCnValue;       //!< 字典类性中文描述

#pragma db column("en_value") type("VARCHAR(255)")
    std::string dictCodeEnValue;       //!< 字典类性英文描述
};
}
#endif //_SYSDICT_H
/**
 * @}
 */