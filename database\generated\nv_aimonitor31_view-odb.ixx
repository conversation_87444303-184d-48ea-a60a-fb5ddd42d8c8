// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

namespace odb
{
  // MonitorDetectPointData
  //

  inline
  void access::view_traits< ::db::MonitorDetectPointData >::
  callback (database& db, view_type& x, callback_event e)
  {
    ODB_POTENTIALLY_UNUSED (db);
    ODB_POTENTIALLY_UNUSED (x);
    ODB_POTENTIALLY_UNUSED (e);
  }

  // PresetProgramData
  //

  inline
  void access::view_traits< ::db::PresetProgramData >::
  callback (database& db, view_type& x, callback_event e)
  {
    ODB_POTENTIALLY_UNUSED (db);
    ODB_POTENTIALLY_UNUSED (x);
    ODB_POTENTIALLY_UNUSED (e);
  }

  // PresetAreasData
  //

  inline
  void access::view_traits< ::db::PresetAreasData >::
  callback (database& db, view_type& x, callback_event e)
  {
    ODB_POTENTIALLY_UNUSED (db);
    ODB_POTENTIALLY_UNUSED (x);
    ODB_POTENTIALLY_UNUSED (e);
  }

  // PresetOffsetData
  //

  inline
  void access::view_traits< ::db::PresetOffsetData >::
  callback (database& db, view_type& x, callback_event e)
  {
    ODB_POTENTIALLY_UNUSED (db);
    ODB_POTENTIALLY_UNUSED (x);
    ODB_POTENTIALLY_UNUSED (e);
  }

  // PresetRoiData
  //

  inline
  void access::view_traits< ::db::PresetRoiData >::
  callback (database& db, view_type& x, callback_event e)
  {
    ODB_POTENTIALLY_UNUSED (db);
    ODB_POTENTIALLY_UNUSED (x);
    ODB_POTENTIALLY_UNUSED (e);
  }

  // ROICheckAreaData
  //

  inline
  void access::view_traits< ::db::ROICheckAreaData >::
  callback (database& db, view_type& x, callback_event e)
  {
    ODB_POTENTIALLY_UNUSED (db);
    ODB_POTENTIALLY_UNUSED (x);
    ODB_POTENTIALLY_UNUSED (e);
  }

  // ROILaneData
  //

  inline
  void access::view_traits< ::db::ROILaneData >::
  callback (database& db, view_type& x, callback_event e)
  {
    ODB_POTENTIALLY_UNUSED (db);
    ODB_POTENTIALLY_UNUSED (x);
    ODB_POTENTIALLY_UNUSED (e);
  }

  // LaneLineData
  //

  inline
  void access::view_traits< ::db::LaneLineData >::
  callback (database& db, view_type& x, callback_event e)
  {
    ODB_POTENTIALLY_UNUSED (db);
    ODB_POTENTIALLY_UNUSED (x);
    ODB_POTENTIALLY_UNUSED (e);
  }

  // AlgoParamsData
  //

  inline
  void access::view_traits< ::db::AlgoParamsData >::
  callback (database& db, view_type& x, callback_event e)
  {
    ODB_POTENTIALLY_UNUSED (db);
    ODB_POTENTIALLY_UNUSED (x);
    ODB_POTENTIALLY_UNUSED (e);
  }

  // DetectPointVideoSourceData
  //

  inline
  void access::view_traits< ::db::DetectPointVideoSourceData >::
  callback (database& db, view_type& x, callback_event e)
  {
    ODB_POTENTIALLY_UNUSED (db);
    ODB_POTENTIALLY_UNUSED (x);
    ODB_POTENTIALLY_UNUSED (e);
  }
}

namespace odb
{
  // MonitorDetectPointData
  //

  // PresetProgramData
  //

  // PresetAreasData
  //

  // PresetOffsetData
  //

  // PresetRoiData
  //

  // ROICheckAreaData
  //

  // ROILaneData
  //

  // LaneLineData
  //

  // AlgoParamsData
  //

  // DetectPointVideoSourceData
  //
}

