/**
 * @addtogroup odbDatabaseGroup
 * @brief 基于ODB的nvidia检测仪aimonitor v3.1的数据库视图结构 用于关联表的查询
 * @{
 */
#ifndef _NV_AIMONITOR_VIEW_H
#define _NV_AIMONITOR_VIEW_H

#include <iostream>
#include <string>
#include <odb/core.hxx>
#include <memory>     // std::auto_ptr
#include <vector>

#include "monitor.h"
#include "detection_point.h"
#include "program_info.h"
#include "preset.h"
#include "program.h"
#include "algorithm_param.h"
#include "alg_param_plan.h"
#include "preset_roi.h"
#include "preset_check_area.h"
#include "preset_lane.h"
#include "preset_offset.h"
#include "video_source.h"
#include "preset_lane_line.h"

namespace db {

	/**
	 * @brief      检测仪(monitor)关联的检测通道(DetectionPoint)
     */
	#pragma db view object(Monitor) object(DetectionPoint inner: DetectionPoint::monitorId == Monitor::id)
	struct MonitorDetectPointData
	{
		std::shared_ptr<Monitor> monitorPtr;
		std::shared_ptr<DetectionPoint> detectPointPtr;
	};

	/**
	 * @brief      程序轮切运行(ProgramInfo)关联的预置位(Preset)，预置位时间(Program)
	 */
	#pragma db view object(ProgramInfo)		\
	object(Program inner: Program::id == ProgramInfo::programId )	  \
	object(Preset inner: Preset::id == ProgramInfo::presetId)
	struct PresetProgramData
	{
		std::shared_ptr<ProgramInfo> programInfoPtr;
		std::shared_ptr<Program> programPtr;
	};

    /**
     * @brief      程序轮切运行(ProgramInfo)关联的预置位(Preset)，预置位时间(Program)
     */
    #pragma db view object(Preset) \
	object(PresetOffset inner: PresetOffset::presetId == Preset::id) \
	object(PresetRoi inner: PresetRoi::presetId == Preset::id)
    struct PresetAreasData
    {
        std::shared_ptr<PresetOffset> presetOffsetPtr;
        std::shared_ptr<PresetRoi> presetRoiPtr;
    };


    #pragma db view object(Preset) \
	object(PresetOffset inner: PresetOffset::presetId == Preset::id)
    struct PresetOffsetData
    {
        std::shared_ptr<PresetOffset> presetOffsetPtr;
    };

    #pragma db view object(Preset) \
	object(PresetRoi inner: PresetRoi::presetId == Preset::id)
    struct PresetRoiData
    {
        std::shared_ptr<PresetRoi> presetRoiPtr;
    };


    #pragma db view object(PresetRoi) \
    object(PresetCheckArea inner: PresetCheckArea::roiId == PresetRoi::id)
    struct ROICheckAreaData
    {
        std::shared_ptr<PresetCheckArea> presetCheckAreaPtr;
    };

    #pragma db view object(PresetRoi) \
    object(PresetLane inner: PresetLane::roiId == PresetRoi::id)
    struct ROILaneData
    {
        std::shared_ptr<PresetLane> presetLanePtr;
    };

    #pragma db view object(Preset) \
    object(PresetLaneLine inner: PresetLaneLine::presetId == Preset::id)
    struct LaneLineData
    {
        std::shared_ptr<PresetLaneLine> presetLaneLinePtr;
    };

    /**
    * @brief      灵敏度预案(AlgParamPlan)关联的灵敏度参数(AlgorithmParam)
    */
	#pragma db view object(AlgorithmParam)		\
	object(AlgParamPlan inner: AlgParamPlan::paramId == AlgorithmParam::id)
	struct AlgoParamsData
	{
        #pragma db column("plan_Id")
        unsigned long planId;               //!< 灵敏度预案ID

        #pragma db column("key")  type("VARCHAR(255)")
        std::string paramKey;               //!< 灵敏度参数Key

        #pragma db column("param_value")  type("VARCHAR(255)")
        std::string paramValue;             //!< 灵敏度参数值
	};

    /**
     * @brief      DetectionPoint关联的视频资源VideoSource
     */
    #pragma db view object(DetectionPoint) object(VideoSource inner: DetectionPoint::id == VideoSource::detectPointId)
    struct DetectPointVideoSourceData
    {
        std::shared_ptr<VideoSource> videoSourcePtr;
        std::shared_ptr<DetectionPoint> detectPointPtr;
    };
}

#endif //_NV_AIMONITOR_VIEW_H

/**
 * @}
 */