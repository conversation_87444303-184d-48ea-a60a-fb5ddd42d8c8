#ifndef DEBUG_INFO_HPP_
#define DEBUG_INFO_HPP_

#include <string>
#include <set>
#include <stdio.h>
#include <fstream>
#include "ailog.h"
#include "basic_define.hpp"


__attribute__((unused)) static void writeFile( char* chFile, const std::string& szData, bool isAdd= false, bool isBinary = false )
{
    std::string szMode = isAdd ? "a":"w";
    if ( isBinary )
        szMode += "b";
    FILE* fp = NULL;
#if defined (WIN32)
    fopen_s(&fp, chFile, szMode.c_str() );
#else
    fp = fopen( chFile, szMode.c_str() );
#endif

    if ( fp )
    {
        if ( isBinary )
            fwrite( szData.c_str(), 1, szData.length(), fp );
        else
            fputs( szData.c_str(), fp );
        fclose( fp );
    }
}
#if defined (WIN32)
#include <windows.h>
__attribute__((unused)) static int wtoe_getThreadid()
{
	return GetCurrentThreadId();
}

__attribute__((unused)) static void show_thread( char* chBuf )
{
    ai::LogInfo << "Thread: " << chBuf;
}

#else //linux

#include <sys/prctl.h>
#include <sys/syscall.h>
__attribute__((unused)) static long int  wtoe_getThreadid()
{
    //getpid 是获得进程id
   return syscall(SYS_gettid);
}

__attribute__((unused)) static void show_thread( char* chBuf )
{
    prctl(PR_SET_NAME, chBuf);
}

#endif

#endif // DEBUG_INFO_HPP_
