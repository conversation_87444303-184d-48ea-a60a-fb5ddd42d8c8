#include <ace/INET_Addr.h>
#include <boost/filesystem.hpp>
#include <boost/algorithm/string.hpp>
#include <boost/date_time/posix_time/ptime.hpp>
#include <boost/date_time/posix_time/posix_time.hpp>
#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>
#include <wtoe/TestSupport/TestSupportExp.hpp>
#include <wtoe/JavaScriptEngine/JavaScriptEngineExp.hpp>
#include "UsgSipStack/include/UsgSipStackExp.hpp"
#include "include/file_info.hpp"
#include "include/debug_info.hpp"
#include "ailog.h"

#include <algorithm>
#include <iostream>
#include <fstream>
#include <sys/prctl.h>
#include <sys/syscall.h>

#if defined( WIN32 ) && defined( _MSC_VER )
#   include <windows.h>
#elif defined( __GNUC__ )
#   include <dlfcn.h>
#endif

#ifdef linux
#include <sys/prctl.h>
#endif

#include "UsgSipStack/include/UsgSipStackExp.hpp"
#include "UsgService.hpp"
#include "UsgManager.hpp"
#include "UsgManager/include/UsgManagerPkg.hpp"
#include "UsgManager/src/QuestCatalogSet.hpp"
#include "QuestCatalogActiveSet.hpp"

namespace usg
{

    static const uint32_t KEEPALIVE_TIMEOUT = 30; // 秒.每次发送保活的时间.(保活报文发送周期不大于30s)
    static const std::string SIP_RES_FILE_NAME = "sipRes.cfg";
    static int g_catalogCount = 1;
    const int REQUESTCATALOGMAX = 100;
    static std::list< long* > m_gsPjThreadDes;

    CUsgManager::CUsgManager()
            : m_notify( 0 ), m_alarmNotify( 0 )
            ,m_protocolWtoe( 0 ),m_h( 0 )
            ,m_rPort(0), m_lPort( 0 )
            ,m_inviteSession( 0 ),m_notifySession( 0 ),m_registSession( 0 ),m_ddcpDoSession( 0 )
            /*m_isSubscribeCatalog(false),m_isSubscribeAlarm(false), */
            ,m_threadKeepalive( 0 ),m_threadForceStop( true ),m_KeepaliveOk( false )
            ,m_expries(0),m_threadByeRealall( 0 )
            ,m_packerType( EPACKERTYPE_PS )
            ,m_bOkey( false ), m_isPlaying( false ), m_bIsListening( false )
    {
        m_loadLibName = "UsgProtocolGbt";
        m_xmlType = "MANSCDP";
        m_mapAllowSip.clear();
    }

    CUsgManager::~CUsgManager()
    {

    }

    bool CUsgManager::startupImpl()
    {
        ///*
        //*	注册js
        //*/
        boost::shared_ptr<wtoe::IJavaScriptVirtualMachine> jvm;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_JavaScriptEngine_JavaScriptVirtualMachine, jvm ) )
            return false;
        if( !jvm->registerJavaScriptObject( &CUsgManager::javaScriptRegister ) )
            return false;

        m_usgService.reset( new CUsgService() );

        //云台对象
//     m_ptzController.reset( new CSipPtzController() );
//     if ( !m_ptzController )
//         return false;


/*        if ( m_loadLibName.empty() )
        {
            std::cout << " not config setLoadLibName form init.js !!!!!!!!!!" << std::endl;
            return false;
        }
*/

        //if ( !loadLib() )
        //    return false;
        m_protocolWtoe = getProtocolLibInstance();

        if ( !m_wtoeSipFunc.init( this ) )
        {
            return false;
        }

        /*
        * 初始化m_protocolWtoe
        */
        if( m_protocolWtoe == 0 )
        {
            return false;
        }

        /*
        * 得到相对路径
        */
        std::string relativePath;
        if( !getRelativePath( relativePath ) )
        {
            ai::LogError << "get relative path fail ";
            return false;
        }

        if( !m_protocolWtoe->startup( &m_wtoeSipFunc, relativePath ) )
        {
            ai::LogError << "protocolWtoe startup fail ";
            return false;
        }

        if( !m_protocolWtoe->getAccess( m_rAddr, m_rPort, m_rCode, m_lAddr, m_lPort, m_lCode ) )
            return false;

        //取消上级配置文件配置的远端code、addr和port，改为由下级注册时解析
        m_rCode = "";
        m_rAddr = "";
        m_rPort = 0;

        if ( m_xmlType.empty() )
        {
            m_xmlType = "DDCP";
        }

        m_remoteCode = "";

        try
        {
            //m_remoteSipUrl = m_rAddr + ":" +  boost::lexical_cast< std::string >( m_rPort );
// 		m_remoteSipUrl = "";

            m_keepaliveSipUrl = "sip:";
            m_keepaliveSipUrl = m_keepaliveSipUrl + m_rCode + "@";
            m_keepaliveSipUrl = m_keepaliveSipUrl + m_rAddr + ":";
            m_keepaliveSipUrl = m_keepaliveSipUrl + boost::lexical_cast< std::string >( m_rPort );
        }
        catch ( boost::bad_lexical_cast & )
        {
            ai::LogError << "boost::lexical_cast fail. m_rPort " << m_rPort;
            return false;
        }
        catch ( ... )
        {
            return false;
        }

        /*
        * 初始化 m_ctxHandler
        */
        if( !m_ctxHandler.init( m_protocolWtoe, this, &m_wtoeSipFunc ) )
            return false;

        CQuestCatalogSet::init();
        CQuestCatalogActiveSet::init();

        //   /*
        //   * 初始化 sip stack
        //   */
        USG_SIPSTACK->startupImpl();
        m_sipUdpService = USG_SIPSTACK->createUdpSipService( m_lAddr, m_lPort, m_lCode, m_xmlType );
        if( m_sipUdpService == 0 )
            return false;
        if( !m_sipUdpService->init() )
            return true;

        m_bIsListening = true;

        m_inviteSession = m_sipUdpService->inviteSession( &m_ctxHandler );
        m_notifySession = m_sipUdpService->notifySession( &m_ctxHandler );
        m_registSession = m_sipUdpService->registSession( &m_ctxHandler );
        m_ddcpDoSession = m_sipUdpService->ddcpDoSession( &m_ctxHandler );
        if( m_inviteSession == 0 || m_notifySession == 0 || m_registSession == 0 || m_ddcpDoSession == 0 )
            return false;

        return true;
    }

    bool CUsgManager::shutdownImpl()
    {
        m_wtoeSipFunc.fini();

        fini();

        if( m_sipUdpService )
            m_sipUdpService->fini();
        m_sipUdpService.reset();

        CQuestCatalogSet::fini();
        CQuestCatalogActiveSet::fini();

        m_ctxHandler.fini();

        if( m_protocolWtoe )
        {
            m_protocolWtoe->shutdown();
            m_protocolWtoe = 0;
        }

        for( std::list< long* >::iterator iter = m_gsPjThreadDes.begin(); iter != m_gsPjThreadDes.end(); ++iter )
        {
            delete[] ( *iter );
        }
        m_gsPjThreadDes.clear();
// 	m_ptzController.reset();
        m_usgService.reset();
        USG_SIPSTACK->shutdownImpl();
        return true;
    }

    std::string CUsgManager::getReceiveSeq(int size)
    {
        std::string seq;
        srand((unsigned)time(NULL));
        for (int i = 0; i < size; i++)
        {
            char c = '0' + rand()%10;
            seq += c;
        }

        return seq;
    }

    bool CUsgManager::playRealStreamAck( const std::string &sid )
    {
        //目前是SIPSTACK自动发的ack，这里直接返回成功
        return true;
    }

    bool CUsgManager::stopRealStream( const std::string &sid )
    {
        if ( !m_inviteSession )
        {
            return false;
        }
        int seq = 0;
        if ( !getCsqlValues( sid, seq ) )
        {
            return false;
        }

        seq++;
        bool bRet = m_inviteSession->bye(sid, seq);

//	if ( bRet )
//		waitResponse(sid, 2);

        // [ 无论bye消息结果如何，USG必须主动清掉自己的缓存 ]
        {
            writeLock w(m_cseqLock);
            m_streamSid2Cseq.erase(sid);
        }

        {
            writeLock w(m_formateLock);
            m_streamSid2Format.erase(sid);
        }

        {
            writeLock w(m_srcAddrLock);
            m_streamSid2SrcAddr.erase(sid);
        }

        {
            writeLock w(m_uuidLock);
            m_streamSid2uuid.erase(sid);
        }
        delEpid2Sids(-1, sid);

        return bRet;
    }

    bool CUsgManager::getMediaInfo( const boost::uuids::uuid& mediaId, SResInfo& resInfo, std::string& remoteSipUrl, std::string& sipUrl )
    {
//	readLock lock( m_mutexAllowSip );
        std::map< std::string, CResManager*>::iterator it = m_mapAllowSip.begin();
        for (; it != m_mapAllowSip.end(); it++ )
        {
            CResManager* pMgr = it->second;
            if ( !pMgr || !pMgr->isRegist() )
                continue;
            if ( pMgr->getResInfoByUuid( mediaId, resInfo ))
            {
                remoteSipUrl = pMgr->getSipId();
                sipUrl = pMgr->getSipUrl();
                return true;
            }
        }
        return false;
    }

    bool CUsgManager::getSidInfo( const std::string& sid, SResInfo& resInfo, std::string& remoteSipUrl, std::string& sipUrl )
    {
        boost::uuids::uuid mediaId;
        {
            readLock r( m_uuidLock );
            std::map< std::string, boost::uuids::uuid >::iterator it = m_streamSid2uuid.find( sid );
            if ( it == m_streamSid2uuid.end() )
                return false;
            mediaId = it->second;
        }
        return getMediaInfo( mediaId, resInfo, remoteSipUrl, sipUrl );
    }

    bool CUsgManager::getMediaInfoByMedia( const boost::uuids::uuid &mediaId, const TimePeriodUnit &timePeriod, std::vector< std::pair< std::string, TimePeriodUnit > > &info )
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getMediaInfo( mediaId, resInfo, remoteSipUrl, sipUrl ) )
            return false;
        if ( !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        std::string sipXml = resInfo.sipResCode;
        std::string snstr;
        if ( !m_protocolWtoe->getHistoryListString( timePeriod.first, timePeriod.second, sipXml, snstr ) )
        {
            return false;
        }

        boost::shared_ptr<CReciveSipData> spSipRec( new( CReciveSipData ) );
        if ( !spSipRec )
        {
            return false;
        }

        std::string sid;
        if ( !m_ddcpDoSession->ddcpDo( sid, sipUrl, sipXml ) )
        {
            return false;
        }

        onNotifyCallBack_t cb = boost::bind( &CReciveSipData::onReceiveHistoryListResponse, spSipRec.get(), _1, _2 );
        m_wtoeSipFunc.regOnNotifyCallBack( snstr, cb );
        m_wtoeSipFunc.regOnNotifyCallBack( sid, cb );

        if ( !spSipRec->waitForRecive() )
        {
            m_wtoeSipFunc.unRegOnNotifyCallBack( snstr );
            m_wtoeSipFunc.unRegOnNotifyCallBack( sid );
            ai::LogError << "getMediaInfoByMedia timeOut !!!!!!!!" ;
            return false;
        }

        if ( !spSipRec->getHistoryListResponse( info ) )
        {
            m_wtoeSipFunc.unRegOnNotifyCallBack( snstr );
            m_wtoeSipFunc.unRegOnNotifyCallBack( sid );
            return false;
        }

        m_wtoeSipFunc.unRegOnNotifyCallBack( snstr );
        m_wtoeSipFunc.unRegOnNotifyCallBack( sid );
        spSipRec.reset();
        return true;
    }

    bool CUsgManager::playHistoryPrepare( const boost::uuids::uuid &mediaId,const std::string& fileName,const TimePeriodUnit &timePeriod, const ACE_INET_Addr &rtpAddr, std::string &sid )
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getMediaInfo( mediaId, resInfo, remoteSipUrl, sipUrl ) )
            return false;
        if ( !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        std::string subject = "";
        subject += resInfo.sipResCode;//m_rCode;
        subject += ":1";
        subject += getReceiveSeq(19);
        subject += ",";
        subject += m_lCode;
        subject += ":";
        subject += getReceiveSeq(20);

        std::string sipXml = resInfo.sipResCode;
        if ( !m_protocolWtoe->getHistoryMediaString( fileName, timePeriod.first, timePeriod.second, rtpAddr,sipXml ) )
        {
            return false;
        }

        boost::shared_ptr<CReciveSipData> spSipRec( new( CReciveSipData ) );
        if ( !spSipRec )
        {
            return false;
        }

        if ( !m_inviteSession->invite( sid, sipUrl, subject, sipXml ) )
        {
            return false;
        }

        if( !waitResponse( sid, 3 ) ) return false;

        return true;
    }

    bool CUsgManager::downloadHistoryPrepare( const boost::uuids::uuid &mediaId, const std::string& fileName, const TimePeriodUnit &timePeriod, const ACE_INET_Addr &rtpAddr,std::string &sid )
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getMediaInfo( mediaId, resInfo, remoteSipUrl, sipUrl ) )
            return false;
        if ( !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        std::string subject = "";
        subject += resInfo.sipResCode;//m_rCode;
        subject += ":1";
        subject += getReceiveSeq(19);
        subject += ",";
        subject += m_lCode;
        subject += ":";
        subject += getReceiveSeq(20);

        std::string sipXml = resInfo.sipResCode;
        if ( !m_protocolWtoe->getDownloadMediaString( fileName, timePeriod.first, timePeriod.second, rtpAddr,sipXml ) )
        {
            return false;
        }

        boost::shared_ptr<CReciveSipData> spSipRec( new( CReciveSipData ) );
        if ( !spSipRec )
        {
            return false;
        }

        if ( !m_inviteSession->invite( sid, sipUrl,subject, sipXml ) )
        {
            return false;
        }

        if( !waitResponse( sid, 3 ) ) return false;

        return true;
    }

///< 历史文件的回放控制命令：开始播放（play）
    bool CUsgManager::playHistoryStreamAck( const std::string &sid, uint8_t n, uint8_t d, bool modifyTime, uint32_t beginTimeOff, uint32_t endTimeOff )
    {
        if ( !m_protocolWtoe || !m_ddcpDoSession )
            return false;
        int cseq = 0;
        if ( !getCsqlValues(sid,cseq) )
            return false;

        cseq += 1;
        std::string sipXml = "";
        std::string strCseq = "";
        strCseq = boost::lexical_cast< std::string >( cseq );

        float scale = 1.0f;
        if ( n == 1 )
        {
            if ( d > 0 && d <= 20 )
                scale = 1 / float(d);
        }
        else if ( d == 1 )
        {
            if ( n > 0 && n <= 20 )
                scale = float(n);
        }
        else
        {
            return false;
        }

        char strScale[10] = {0};
        sprintf(strScale,"%0.01f",scale);

        sipXml  = "PLAY RTSP/1.0\r\nCSeq: ";
        sipXml += strCseq;
        sipXml += "\r\nScale: ";
        sipXml += strScale;
        if( modifyTime )
        {
            sipXml += "\r\nRange: npt=";
            sipXml += boost::lexical_cast< std::string >( beginTimeOff ) + "-\r\n";
        }
        else
        {
            sipXml += "\r\nRange: npt=now-\r\n";
        }

        pjsip_uri      *uri  = 0;
        pjsip_from_hdr *from = 0;
        pjsip_to_hdr   *to   = 0;
        pjsip_cid_hdr  *cid  = 0;
        pjsip_contact_hdr *contact = 0;

        if ( !m_inviteSession->getReadSipInfo( sid, uri,from,to,cid, contact ) )
        {
            return false;
        }

        std::string xmlType = "MANSRTSP";
        if ( !m_ddcpDoSession->ddcpDoByCallId( uri,from,to,cid,contact,cseq,xmlType,sipXml ) )
        {
            return false;
        }

        writeLock w( m_cseqLock );
        m_streamSid2Cseq[sid] = cseq;
        return true;
    }

    bool CUsgManager::pause( const std::string &sid)
    {
        if ( !m_protocolWtoe || !m_ddcpDoSession )
        {
            return false;
        }
        int cseq = 0;
        if ( !getCsqlValues(sid,cseq) )
            return false;

        cseq += 1;
        std::string sipXml = "";
        if ( !m_protocolWtoe->getPauseCommandString(cseq,sipXml ) )
        {
            return false;
        }

        pjsip_uri      *uri  = 0;
        pjsip_from_hdr *from = 0;
        pjsip_to_hdr   *to   = 0;
        pjsip_cid_hdr  *cid  = 0;
        pjsip_contact_hdr *contact = 0;

        if ( !m_inviteSession->getReadSipInfo( sid, uri,from,to,cid, contact ) )
        {
            return false;
        }

        std::string xmlType = "MANSRTSP";
        if ( !m_ddcpDoSession->ddcpDoByCallId( uri,from,to,cid,contact,cseq,xmlType,sipXml ) )
        {
            return false;
        }

        writeLock w( m_cseqLock );
        m_streamSid2Cseq[sid] = cseq;
        return true;
    }

    bool CUsgManager::stopHistoryStream( const std::string &sid)
    {
        return stopRealStream( sid );
    }

    bool CUsgManager::getAllResInfo( std::map<boost::uuids::uuid, SResInfo >& mapRes, uint32_t& timeStamp )
    {
// 	readLock lock( m_mutexAllowSip );
        if( m_mapAllowSip.empty())
        {
            return false;
        }
        mapRes.clear();
        std::map< std::string, CResManager*>::iterator it = m_mapAllowSip.begin();
        for (; it != m_mapAllowSip.end(); it++ )
        {
            it->second->getAllResInfo( mapRes,timeStamp );
        }
        return true;
    }

    CSpIPtzController CUsgManager::getPtzControllerMgr()
    {
        return m_ptzController;
    }

    void CUsgManager::setLoacalAddr( const std::string &addr )
    {
    }

    void CUsgManager::setDBParam(const std::string& ip, const std::string& dbName)
    {
    }

    bool CUsgManager::startAllThread()
    {
        //已初始化，不再初始化
        if ( m_threadByeRealall )
            return true;
        m_threadForceStop = false;
        m_threadByeRealall = new_o( boost::thread, boost::bind( &CUsgManager::byeAllRealThread, this ) );
        return true;
    }

    void CUsgManager::stopAllThread()
    {
        if ( !m_threadForceStop )
        {
            m_threadForceStop = true;
            m_realSidsCond.notify_one();
        }
        if ( m_threadByeRealall )
        {
            m_threadByeRealall->join();
            delete_o( m_threadByeRealall );
            m_threadByeRealall = 0;
        }
        clearEpid2Sids();
    }

    bool CUsgManager::getRelativePath( std::string &rp )
    {
        rp = "../config/";
        std::string init;
        if ( wtoe::getMainApp()->getCmndLineParser().getCmndLineOption( "init", init ) )
        {
            size_t pos = init.find("init");
            if ( pos != std::string::npos )
                rp = init.substr( 0, pos );
        }
        return true;
    }

    bool CUsgManager::onReceiveKeepalive( const std::string &rCode )
    {
        CResManager* pMgr = getResManager( rCode );
        if ( !pMgr || !pMgr->isRegist() )
            return false;
        pMgr->keepAlive();
        return true;
    }

    bool CUsgManager::onReceivePresetListResponse( const std::string& sid,SPresetListResponse& out )
    {
        return true;
    }


    bool CUsgManager::doPtzControl(const std::string& szSipId, const std::string& szResCode, EPtzCommand ptzCmd, int iParam1, int iParam2)
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if (!getResId(szSipId, szResCode, resInfo, remoteSipUrl, sipUrl))
            return false;
        return onReceivePtzCommand(ptzCmd, resInfo.resId, iParam1 );
    }

    bool CUsgManager::onReceivePtzCommand( const EPtzCommand ptzCommand,const boost::uuids::uuid &resId, const uint8_t arg )
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getMediaInfo( resId, resInfo, remoteSipUrl, sipUrl ) )
            return false;
        if (  !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        SPtzCommand command;
        command.command = ptzCommand;
        switch( ptzCommand )
        {
            case EPTZCOMMAND_UP:
            case EPTZCOMMAND_DOWN:
            case EPTZCOMMAND_ZOOMIN:
            case EPTZCOMMAND_ZOOMOUT:
            case EPTZCOMMAND_FOCUSNEAR:
            case EPTZCOMMAND_FOCUSFAR:
            {
                command.commandParam1 = arg;
                command.commandParam2 = 0;
            }
                break;
            case EPTZCOMMAND_FOCUSSTOP:
            {
                command.commandParam1 = 0;
                command.commandParam2 = 0;
            }
                break;
            case EPTZCOMMAND_LEFT:
            case EPTZCOMMAND_RIGHT:
            case EPTZCOMMAND_APERTUREWIDE:
            case EPTZCOMMAND_APERTURETELE:
            case EPTZCOMMAND_SWITCH:
            {
                command.commandParam1 = 0;
                command.commandParam2 = arg;
            }
                break;
            case EPTZCOMMAND_WIPERON:
            case EPTZCOMMAND_WIPEROFF:
            case EPTZCOMMAND_LEDON:
            case EPTZCOMMAND_LEDOFF:
            {
                command.commandParam1 = 0;
                command.commandParam2 = 0;
            }
                break;
            case EPTZCOMMAND_STOP: //多了个镜头的停止，我们平台没有的//todo
            {
                command.commandParam1 = 0;
                command.commandParam2 = 0;
            }
                break;
            default:
                return false;
        }

        //todo
        std::string sipXml = resInfo.sipResCode;
        if ( !m_protocolWtoe->getPtzCommandString( command,sipXml ) )
        {
            return false;
        }

        std::string sid;
        if ( !m_ddcpDoSession->ddcpDo( sid, sipUrl, sipXml ) )
        {
            return false;
        }

        return true;
    }

    bool CUsgManager::onReceivePtzCommand( const EPtzCommand ptzCommand, const boost::uuids::uuid &resId, const uint16_t arg1, const uint16_t arg2 )
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getMediaInfo( resId, resInfo, remoteSipUrl, sipUrl ) )
            return false;
        if (  !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        SPtzCommand command;
        command.command = ptzCommand;
        command.commandParam1 = arg1;
        command.commandParam2 = arg2;

        std::string sipXml = resInfo.sipResCode;
        if ( !m_protocolWtoe->getPtzCommandString( command,sipXml ) )
        {
            return false;
        }

        std::string sid;
        if ( !m_ddcpDoSession->ddcpDo( sid, sipUrl, sipXml ) )
        {
            return false;
        }

        return true;

    }

    bool CUsgManager::onReceiveDeviceReboot(const boost::uuids::uuid &resId)
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getMediaInfo( resId, resInfo, remoteSipUrl, sipUrl ) )
            return false;
        if (  !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        std::string sipXml = resInfo.sipResCode;
        if ( !m_protocolWtoe->getDeviceRebootString( sipXml ) )
        {
            return false;
        }

        std::string sid;
        if ( !m_ddcpDoSession->ddcpDo( sid, sipUrl, sipXml ) )
        {
            return false;
        }

        return true;
    }

    bool CUsgManager::onReceiveRecordContronl(const boost::uuids::uuid &resId, const bool flag)
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getMediaInfo( resId, resInfo, remoteSipUrl, sipUrl ) )
            return false;
        if (  !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        std::string sipXml = resInfo.sipResCode;
        if ( !m_protocolWtoe->getRecordContronlString( sipXml, flag ) )
        {
            return false;
        }

        std::string sid;
        if ( !m_ddcpDoSession->ddcpDo( sid, sipUrl, sipXml ) )
        {
            return false;
        }

        return true;
    }

    bool CUsgManager::onReceiveGuardContronl(const boost::uuids::uuid &resId, const bool flag)
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getMediaInfo( resId, resInfo, remoteSipUrl, sipUrl ) )
            return false;
        if (  !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        std::string sipXml = resInfo.sipResCode;
        if ( !m_protocolWtoe->getGuardContronlString( sipXml, flag ) )
        {
            return false;
        }

        std::string sid;
        if ( !m_ddcpDoSession->ddcpDo( sid, sipUrl, sipXml ) )
        {
            return false;
        }

        return true;
    }

    bool CUsgManager::onReceiveAlarmReset( const boost::uuids::uuid &resId )
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getMediaInfo( resId, resInfo, remoteSipUrl, sipUrl ) )
            return false;
        if (  !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        std::string sipXml = resInfo.sipResCode;
        if ( !m_protocolWtoe->getAlarmResetString( sipXml ) )
        {
            return false;
        }

        std::string sid;
        if ( !m_ddcpDoSession->ddcpDo( sid, sipUrl, sipXml ) )
        {
            return false;
        }

        return true;
    }

    bool CUsgManager::onReceiveQueryDeviceCatalog( CResManager* pMgr )
    {
        if ( !pMgr || !m_protocolWtoe || !m_ddcpDoSession )
            return false;
        if ( !pMgr->isRegist() )
            return false;
        if( g_catalogCount > REQUESTCATALOGMAX )
        {
            std::cerr << "g_catalogCount > REQUESTCATALOGMAX" << std::endl;
            return false;
        }

        std::string sipXml = pMgr->getSipId();
        if ( !m_protocolWtoe->getQueryDeviceCatalogString(sipXml))
        {
            return false;
        }

        boost::shared_ptr<CReciveSipData> spSipRec( new( CReciveSipData ) );
        if ( !spSipRec )
        {
            return false;
        }

        std::string sipUrl = pMgr->getSipUrl();
        std::string rCode = pMgr->getSipId();
        CQuestCatalogSet::get()->createSet( rCode );
        std::string sid;
        if ( !m_ddcpDoSession->ddcpDo( sid, sipUrl, sipXml ) )
        {
            return false;
        }

        m_questCatalogCmds[sid] = rCode;
        onNotifyCallBack_t cb = boost::bind( &CUsgManager::questCatalogError, this, _1, _2 );
        m_wtoeSipFunc.regOnNotifyCallBack( sid, cb );
        CQuestCatalogActiveSet* pSet = CQuestCatalogActiveSet::get();
        if ( pSet )
            pSet->creatActive( sid, rCode, 30 * g_catalogCount++ );
        return true;
    }

    CResManager* CUsgManager::getResManager( const std::string& rCode )
    {
        //readLock lock( m_mutexAllowSip );
        std::map< std::string, CResManager*>::iterator it = m_mapAllowSip.find( rCode );
        if ( it != m_mapAllowSip.end() )
            return it->second;
        return NULL;
    }

///< 向下级发送目录查询
    bool CUsgManager::onReceiveQueryDeviceCatalog( const std::string &rCode )
    {
        CResManager* pMgr = getResManager( rCode );
        if ( pMgr )
            return onReceiveQueryDeviceCatalog( pMgr );
        return false;
    }

    bool CUsgManager::getResInfo( const boost::uuids::uuid &resId, SResInfo& resInfo, std::string& sipUrl )
    {
        readLock lock( m_mutexAllowSip );
        std::map< std::string, CResManager*>::iterator it;
        for ( it = m_mapAllowSip.begin(); it!=m_mapAllowSip.end(); it++ )
        {
            CResManager* pMgr = it->second;
            if ( !pMgr || !pMgr->isRegist() )
                continue;
            if ( pMgr->getResInfoByUuid( resId, resInfo ) )
            {
                sipUrl = pMgr->getSipUrl();
                return true;
            }
        }
        return false;
    }

    bool CUsgManager::onReceiveQueryDeviceInfo(const boost::uuids::uuid &resId, SDeviceInfoResponse &info)
    {
        SResInfo resInfo;
        std::string sipUrl;
        if ( !getResInfo( resId, resInfo, sipUrl ) )
            return false;

        std::string sipXml = resInfo.sipResCode;
        std::string snstr;
        if ( !m_protocolWtoe->getQueryDeviceInfoString(sipXml, snstr ) )
        {
            return false;
        }

        boost::shared_ptr<CReciveSipData> spSipRec( new( CReciveSipData ) );
        if ( !spSipRec )
        {
            return false;
        }

        std::string sid;
        if ( !m_ddcpDoSession->ddcpDo( sid, sipUrl, sipXml ) )
        {
            return false;
        }

        onNotifyCallBack_t cb = boost::bind( &CReciveSipData::onReceiveDeviceInfoResponse, spSipRec.get(), _1, _2 );
        m_wtoeSipFunc.regOnNotifyCallBack( snstr, cb );

        if ( !spSipRec->waitForRecive() )
        {
            m_wtoeSipFunc.unRegOnNotifyCallBack( snstr );
            std::cout << " onReceiveQueryDeviceInfo timeOut !!!!!!!!" << std::endl;
            return false;
        }

        if ( !spSipRec->getDeviceInfoResponse( info ) )
        {
            m_wtoeSipFunc.unRegOnNotifyCallBack( snstr );
            return false;
        }

        m_wtoeSipFunc.unRegOnNotifyCallBack( snstr );
        return true;
    }

    bool CUsgManager::onReceiveQueryDeviceStatus(const boost::uuids::uuid &resId, SDeviceStatusResponse &info)
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getMediaInfo( resId, resInfo, remoteSipUrl, sipUrl ) )
            return false;
        if (  !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        std::string sipXml = resInfo.sipResCode;
        std::string snstr;
        if ( !m_protocolWtoe->getQueryDeviceStatusString(sipXml, snstr ) )
        {
            return false;
        }

        boost::shared_ptr<CReciveSipData> spSipRec( new( CReciveSipData ) );
        if ( !spSipRec )
        {
            return false;
        }

        std::string sid;
        if ( !m_ddcpDoSession->ddcpDo( sid, sipUrl, sipXml ) )
        {
            return false;
        }

        onNotifyCallBack_t cb = boost::bind( &CReciveSipData::onReceiveDeviceStatusResponse, spSipRec.get(), _1, _2 );
        m_wtoeSipFunc.regOnNotifyCallBack( snstr, cb );

        if ( !spSipRec->waitForRecive() )
        {
            m_wtoeSipFunc.unRegOnNotifyCallBack( snstr );
            ai::LogError << "onReceiveQueryDeviceStatus timeOut !!!!!!!!";
            return false;
        }

        if ( !spSipRec->getDeviceStatusResponse( info ) )
        {
            m_wtoeSipFunc.unRegOnNotifyCallBack( snstr );
            return false;
        }

        m_wtoeSipFunc.unRegOnNotifyCallBack( snstr );
        return true;
    }

    bool CUsgManager::onqueryPreposition( const boost::uuids::uuid &resId, std::map< uint8_t, std::string > &infos )
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getMediaInfo( resId, resInfo, remoteSipUrl, sipUrl ) )
            return false;
        if (  !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        std::string sipXml = resInfo.sipResCode;
        if ( !m_protocolWtoe->getPresetListString( 1,32,sipXml ) )
        {
            return false;
        }

        boost::shared_ptr<CReciveSipData> spSipRec( new( CReciveSipData ) );
        if ( !spSipRec )
        {
            return false;
        }

        std::string sid;
        if ( !m_ddcpDoSession->ddcpDo( sid, sipUrl, sipXml ) )
        {
            return false;
        }

        onNotifyCallBack_t cb = boost::bind( &CReciveSipData::onReceivePresetListResponse, spSipRec.get(), _1, _2 );

        m_wtoeSipFunc.regOnNotifyCallBack( sid, cb );

        if ( !spSipRec->waitForRecive() )
        {
            m_wtoeSipFunc.unRegOnNotifyCallBack( sid );
            ai::LogError << "onqueryPreposition timeOut !!!!!!!!" ;
            return false;
        }

        if ( !spSipRec->getPresetListResponse( infos ) )
        {
            m_wtoeSipFunc.unRegOnNotifyCallBack( sid );
            return false;
        }

        m_wtoeSipFunc.unRegOnNotifyCallBack( sid );
        return true;
    }

///< 处理下级设备注册
///< sid : 当下命令的id（28181），oid ： 为设备域地址，expries ： 保活的单位时间（60秒）
    bool CUsgManager::onReceiveRegist( const std::string &sid,const std::string &oid,int expires )
    {
        std::stringstream ss;
        ss << "------onReceiveRegist " << oid.c_str() << " expires: " << expires ;
        if ( m_mapAllowSip.empty() )
        {
            ai::LogInfo << ss.str() << " allow sip is empty";
            return false;
        }
        ///< oid的结构：设备编码+ip地址+端口号；设备的20为编码@设备ip:port
        int pos_ip = oid.find('@');
        if ( pos_ip < 0 )
            return false;
        std::string remoteAddr = oid.substr( pos_ip+1, oid.length() );
        std::string szSipId = oid.substr( 0, pos_ip );
        CResManager* pMgr = getResManager( szSipId );
        if ( !pMgr )
        {
            if (expires > 0)
                ai::LogInfo << ss.str() << " not allowed lower \n";
            return false;
        }
        bool bOldRegist = pMgr->isRegist();
        if ( pMgr->getRemoteAddr() != remoteAddr && bOldRegist )
        {
            if (expires > 0)
                ai::LogInfo << ss.str() << " not allowed lower " << pMgr->getRemoteAddr().c_str();
            return false;
        }
        bool bNewRegist = (expires > 0);
    	if (bOldRegist != bNewRegist )
        {
	    	ai::LogInfo << ss.str();
        }
        if ( expires > 0 )
        {
            m_ddcpDoSession->setSipUrl( "sip:" + oid );
            m_notifySession->setExpries( m_registSession->getExpries());
        }
        pMgr->onRegist( expires );
        if (pMgr->isRegist())
        {
            boost::thread thd(boost::bind(&CUsgManager::getCatalog, this, pMgr));
            thd.detach();
        }
            /*if ( !pMgr->isSubscribeCatalog() )
            {
                g_catalogCount = 1;
                onReceiveQueryDeviceCatalog( pMgr );
                if ( subscribeCatalog( pMgr )  )
                {
                    subscribeAlarm( pMgr );
                    pMgr->setSubscribeCatalog( true );
                }
                else
                {
                    ai::LogError << "subscribeCatalog or subscribeAlarm false";
                }
            }
        }*/
        return true;
    }

    void CUsgManager::getCatalog(CResManager* pMgr)
    {
        char chBuf[255];
        sprintf(chBuf, "%ld:28P:Cata", wtoe_getThreadid());
        show_thread(chBuf);
        if (!pMgr->isSubscribeCatalog())
        {
            g_catalogCount = 1;
            onReceiveQueryDeviceCatalog(pMgr);
            if (subscribeCatalog(pMgr))
            {
                subscribeAlarm(pMgr);
                pMgr->setSubscribeCatalog(true);
            }
            else
            {
                ai::LogError << "subscribeCatalog or subscribeAlarm false";
            }
        }
    }
    bool CUsgManager::isRegist( const std::string& oid )
    {
        if ( m_mapAllowSip.empty() )
            return false;
// 	readLock lock( m_mutexAllowSip );
        std::map< std::string, CResManager*>::iterator it = m_mapAllowSip.begin();
        for ( ; it != m_mapAllowSip.end(); it++)
        {
            CResManager* pMgr = it->second;
            if ( !pMgr )
                continue;
            if ( pMgr->getSipId() == oid )
                return pMgr->isRegist();
        }
        return false;
    }

///< 订阅设备目录
    bool CUsgManager::subscribeCatalog( CResManager* pMgr )
    {
        if ( !pMgr || !m_protocolWtoe || !m_ddcpDoSession )
        {
            return false;
        }
        if ( !pMgr->isRegist() )
            return false;

        std::string sipUrl = pMgr->getSipUrl();

        boost::posix_time::ptime nowTime =  boost::posix_time::microsec_clock::local_time();
        tm tm_localTime = boost::posix_time::to_tm( nowTime ); // 本地时间
        uint32_t t = mktime( &tm_localTime );
        uint32_t startTime = t/* - 24 * 3600 * 30*/;
        uint32_t endTime = t + 24 * 3600 * 30;

        std::string sipXml = pMgr->getSipId();
        if ( !m_protocolWtoe->getSubscribeCatalogString( startTime, endTime, sipXml ) )
        {
            return false;
        }

        boost::shared_ptr<CReciveSipData> spSipRec( new( CReciveSipData ) );
        if ( !spSipRec )
        {
            return false;
        }

        std::string sid;
        if ( !m_notifySession->subscribe( sid, sipUrl, sipXml ) )
        {
            return false;
        }
        return true;
    }

    bool CUsgManager::subscribeAlarm( CResManager* pMgr )
    {
        if ( !m_protocolWtoe || !m_ddcpDoSession || !pMgr )
            return false;

        if ( !pMgr->isRegist() )
            return false;

        std::string sipUrl = pMgr->getSipUrl();
        gb28181::SAlarmSubscribeParam param;
        param.resId = pMgr->getSipId();
        param.startAlarmPriority = 1;
        param.endAlarmPriority = 4;
        param.alarmMethod = "0";

        boost::posix_time::ptime nowTime =  boost::posix_time::microsec_clock::local_time();
        tm tm_localTime = boost::posix_time::to_tm( nowTime ); // 本地时间
        uint32_t t = mktime( &tm_localTime );

        param.startTime = t;
        param.endTime = t + 24 * 3600 * 30;

        std::string sipXml = pMgr->getSipId();
        if ( !m_protocolWtoe->getSubscribeAlarmString(param, sipXml ) )
        {
            return false;
        }

        boost::shared_ptr<CReciveSipData> spSipRec( new( CReciveSipData ) );
        if ( !spSipRec )
        {
            return false;
        }

        std::string sid;
        if ( !m_notifySession->subscribe( sid, sipUrl, sipXml ) )
        {
            return false;
        }
        return true;
    }

    bool CUsgManager::audioBroadcast( const std::string &sourceId,  const std::string &targetId)
    {
#if 0
        std::string sipXml = "";
    if ( !m_protocolWtoe->getAudioBroadcastString( sourceId, targetId,sipXml ) )
    {
        return false;
    }

    std::string sipUrl = "sip:";
    sipUrl += m_rCode;
    sipUrl += "@";
    sipUrl += remoteSipUrl;

    std::string sid = "";
    if ( !m_ddcpDoSession->ddcpDo( sid, sipUrl,sipXml ) )
    {
        return false;
    }

    boost::shared_ptr<CReciveSipData> spSipRec( new( CReciveSipData ) );
    if ( !spSipRec )
    {
        return false;
    }

    bool ret = false;
    onNotifyCallBack_t cb = boost::bind( &CReciveSipData::onReceiveBroadcastResponse, spSipRec.get() );
    m_wtoeSipFunc.regOnNotifyCallBack( sid, cb );

    if ( !spSipRec->waitForRecive() )
    {
        m_wtoeSipFunc.unRegOnNotifyCallBack( sid );
        std::cout << " audioBroadcast timeOut !!!!!!!!" << std::endl;
        return false;
    }

    if ( !spSipRec->getBroadcastResponse( ret ) )
    {
        m_wtoeSipFunc.unRegOnNotifyCallBack( sid );
        return false;
    }

    m_wtoeSipFunc.unRegOnNotifyCallBack( sid );
#endif
        return true;
    }

    bool CUsgManager::onReceiveFiletoEnd( const std::string& sid )
    {
        boost::thread endthread( boost::bind( &CUsgManager::onReceiveFiletoEndImp, this, sid ) );
        return true;
    }

    void CUsgManager::onReceiveFiletoEndImp(const std::string& sid)
    {
#ifdef linux
        char chBuf[255];
	sprintf(chBuf,"%s:%s", __FUNCTION__, __FILE__);
	prctl(PR_SET_NAME, chBuf );
#endif
        wtoe::CSharedTimer::sleep( 200 );

        stopRealStream(sid);

    }

///< 目录订阅的响应
    bool CUsgManager::onReceiveCatalog( const std::string &sid, const SCatalog& info, SCatalogResponse& out )
    {
// 	std::stringstream ss;
// 	ss << "------onReceiveCatalog subscribe " << info.deviceId.c_str() << " at: " << getCurTime() << std::endl;
// 	std::cout << ss.str();
//
        out.isOk = false;
        CResManager* pMgr = getResManager( info.deviceId );
        if ( !pMgr || !pMgr->isRegist() )
            return false;
        out.isOk = true;
        return pMgr->onReceiveCatalog( info );
    }

///< 用于目录查询的响应
    bool CUsgManager::onReceiveCatalog( const std::string &sid, const SCatalog& info )
    {
// 	std::stringstream ss;
// 	ss << "------onReceiveCatalog query " << info.deviceId.c_str() << " at: " << getCurTime() << std::endl;
// 	std::cout << ss.str();
        CQuestCatalogSet* qcs = CQuestCatalogSet::get();
        if ( !qcs->add( sid, info.deviceId, info.num, info ) )
            return false;
        CResManager* pMgr = getResManager( info.deviceId );
        if ( !pMgr || !pMgr->isRegist() )
            return false;
        return pMgr->onReceiveCatalog( info );
    }

///< 推送目录查询的响应数据，由CQuestCatalogActive对象调用
    bool CUsgManager::pushReceiveCatalog( const SCatalog &info )
    {
        CResManager* pMgr = getResManager( info.deviceId );
        if ( pMgr )
            return pMgr->onReceiveCatalog( info );
        return false;
    }

    bool CUsgManager::onReceiveAlarmNotify(const SAlarmParam &alarm)
    {
        SResInfo info;

        bool bFind = false;
        std::map< std::string, CResManager*>::iterator it = m_mapAllowSip.begin();
        for (; it != m_mapAllowSip.end(); it++ )
        {
            if ( it->second->getResInfoBySipCode( alarm.resId, info ) )
            {
                bFind = true;
                break;
            }
        }
        if ( !bFind )
            return false;

        ISgAlarmNotify *pSgAlarmNotify = getAlarmNotify();

        if (pSgAlarmNotify == 0 )
            return false;

        usg::SAlarmInfo alarmInfo;
        alarmInfo.sipResCode = alarm.resId;
        alarmInfo.resId = info.resId;
        alarmInfo.type = alarm.method;
        alarmInfo.time = alarm.time;
        alarmInfo.description = info.resName;
        alarmInfo.longitude = alarm.longitude;
        alarmInfo.latitude = alarm.latitude;

        pSgAlarmNotify->sgAlarmNotify(alarmInfo);

        return true;
    }

    bool CUsgManager::canControlPtz( const boost::uuids::uuid &resId, bool &retbool )
    {
        SResInfo info;
        std::string remoteSipUrl, sipUrl;
        if ( !getMediaInfo( resId, info, remoteSipUrl, sipUrl ) )
            return false;
        retbool = info.hasPtz;
        return true;
    }

    bool CUsgManager::realKeepalive( const std::string &sid )
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getSidInfo( sid, resInfo, remoteSipUrl, sipUrl ) )
            return false;
        if ( !m_ddcpDoSession || !m_inviteSession )
            return false;

        std::string sipXml = "<?xml version="
                             "1.0"
                             "?>\n"
                             "<Action>\n"
                             "<Notify>\n"
                             "<Variable>RealMediaKeepAlive</Variable>\n"
                             "</Notify>\n"
                             "</Action>\n";

        int cseq = 2;

        {
            readLock r( m_cseqLock );
            std::map< std::string, int >::iterator it = m_streamSid2Cseq.find( sid );
            if ( it != m_streamSid2Cseq.end() )
            {
                cseq = it->second;
                cseq++;
                it->second = cseq;
            }
            else
                return false;
        }

        pjsip_uri      *uri  = 0;
        pjsip_from_hdr *from = 0;
        pjsip_to_hdr   *to   = 0;
        pjsip_cid_hdr  *cid  = 0;
        //pjsip_contact_hdr *contact = 0;

        if ( !m_inviteSession->getReadSipInfo( sid, uri,from,to,cid ) )
        {
            return false;
        }

        if ( !m_ddcpDoSession->ddcpDoByCallId( uri,from,to,cid,cseq,sipXml ) )
        {
            return false;
        }
        return true;
    }

    bool CUsgManager::deviceReboot( const boost::uuids::uuid &resId )
    {
        return onReceiveDeviceReboot(resId);
    }

    bool CUsgManager::recordContronl( const boost::uuids::uuid &resId, const bool flag )
    {
        return onReceiveRecordContronl(resId, flag);
    }

    bool CUsgManager::guardContronl( const boost::uuids::uuid &resId, const bool flag )
    {
        return onReceiveGuardContronl(resId, flag);
    }

    bool CUsgManager::alarmReset( const boost::uuids::uuid &resId )
    {
        return onReceiveAlarmReset(resId);
    }

    bool CUsgManager::ptzCtrl( const uint8_t ptzCommand, const boost::uuids::uuid &resId, const uint16_t arg1, const uint16_t arg2 )
    {
        return onReceivePtzCommand(EPtzCommand(ptzCommand), resId, arg1, arg2);
    }

    bool CUsgManager::deviceInfo( const boost::uuids::uuid &resId, SQueryInfo &info )
    {
        SDeviceInfoResponse response;
        if (!onReceiveQueryDeviceInfo(resId, response))
            return false;

        info.online 	= response.online;
        info.status 	= response.status;
        info.resAddr 	= response.resAddr;
        info.deviceType	= response.deviceType;
        info.model		= response.model;
        info.firmware	= response.firmware;
        info.maxCamera 	= response.maxCamera;
        info.maxAlarm	= response.maxAlarm;
        info.manufacturer = response.manufacturer;
        return true;
    }

    bool CUsgManager::deviceStatus( const boost::uuids::uuid &resId, SQueryStatus &info )
    {
        SDeviceStatusResponse response;
        if (!onReceiveQueryDeviceStatus(resId, response))
            return false;

        info.resAddr 	= response.resAddr;
        info.isEncoder 	= response.isEncoder;
        info.isRecord 	= response.isRecord;
        info.isOnline 	= response.isOnline;
        info.isNormal 	= response.isNormal;
        info.strFaultReason = response.strFaultReason;
        return true;
    }

    bool CUsgManager::setCseqValues( const std::string& sid ,int cseq )
    {
        writeLock w( m_cseqLock );
        std::map< std::string, int >::iterator it = m_streamSid2Cseq.find( sid );
        if ( it == m_streamSid2Cseq.end() )
        {
            m_streamSid2Cseq[sid] = cseq;
        }
        return true;
    }

    bool CUsgManager::getCsqlValues( const std::string& sid ,int& cseq )
    {
        readLock r( m_cseqLock );
        std::map< std::string, int >::iterator it = m_streamSid2Cseq.find( sid );
        if ( it != m_streamSid2Cseq.end() )
        {
            cseq = it->second;
            if (cseq == 0)
            {
                m_streamSid2Cseq.erase(it);
                return true;
            }
            return true;
        }
        return false;
    }

    bool CUsgManager::setFormatValues( const std::string& sid ,std::string format )
    {
        writeLock w( m_formateLock );
        std::map< std::string, std::string >::iterator it = m_streamSid2Format.find( sid );
        if ( it == m_streamSid2Format.end() )
        {
            m_streamSid2Format[sid] = format;
        }
        return true;
    }

    bool CUsgManager::getFormatValues( const std::string& sid ,std::string &format )
    {
        readLock r( m_formateLock );
        std::map< std::string, std::string >::iterator it = m_streamSid2Format.find( sid );
        if ( it != m_streamSid2Format.end() )
        {
            format = it->second;
            return true;
        }
        return false;
    }

    bool CUsgManager::setSrcAddr( const std::string& sid, std::string srcAddr )
    {
        writeLock w( m_srcAddrLock );
        std::map< std::string, std::string >::iterator it = m_streamSid2SrcAddr.find( sid );
        if ( it == m_streamSid2SrcAddr.end() )
        {
            m_streamSid2SrcAddr[sid] = srcAddr;
        }
        return true;
    }

    bool CUsgManager::getSrcAddr( const std::string& sid, std::string &srcAddr )
    {
        readLock r( m_srcAddrLock );
        std::map< std::string, std::string >::iterator it = m_streamSid2SrcAddr.find( sid );
        if ( it != m_streamSid2SrcAddr.end() )
        {
            srcAddr = it->second;
            return true;
        }
        return false;
    }

    void CUsgManager::rgLinkDown()
    {
        writeLock w( m_cseqLock );
        m_realSidsCond.notify_all();
    }

    void CUsgManager::byeAllRealThread()
    {
        char chBuf[255];
        sprintf(chBuf,"%ld:Usg:Bye", wtoe_getThreadid() );
        show_thread( chBuf );
        ai::LogInfo << "Thread: " << chBuf;

        while( true )
        {
            if( m_threadForceStop )
            {
                ai::LogInfo << "Thread: " << chBuf << " EXIT";
                return;
            }
            if ( !m_inviteSession )
            {
                ai::LogInfo << "Thread: " << chBuf << " EXIT";
                return;
            }
            //============ 有引起死锁的隐患
			{
            writeLock w( m_cseqLock );
            m_realSidsCond.wait( w );
            //=============
            if ( m_streamSid2Cseq.empty() )
                continue;

            std::map< std::string, int >::iterator it = m_streamSid2Cseq.begin(), ite = m_streamSid2Cseq.end();
            for ( ; it != ite; ++it )
            {
                int cseq = it->second;
                cseq++;
                m_inviteSession->bye( it->first, cseq );
            }

            m_streamSid2Cseq.clear();
			}
			{
            writeLock w2( m_formateLock );
            m_streamSid2Format.clear();
			}
			{
            writeLock w3( m_srcAddrLock );
            m_streamSid2SrcAddr.clear();
			}
			{
            writeLock w4( m_uuidLock );
            m_streamSid2uuid.clear();
			}
        }
        ai::LogInfo << "Thread: " << chBuf << " EXIT";
    }

    bool CUsgManager::setPackerType( uint8_t type )
    {
        if ( type > 2 )
        {
            return false;
        }
        m_packerType = (EPackerType)type;
        return true;
    }

    bool CUsgManager::preparePlayRealStream( const boost::uuids::uuid &mediaId, const uint8_t mOrs, const ACE_INET_Addr& clientAddr,  ACE_INET_Addr &remoteAddr, uint16_t& streamRate,  uint8_t &packerType, std::string &sid )
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getMediaInfo( mediaId, resInfo, remoteSipUrl, sipUrl ) )
            return false;
        if (  !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        //默认值为8192
        streamRate = 2048;
        if( !resInfo.resStatus ) return false;

        packerType = ( uint8_t )resInfo.packerType;
        sid = resInfo.sipResCode;

        std::string sipXml;
        char tempBuf[20];
        clientAddr.get_host_addr(tempBuf,20);
        std::string strAddr = tempBuf;
        ESocketType socketType = ESOCKETTYPE_UDP;
        strAddr += " " + getSocketType( socketType ) + " ";
        u_short port = clientAddr.get_port_number();
        std::stringstream stream;
        stream << port;
        std::string strPort;
        stream >> strPort;
        strAddr += strPort;

        sipXml = resInfo.sipResCode; //江西XML结构里面需要sipResCode

        uint8_t imageSize;

        if( mOrs == 1 )//主码流
        {
            imageSize = 3;
        }
        else if( mOrs == 2 )//子码流
        {
            imageSize = 1;
        }
        else
        {
            return false;
        }

        if ( !m_protocolWtoe->getRealMediaString( imageSize, strAddr,sipXml ) )
        {
            return false;
        }

        std::string subject = "";
        subject += resInfo.sipResCode;
        subject += ":0";
        subject += getReceiveSeq(19);
        subject += ",";
        subject += m_lCode;
        subject += ":";
        subject += getReceiveSeq(20);

        boost::shared_ptr<CReciveSipData> spSipRec( new( CReciveSipData ) );
        if ( !spSipRec )
        {
            return false;
        }

        if ( !m_inviteSession->invite( sid, sipUrl,subject,sipXml ) )
        {
            return false;
        }

        if( !waitResponse( sid, 2 ) ) return false;

        //解析format字段，获取格式信息（如码率等），如果解析失败，不影响返回结果
        std::string format = "";
        if( getFormatValues(sid, format) )
        {
            parseFormat(format, streamRate);
        }
        std::string szSrcAddr;
        getSrcAddr( sid, szSrcAddr );
        const ACE_INET_Addr addr( szSrcAddr.c_str() );
        remoteAddr = ACE_INET_Addr(szSrcAddr.c_str());

        ai::LogInfo << "INVITE: " << resInfo.sipResCode << " FROM: " << szSrcAddr.c_str() << " TO: " << strAddr.c_str();
        return true;
    }

///< waitResponse主要是用来处理流请求中，等待下级确认的
    bool CUsgManager::waitResponse( const std::string &sid, int sec )
    {
        for( int i = 0; i < sec*2; ++i )
        {
            wtoe::CSharedTimer::sleep( 1 * 500 );

            int cseq;
            if (getCsqlValues(sid, cseq))
            {                
                return (cseq>0);
            }
        }

        return false;
    }

    void CUsgManager::registResNotify( ISgResNotify *notify )
    {
        m_notify = notify;
    }

    void CUsgManager::unregistResNofity( ISgResNotify *notify )
    {
        m_notify = 0;
    }

    ISgResNotify* CUsgManager::getNotify()
    {
        return m_notify;
    }

    void CUsgManager::registAlarmNotify( ISgAlarmNotify *notify )
    {
        m_alarmNotify = notify;
    }

    void CUsgManager::unregistAlarmNotify( ISgAlarmNotify *notify )
    {
        m_alarmNotify = 0;
    }

    ISgAlarmNotify* CUsgManager::getAlarmNotify()
    {
        return m_alarmNotify;
    }

    bool CUsgManager::preparePlayHistoryStream( const boost::uuids::uuid &mediaId, const TimePeriodUnit &timePeriod, const ACE_INET_Addr clientAddr, ACE_INET_Addr &remoteAddr, std::string &sid )
    {
        std::vector< std::pair< std::string, TimePeriodUnit > > infos;
        if( !getMediaInfoByMedia( mediaId, timePeriod, infos ) ) return false;

        if( infos.size() == 0 )
        {
            return false;
        }

        if( !playHistoryPrepare( mediaId, infos[0].first, timePeriod, clientAddr, sid ) ) return false;

        return true;
    }

    bool CUsgManager::prepareDownloadHistoryStream( const boost::uuids::uuid &mediaId, const TimePeriodUnit &timePeriod, const ACE_INET_Addr clientAddr, ACE_INET_Addr &remoteAddr, std::string &sid )
    {
        std::vector< std::pair< std::string, TimePeriodUnit > > infos;
        if( !getMediaInfoByMedia( mediaId, timePeriod, infos ) ) return false;

        if( infos.size() == 0 )
        {
            return false;
        }

        if( !downloadHistoryPrepare( mediaId, infos[0].first, timePeriod, clientAddr, sid ) ) return false;

        return true;
    }

    bool CUsgManager::getAllResInfos( std::map<boost::uuids::uuid, SResInfo >& mapRes, uint32_t& timeStamp )
    {
        return getAllResInfo( mapRes, timeStamp );
    }

    bool CUsgManager::questCatalogError( const std::string &sid, void *param )
    {
        if( m_questCatalogCmds.find( sid ) == m_questCatalogCmds.end() )
            return true;

        std::string devid = m_questCatalogCmds[sid];
        CQuestCatalogActiveSet* pSet = CQuestCatalogActiveSet::get();
        if ( pSet )
            pSet->removeActive( sid, devid );
        m_questCatalogCmds.erase( sid );

        return true;
    }

    //static uint32_t g_addPJThreadDesCount = 0;

    bool CUsgManager::addPJThreadDes( long *des )
    {
        m_gsPjThreadDes.push_back( des );

        return true;
    }

    void CUsgManager::addEpid2Sids( const uint32_t& epid, const std::string& sid )
    {
        lock_type lock( m_mutexEpid2Sids );
        m_epid2Sids[ epid ].insert( sid );
    }

    void CUsgManager::delEpid2Sids( const uint32_t& epid /*= -1*/, const std::string& sid /*= "" */ )
    {
        lock_type lock( m_mutexEpid2Sids );
        if ( epid != (uint32_t)-1 )
        {
            m_epid2Sids.erase( epid );
        }
        else if ( !sid.empty() )
        {
            std::map< uint32_t, std::set<std::string> > ::iterator itor     = m_epid2Sids.begin();
            std::map< uint32_t, std::set<std::string> > ::iterator itor_end = m_epid2Sids.end();
            for ( ; itor != itor_end; ++itor )
            {
                std::set<std::string>::iterator itor_sid     =  (*itor).second.find( sid );
                std::set<std::string>::iterator itor_sidEnd  =  (*itor).second.end();
                if ( itor_sid != itor_sidEnd )
                {
                    (*itor).second.erase( sid );
                    return;
                }
            }
        }
    }

    void CUsgManager::getSidsByEpid( const uint32_t& epid, std::set<std::string>& setSids )
    {
        setSids.clear();
        if ( epid != (uint32_t)-1 )
        {
            lock_type lock( m_mutexEpid2Sids );
            setSids = m_epid2Sids[epid];
        }
    }

    void CUsgManager::clearEpid2Sids()
    {
        lock_type lock( m_mutexEpid2Sids );
        m_epid2Sids.clear();
    }

    void CUsgManager::parseFormat( std::string format, uint16_t &streamRate )
    {
        /*
         * format格式:v/1/2/3/4/5a/6/7/8
         * 第0个参数：编码格式
         * 第1个参数：分辨率
         * 第2个参数：帧率
         * 第3个参数：码率类型
         * 第4个参数：码率
         */
//     std::cout<<"parseFormat,format="<<format<<std::endl;

        //先过滤掉出视频参数
        std::string::size_type pos = format.find("a/");
        if ( pos == std::string::npos )
            return;
        format = format.substr(2, pos-2);

        std::vector<int> vecInts = splitFormat(format, "/");
        if( vecInts.size() != 5 )
        {
            return ;
        }
        /*
         * 获取码率的方式：
         * 1.先读码率字段，如果得到，直接使用
         * 2.如果读取码率字段小于等于0（失败），再去读视频尺寸，根据视频尺寸估算码率
         * 3.如果视频尺寸字段小于等于0（失败），直接使用默认值
         */
        if( vecInts[4] > 0 )
        {
            streamRate = vecInts[4];
            return;
        }

        if( vecInts[1] > 0 )
        {
            convertSizeToRate(vecInts[1], streamRate);
            return;
        }
    }

    void CUsgManager::convertSizeToRate(int videoSize, uint16_t &streamRate)
    {
        uint16_t cif_size = 512;
        switch(videoSize)
        {
            case 1:
            {
                streamRate = cif_size/4;  //QCIF
                return;
            }
            case 2:
            {
                streamRate = cif_size;    //CIF
                return;
            }
            case 3:
            {
                streamRate = cif_size*4;  //4CIF
                return;
            }
            case 4:
            {
                streamRate = cif_size*8;  //D1
                return;
            }
            case 5:
            {
                streamRate = cif_size*8;  //720P
                return;
            }
            case 6:
            {
                streamRate = cif_size*16;  //1080P
                return;
            }
            default:
            {
                //其他情况则不修改，直接使用默认值
                return;
            }
        }
    }

//字符串分割函数
    std::vector<int> CUsgManager::splitFormat(std::string str,std::string pattern)
    {
        int pos;
        std::vector<int> result;
        str+=pattern;//扩展字符串以方便操作
        int size=str.size();
        for(int i=0; i<size; i++)
        {
            pos=str.find(pattern,i);
            if(pos<size)
            {
                std::string s=str.substr(i,pos-i);
                int tmp = atoi(s.c_str());
                result.push_back(tmp);
                i=pos+pattern.size()-1;
            }
        }
        return result;
    }

    std::vector<std::string> CUsgManager::getHelpInfos()
    {
        std::vector<std::string> helps;
        return helps;
    }

    CSpIUsgService CUsgManager::getUsgService()
    {
        return m_usgService;
    }

    std::string CUsgManager::getSocketType( ESocketType socketType )
    {
        std::string szSocket;
        switch ( socketType)
        {
            case ESOCKETTYPE_TCP_PASSIVE:
                szSocket = "TCPPASSIVE";
                break;
            case ESOCKETTYPE_TCP_ACTIVE:
                szSocket = "TCPACTIVE";
                break;
            case ESOCKETTYPE_UDP:
            default:
                szSocket = "UDP";
                break;
        }
        return szSocket;
    }

    bool CUsgManager::playRealStream( const SResInfo& resInfo, const std::string& sipUrl, const std::string& szDest, std::string& szSrc, std::string& sid, ESocketType socketType, bool bUseSub, std::string& szError)
    {
        //uint16_t streamRate;
        //uint8_t  packerType;

        if (  !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        //默认值为8192
        //streamRate = 2048;
 //      if( !resInfo.resStatus ) return false;

        //packerType = ( uint8_t )resInfo.packerType;
        sid = resInfo.sipResCode;

        std::string sipXml;
        std::string strAddr = szDest;
        size_t pos = strAddr.find( ':');
        if (pos != std::string::npos)
        {
            std::string szSocket = getSocketType( socketType );
            strAddr = szDest.substr(0, pos) + " " + szSocket + " " + szDest.substr(pos + 1, szDest.length());
        }
        sipXml = resInfo.sipResCode; //江西XML结构里面需要sipResCode

	    uint8_t imageSize = 6;
	    if (bUseSub)
		    imageSize = 3;
        if ( !m_protocolWtoe->getRealMediaString( imageSize, strAddr,sipXml ) )
        {
            szError = "VIDEO " + resInfo.sipResCode + " getRealMedia is FAIL";
            return false;
        }

        std::string subject = "";
        subject += resInfo.sipResCode;
        subject += ":0";
        subject += getReceiveSeq(19);
        subject += ",";
        subject += m_lCode;
        subject += ":";
        subject += getReceiveSeq(20);

        boost::shared_ptr<CReciveSipData> spSipRec( new( CReciveSipData ) );
        if ( !spSipRec )
        {
            return false;
        }

        boost::uuids::uuid uid = boost::uuids::random_generator()();
        std::string szUuid = boost::uuids::to_string(uid);
        boost::algorithm::erase_all(szUuid, "-");
        sid = szUuid;

        if ( !m_inviteSession->invite( sid, sipUrl,subject,sipXml ) )
        {
            szError = "INVITE: " + resInfo.sipResCode + " sid:" + sid + " INVITE is FAIL";
            return false;
        }

        if ( !waitResponse( sid, 5 ) ) {
            szError = "INVITE: " + resInfo.sipResCode + " sid:" + sid + " waitResponse is FAIL";
            return false;
        }
        getSrcAddr( sid, szSrc );

        ai::LogTrace << "INVITE: " << resInfo.sipResCode << " sid: " << sid
          << " FROM: " << szSrc << " TO: " << strAddr;
        return true;
    }

    bool CUsgManager::isPlaying()
    {
        boost::recursive_mutex::scoped_lock lock( m_mutexPlaying );
        return m_isPlaying;
    }

    int CUsgManager::startPlay( const std::string& szSipId, const std::string& szResCode, const std::string& szDest, std::string& szSrc, ESocketType socketType, bool bUseSub, std::string& szError)
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getResId( szSipId, szResCode, resInfo, remoteSipUrl, sipUrl ) )
        {
            szError = "No such Video " + szResCode;
            return -2;
        }
/*        {
            boost::recursive_mutex::scoped_lock lock( m_mutexPlaying );
            if ( m_isPlaying )
                return false;
            m_isPlaying = true;
        }*/
	std::string szKey = szResCode + "-" + szDest + "-" + int2string( bUseSub) ;
        ai::LogInfo << "CUsgManager::startPlay BEGIN " << szKey;
        {
            boost::mutex::scoped_lock lock( m_mutexPlaySid );
            if ( m_mapPlaySid.find( szKey ) != m_mapPlaySid.end() )
            {
/*                boost::recursive_mutex::scoped_lock lock( m_mutexPlaying );
                m_isPlaying = false;*/
                return 0;
            }
        }
        std::string sid;
        std::string streamSipUrl = sipUrl;
        CResManager* pMgr = getResManager(szSipId);
        if (pMgr)
        {
            streamSipUrl = pMgr->getSipUrl(resInfo.sipResCode);
        }
        if ( !playRealStream( resInfo, streamSipUrl, szDest, szSrc, sid, socketType, bUseSub, szError ) )
        {
/*
            boost::recursive_mutex::scoped_lock lock( m_mutexPlaying );
            m_isPlaying = false;
*/
            return -3;
        }
        {
            writeLock w( m_uuidLock );
            std::map< std::string, boost::uuids::uuid >::iterator it = m_streamSid2uuid.find( sid );
            if ( it == m_streamSid2uuid.end() )
            {
                m_streamSid2uuid[sid] = resInfo.resId;
            }
            else
            {
               ai::LogInfo << "CUsgManager " << szKey << " sid: " << sid << " old uuid: " << boost::uuids::to_string(m_streamSid2uuid[sid]);
            }
        }
        {
            boost::mutex::scoped_lock lock( m_mutexPlaySid );
            m_mapPlaySid[szKey] = sid;
        }
/*        {
            boost::recursive_mutex::scoped_lock lock( m_mutexPlaying );
            m_isPlaying = false;
        }*/
        return 0;
    }

    bool CUsgManager::stopPlay( const std::string& szSipId, const std::string& szResCode, const std::string& szDest, bool bUseSub )
    {
        std::string sId = "";
        {
            boost::mutex::scoped_lock lock( m_mutexPlaySid );
            if ( szDest != "" ) {
		std::string szKey = szResCode + "-" + szDest + "-" + int2string(bUseSub);
                std::map<std::string, std::string>::iterator it = m_mapPlaySid.find(szKey);
                if (it != m_mapPlaySid.end()) {
                    sId = it->second;
                    m_mapPlaySid.erase(it);
                }
            }
            else if ( !m_mapPlaySid.empty() )
            {
                std::string szKey = szResCode + "-";
                std::map<std::string, std::string>::iterator it = m_mapPlaySid.begin();
                for ( ; it != m_mapPlaySid.end(); it++ )
                {
                    std::string szTmp = it->first;
                    if ( szTmp.find( szKey ) == 0 )
                    {
					    size_t pos = szTmp.find_last_of('-');
						if (pos != std::string::npos )
						{
						 	if ( szTmp.substr(pos+1, szTmp.length() ) == int2string(bUseSub) )
							{
                                sId = it->second;
                                m_mapPlaySid.erase( it );
                                break;
							}
						}
                    }
                }
            }
        }
        if ( sId.empty() )
        {
            return true;
        }
        bool bRet = stopRealStream( sId );
        ai::LogInfo << "BYE " << szResCode.c_str() << " sid: " << sId.c_str() << " FROM: " << szDest.c_str() << " RESULT: " << bRet;
        return bRet;
    }

    bool CUsgManager::addRemote( uint32_t id, const std::string& szSipId, const std::string& szAddr )
    {
        if (!m_bIsListening)
            return true;

        writeLock lock( m_mutexAllowSip );
        if ( m_mapAllowSip.find( szSipId ) != m_mapAllowSip.end() )  //重新加入
        {
            return true;
        }
        CResManager* pMgr = new_o( CResManager, id, szSipId, szAddr, m_packerType );
        if ( !pMgr->init( m_funcInsertVideo, m_funcUpdateRemoteStatus, m_funcUpdateVideoName ) )
        {
            delete_o( pMgr );
            return false;
        }
        m_mapAllowSip[szSipId] = pMgr;
        return true;
    }

    bool CUsgManager::delRemote( const std::string& szSipId, const std::string& szAddr )
    {
        ai::LogInfo << "CUsgManager::delRemote "  << szSipId << " addr: " << szAddr;
        CResManager* pMgr = NULL;
        {
            writeLock lock(m_mutexAllowSip);
            std::map<std::string, CResManager*>::iterator it = m_mapAllowSip.find(szSipId);
            if (it == m_mapAllowSip.end())
            {
                return false;
            }
            pMgr = it->second;
            m_mapAllowSip.erase(it);
            if (!pMgr /*|| pMgr->getRemoteAddr() != szAddr*/)
                return false;
        }
//         {
//             //删除请求的所有流
//             boost::mutex::scoped_lock lock(m_mutexPlaySid);
//             std::map< std::string, std::string>::iterator it = m_mapPlaySid.begin(), itNext;
//             while (it != m_mapPlaySid.end())
//             {
//                 itNext = it;
//                 itNext++;
//                 std::string szKey = it->first;
//                 std::string sId = it->second;
//                 stopRealStream(sId);
//                 m_mapPlaySid.erase(it);
//                 it = itNext;
//             }
//         }
        pMgr->fini();
        delete_o( pMgr );

        return true;
    }

    bool CUsgManager::addVideo( const std::string& szSipId, const std::string& szVideoAddr, const std::string& szName, uint32_t id )
    {
        CResManager* pMgr = getResManager( szSipId );
        if ( pMgr )
            return pMgr->addVideo( szVideoAddr, szName, id );
        return false;
    }

    bool CUsgManager::getVideoStatus( const std::string& szSipId, const std::string& szVideoAddr )
    {
        CResManager* pMgr = getResManager( szSipId );
        if ( pMgr )
            return pMgr->getVideoStatus( szVideoAddr );
        return false;
    }

    void CUsgManager::setLevel( uint16_t level )
    {
        USG_SIPSTACK->setLevel( level );
    }

    bool CUsgManager::init(FUNC_INSERTVIDEO_CALLBACK func1,
                           FUNC_UPDATEREMOTESTATUS_CALLBACK func2,
                           FUNC_UPDATEVIDEONAME_CALLBACK func3 )
    {
        if ( m_bOkey )
            return true;
        if (!m_bIsListening)
            return true;
        if ( !startAllThread() )
            return false;

        m_funcInsertVideo = func1;
        m_funcUpdateRemoteStatus = func2;
        m_funcUpdateVideoName = func3;

        m_bOkey = true;
        return true;
    }

    void CUsgManager::fini()
    {
        if ( !m_bOkey )
            return;

        m_bOkey = false;
        stopAllThread();
        {
            boost::mutex::scoped_lock lock( m_mutexPlaySid );
            std::map< std::string, std::string>::iterator it;
            for ( it = m_mapPlaySid.begin(); it != m_mapPlaySid.end(); it++ )
            {
                std::string sId = it->second;
                bool bRet = stopRealStream( sId );
                ai::LogInfo << "BYE " << it->first.c_str() << " sid: " << sId.c_str() << " RESULT: " << bRet;
            }
            m_mapPlaySid.clear();
        }
        {
            writeLock lock( m_mutexAllowSip );
            std::map< std::string, CResManager*>::iterator it = m_mapAllowSip.begin();
            for (; it != m_mapAllowSip.end(); it++ )
            {
                CResManager* pMgr = it->second;
                pMgr->fini();
                delete_o( pMgr );
            }

            m_mapAllowSip.clear();
        }
    }

    bool CUsgManager::getResId( const std::string& szSipId, const std::string& szResCode, SResInfo& info, std::string& remoteSipUrl, std::string& sipUrl )
    {
        if (!m_bOkey)
            return false;
        CResManager* pMgr = getResManager( szSipId );
        if ( !pMgr || !pMgr->isRegist() )
            return false;
        if ( !pMgr->getResInfoBySipCode( szResCode, info ) )
            return false;

        remoteSipUrl = pMgr->getSipId();
        sipUrl = pMgr->getSipUrl();
        return true;
    }

    bool CUsgManager::ptzControl( const std::string& szSipId, const std::string& szResCode, uint8_t iPreset )
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getResId( szSipId, szResCode, resInfo, remoteSipUrl, sipUrl ) )
            return false;

// 	if ( !ptzCtrl( EPTZCOMMAND_SWITCH, resInfo.resId, 0, iPreset ) )
// 	{
// 		return false;
// 	}

        if ( !m_protocolWtoe || !m_ddcpDoSession )
            return false;

        SPtzCommand command;
        command.command = EPTZCOMMAND_SWITCH;
        command.commandParam1 = 0;
        command.commandParam2 = iPreset;

        std::string sipXml = szResCode;
        if ( !m_protocolWtoe->getPtzCommandString( command,sipXml ) )
        {
            return false;
        }

        std::string sid;
        if ( !m_ddcpDoSession->ddcpDo( sid, sipUrl, sipXml ) )
        {
            return false;
        }
        ai::LogInfo << "CUsgManager::ptzControl " << szResCode;
        return true;
    }

    bool CUsgManager::getPtzs( const std::string& szSipId, const std::string& szResCode, std::map< uint8_t, std::string>& mapPtzs )
    {
        SResInfo resInfo;
        std::string remoteSipUrl, sipUrl;
        if ( !getResId( szSipId, szResCode, resInfo, remoteSipUrl, sipUrl ) )
            return false;

        if ( !onqueryPreposition( resInfo.resId, mapPtzs ) )
        {
            return false;
        }
        return true;

    }

}
