#include <iostream>
#include <string>
#include <odb/core.hxx>
#include "person.hxx"
#include "person-odb.hxx"

#include <memory> // std::auto_ptr
#include <odb/database.hxx>
#include <odb/transaction.hxx>
#include <odb/mysql/database.hxx>

#include "video_server.h"
#include "video_server-odb.hxx"
#include "video_source.h"
#include "video_source-odb.hxx"
#include "detection_point.h"
#include "detection_point-odb.hxx"
#include "monitor.h"
#include "monitor-odb.hxx"

#include "../database_process.h"
#include "nv_aimonitor31_api.h"


using namespace std;
using namespace odb::core;
using namespace db;

bool test_read_VideoServer(std::shared_ptr<odb::database>& db);
bool test_read_VideoSource(std::shared_ptr<odb::database>& db);
bool test_queryData();
bool test_insertData();
bool test_updateData();
bool test_eraseData();
bool test_eraseQueryData();
bool test_queryProgramPreset();
bool test_queryMonitorDetectPoint();
bool test_queryAlgoPlanParams();

bool test_queryMonitorChannelId();
int main(int argc, char* argv[])
{
    
    init("root", "welltrans8746", "aimonitorv3", "127.0.0.1", 3306);

    auto databasePtr = getDatabase();
    test_read_VideoServer(databasePtr);
    test_read_VideoSource(databasePtr);

	test_queryData();
    test_queryProgramPreset();
    test_queryMonitorDetectPoint();
    test_queryAlgoPlanParams();
    test_queryMonitorChannelId();

    std::cout << "test  deinit() :" << std::endl;
    deinit();
    test_queryData();


}              



bool test_queryMonitorChannelId()
{
    unsigned long channelId = 0;
    unsigned long detectPointId = 1;
    std::string monitorIp = "**************";

    queryMonitorChannelId(channelId, detectPointId, monitorIp);

    cout << "channelId = " << channelId << endl;
}



bool test_queryProgramPreset()
{

    std::vector<PresetProgramData> results;
    odb::query<PresetProgramData> query(odb::query<PresetProgramData>::Program::id == 1);
    queryProgramPreset(results, query);
    for (auto& data : results)
    {
        cout << "qurey Program::id == 1 :  getSwitchTime " << data.programInfoPtr->getSwitchTime() << " getStartDate " << data.programPtr->getStartDate() << endl;
    }

    std::vector<PresetProgramData> results1;
    odb::query<PresetProgramData> query1;
    queryProgramPreset(results1, query1);
    for (auto& data : results1)
    {
        cout << "qurey Program::id == 1 :  getSwitchTime " << data.programInfoPtr->getSwitchTime() << " getStartDate " << data.programPtr->getStartDate() << endl;
    }

}


bool test_queryAlgoPlanParams()
{

    std::vector<AlgoParamsData> results;
    odb::query<AlgoParamsData> query(odb::query<AlgoParamsData>::AlgParamPlan::planId == 1);
    queryAlgoPlanParams(results, query);
    for (auto& data : results)
    {
        cout << "qurey AlgParamPlan::planId == 1: getPlanId " << data.paramValue << " paramValue " << data.paramKey << endl;
    }

    std::vector<AlgoParamsData> results1;
    odb::query<AlgoParamsData> query1;
    queryAlgoPlanParams(results1, query1);
    for (auto& data : results1)
    {
        cout << "qurey all getPlanId " << data.planId << " getParamKey " << data.paramKey << endl;
    }

}


bool test_queryMonitorDetectPoint()
{
#if 0
    std::vector<MonitorDetectPointData> results;
    odb::query<MonitorDetectPointData> query(odb::query<MonitorDetectPointData>::DetectionPoint::id == 1);
    queryMonitorDetectPoint(results, query);
    for (auto& data : results)
    {
        cout << "qurey DetectionPoint::id == 1 : monitor ip " << data.monitorPtr->getIp() << " DetecPointName " << data.detctPointPtr->getDetecPointName() << endl;
    }

    std::vector<MonitorDetectPointData> results1;
    odb::query<MonitorDetectPointData> query1;
    queryMonitorDetectPoint(results1, query1);
    for (auto& data : results1)
    {
        cout << "monitor ip " << data.monitorPtr->getIp() << " DetecPointName " << data.detctPointPtr->getDetecPointName() << endl;
    }
#endif
    std::vector<MonitorDetectPointData> results = {};
    odb::query<MonitorDetectPointData> query(odb::query<MonitorDetectPointData>::DetectionPoint::id == 1);


    queryData(results, query);

    for (auto& data : results)
    {
        cout << "qurey DetectionPoint::id == 1 : monitor ip " << data.monitorPtr->getIp() << " DetecPointName " << data.detectPointPtr->getDetectPointName() << endl;
    }

    std::vector<MonitorDetectPointData> results1;
    odb::query<MonitorDetectPointData> query1;
    //queryMonitorDetectPoint(results1, query1);
    queryData(results1, query1);
    for (auto& data : results1)
    {
        cout << "monitor ip " << data.monitorPtr->getIp() << " DetecPointName " << data.detectPointPtr->getDetectPointName() << endl;
    }


}



bool test_eraseData()
{
    odb::query<person> q(odb::query<person>::id == 2);
    std::vector<person> vecResult = {};
    queryData(vecResult, q);
    person person1 = vecResult.at(0);

    eraseData(person1);

}

bool test_eraseQueryData()
{
    odb::query<person> q(odb::query<person>::id == 33);

    eraseData(q);

}


bool test_updateData()
{
    odb::query<person> q(odb::query<person>::id == 2);
    std::vector<person> vecResult = {};
    queryData(vecResult, q);
    person p1 = vecResult.at(0);

    p1.age(100);

    updateData(p1);

}

bool test_insertData()
{

    person john("TEST", "Doe", 11);

    insertData(john);

}



bool test_queryData()
{

    odb::query<person> q(odb::query<person>::id == 2);

    std::vector<person> vecResult = {};
    queryData(vecResult, q);

    for (auto& i : vecResult)
    {
        cout << "Hello2, " << i.age() << endl;
    }
}


bool test(unique_ptr<database>& dbPtr)
{
    try
    {
        cout << "try connetting ************** database ..." << endl;
        // mysql server's host name: root, password:123456, ip:************, port:30306
        unique_ptr<database> db(new odb::mysql::database("root", "welltrans8746", "aimonitorv3", "**************", 3306));
        cout << "connetted success" << endl;

        // Create a few persistent person objects.
            //
        //unsigned long john_id, jane_id, joe_id;
        //{
        //    person john("John", "Doe", 33);
        //    person jane("Jane", "Doe", 32);
        //    person joe("Joe", "Dirt", 30);

        //    transaction t(db->begin());

        //    //// Make objects persistent and save their ids for later use.
        //    ////
        //    john_id = db->persist(john);
        //    jane_id = db->persist(jane);
        //    joe_id = db->persist(joe);

        //    t.commit();
        //}

        {
            typedef odb::query<person> query;
            typedef odb::result<person> result;
            transaction t(db->begin());
            result r(db->query<person>(query::id > 0));

            for (result::iterator i(r.begin()); i != r.end(); ++i)
            {
                cout << "Hello, " << i->age() << endl;
                //auto_ptr<person> joe(db->load<person>(joe_id));
                //i->age(i->age() + 1);
                //db->update(*i);
            }
        }


        //typedef odb::query<video_source> query;
        //typedef odb::result<video_source> result;
        ////// Say hello to those over 30.
        ////
        //{
        //    transaction t(db->begin());
        //    result r(db->query<video_source>(query::id > 0));
        //    for (result::iterator i(r.begin()); i != r.end(); ++i)
        //    {
        //        cout << "Hello, " << i->getVideoUUID() << "!" << endl;
        //    }
        //    t.commit();
        //}
    }
    catch (const odb::exception& e)
    {
        cerr << e.what() << endl;
        return 1;
    }
}

bool test_read_VideoServer(std::shared_ptr<odb::database>& db)
{

#if 1
    {
        typedef odb::query<VideoServer> query;
        
        typedef odb::result<VideoServer> result;
        transaction t(db->begin());
        result queResult(db->query<VideoServer>(query::id > 0));

        cout << endl;
        cout << "********* testReadVideoSource : wn_access_front_end  start ********* " << endl;

        for (auto& videoServer : queResult)
        {

            cout << " id : " << videoServer.getId() << " | ";
            cout << " name : "<< videoServer.getVideoSrcName() << " | ";
            cout << " access_type : " << videoServer.getAccessType() << " | ";
            cout << " ip : " << videoServer.getIp() << " | ";
            cout << " port : " << videoServer.getPort() << " | ";
            cout << " user_name : " << videoServer.getUserName() << " | ";
            cout << " password : " << videoServer.getPassword() << " | ";
            cout << " factory : " << videoServer.getFactory() << " | ";
            cout << " sipid : " << videoServer.getSipid() << " | ";
            cout << " status : " << videoServer.getStatus() << " | ";
            cout << " sub_num : " << videoServer.getSubNum() << " | ";
            cout << " is_del : " << videoServer.getIsDel() << " | " << endl;



        }
        cout << "********* testReadVideoSource : wn_access_front_end  end ********* " << endl;
        cout << endl;
        t.commit();
    }
#endif
}

bool test_read_VideoSource(std::shared_ptr<odb::database>& db)
{
#if 1
    {
        typedef odb::query<VideoSource> query;
        typedef odb::result<VideoSource> result;
        transaction t(db->begin());
        result queResult(db->query<VideoSource>(query::id > 0));
        //result queResult(db->query<VideoSource>("ORDER BY" + query::id + "DESC"));

        cout << endl;
        cout << "********* testVideoServer : wn_video_source  start ********* " << endl;
        for (auto& videoSource : queResult)
        {

            cout << " id : " << videoSource.getId() << " | ";
            cout << " name : " << videoSource.getName() << " | ";
            cout << " adress : " << videoSource.getAddress() << " | ";
            cout << " access_front_end_id : " << videoSource.getVideoServerId() << " | ";
            cout << " location : " << videoSource.getLocation() << " | ";
            cout << " has_ptz : " << videoSource.getHasPtz() << " | ";
            cout << " is_detectable : " << videoSource.getIsDetectable() << " | ";
            cout << " is_enable : " << videoSource.getIsEnable() << " | ";
            cout << " is_change : " << videoSource.getIsChange() << " | " << endl;

        }
        cout << "********* testVideoServer : wn_video_source  end ********* " << endl;
        cout << endl;

    }
#endif
}