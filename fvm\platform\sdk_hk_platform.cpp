/**
 * Project FVM
 */
#ifdef SDK_HK_SUPPORTED
#include "sdk_hk_platform.h"
#include "util/worker_pool.h"
#include "ailog.h"
#include "HCNetSDK.h"

using namespace odb::core;
using namespace timer;
/**
 * SDKHKPlatform implementation
 */
namespace fvm::platform
{
//等待10秒
#define WAIT_TIME 10
//触发登录的周期时间(单位：s)
#define LOGIN_CYCLE 60
//获取资源周期时间(单位：s)
#define GETRESOURCE_CYCLE 300

    void HKPlay::dealData(uint8_t* buf, int bufSize)
    {
        if (fRecvCallback)
        fRecvCallback(buf, bufSize);
    }

    bool HKPlay::startPlay(RecvCallback pCallback)
    {
        if (iRealPlayHandle > -1)
            return false;
        //设置回调
        setCallback(pCallback);
        //启动预览并设置回调数据流
        NET_DVR_CLIENTINFO clientInfo = { 0 };
        //指定预览通道号
        clientInfo.lChannel = iChanIndex;
        //海康回调
        auto fRealDataCallBack = [](LONG lRealHandle, DWORD dwDataType, uint8_t* pBuffer, DWORD dwBufSize, void* pUser)
        {
            switch (dwDataType)
            {
            case NET_DVR_SYSHEAD:
                break;
            case NET_DVR_STREAMDATA:
                if (dwBufSize == 0 || pUser == 0)
                    return;
                HKPlay* pHKPlat = (HKPlay*)(pUser);
                if (pHKPlat)
                    pHKPlat->dealData(pBuffer, dwBufSize);
                break;
            }
        };

        //播放注册回调
        iRealPlayHandle = NET_DVR_RealPlay_V30(iUserId, &clientInfo, fRealDataCallBack, this, 0);
        if (iRealPlayHandle == -1)
        {
            LONG dwErr = 0;
            std::string szMsg = NET_DVR_GetErrorMsg(&dwErr);
            ai::LogError << "iChanIndex=" << iChanIndex << " NET_DVR_RealPlay_V30 " << dwErr << ", error:" << szMsg;
            //关闭预览
            NET_DVR_StopRealPlay(iRealPlayHandle);
            iRealPlayHandle = -1;
            return false;
        }
        return true;
    }

    void HKPlay::stopPlay(void)
    {
        if (iRealPlayHandle > -1)
        {
            //关闭预览
            NET_DVR_StopRealPlay(iRealPlayHandle);
            iRealPlayHandle = -1;
        }
    }

    void HKPlay::setCallback(RecvCallback pCallback)
    {
        fRecvCallback = pCallback;
    }

    bool HKPlay::ptzControl(int iActPreset)
    {
        if (iRealPlayHandle > -1)
        {
            return (bool)NET_DVR_PTZPreset(iRealPlayHandle, GOTO_PRESET, iActPreset);
        }
        return false;
    }

    bool HKPlay::savePreset(int iActPreset)
    {
        if (iRealPlayHandle > -1)
        {
            return (bool)NET_DVR_PTZPreset(iRealPlayHandle, SET_PRESET, iActPreset);
        }
        return false;
    }

    bool HKPlay::getPtzPosition(double& x, double& y, double& z)
    {
        if (iUserId > -1)
        {
            NET_DVR_PTZPOS struPtzPos;
            DWORD dwReturn = 0;
            if (NET_DVR_GetDVRConfig(iUserId, NET_DVR_GET_PTZPOS, iChanIndex, &struPtzPos, sizeof(struPtzPos), &dwReturn))
            {
                x = (double)struPtzPos.wPanPos;
                y = (double)struPtzPos.wTiltPos;
                z = (double)struPtzPos.wZoomPos;
                return true;
            }
            else
            {
                ai::LogError << "iChanIndex:"<< iChanIndex <<" NET_DVR_GetLastError: " << NET_DVR_GetLastError();
            }
        }
        return false;
    }


    void SDKHKPlatform::initSDK()
    {
        //海康SDK初始化
        NET_DVR_SetSDKInitCfg(NET_SDK_INIT_CFG_LIBEAY_PATH, (char*)"./lib/HKSDK/libcrypto.so");
        NET_DVR_SetSDKInitCfg(NET_SDK_INIT_CFG_SSLEAY_PATH, (char*)"./lib/HKSDK/libssl.so");
        NET_DVR_LOCAL_SDK_PATH struComPath = { 0 };
        sprintf(struComPath.sPath, "./lib/HKSDK"); //HCNetSDKCom文件夹所在的路径
        //sprintf(struComPath.sPath, "./lib/HKSDK/HCNetSDKCom");
        NET_DVR_SetSDKInitCfg(NET_SDK_INIT_CFG_SDK_PATH, &struComPath);
        NET_DVR_Init();
    }

    void SDKHKPlatform::cleanupSDK()
    {
        //海康SDK清理
        NET_DVR_Cleanup();
    }

    SDKHKPlatform::SDKHKPlatform()
    {
    };
 
    SDKHKPlatform::~SDKHKPlatform()
    {
        //注销海康登录
        fini();
    };

    void SDKHKPlatform::init(data::VideoServerPtr svrPtr)
    {
        FrontPlatform::init(svrPtr);
    }

    void SDKHKPlatform::fini(void)
    {
        bExit = true;                           //1.阻止startPlay
        //这里必须保证都停止播放才能退出海康登录
        {
            std::lock_guard<std::mutex> lock(playMtx);
            for (auto& it : vecRes)                 //2.停止所有stopPlay
            {
                it.stopPlay();
            }
            vecRes.clear();
        }
        
        if (iLoginID > -1)                      //3.退出海康登录
        {
            BOOL bRet = NET_DVR_Logout(iLoginID);
            iLoginID = -1;
            bOnline = false;
        }
    };

    bool SDKHKPlatform::startPlay(const std::string& addr, RecvCallback pCallback)
    {
        bool ret = false;
        if (bExit)
            return ret;
        //解析通道号
        int iChanel(0);
        if (!getChanelId(addr, iChanel))
            return ret;

        //等待登录
        int timeout = 0;
        if ( !bOnline )
        {
            doLogin();
        }
        while (!bOnline)
        {
            if (bExit)
                return false;
            //超过10秒没登录上 此次播放失败
            if (timeout++ > WAIT_TIME * 2)
                return ret;
            boost::this_fiber::sleep_for(std::chrono::milliseconds(500));
        }

        {
            std::lock_guard<std::mutex> lock(playMtx);
            auto findk = find_if(vecRes.begin(), vecRes.end(), [&](HKPlay& task)
                {
                    return task.iChanIndex == iChanel;
                });
            if (findk != vecRes.end())
                ret = findk->startPlay(pCallback);      //传递给海康播放
        }

        if (!ret)
        {
            {
                std::lock_guard<std::mutex> lock(playMtx);
                for (auto& it : vecRes)                 //2.停止所有stopPlay
                {
                    it.stopPlay();
                }
                vecRes.clear();
            }
            if (iLoginID > -1) {
                BOOL bRet = NET_DVR_Logout(iLoginID);
                iLoginID = -1;
                bOnline = false;
                bNeedGetResource = true;
            }
        }
        
        return ret;
    }

    void SDKHKPlatform::stopPlay(const std::string& addr)
    {
        std::lock_guard<std::mutex> lock(playMtx);
        //解析通道号
        int iChanel(0);
        if (!getChanelId(addr, iChanel))
            return;

        auto findk = find_if(vecRes.begin(), vecRes.end(), [&](HKPlay& task)
            {
                return task.iChanIndex == iChanel;
            });
        if (findk != vecRes.end())
        {
            findk->stopPlay();     //停止播放
        }
    }

    bool SDKHKPlatform::ptzControl(int iPreset)
    {
        return NET_DVR_PTZPreset_Other(iLoginID, 1, GOTO_PRESET, iPreset);
    };

    bool SDKHKPlatform::ptzControl(const std::string& addr, int iPreset)
    {
        std::lock_guard<std::mutex> lock(playMtx);
        int iChanel(0);
        if (!getChanelId(addr, iChanel))
            return false;

        auto findk = find_if(vecRes.begin(), vecRes.end(), [&](HKPlay& task)
            {
                return task.iChanIndex == iChanel;
            });
        if (findk != vecRes.end())
        {
            return findk->ptzControl(iPreset);
        }
        return false;
    };

    bool SDKHKPlatform::getPtzPosition(const std::string& addr,double& x, double& y, double& z)
    {
        std::lock_guard<std::mutex> lock(playMtx);
        int iChanel(0);
        if (!getChanelId(addr, iChanel))
            return false;

        auto findk = find_if(vecRes.begin(), vecRes.end(), [&](HKPlay& task)
            {
                return task.iChanIndex == iChanel;
            });
        if (findk != vecRes.end())
        {
            return findk->getPtzPosition(x, y, z);
        }
        return false;
    }

    void SDKHKPlatform::onChanged()
    {
        //页面上只有没登录成功的设备才可 修改提交 故不做操作
    }

    bool SDKHKPlatform::doLogin(void)
    {
        if(loginIgnoreCount > 0)
        {
            loginIgnoreCount--;
            return false;
        }

        NET_DVR_DEVICEINFO_V30 struDeviceInfo;
        memset(&struDeviceInfo, 0, sizeof(NET_DVR_DEVICEINFO_V30));
        //每次都会获取新数据
        std::string szIp = serverPtr->getIp();
        int iPort = serverPtr->getPort();
        std::string szUser = serverPtr->getUserName();
        std::string szPass = serverPtr->getPassword();

        {
            std::lock_guard<std::mutex> loginLock(loginMtx);
            LONG devLoginID = NET_DVR_Login_V30((char*)szIp.c_str(), iPort, (char*)szUser.c_str(), (char*)szPass.c_str(), &struDeviceInfo);
            if (devLoginID == -1) //登录不成功
            {
                LONG dwErr = 0;
                std::string szMsg = NET_DVR_GetErrorMsg(&dwErr);
                lastErrorString = "NET_DVR_Login_V30 " + szMsg;
                if (dwErr != m_iLastError)
                {
                    ai::LogError << "HK " << szIp << " LOGIN ERR " << dwErr << " " << szMsg;
                    m_iLastError = dwErr;
                }

                // 登录失败 清除之前数据，保证设备故障恢复后能正常取流
                {
                    std::lock_guard<std::mutex> lock(playMtx);
                    // 停止所有stopPlay
                    for (auto& it : vecRes) {
                        it.stopPlay();
                    }
                    vecRes.clear();
                }

                // 退出登录
                if (iLoginID > -1) {
                    ai::LogError << "HK " << szIp << " LOGIN ERR " << dwErr << " NET_DVR_Logout iLoginID : " << iLoginID;
                    BOOL bRet = NET_DVR_Logout(iLoginID);
                    iLoginID = -1;
                    bOnline = false;
                }
                
                if(dwErr == NET_DVR_USERNAME_NOT_EXIST || dwErr == NET_DVR_USER_LOCKED || dwErr == NET_DVR_TEST_SERVER_PASSWORD_ERROR || dwErr == NET_DVR_PASSWORD_ERROR || dwErr == NET_DVR_INVALID_USERID)
                {
                    //用户问题登录失败
                    loginIgnoreCount = AUTH_LOGIN_IGNORE_COUNT;
                }
                else if(dwErr == NET_DVR_NETWORK_FAIL_CONNECT || dwErr == NET_DVR_NETWORK_SEND_ERROR)
                {
                    //网络问题登录失败
                    loginIgnoreCount = NET_LOGIN_IGNORE_COUNT;
                }
                else
                {
                    loginIgnoreCount = LOGIN_IGNORE_COUNT;
                }

                // 登录失败更新平台状态为离线
                DATA_MANAGER.updateVideoServerInfo(serverPtr->getId(), 0);

                return false;
            }
            
            if (iLoginID == -1) {
                iLoginID = devLoginID;
                bNeedGetResource = true;
            }
            else {
            NET_DVR_Logout(devLoginID);
            }
        }

        m_iLastError = 0;
        loginIgnoreCount = 0;
        lastErrorString = "";
        iDeviceChanNum = struDeviceInfo.byChanNum;      //模拟通道数
        iStartChan = struDeviceInfo.byStartChan;        //起始通道号
        bOnline = true;

        //更新平台状态为在线 页面的状态会变化
        DATA_MANAGER.updateVideoServerInfo(serverPtr->getId(), 1);   //在线状态就是填1
        ai::LogInfo << "HK " << szIp << " LOGIN ok! " << ", iLoginID : " << iLoginID << "," << iStartChan << "," << iStartChan;

        // 只在第一次登录时获取资源
        if ( bNeedGetResource )
        {
            getResource();
        }

        return true;
    }

    void SDKHKPlatform::getResource(void)
    {
        if ( !bOnline || iLoginID < 0 )
            return;
        DWORD dwReturned = 0;
        NET_DVR_IPPARACFG_V40 ipAccessCfg;
        memset(&ipAccessCfg, 0, sizeof(ipAccessCfg));
        BOOL bIPRet = NET_DVR_GetDVRConfig(iLoginID, NET_DVR_GET_IPPARACFG_V40, 0, &ipAccessCfg, sizeof(NET_DVR_IPPARACFG_V40), &dwReturned);
        int i;
        HKPlay struChanInfo[MAX_CHANNUM_V30];
        //不支持ip接入,9000以下设备不支持禁用模拟通道
        if (!bIPRet)  
        {
            for (i = 0; i < MAX_ANALOG_CHANNUM; i++)//32
            {
                if (i < iDeviceChanNum)
                {
                    auto szChanName = std::string("camera") + std::to_string(i + iStartChan);
                    insertVecRes(iLoginID, szChanName, i + iStartChan);
                }
            }
        }
        else        //支持IP接入,9000设备
        {
            //模拟通道
            for (i = 0; i < MAX_ANALOG_CHANNUM; i++) 
            {
                if (i < iDeviceChanNum)
                {
                    auto szChanName = std::string("camera") + std::to_string(i + iStartChan);
                    insertVecRes(iLoginID, szChanName, i + iStartChan);
                }
            }
            //数字通道
            for (i = 0; i < MAX_IP_CHANNEL; i++)//32
            {
                if (ipAccessCfg.struStreamMode[i].uGetStream.struChanInfo.byEnable) //ip通道在线
                {
                    auto szChanName = std::string("IP Camera ") + std::to_string(i + 1);
                    insertVecRes(iLoginID, szChanName, i + ipAccessCfg.dwStartDChan);
                }
            }
        }

        bNeedGetResource = false;
    }

    /* 根据url获取通道ID addr必须为: ip:通道号
    * @param[in]  addr 为传过来的url       addr = ***********:33
    * @param[out] iRet 为分离出的通道号    iRet = 33
    * @return     解析执行是否成功
    */
    bool SDKHKPlatform::getChanelId(const std::string& addr, int& iRet)
    {
        try
        {
            //分离addr得到通道号 例如 ***********:33 分离 iRet = 33
            iRet = std::stoi(addr.substr(addr.find_last_of(':') + 1));
            return true;
        }
        catch (...)
        {
            //传入异常的字符串
            ai::LogError << "addrUrl: " << addr << " is error url!";
            return false;
        }
    }

    //内部使用 添加维护设备表(登录ID,名称,通道ID)
    void SDKHKPlatform::insertVecRes(int iId, std::string szName, int iChanelId)
    {
        //下面维护管理 VecRes
        std::string szAddr = serverPtr->getIp() + ":" + std::to_string(iChanelId);
        std::string szChanName = szName;
        if (szChanName == "")
            szChanName = szAddr;
        //ai::LogTrace << "index " << i << " szAddr " << szAddr ;

        //根据id在vecRes中查询是否存在 不存在就同步新增
        {
            std::lock_guard<std::mutex> lock(playMtx);
            auto findk = find_if(vecRes.begin(), vecRes.end(), [&](HKPlay& task)
                {
                    return task.iChanIndex == iChanelId;
                });
            if (findk == vecRes.end())   //没有找到可以添加
            {
                vecRes.emplace_back(HKPlay(iId, szChanName, iChanelId));
            }
        }

        //wn_access_front_end的id做一次存在查询 检查serverPtr->getId()记录存在
        if (DATA_MANAGER.queryAccessFrontDetectable(serverPtr->getId()))
        {
            //维护更新数据库
            DATA_MANAGER.insertVideoSource(serverPtr->getId(), szChanName, szAddr);
        }
    }

    std::string SDKHKPlatform::getLastError(void) 
    {
        ai::LogInfo<<" SDKHKPlatform::getLastError "<<m_iLastError;
        return lastErrorString;
    }

    bool SDKHKPlatform::login(void)
    {
        return doLogin();
    }

    bool SDKHKPlatform::focusRect(VideoSourceInfoPtr videoSourceInfo, int xTop, int yTop, int xBottom, int yBottom)
    {
        NET_DVR_POINT_FRAME frame{ xTop, yTop, xBottom, yBottom };
        NET_DVR_PTZSelZoomIn_EX(iLoginID, 1, &frame);
        return true;
    }

    // 云台保存预置位
    bool SDKHKPlatform::savePreset(int presetId)
    {
        return NET_DVR_PTZPreset_Other(iLoginID,1, SET_PRESET, presetId);
    }

    static const std::map<int, DWORD> actionMapping = {
    {1, PAN_LEFT},
    {2, PAN_RIGHT},
    {3, TILT_UP},
    {4, TILT_DOWN},
    {5, ZOOM_IN},
    {6, ZOOM_OUT},
    {7, FOCUS_NEAR},
    {8, FOCUS_FAR},
    };
    bool SDKHKPlatform::controlPtz(int action, int step)
    {
        // https://open.hikvision.com/hardware/definitions/NET_DVR_PTZControl_Other.html?_blank
        if (action == 0)
        {
            if (lastPTZcmd > 0)
            {
                int cmd = lastPTZcmd;
                lastPTZcmd = 0;
                return NET_DVR_PTZControlWithSpeed_Other(iLoginID, 1, cmd, 1, step);
            }
        }
        else
        {
            if (lastPTZcmd > 0)
            {
                NET_DVR_PTZControlWithSpeed_Other(iLoginID, 1, lastPTZcmd, 1, step);
                lastPTZcmd = 0;
            }
            if (actionMapping.find(action) != actionMapping.end())
            {
                lastPTZcmd = actionMapping.at(action);
                return NET_DVR_PTZControlWithSpeed_Other(iLoginID, 1, lastPTZcmd, 0, step);
            }
        }

        return false;
    }
}

#endif