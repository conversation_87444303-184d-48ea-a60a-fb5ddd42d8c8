/**
 * Project GUM
 */

#include "worker_pool.h"
#include <boost/asio.hpp>
#include "util/fiber_pool.hpp"

/**
 * @brief: 任务池管理
 *          主要定义各类模块任务线程
 */
namespace gum::worker{

    std::shared_ptr<boost::asio::thread_pool> threadPool = nullptr;
    std::map<WorkerType, std::shared_ptr<fvm::fiberpool::WorkerPool>> workerPools;

    /*
     * 初始化 任务池
     */
    void init()
    {
        auto poolThreadCount = std::max(std::thread::hardware_concurrency(), 20u) - 2u;
        if(!threadPool)
            threadPool = std::make_shared<boost::asio::thread_pool>(poolThreadCount);

        workerPools.clear();
        workerPools[WorkerType::Message] = std::make_shared<fvm::fiberpool::WorkerPool>(2u, 32u);
    }

    /*
     * 上报新任务
     * @param type 任务类型
     * @param work 具体任务
     */
    FiberFuture post(WorkerType type, std::function<void(void)> work)
    {
        if(workerPools.find(type) != workerPools.end())
            return workerPools[type]->submit(work);
        else
            return std::nullopt;
    }

    /*
     * 上报新任务 (额外线程池)
     * @param work 具体任务
     */
    void post(std::function<void(void)> work)
    {
        boost::asio::post(*threadPool, work);
    }

    /*
     * 关闭任务池队列
     */
    void close()
    {
        for(auto& [key, pool] : workerPools)
        {
            pool->close_queue();
        }
        if(threadPool)
            threadPool->join();
    }
}