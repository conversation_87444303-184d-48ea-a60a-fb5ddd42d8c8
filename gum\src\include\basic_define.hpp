#ifndef BASICDEFINE_HPP_
#define BASICDEFINE_HPP_

#include <string>
#include <vector>
#include <list>
#include <stdio.h>
#include <boost/shared_array.hpp>
#include <boost/function.hpp>
#include <boost/date_time/posix_time/posix_time.hpp>

#ifdef _WIN32
#include <Windows.h>
#endif
//功能为休眠 iTm 个毫秒
__attribute__((unused)) static void sleepMillion( int32_t iTm )
{
    if (iTm < 0)
        return;
#ifdef _WIN32
    ::Sleep( iTm );  //此处单位为毫秒
#elif defined( __GNUC__ )
    usleep( iTm * 1000 );  //此处单位为微秒
#endif
}

__attribute__((unused)) static boost::posix_time::ptime getCurTime()
{
    return boost::posix_time::microsec_clock::local_time();
}

__attribute__((unused)) static std::string getTimeString( boost::posix_time::ptime tmSrc = boost::posix_time::ptime(), bool bHasMicro = true )
{
    boost::posix_time::ptime pt;

    if ( tmSrc != boost::posix_time::ptime() )
    {
        if ( tmSrc.is_not_a_date_time() )
            return " time is invalid";
        pt = tmSrc;
    }
    else
    {
        pt = getCurTime();
    }
    //"20170712T143536.784363"
    //"2019-08-27T11:44:05.104660"
    char chBuf[100];
    const boost::posix_time::time_duration td = pt.time_of_day();
    int h = td.hours();
    int m = td.minutes();
    int s = td.seconds();
    int ms = (int)td.total_microseconds() - ((h * 3600 + m * 60 + s) * 1000000);
    const boost::gregorian::date nowDay = pt.date();
    int y = nowDay.year();
    int mm = nowDay.month();
    int d = nowDay.day();
    if ( bHasMicro )
    {
#if defined (WIN32 )
        sprintf_s( chBuf, "%04d-%02d-%02d %02d:%02d:%02d.%06d", y, mm, d, h, m, s, ms );
#else
        sprintf( chBuf, "%04d-%02d-%02d %02d:%02d:%02d.%06d", y, mm, d, h, m, s, ms );
#endif
    }
    else
    {
#if defined (WIN32 )
        sprintf_s( chBuf, "%04d-%02d-%02d %02d:%02d:%02d", y, mm, d, h, m, s );
#else
        sprintf( chBuf, "%04d-%02d-%02d %02d:%02d:%02d", y, mm, d, h, m, s );
#endif
    }
    return std::string( chBuf );
}

/** 回调函数类型定义 */
typedef boost::function< void( uint8_t*, uint32_t, const std::string& ) > FUNC_RECVCALLBACK;
typedef boost::function< void( const std::string& ) > FUNC_STOPCALLBACK;
typedef boost::function< bool(uint32_t, const std::string&, const std::string&) > FUNC_UPDATEVIDEONAME_CALLBACK;
typedef boost::function< bool(uint32_t, bool ) > FUNC_UPDATEREMOTESTATUS_CALLBACK;
typedef boost::function< bool(uint32_t, const std::string&, const std::string&, uint32_t& ) > FUNC_INSERTVIDEO_CALLBACK;

static const int MAX_CHANNEL = 990;  //方便后续加1000的rtmp端口
static const int RTMP_CHANNEL_INDEX = 1000;

#endif // BASICDEFINE_HPP_

