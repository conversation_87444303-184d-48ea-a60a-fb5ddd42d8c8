/**
 * Project FVM
 */

#include "ffmpeg_element.h"
#include "ailog.h"

/**
 * @brief: 视频流 ffmpeg上下文封装
 *
 */
namespace fvm::stream
{
    constexpr std::chrono::seconds OPEN_STREAM_TIMEOUT_INTERVAL = std::chrono::seconds(10 );

    /**
     * 创建流数据的相关context,设置中断回调函数
     */
    bool FFMPEGElement::createContext()
    {
        auto* ctx = &formatCtx;
        *ctx = avformat_alloc_context();
        resetContextTimer();
        if (*ctx == nullptr)
        {
            ai::LogInfo << "Failed to alloc context";
            return false;
        }
        //formatCtx->flags |= AVIO_FLAG_NONBLOCK;
        formatCtx->interrupt_callback.callback = static_cast<int (*)(void *)>([](void *obj)->int {
            if (obj == nullptr)
            {
                ai::LogError << "checkTimeout: ctx is nullptr!";
                return 1;
            }
            auto *ffmpegElement = reinterpret_cast<FFMPEGElement*>(obj);

            auto timePassed = std::chrono::steady_clock::now() - ffmpegElement->startTime;
            if ((std::chrono::steady_clock::now() - ffmpegElement->startTime >= OPEN_STREAM_TIMEOUT_INTERVAL)
                || !ffmpegElement->jobIsRunning() )
            {
                ai::LogWarn << ffmpegElement->getAddress() << " open stream timeout or job is not running!";
                return 1;
            }
            return 0;
        });
        formatCtx->interrupt_callback.opaque = this;
        return true;
    }

    void FFMPEGElement::disposeContext()
    {
        if (options != nullptr)
        {
            av_dict_free(&options);
        }
        if (&formatCtx != nullptr)
        {
            avformat_close_input(&formatCtx);
        }
        if (formatCtx != nullptr)
        {
            avformat_free_context(formatCtx);
        }
    }

    void FFMPEGElement::freeAVPacket(AVPacket* pkt)
    {
        av_packet_unref(pkt);
        av_packet_free(&pkt);
    }

}