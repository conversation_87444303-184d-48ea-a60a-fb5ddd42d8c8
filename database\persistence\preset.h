/**
 * @addtogroup odbDatabaseGroup
 * @brief 预置位
 * @{
 */
#ifndef _PRESET_H
#define _PRESET_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
/**
 * @brief: 预置位 对应数据库aimonitorV3的表wn_preset
 */
#pragma db object table("wn_preset")
class Preset {
public:

    Preset(unsigned long videoId, unsigned long actPreset,int detectType,
           const std::string& checkArea, unsigned long paramPlanId,const std::string& param,
           bool isDel, bool isEnable, const std::string& pos)
        : videoId(videoId), actPreset(actPreset),detectType(detectType),
          checkArea(checkArea), paramPlanId(paramPlanId),param(param),
        isDel(isDel), isEnable(isEnable),position(pos)
    {
    }

    unsigned long getId() const {
        return id;
    }

    unsigned long getVideoId()const {
        return videoId;
    }

    void setVideoId(unsigned long id) {
        this->videoId = id;
    }

    unsigned long getActPreset()const {
        return actPreset;
    }

    void setActPreset(unsigned long presetId) {
        this->actPreset = presetId;
    }

   int getDetectType() const {
        return detectType;
    }

    void setDetectType(int type) {
        this->detectType = type;
    }

    const std::string& getCheckArea() const {
        return checkArea;
    }

    void setCheckArea(const std::string& area) {
        this->checkArea = area;
    }

    unsigned long getParamPlanId() const {
        return paramPlanId;
    }

    void setParamPlanId(unsigned long id) {
        this->paramPlanId = id;
    }

    const std::string& getParam() const {
        return param;
    }

    void setParam(const std::string& param) {
        this->param = param;
    }

    bool getIsDel() const {
        return isDel;
    }

    void setIsDel(bool del) {
        this->isDel = del;
    }

    bool getIsEnable() const {
        return isEnable;
    }

    void setIsEnable(bool enable) {
        this->isEnable = enable;
    }

    const std::string& getPosition() const {
        return position;
    }

    void setPosition(const std::string& pos) {
        this->position = pos;
    }

private:

    friend class odb::access;
    Preset() {}

private:

#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("video_id")
    unsigned long videoId;              //!< 预置位id 对应表wn_video_source(VideoSource) id

#pragma db column("act_preset")
   unsigned long actPreset;

#pragma db column("detect_type")
   int detectType;

#pragma db column("check_area") type("VARCHAR(255)")
    std::string checkArea;              //!< 检测区

#pragma db column("param_plan_id")
    unsigned long paramPlanId;          //!< 灵敏度度预案

#pragma db column("param") type("VARCHAR(255)")
    std::string param;                  //!< 检测参数

#pragma db column("is_del") type("INT")
    bool isDel;                         //!< 是否删除

#pragma db column("is_enable") type("INT")
    bool isEnable;                      //!< 是否使能

#pragma db column("position") type("VARCHAR(255)")
    std::string position;               //!< 预置位的基准坐标

    };
}
#endif //_PRESET_H
/**
 * @}
 */
