
#pragma once

#include <optional>
#include <string>
#include <mutex>
#include <list>
#include "boost/serialization/singleton.hpp"
#include "boost/asio/steady_timer.hpp"
#include "boost/asio/system_timer.hpp"
#include "boost/asio/high_resolution_timer.hpp"
#include "boost/asio.hpp"
#include "iostream"


namespace timer
{
    using TimePoint    = std::chrono::system_clock::time_point ;   //!< time_point结构的时间点
    using Ptime        = boost::posix_time::ptime;                 //!< ptime结构的时间点

    using Duration     = std::chrono::system_clock::duration;      //!< 时间间隔



    /**
     * @brief 定时器上下文参数
     */
    struct TimerContext{

        explicit TimerContext(boost::asio::io_service &service) : systemTimer(service){}

        int32_t    repeatTimes = 0;                                                //!< 定时器重复次数
        TimePoint  when;                                                           //!< 定时器的时间点
        boost::asio::system_timer  systemTimer;                                    //!< 定时器时钟
        std::function<void(const boost::system::error_code &e)> funcWrapper;       //!< 定时器回调函数封装
    };

    using TimerPtr   = std::shared_ptr<TimerContext>;

    using TimerFunc    = std::function<void(TimerPtr)> ;                   //!< 定时回调函数

    /**
     * @brief 定时器管理接口
     */
    class TimerManager
    {

    public:
        TimerManager() {
            UTCInterval = boost::posix_time::second_clock::local_time() - boost::posix_time::second_clock::universal_time();
        };
        ~TimerManager(){stop();};

        /**
         * @brief                   添加时间点为TimePoint的定时器
         * @param[in] when:         指定的定时时间点，到该时间后调用func
         * @param[in] func:         用户注册的定时器回调函数
         * @param[in] duration:     当指定repeatTimes时，duration就是when距离下一次的时间点间隔
         * @param[in] repeatTimes： 定时器重复次数 -1:循环执行. 0: 只执行一次. >0:重复n次
         * @return                  返回定时器上下文参数指针，通过该指针可以取消当前定时器
         */
        TimerPtr addTimer(const TimePoint& when, const TimerFunc& func, const Duration& duration = std::chrono::seconds(0), int32_t repeatTimes = 0);

        /**
         * @brief                   添加时间点为Ptime的定时器
         * @param[in] when:         指定的定时时间点，到该时间后调用func
         * @param[in] func:         用户注册的定时器回调函数
         * @param[in] duration:     当指定repeatTimes时，duration就是when距离下一次的时间点间隔
         * @param[in] repeatTimes： 定时器重复次数 -1:循环执行. 0: 只执行一次. >0:重复n次
         * @return                  返回定时器上下文参数指针，通过该指针可以取消当前定时器
         */
        TimerPtr addTimer(const Ptime& when, const TimerFunc& func, const Duration& duration = std::chrono::seconds(0), int32_t repeatTimes = 0);

        /**
         * @brief                   添加时间间隔为duration的定时器
         * @param[in] duration:     到达指定时间间隔后调用func
         * @param[in] func:         用户注册的定时器回调函数
         * @param[in] repeatTimes： 定时器重复次数 -1:循环执行. 0: 只执行一次. >0:重复n次
         * @return                  返回定时器上下文参数指针，通过该指针可以取消当前定时器
         */
        TimerPtr addTimer(const Duration& duration, const TimerFunc& func, int32_t repeatTimes = 0);

        /**
         * @brief                   删除取消指定定时器任务
         * @param[in]timer:         要取消的定时器任务指针(该指针是调用addTimer时返回的)
         */
        void removeTimer(TimerPtr& timer);

        /**
         * @brief                   启动定时器，执行io_service run
         */
        void start();

        /**
         * @brief                   停止定时器，执行io_service stop
         */
        void stop();


    private:
        std::mutex               timersLock;      //!< 定时器锁
        std::list<TimerPtr>      timers;          //!< 用户添加的定时器
        std::thread              threadTimer;     //!< io service线程
        boost::asio::io_service  service;         //!< io service
        std::atomic<bool>        running = false; //!< 线程运行标志
        boost::posix_time::time_duration UTCInterval = boost::posix_time::seconds(0); //!< 系统本地时间转为time_t时区时间的时间间隔

    };
    typedef boost::serialization::singleton<timer::TimerManager> SingletonTimerManager;
    #define TIMER_MANAGER SingletonTimerManager::get_mutable_instance()
}

