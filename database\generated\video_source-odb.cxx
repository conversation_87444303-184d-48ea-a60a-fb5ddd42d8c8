// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#include <odb/pre.hxx>

#include "video_source-odb.hxx"

#include <cassert>
#include <cstring>  // std::memcpy


#include <odb/mysql/traits.hxx>
#include <odb/mysql/database.hxx>
#include <odb/mysql/transaction.hxx>
#include <odb/mysql/connection.hxx>
#include <odb/mysql/statement.hxx>
#include <odb/mysql/statement-cache.hxx>
#include <odb/mysql/simple-object-statements.hxx>
#include <odb/mysql/container-statements.hxx>
#include <odb/mysql/exceptions.hxx>
#include <odb/mysql/simple-object-result.hxx>
#include <odb/mysql/enum.hxx>

namespace odb
{
  // VideoSource
  //

  struct access::object_traits_impl< ::db::VideoSource, id_mysql >::extra_statement_cache_type
  {
    extra_statement_cache_type (
      mysql::connection&,
      image_type&,
      id_image_type&,
      mysql::binding&,
      mysql::binding&)
    {
    }
  };

  access::object_traits_impl< ::db::VideoSource, id_mysql >::id_type
  access::object_traits_impl< ::db::VideoSource, id_mysql >::
  id (const id_image_type& i)
  {
    mysql::database* db (0);
    ODB_POTENTIALLY_UNUSED (db);

    id_type id;
    {
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        id,
        i.id_value,
        i.id_null);
    }

    return id;
  }

  access::object_traits_impl< ::db::VideoSource, id_mysql >::id_type
  access::object_traits_impl< ::db::VideoSource, id_mysql >::
  id (const image_type& i)
  {
    mysql::database* db (0);
    ODB_POTENTIALLY_UNUSED (db);

    id_type id;
    {
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        id,
        i.id_value,
        i.id_null);
    }

    return id;
  }

  bool access::object_traits_impl< ::db::VideoSource, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // id
    //
    t[0UL] = 0;

    // name
    //
    if (t[1UL])
    {
      i.name_value.capacity (i.name_size);
      grew = true;
    }

    // address
    //
    if (t[2UL])
    {
      i.address_value.capacity (i.address_size);
      grew = true;
    }

    // videoServerId
    //
    t[3UL] = 0;

    // location
    //
    if (t[4UL])
    {
      i.location_value.capacity (i.location_size);
      grew = true;
    }

    // hasPtz
    //
    t[5UL] = 0;

    // isDetectable
    //
    t[6UL] = 0;

    // detectPointId
    //
    t[7UL] = 0;

    // ptzControl
    //
    if (t[8UL])
    {
      i.ptzControl_value.capacity (i.ptzControl_size);
      grew = true;
    }

    // videoStatus
    //
    t[9UL] = 0;

    // roadId
    //
    t[10UL] = 0;

    // isEnable
    //
    t[11UL] = 0;

    // isChange
    //
    t[12UL] = 0;

    // videoType
    //
    t[13UL] = 0;

    // domePreset
    //
    t[14UL] = 0;

    // groupId
    //
    t[15UL] = 0;

    // groupUUID
    //
    if (t[16UL])
    {
        i.groupUUID_value.capacity(i.groupUUID_size);
        grew = true;
    }
    return grew;
  }

  void access::object_traits_impl< ::db::VideoSource, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i,
        mysql::statement_kind sk)
  {
    ODB_POTENTIALLY_UNUSED (sk);

    using namespace mysql;

    std::size_t n (0);

    // id
    //
    if (sk != statement_update)
    {
      b[n].buffer_type = MYSQL_TYPE_LONGLONG;
      b[n].is_unsigned = 1;
      b[n].buffer = &i.id_value;
      b[n].is_null = &i.id_null;
      n++;
    }

    // name
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.name_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.name_value.capacity ());
    b[n].length = &i.name_size;
    b[n].is_null = &i.name_null;
    n++;

    // address
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.address_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.address_value.capacity ());
    b[n].length = &i.address_size;
    b[n].is_null = &i.address_null;
    n++;

    // videoServerId
    //
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.videoServerId_value;
    b[n].is_null = &i.videoServerId_null;
    n++;

    // location
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.location_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.location_value.capacity ());
    b[n].length = &i.location_size;
    b[n].is_null = &i.location_null;
    n++;

    // hasPtz
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.hasPtz_value;
    b[n].is_null = &i.hasPtz_null;
    n++;

    // isDetectable
    //
    if (sk != statement_update)
    {
      b[n].buffer_type = MYSQL_TYPE_LONG;
      b[n].is_unsigned = 0;
      b[n].buffer = &i.isDetectable_value;
      b[n].is_null = &i.isDetectable_null;
      n++;
    }

    // detectPointId
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.detectPointId_value;
    b[n].is_null = &i.detectPointId_null;
    n++;

    // ptzControl
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.ptzControl_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.ptzControl_value.capacity ());
    b[n].length = &i.ptzControl_size;
    b[n].is_null = &i.ptzControl_null;
    n++;

    // videoStatus
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.videoStatus_value;
    b[n].is_null = &i.videoStatus_null;
    n++;

    // roadId
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.roadId_value;
    b[n].is_null = &i.roadId_null;
    n++;

    // isEnable
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.isEnable_value;
    b[n].is_null = &i.isEnable_null;
    n++;

    // isChange
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.isChange_value;
    b[n].is_null = &i.isChange_null;
    n++;

    // videoType
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.videoType_value;
    b[n].is_null = &i.videoType_null;
    n++;

    // domePreset
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.domePreset_value;
    b[n].is_null = &i.domePreset_null;
    n++;

    // groupId
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.groupId_value;
    b[n].is_null = &i.groupId_null;
    n++;

    // groupUUID
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.groupUUID_value.data();
    b[n].buffer_length = static_cast<unsigned long> (
        i.groupUUID_value.capacity());
    b[n].length = &i.groupUUID_size;
    b[n].is_null = &i.groupUUID_null;
    n++;
  }

  void access::object_traits_impl< ::db::VideoSource, id_mysql >::
  bind (MYSQL_BIND* b, id_image_type& i)
  {
    std::size_t n (0);
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.id_value;
    b[n].is_null = &i.id_null;
  }

  bool access::object_traits_impl< ::db::VideoSource, id_mysql >::
  init (image_type& i,
        const object_type& o,
        mysql::statement_kind sk)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (sk);

    using namespace mysql;

    bool grew (false);

    // id
    //
    if (sk == statement_insert)
    {
      long unsigned int const& v =
        o.id;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.id_value, is_null, v);
      i.id_null = is_null;
    }

    // name
    //
    {
      ::std::string const& v =
        o.name;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.name_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.name_value,
        size,
        is_null,
        v);
      i.name_null = is_null;
      i.name_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.name_value.capacity ());
    }

    // address
    //
    {
      ::std::string const& v =
        o.address;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.address_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.address_value,
        size,
        is_null,
        v);
      i.address_null = is_null;
      i.address_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.address_value.capacity ());
    }

    // videoServerId
    //
    {
      long unsigned int const& v =
        o.videoServerId;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.videoServerId_value, is_null, v);
      i.videoServerId_null = is_null;
    }

    // location
    //
    {
      ::std::string const& v =
        o.location;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.location_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.location_value,
        size,
        is_null,
        v);
      i.location_null = is_null;
      i.location_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.location_value.capacity ());
    }

    // hasPtz
    //
    {
      bool const& v =
        o.hasPtz;

      bool is_null (false);
      mysql::value_traits<
          bool,
          mysql::id_long >::set_image (
        i.hasPtz_value, is_null, v);
      i.hasPtz_null = is_null;
    }

    // isDetectable
    //
    if (sk == statement_insert)
    {
      int const& v =
        o.isDetectable;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.isDetectable_value, is_null, v);
      i.isDetectable_null = is_null;
    }

    // detectPointId
    //
    {
      long unsigned int const& v =
        o.detectPointId;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_long >::set_image (
        i.detectPointId_value, is_null, v);
      i.detectPointId_null = is_null;
    }

    // ptzControl
    //
    {
      ::std::string const& v =
        o.ptzControl;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.ptzControl_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.ptzControl_value,
        size,
        is_null,
        v);
      i.ptzControl_null = is_null;
      i.ptzControl_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.ptzControl_value.capacity ());
    }

    // videoStatus
    //
    {
      int const& v =
        o.videoStatus;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.videoStatus_value, is_null, v);
      i.videoStatus_null = is_null;
    }

    // roadId
    //
    {
      long unsigned int const& v =
        o.roadId;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_long >::set_image (
        i.roadId_value, is_null, v);
      i.roadId_null = is_null;
    }

    // isEnable
    //
    {
      bool const& v =
        o.isEnable;

      bool is_null (false);
      mysql::value_traits<
          bool,
          mysql::id_long >::set_image (
        i.isEnable_value, is_null, v);
      i.isEnable_null = is_null;
    }

    // isChange
    //
    {
      bool const& v =
        o.isChange;

      bool is_null (false);
      mysql::value_traits<
          bool,
          mysql::id_long >::set_image (
        i.isChange_value, is_null, v);
      i.isChange_null = is_null;
    }

    // videoType
    //
    {
        int const& v =
            o.videoType;

        bool is_null(false);
        mysql::value_traits<
            int,
            mysql::id_long >::set_image(
                i.videoType_value, is_null, v);
        i.videoType_null = is_null;
    }

    // domePreset
    //
    {
        long unsigned int const& v =
            o.domePreset;

        bool is_null(false);
        mysql::value_traits<
            long unsigned int,
            mysql::id_long >::set_image(
                i.domePreset_value, is_null, v);
        i.domePreset_null = is_null;
    }

    // groupId
    //
    {
        long unsigned int const& v =
            o.groupId;

        bool is_null(false);
        mysql::value_traits<
            long unsigned int,
            mysql::id_long >::set_image(
                i.groupId_value, is_null, v);
        i.groupId_null = is_null;
    }

    // groupUUID
    //
    {
        ::std::string const& v =
            o.groupUUID;

        bool is_null(false);
        std::size_t size(0);
        std::size_t cap(i.groupUUID_value.capacity());
        mysql::value_traits<
            ::std::string,
            mysql::id_string >::set_image(
                i.groupUUID_value,
                size,
                is_null,
                v);
        i.groupUUID_null = is_null;
        i.groupUUID_size = static_cast<unsigned long> (size);
        grew = grew || (cap != i.groupUUID_value.capacity());
    }
    return grew;
  }

  void access::object_traits_impl< ::db::VideoSource, id_mysql >::
  init (object_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    // id
    //
    {
      long unsigned int& v =
        o.id;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.id_value,
        i.id_null);
    }

    // name
    //
    {
      ::std::string& v =
        o.name;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.name_value,
        i.name_size,
        i.name_null);
    }

    // address
    //
    {
      ::std::string& v =
        o.address;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.address_value,
        i.address_size,
        i.address_null);
    }

    // videoServerId
    //
    {
      long unsigned int& v =
        o.videoServerId;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.videoServerId_value,
        i.videoServerId_null);
    }

    // location
    //
    {
      ::std::string& v =
        o.location;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.location_value,
        i.location_size,
        i.location_null);
    }

    // hasPtz
    //
    {
      bool& v =
        o.hasPtz;

      mysql::value_traits<
          bool,
          mysql::id_long >::set_value (
        v,
        i.hasPtz_value,
        i.hasPtz_null);
    }

    // isDetectable
    //
    {
      int& v =
        o.isDetectable;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.isDetectable_value,
        i.isDetectable_null);
    }

    // detectPointId
    //
    {
      long unsigned int& v =
        o.detectPointId;

      mysql::value_traits<
          long unsigned int,
          mysql::id_long >::set_value (
        v,
        i.detectPointId_value,
        i.detectPointId_null);
    }

    // ptzControl
    //
    {
      ::std::string& v =
        o.ptzControl;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.ptzControl_value,
        i.ptzControl_size,
        i.ptzControl_null);
    }

    // videoStatus
    //
    {
      int& v =
        o.videoStatus;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.videoStatus_value,
        i.videoStatus_null);
    }

    // roadId
    //
    {
      long unsigned int& v =
        o.roadId;

      mysql::value_traits<
          long unsigned int,
          mysql::id_long >::set_value (
        v,
        i.roadId_value,
        i.roadId_null);
    }

    // isEnable
    //
    {
      bool& v =
        o.isEnable;

      mysql::value_traits<
          bool,
          mysql::id_long >::set_value (
        v,
        i.isEnable_value,
        i.isEnable_null);
    }

    // isChange
    //
    {
      bool& v =
        o.isChange;

      mysql::value_traits<
          bool,
          mysql::id_long >::set_value (
        v,
        i.isChange_value,
        i.isChange_null);
    }

    // videoType
    //
    {
        int& v =
            o.videoType;

        mysql::value_traits<
            int,
            mysql::id_long >::set_value(
                v,
                i.videoType_value,
                i.videoType_null);
    }

    // domePreset
    //
    {
        long unsigned int& v =
            o.domePreset;

        mysql::value_traits<
            long unsigned int,
            mysql::id_long >::set_value(
                v,
                i.domePreset_value,
                i.domePreset_null);
    }

    // groupId
    //
    {
        long unsigned int& v =
            o.groupId;

        mysql::value_traits<
            long unsigned int,
            mysql::id_long >::set_value(
                v,
                i.groupId_value,
                i.groupId_null);
    }

    // groupUUID
    //
    {
        ::std::string& v =
            o.groupUUID;

        mysql::value_traits<
            ::std::string,
            mysql::id_string >::set_value(
                v,
                i.groupUUID_value,
                i.groupUUID_size,
                i.groupUUID_null);
    }
  }

  void access::object_traits_impl< ::db::VideoSource, id_mysql >::
  init (id_image_type& i, const id_type& id)
  {
    {
      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.id_value, is_null, id);
      i.id_null = is_null;
    }
  }

  const char access::object_traits_impl< ::db::VideoSource, id_mysql >::persist_statement[] =
  "INSERT INTO `wn_video_source` "
  "(`id`, "
  "`name`, "
  "`address`, "
  "`access_front_end_id`, "
  "`location`, "
  "`has_ptz`, "
  "`is_detectable`, "
  "`detect_point_id`, "
  "`ptz_control`, "
  "`video_status`, "
  "`road_id`, "
  "`is_enable`, "
  "`is_change`, "
  "`video_type`, "
  "`dome_preset`, "
  "`group_id`, "
  "`group_uuid`) "
  "VALUES "
  "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

  const char access::object_traits_impl< ::db::VideoSource, id_mysql >::find_statement[] =
  "SELECT "
  "`wn_video_source`.`id`, "
  "`wn_video_source`.`name`, "
  "`wn_video_source`.`address`, "
  "`wn_video_source`.`access_front_end_id`, "
  "`wn_video_source`.`location`, "
  "`wn_video_source`.`has_ptz`, "
  "`wn_video_source`.`is_detectable`, "
  "`wn_video_source`.`detect_point_id`, "
  "`wn_video_source`.`ptz_control`, "
  "`wn_video_source`.`video_status`, "
  "`wn_video_source`.`road_id`, "
  "`wn_video_source`.`is_enable`, "
  "`wn_video_source`.`is_change`, "
  "`wn_video_source`.`video_type`, "
  "`wn_video_source`.`dome_preset`, "
  "`wn_video_source`.`group_id`, "
  "`wn_video_source`.`group_uuid` "
  "FROM `wn_video_source` "
  "WHERE `wn_video_source`.`id`=?";

  const char access::object_traits_impl< ::db::VideoSource, id_mysql >::update_statement[] =
  "UPDATE `wn_video_source` "
  "SET "
  "`name`=?, "
  "`address`=?, "
  "`access_front_end_id`=?, "
  "`location`=?, "
  "`has_ptz`=?, "
  "`detect_point_id`=?, "
  "`ptz_control`=?, "
  "`video_status`=?, "
  "`road_id`=?, "
  "`is_enable`=?, "
  "`is_change`=?, "
  "`video_type`=?, "
  "`dome_preset`=?, "
  "`group_id`=?, "
  "`group_uuid`=? "
  "WHERE `id`=?";

  const char access::object_traits_impl< ::db::VideoSource, id_mysql >::erase_statement[] =
  "DELETE FROM `wn_video_source` "
  "WHERE `id`=?";

  const char access::object_traits_impl< ::db::VideoSource, id_mysql >::query_statement[] =
  "SELECT "
  "`wn_video_source`.`id`, "
  "`wn_video_source`.`name`, "
  "`wn_video_source`.`address`, "
  "`wn_video_source`.`access_front_end_id`, "
  "`wn_video_source`.`location`, "
  "`wn_video_source`.`has_ptz`, "
  "`wn_video_source`.`is_detectable`, "
  "`wn_video_source`.`detect_point_id`, "
  "`wn_video_source`.`ptz_control`, "
  "`wn_video_source`.`video_status`, "
  "`wn_video_source`.`road_id`, "
  "`wn_video_source`.`is_enable`, "
  "`wn_video_source`.`is_change`, "
  "`wn_video_source`.`video_type`, "
  "`wn_video_source`.`dome_preset`, "
  "`wn_video_source`.`group_id`, "
  "`wn_video_source`.`group_uuid` "
  "FROM `wn_video_source`";

  const char access::object_traits_impl< ::db::VideoSource, id_mysql >::erase_query_statement[] =
  "DELETE FROM `wn_video_source`";

  const char access::object_traits_impl< ::db::VideoSource, id_mysql >::table_name[] =
  "`wn_video_source`";

  void access::object_traits_impl< ::db::VideoSource, id_mysql >::
  persist (database& db, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    callback (db,
              static_cast<const object_type&> (obj),
              callback_event::pre_persist);

    image_type& im (sts.image ());
    binding& imb (sts.insert_image_binding ());

    if (init (im, obj, statement_insert))
      im.version++;

    im.id_value = 0;

    if (im.version != sts.insert_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_insert);
      sts.insert_image_version (im.version);
      imb.version++;
    }

    {
      id_image_type& i (sts.id_image ());
      binding& b (sts.id_image_binding ());
      if (i.version != sts.id_image_version () || b.version == 0)
      {
        bind (b.bind, i);
        sts.id_image_version (i.version);
        b.version++;
      }
    }

    insert_statement& st (sts.persist_statement ());
    if (!st.execute ())
      throw object_already_persistent ();

    obj.id = id (sts.id_image ());

    callback (db,
              static_cast<const object_type&> (obj),
              callback_event::post_persist);
  }

  void access::object_traits_impl< ::db::VideoSource, id_mysql >::
  update (database& db, const object_type& obj)
  {
    ODB_POTENTIALLY_UNUSED (db);

    using namespace mysql;
    using mysql::update_statement;

    callback (db, obj, callback_event::pre_update);

    mysql::transaction& tr (mysql::transaction::current ());
    mysql::connection& conn (tr.connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    id_image_type& idi (sts.id_image ());
    init (idi, id (obj));

    image_type& im (sts.image ());
    if (init (im, obj, statement_update))
      im.version++;

    bool u (false);
    binding& imb (sts.update_image_binding ());
    if (im.version != sts.update_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_update);
      sts.update_image_version (im.version);
      imb.version++;
      u = true;
    }

    binding& idb (sts.id_image_binding ());
    if (idi.version != sts.update_id_image_version () ||
        idb.version == 0)
    {
      if (idi.version != sts.id_image_version () ||
          idb.version == 0)
      {
        bind (idb.bind, idi);
        sts.id_image_version (idi.version);
        idb.version++;
      }

      sts.update_id_image_version (idi.version);

      if (!u)
        imb.version++;
    }

    update_statement& st (sts.update_statement ());
    if (st.execute () == 0)
      throw object_not_persistent ();

    callback (db, obj, callback_event::post_update);
    pointer_cache_traits::update (db, obj);
  }

  void access::object_traits_impl< ::db::VideoSource, id_mysql >::
  erase (database& db, const id_type& id)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    id_image_type& i (sts.id_image ());
    init (i, id);

    binding& idb (sts.id_image_binding ());
    if (i.version != sts.id_image_version () || idb.version == 0)
    {
      bind (idb.bind, i);
      sts.id_image_version (i.version);
      idb.version++;
    }

    if (sts.erase_statement ().execute () != 1)
      throw object_not_persistent ();

    pointer_cache_traits::erase (db, id);
  }

  access::object_traits_impl< ::db::VideoSource, id_mysql >::pointer_type
  access::object_traits_impl< ::db::VideoSource, id_mysql >::
  find (database& db, const id_type& id)
  {
    using namespace mysql;

    {
      pointer_type p (pointer_cache_traits::find (db, id));

      if (!pointer_traits::null_ptr (p))
        return p;
    }

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);

    if (l.locked ())
    {
      if (!find_ (sts, &id))
        return pointer_type ();
    }

    pointer_type p (
      access::object_factory<object_type, pointer_type>::create ());
    pointer_traits::guard pg (p);

    pointer_cache_traits::insert_guard ig (
      pointer_cache_traits::insert (db, id, p));

    object_type& obj (pointer_traits::get_ref (p));

    if (l.locked ())
    {
      select_statement& st (sts.find_statement ());
      ODB_POTENTIALLY_UNUSED (st);

      callback (db, obj, callback_event::pre_load);
      init (obj, sts.image (), &db);
      load_ (sts, obj, false);
      sts.load_delayed (0);
      l.unlock ();
      callback (db, obj, callback_event::post_load);
      pointer_cache_traits::load (ig.position ());
    }
    else
      sts.delay_load (id, obj, ig.position ());

    ig.release ();
    pg.release ();
    return p;
  }

  bool access::object_traits_impl< ::db::VideoSource, id_mysql >::
  find (database& db, const id_type& id, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);
    assert (l.locked ()) /* Must be a top-level call. */;

    if (!find_ (sts, &id))
      return false;

    select_statement& st (sts.find_statement ());
    ODB_POTENTIALLY_UNUSED (st);

    reference_cache_traits::position_type pos (
      reference_cache_traits::insert (db, id, obj));
    reference_cache_traits::insert_guard ig (pos);

    callback (db, obj, callback_event::pre_load);
    init (obj, sts.image (), &db);
    load_ (sts, obj, false);
    sts.load_delayed (0);
    l.unlock ();
    callback (db, obj, callback_event::post_load);
    reference_cache_traits::load (pos);
    ig.release ();
    return true;
  }

  bool access::object_traits_impl< ::db::VideoSource, id_mysql >::
  reload (database& db, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);
    assert (l.locked ()) /* Must be a top-level call. */;

    const id_type& id (object_traits_impl::id (obj));
    if (!find_ (sts, &id))
      return false;

    select_statement& st (sts.find_statement ());
    ODB_POTENTIALLY_UNUSED (st);

    callback (db, obj, callback_event::pre_load);
    init (obj, sts.image (), &db);
    load_ (sts, obj, true);
    sts.load_delayed (0);
    l.unlock ();
    callback (db, obj, callback_event::post_load);
    return true;
  }

  bool access::object_traits_impl< ::db::VideoSource, id_mysql >::
  find_ (statements_type& sts,
         const id_type* id)
  {
    using namespace mysql;

    id_image_type& i (sts.id_image ());
    init (i, *id);

    binding& idb (sts.id_image_binding ());
    if (i.version != sts.id_image_version () || idb.version == 0)
    {
      bind (idb.bind, i);
      sts.id_image_version (i.version);
      idb.version++;
    }

    image_type& im (sts.image ());
    binding& imb (sts.select_image_binding ());

    if (im.version != sts.select_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_select);
      sts.select_image_version (im.version);
      imb.version++;
    }

    select_statement& st (sts.find_statement ());

    st.execute ();
    auto_result ar (st);
    select_statement::result r (st.fetch ());

    if (r == select_statement::truncated)
    {
      if (grow (im, sts.select_image_truncated ()))
        im.version++;

      if (im.version != sts.select_image_version ())
      {
        bind (imb.bind, im, statement_select);
        sts.select_image_version (im.version);
        imb.version++;
        st.refetch ();
      }
    }

    return r != select_statement::no_data;
  }

  result< access::object_traits_impl< ::db::VideoSource, id_mysql >::object_type >
  access::object_traits_impl< ::db::VideoSource, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));

    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.select_image_binding ());

    if (im.version != sts.select_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_select);
      sts.select_image_version (im.version);
      imb.version++;
    }

    std::string text (query_statement);
    if (!q.empty ())
    {
      text += " ";
      text += q.clause ();
    }

    q.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        text,
        false,
        true,
        q.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::object_result_impl<object_type> > r (
      new (shared) mysql::object_result_impl<object_type> (
        q, st, sts, 0));

    return result<object_type> (r);
  }

  unsigned long long access::object_traits_impl< ::db::VideoSource, id_mysql >::
  erase_query (database& db, const query_base_type& q)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));

    std::string text (erase_query_statement);
    if (!q.empty ())
    {
      text += ' ';
      text += q.clause ();
    }

    q.init_parameters ();
    delete_statement st (
      conn,
      text,
      q.parameters_binding ());

    return st.execute ();
  }
}

#include <odb/post.hxx>
