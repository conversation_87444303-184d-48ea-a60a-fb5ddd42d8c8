#ifndef INVITESESSION_HPP_
#define INVITESESSION_HPP_

#include <pjlib.h>
#include <pjsip.h>

#include "../include/UsgSipStackItf.hpp"
#include "SessionLogger.hpp"

namespace usg {

    class CInviteSession : public IInviteSession
    {
    public:
//    CInviteSession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from );
        CInviteSession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from,const std::string& xmlType );
        virtual ~CInviteSession();

    public:
        CInviteSession *setHandler( ICtxHandler *handler );

    public:
        virtual bool invite( std::string &sid, const std::string &sipUri, const std::string &result );
        virtual bool answer( pjsip_rx_data *rdata, int status, const std::string &result );
        virtual bool cancel( const std::string &sid );

//	virtual bool ack( const std::string &cid, const std::string &sipUri );
        virtual bool bye( const std::string &sid );
        virtual bool bye( const std::string&sid, int cseq );

        virtual bool getReadSipInfo( const std::string& sid,
                                     pjsip_uri *&target,
                                     pjsip_from_hdr *&from,
                                     pjsip_to_hdr *&to,
                                     pjsip_cid_hdr *&call_id );
        virtual bool getReadSipInfo( const std::string& sid,
                                     pjsip_uri *&target,
                                     pjsip_from_hdr *&from,
                                     pjsip_to_hdr *&to,
                                     pjsip_cid_hdr *&call_id,
                                     pjsip_contact_hdr *&contact );

        //gb28181项目定制
        virtual bool invite( std::string &sid, const std::string &sipUri, const std::string &subject, const std::string &result );
        virtual bool invite( std::string &sid, const std::string &cid, const std::string &sipUri, const std::string &subject, const std::string &result );
        virtual bool setSipContactUri( const std::string sipContactUri );

    public:
        bool onInvite( pjsip_rx_data *rdata );
        bool onAnswer( pjsip_rx_data *rdata );
        bool onCancel( pjsip_rx_data *rdata );

        bool onAck( pjsip_rx_data *rdata );
        bool onBye( pjsip_rx_data *rdata );

    private:
        std::string createSid( pjsip_cid_hdr *cid, pjsip_cseq_hdr *cseq );
        bool ackImp( pjsip_rx_data *rdata );

    private:
        pj_pool_t      *m_pool;
        pjsip_endpoint *m_endPoint;

    private:
        ICtxHandler *m_handler;
        const std::string m_from;

    private:
        CSessionLogger m_logger;
        const std::string m_xmlType; //DDCP OR IVS_XML

        std::string m_sipContactUri;
    };

}

#endif // INVITESESSION_HPP_
