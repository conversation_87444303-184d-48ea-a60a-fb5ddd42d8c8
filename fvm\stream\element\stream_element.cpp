
#include "stream_element.h"
#include "ailog.h"
#ifdef __cplusplus
extern "C"
{
#endif
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/time.h>
#ifdef __cplusplus
}
#endif

/**
 *  StreamElement implementation
 * @brief: 视频流元件定义
 *          主要定义视频流通用状态机，统一控制
 *          派生视频输入、输出元件
 */
namespace fvm::stream
{
    /**
     * （状态）初始化
     */
    void StreamElement::inited()
    {
        if (this->status == StreamElementStatus::Null)
        {
            this->status = StreamElementStatus::Ready;
            this->onStreamStatusChanged(StreamElementStatus::Ready);
        }
    }

    /**
     * （状态）开始
     */
    void StreamElement::played()
    {
        if (this->status == StreamElementStatus::Ready || this->status == StreamElementStatus::Paused)
        {
            this->status = StreamElementStatus::Playing;
            this->onStreamStatusChanged(StreamElementStatus::Playing);
        }
        else
        {
            std::cout << "StreamElement could not change to playing! current state: " << getStatusString() << std::endl;
        }
    }

    /**
     * （状态）暂停
     */
    void StreamElement::paused()
    {
        if (status == StreamElementStatus::Playing)
        {
            status = StreamElementStatus::Paused;
            this->onStreamStatusChanged(StreamElementStatus::Paused);
        }
        else
        {
            std::cout << "StreamElement could not change to paused! current state: "  << getStatusString() << std::endl;
        }
    }

    /**
     * 开始收流
     */
    void StreamElement::startPlay()
    {
        if (!this->playable() || this->isDisposed())
        {
            std::cout << "StreamElement could be played ! current state: "  << getStatusString() << std::endl;
        }
        else
        {
            auto capturedSelf = weak_from_this();
            future = worker::post(workerType(), [capturedSelf]()
            {
                if (auto self = capturedSelf.lock())
                {
                    self->flagRequestExitJob = false;
                    self->played();
                    self->process();
                    self->paused();

                    if (self->markDispose)
                    {
                        self->status = StreamElementStatus::Disposed;
                    }
                }
            });
        }
    }

    /**
     * 等待流完成
     */
    void StreamElement::waitForFinished()
    {
        if(future.has_value())
            future->wait();
    }

    /**
     * 停止收流
     */
    void StreamElement::stopPlay(bool dispose)
    {
        if (this->isPlaying())
        {
            flagRequestExitJob = true;
        }
        else
        {
            flagRequestExitJob = true; 
            std::cout << "StreamElement could be stopped ! current state: "  << getStatusString() << std::endl;
        }
        if(dispose)
        {
            this->dispose();
        }
    }

    /**
     * 标记释放
     */
    void StreamElement::dispose()
    {
        this->onStreamStatusChanged.disconnect_all_slots();
        if (this->isPlaying())
        {
            this->markDispose = true;
        }
        else
        {
            this->status = StreamElementStatus::Disposed;
        }
    }

    /**
     * 信息打印（附带调试协程线程信息）
     */
    void StreamElement::printInfo(const std::string &info)
    {
        auto fiberID = boost::this_fiber::get_id();
        auto threadID = std::this_thread::get_id();
        ai::LogDebug << info << " at threadID=" << threadID << " fiberID=" << fiberID;
    }

    /**
     * 获取ffmpeg错误字符输出
     */
    const std::string StreamElement::getErrorString(int avError)
    {
        char buffer[128] = {0};
        av_strerror(avError, buffer, sizeof(buffer));
        return std::string(buffer);
    }

    /**
    * 设置错误信息
    * @param lastError      log内容
    * @param lastErrorCode  错误码 
    */
    void StreamElement::setLastError(std::string error, int code)
    {
        //特例处理
        if (lastErrorCode == 0)
        {
            lastErrorCode = code;
            //过滤重复打印日志
            if (lastError != error)
            {
                //第一次赋值和打印
                lastError = error;
                ai::LogError << lastError;
                errorCount = 0;
            }
            else {
                errorCount++;
            }
            return;
        } 
        //处理ffmpeg error
        if (lastError != error)
        {
            if (lastErrorCode != code)
            {
                lastErrorCode = code;
                //第一次赋值和打印
                lastError = error;
                ai::LogError << lastError;
                errorCount = 0;
            }
            else {
                errorCount++;
            }
        }
        else {
            errorCount++;
        }
    }

    /**
     * 获取元件状态字符
     */
    const std::string StreamElement::getStatusString()
    {
        std::string statusStr = "";
        switch (this->status)
        {
            case StreamElementStatus::Ready:
                statusStr = "Ready";
                break;
            case StreamElementStatus::Paused:
                statusStr = "Paused";
                break;
            case StreamElementStatus::Playing:
                statusStr = "Playing";
                break;
            case StreamElementStatus::Disposed:
                statusStr = "Disposed";
                break;
            default:
                break;
        }
        return statusStr;
    }
}


