/**
 * Project FVM
 */
#include "platform_puller.h"
#include "platform/platform_manager.h"

/**
 * PlatformPuller implementation
 * @brief: 平台接流输入
 */

namespace fvm::stream
{
    using namespace platform;

    PlatformPuller::PlatformPuller(FrontPlatformPtr platform)
    {
        this->frontPlatform = platform;
    }

    void PlatformPuller::initSource(const VideoSourceInfoPtr source)
    {
        StreamInput::initSource(source);
        int videoId = source->videoSourcePtr->getId();
        this->sourceAddr = source->videoSourcePtr->getAddress();
        registerMsgPuller(videoId, [&](const std::string& srcAddr) {
            std::unique_lock<boost::fibers::mutex> lock(mutexRequest);
            this->sourceAddr = srcAddr;
            this->condRequest.notify_all();
            //ai::LogInfo << "request video " << msg.iVideoId << " ret: " << msg.iResult << " dest: " << msg.destAddr << " src: " << msg.srcAddr;
            });
    }

    void PlatformPuller::process()
    {

    }

    bool PlatformPuller::remoteRequest() {
        if (frontPlatform == nullptr) return false;
        sourceAddr = "";
        
        frontPlatform->startPlay(videoSourceInfo, destAddr );

        {
            std::unique_lock<boost::fibers::mutex> lock(mutexRequest);
            for (int i = 0; i < 400; i++)
            {
                condRequest.wait_for(lock, std::chrono::milliseconds(100));
                if (sourceAddr != "" || !jobIsRunning())
                    break;
            }
        }

        if ( sourceAddr == "" ) {
            return false;
        }
        return true;
    }

    void PlatformPuller::remoteStop() {
        //通知条件变量立即返回
        {
            std::unique_lock<boost::fibers::mutex> lock(mutexRequest);
            condRequest.notify_all();
        }

        if ( frontPlatform )
            frontPlatform->stopPlay( videoSourceInfo, destAddr );
        destAddr = "";
        sourceAddr = "";
    }

    void PlatformPuller::dispose() {
        StreamElement::dispose();
    }

    bool PlatformPuller::callCameraPreset(int presetId) {
        return frontPlatform->callPreset( videoSourceInfo, presetId );
    }

    bool PlatformPuller::isPtzCapable() {
        return videoSourceInfo->videoSourcePtr->getHasPtz(); //这里只判断video是否可控云台
    }

    bool PlatformPuller::controlPtz(int action, int step)
    {
        return frontPlatform->controlPtz(videoSourceInfo, convertPtzCmd(action), step);
    }
}