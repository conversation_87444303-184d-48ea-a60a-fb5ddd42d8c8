#ifndef CFGCONFIGFILETESTCASE_HPP_
#define CFGCONFIGFILETESTCASE_HPP_

#include <cppunit/extensions/HelperMacros.h>

namespace usg
{

class CCfgConfigFileTestCase : public CPPUNIT_NS::TestFixture
{
    CPPUNIT_TEST_SUITE( CCfgConfigFileTestCase );

    CPPUNIT_TEST( test_readFile );
    CPPUNIT_TEST( test_writeFile );
    CPPUNIT_TEST( test_split );


    CPPUNIT_TEST_SUITE_END();

public:
    CCfgConfigFileTestCase();
    ~CCfgConfigFileTestCase();

public:
    virtual void setUp();
    virtual void tearDown();

public:
    void test_readFile();
    void test_writeFile();
    void test_split();
};

}

#endif