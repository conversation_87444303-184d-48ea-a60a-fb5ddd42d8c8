/**
 * Project FVM
 */
#include "gb28181_puller.h"
#include "ailog.h"
#include "boost/asio/time_traits.hpp"  //TODO
#include "platform/platform_manager.h"

/**
 * GB28181Puller implementation
 * @brief: GB28181输入
 */

namespace fvm::stream
{
    using namespace data;
    using namespace platform;
    using std::chrono::steady_clock;
    using std::chrono::duration_cast;
    using std::chrono::milliseconds;

    constexpr int RTP_VERSION = 2;        //RTP版本
    const int STREAM_QUEUE_SIZE = 1024 * 1024 * 2;
    const int CONNECTION_TIMEOUT = 15000;

    struct RtpHeader 
    {  
        //LITTLE_ENDIAN Rtp包头
        uint16_t   cc : 4;		/* CSRC count                 */
        uint16_t   x : 1;		/* header extension flag      */
        uint16_t   p : 1;		/* padding flag               */
        uint16_t   v : 2;		/* packet type                */
        uint16_t   pt : 7;		/* payload type               */
        uint16_t   m : 1;		/* marker bit                 */

        uint16_t   seq;		/* sequence number            */
        uint32_t    ts;		/* timestamp                  */
        uint32_t    ssrc;		/* synchronization source     */
    };

    struct RtpHeaderEx
    {
        //LITTLE_ENDIAN， Rtp附属信息
        uint16_t    dbp;		/* defined by profile         */
        uint16_t    len;		/* length                     */
    };

    template<typename T>
    static T getIntData(uint8_t* buf)
    {
        T value = 0;
        int sz = sizeof(T);
        for (int i = 0; i < sz; i++)
        {
            value <<= 8;
            value |= buf[i];
        }
        return value;
    }

    GB28181Puller::GB28181Puller(FrontPlatformPtr platform) :PlatformPuller(platform)
    {
    }

    void GB28181Puller::initOptions()
    {
        TransferType eType = frontPlatform->getTransferType();
        {
            av_dict_set(&options, "fflags", "nobuffer", 0);
            av_dict_set(&options, "max_analyze_duration", "10", 0);
            av_dict_set(&options, "max_delay", "500000", 0);
            av_dict_set(&options, "stimeout", "3000000", 0);  //设置超时断开连接时间 3s
            av_dict_set(&options, "probesize", "2048000", 0);
        }
    }

    /**
     * 重载StreamInput的open，需要读取缓冲区，打开输入流，获取输入流信息
     */
    bool GB28181Puller::open()
    {
        if (isOpened)
            return true;
        TransferType eType = frontPlatform->getTransferType();
        //如果是用rtmp请求，直接返回rtmp的请求机制
        if (eType == TransferType::RTMP)
        {
            destAddr = DATA_MANAGER.getLocalIP() + ":0";
            //向远端请求失败
            if (!remoteRequest())
            {
                ai::LogError << videoSourceInfo->videoSourcePtr->getAddress() << " remoteRequest failed";
                return false;
            }
            address = sourceAddr;
            return StreamInput::open();
        }
        //28181网络连接
        else if (eType == TransferType::UDP)
        {
            localPort = getAvailPort(true);
            if (localPort == 0)
            {
                ai::LogError << "localPort =0 failed ";
                return false;
            }
            streamSock.reset(new UdpServer());
        }
        else if(eType == TransferType::TCPPASSIVE)
        {
            localPort = getAvailPort(false);
            if (localPort == 0)
            {
                ai::LogError << "localPort =0 failed ";
                return false;
            }
            streamSock.reset(new TcpServer());
        }
        else {
            //tcp主动待定...
        }
        //处理环队列
        int videoId = videoSourceInfo->videoSourcePtr->getId();
        if (!streamQueue.init(videoId, STREAM_QUEUE_SIZE))
        {
            ai::LogError << address << " init stream queue is failed";
            return false;
        }
        printInfo(str(boost::format("Start 28181 %d %s") % videoId % videoSourceInfo->videoSourcePtr->getAddress()));
        isOpened = false;

        //socket流回调函数
        auto streamCallback = [&](uint8_t* buf, int buf_size)
        {
            //处理rtp头
            uint8_t recvData[BUF_SIZE];
            int outLen;
            uint8_t* ptr = dealRtpHeader(buf, buf_size, outLen);
            if (ptr)
            {
                memcpy(recvData, ptr, outLen);
                dealData(recvData, outLen);
            }
        };

        bool bRet = streamSock->start(streamCallback, localPort);
        if (!bRet)
        {
            ai::LogError << "socket open " << sourceAddr << " failed ";
            streamSock.reset();
            return false;
        }

        destAddr = DATA_MANAGER.getLocalIP() + ":" + std::to_string(localPort);
        //向远端请求失败
        if (!remoteRequest())
        {
            ai::LogError << videoSourceInfo->videoSourcePtr->getAddress() << " remoteRequest failed";
            return false;
        }

        //等待连接上远端
        resetContextTimer();
        while (jobIsRunning())
        {
            boost::this_fiber::sleep_for(std::chrono::milliseconds(50));
            if (streamSock->isConnect())
                break;
            int passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
            if (passed > CONNECTION_TIMEOUT) {
                ai::LogError << address << " connect " << sourceAddr << " is failed in " << passed << "ms";
                return false;
            }
        }

        int ret;
        int tryCount = 1;
        uint8_t* ioContextBuffer = nullptr;
        AVIOContext* pb = nullptr;
        videoIndex = -1;

        resetContextTimer();
        while (jobIsRunning())
        {
            boost::this_fiber::sleep_for(std::chrono::milliseconds(50));
            int passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
            if (passed > CONNECTION_TIMEOUT)
            {
                ai::LogError << address << " get stream size is failed in " << passed << "ms";
                return false;
            }

            if (formatCtx != nullptr)
            {
                avformat_close_input(&formatCtx);
                if (pb)
                {
                    if (pb->buffer)
                        av_freep(&pb->buffer);
                    avio_context_free(&pb);
                    pb = nullptr;
                }
            }
            if (!FFMPEGElement::createContext())
            {
                ai::LogError << address << "avformat_alloc_context failed";
                return false;
            }
            ioContextBuffer = (uint8_t*)av_malloc( BUF_SIZE );
            if (!ioContextBuffer) 
            {
                avformat_close_input(&formatCtx);
                ai::LogError << address << " av_malloc ctx buffer failed";
                return false;
            }
            //avio回调绑定处理
            pb = avio_alloc_context(ioContextBuffer, BUF_SIZE, 0, this, [](void* pUser, uint8_t* buf, int buf_size)->int
            {
                GB28181Puller* pGBPuller = (GB28181Puller*)pUser;
                if (!pGBPuller)
                    return -1;
                auto startTime = steady_clock::now();
                do
                {
                    int readLen = pGBPuller->streamQueue.popdata(buf, buf_size);
                    if (readLen > 0)
                        return readLen;
                    else if (readLen < 0)
                        return -1;
                    else
                    {
                        int passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
                        if (passed > 5000)
                        {
                            std::cerr << pGBPuller->sourceAddr << " Read No Data for " << passed << "ms" << std::endl;
                            return -1;
                        }
                    }
                } while (pGBPuller->jobIsRunning());
                return -1;
            }, nullptr, nullptr);
            if (!pb)  //分配空间失败
            {
                if (ioContextBuffer)
                    av_freep(&ioContextBuffer);
                avformat_close_input(&formatCtx);
                ai::LogError << address << "avio_alloc_context failed";
                return false;
            }
            formatCtx->pb = pb;
            formatCtx->max_analyze_duration = 7 * AV_TIME_BASE;
            formatCtx->flags |= AVFMT_FLAG_DISCARD_CORRUPT;
            if ((ret = avformat_open_input(&formatCtx, streamUrl(), nullptr, nullptr)) < 0)
            {
                setLastError(str(boost::format("avformat_open_input %s FAILED: %s") % streamUrl() % getErrorString(ret)), ret);
                continue;
            }
            
            if ((ret = avformat_find_stream_info(formatCtx, nullptr)) < 0)//nbstreams,streams赋值
            {
                setLastError(str(boost::format("avformat_find_stream_info %s FAILED: %s") % streamUrl() % getErrorString(ret)), ret);
                continue;
            }
#ifdef FFMPEG_LOW
            AVCodec* pi_code = nullptr;
#else
            const AVCodec* pi_code = nullptr;
#endif
            auto index_stream = av_find_best_stream(formatCtx, AVMEDIA_TYPE_VIDEO, -1, -1, &(pi_code), 0);
            if (index_stream < 0)
            {
                std::cout << streamUrl() << " av_find_best_stream is err, trynum: " << tryCount << std::endl;
                tryCount++;
                if (tryCount > 10)      //10次重试失败 close网络重连
                    return false;
                continue;
            }
            AVStream* in_stream = formatCtx->streams[index_stream];
            videoIndex = index_stream;
            auto timebase = in_stream->time_base;
            auto codecpar = in_stream->codecpar;
            //保证在output有size 解决rtmp error的问题
            if (codecpar->width <= 0 || codecpar->height <= 0)
            {
                ai::LogDebug << streamUrl() << " codecpar: no size info";
                tryCount++;
                if (tryCount > 10)      //10次重试失败 close网络重连
                    return false;
                continue;
            }
            auto frameRate = av_guess_frame_rate(formatCtx, in_stream, nullptr);
            if (frameRate.den == 0 || frameRate.num == 0) 
            {
                frameRate.den = 1;
                frameRate.num = 25;
            }
            duration = (int64_t)((double)AV_TIME_BASE / av_q2d(frameRate)) / 1000;
            if (duration == 0)
                duration = 40;
            auto codecInfo = std::make_shared<CodecInfo>(timebase, frameRate, codecpar);
            this->onStreamCodecInfoRetrieved(codecInfo);
            av_dump_format(formatCtx, 0, formatCtx->url, 0);
            break;
        }
        if (videoIndex < 0)
            return false;
        isOpened = true;
        return true;
    }

    /**
     * 关闭输入流
     */
    void GB28181Puller::close()
    {
        if ( sourceAddr != "" )
            remoteStop();
        streamSock.reset();
        TransferType eType = frontPlatform->getTransferType();
        AVIOContext* pb = nullptr;
        if (formatCtx && eType != TransferType::RTMP)
            pb = formatCtx->pb;
        if (pb)
        {
            if (pb->buffer)
                av_freep(&pb->buffer);
            avio_context_free(&pb);
        }
        streamQueue.reset(true);
        StreamInput::close();
        streamQueue.fini();
        frameIndex = 0;
    }

    /**
    * 线程函数
    */
    void GB28181Puller::process()
    {
        auto videoId = videoSourceInfo->videoSourcePtr->getId();
        address = "28181 V " + std::to_string(videoId);
        TransferType eType = frontPlatform->getTransferType();

        // 初始化参数
        int ret;
        std::chrono::steady_clock::time_point firstTime;
        frameIndex = 0;
        
        while (jobIsRunning())
        {
            auto openStartTime = steady_clock::now();
            if (!isOpened) // 尝试连接
            {
                DO_IN_THREAD(this->open();)
                if (isOpened)
                {
                    frameIndex = 0;
                    firstTime = steady_clock::now();
                    int passed = duration_cast<milliseconds>(steady_clock::now() - openStartTime).count();
                    if ( passed > 1000 )
                        ai::LogDebug << address << " openContext " << passed << "ms" ;
                }
            }

            if (!isOpened) // 连接失败 等待后尝试
            {
                close();
                int passed = duration_cast<milliseconds>(steady_clock::now() - openStartTime).count() / 1000;
                if (passed < 10)
                    WAIT_FOR_SECONDS(10 - passed)
                continue;
            }

            // 连接成功 开始收包
            AVPacket* pkt = av_packet_alloc();
            resetContextTimer();
            DO_IN_THREAD(ret = av_read_frame(formatCtx, pkt);)
            int passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
            if(passed > 2000)
                ai::LogDebug << address<< " av_read_frame "<<  passed << "ms ";

            // 收包错误处理
            if (ret < 0)
            {
                av_packet_unref(pkt);
                av_packet_free(&pkt);
                if ( !jobIsRunning() )
                    break;

                setLastError(str(boost::format("av_read_frame %s FAILED: %s") % address % getErrorString(ret)), ret);
                if (ret == AVERROR_EOF || !formatCtx->pb || avio_feof(formatCtx->pb) || formatCtx->pb->error)
                {
                    close();
                    onStreamInputLost(); //通知web流状态
                    WAIT_FOR_SECONDS(10); //等10秒再重新请求
                }
                else
                    boost::this_fiber::sleep_for(std::chrono::milliseconds(10));
                continue;
            }
            if (pkt->stream_index != videoIndex)
            {
                av_packet_unref(pkt);
                av_packet_free(&pkt);
                continue;
            }
            auto packet = std::make_shared<PacketData>(pkt);
            this->onStreamDataReceived(packet);

            frameIndex++;
            if (eType == TransferType::RTMP)
            {
                int actTime = duration_cast<milliseconds>(steady_clock::now() - firstTime).count();
                int needTime = frameIndex * duration;
                int waitTime = max(needTime - actTime - 1, 0);
                if (actTime < needTime)
                {
                    if (waitTime > 0 && waitTime < duration)
                        boost::this_fiber::sleep_for(std::chrono::milliseconds(waitTime));
                    else
                        boost::this_fiber::yield();
                }
            }
            else
                boost::this_fiber::yield();
        }
        close();
        printInfo(str(boost::format("EXIT %s") % address));
    }

    /**
    * 处理实时数据
    */
    void GB28181Puller::dealData(uint8_t* buf, int buf_size)
    {
        int32_t len = buf_size;         //写入size
        uint8_t* dbuf = buf;            //环
        int32_t total_read = 0;
        int32_t ret = 0;
        int emptyCount = 0;
        do
        {
            //插入环形队列
            ret = streamQueue.pushdata(dbuf, buf_size);
            if (ret == 0)               //没有数据
            {
                emptyCount++;
                if (emptyCount >= 10)
                    return;
            }
            else if (ret < 0)           //跳过
                return;
            else
                emptyCount = 0;
            total_read += ret;
            dbuf += ret;
        } while (total_read < buf_size && jobIsRunning());
    }

    /**
    * 处理rtp包头
    */
    uint8_t* GB28181Puller::dealRtpHeader(uint8_t* buf, int len, int& outLen)
    {
        int szHeader = sizeof(RtpHeader);
        if (len < szHeader)
            return nullptr;
        uint8_t* ptr = buf;
        //处理rtp头
        RtpHeader header;
        memset(&header, 0, sizeof(RtpHeader));
        memcpy(&header, ptr, 2);
        header.seq = getIntData<uint16_t>(ptr + 2 );
        header.ts = getIntData<uint32_t>(ptr + 4 );
        header.ssrc = getIntData<uint32_t>(ptr + 8);

        uint16_t offset = szHeader + header.cc * 4;

        //处理rtp 附加信息
        if (header.x)
        {
            RtpHeaderEx ex;
            ex.dbp = getIntData<uint16_t>(ptr + offset);
            offset += 2;
            ex.len = getIntData<uint16_t>(ptr + offset);
            offset += 2;
            offset += ex.len * 4;
        }
        if (len <= offset)
        {
            return nullptr;
        }
        ptr += offset;
        outLen = len - offset;

        // 丢弃后面的填充字符
        if (header.p)
        {
            uint16_t paddingSize = ptr[outLen - 1];
            if (outLen <= paddingSize)
            {
                return nullptr;
            }
            outLen -= paddingSize;
        }

        // Check the RTP version number (it should be 2):
        if (header.v != RTP_VERSION || header.pt == 0 )
            return nullptr;
            
        // 检查H.264 NAL单元类型，过滤掉未定义的类型0
        if (outLen > 1 && header.pt == 96) // 96通常是H.264的动态payload type
        {
            // RTP H.264负载的第一个字节包含NAL单元类型信息
            uint8_t nal_header = ptr[0];
            uint8_t nal_type = nal_header & 0x1F;
            
            if (nal_type == 0) // 未定义的NAL类型
            {
                return nullptr; // 丢弃这个包
            }
        }
        
        return ptr;
    }

}