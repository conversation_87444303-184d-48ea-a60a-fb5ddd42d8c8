﻿/**
 * Project FVM
 */

#pragma once
#include <boost/serialization/singleton.hpp>
#include <boost/signals2.hpp>
#include "protocol/all.h"
#include "protocol/fvm.h"
#include "network_type.h"

/**
 * @brief: 协议管理器
 */
namespace fvm
{
    using namespace network;
    namespace protocol
    {
        #define PROTOCOL_SIGNAL(T) boost::signals2::signal<void(T)>

        class ProtocolManager :public boost::noncopyable {

        public:
            /*
             * 初始化（启动服务端及相关客户端）
             */
            bool init();

            /*
             * 发送消息至IVA
             * @param msgType 消息类型
             * @param data 消息内容
             * @param processID 进程ID 默认为进程1
             */
            template<typename T>
            bool sendToIVA(ProtocolType msgType, const T& data, int processID= network::DEFAULT_CLIENT_ID);

            /*
             * 发送消息至WEB
             * @param msgType 消息类型
             * @param data 消息内容
             * @param isPlatform 是否平台 默认本地
             */
            template<typename T>
            bool sendToWEB(ProtocolType msgType, const T& data, bool isPlatform= false);

            /*
             * 发送消息至GUM
             * @param msgType 消息类型
             * @param data 消息内容
             */
            template<typename T>
            bool sendToGUM(ProtocolType msgType, const T& data);

            /*
             * 发送消息至MSM
             * @param msgType 消息类型
             * @param data 消息内容
             */
            template<typename T>
            bool sendToMSM(ProtocolType msgType, const T& data);

            /*
            * 发送数据 （默认UDP）
            * @param host 目标地址
            * @param port 目标端口
            * @param msgType 消息类型
            * @param data 消息内容
            */
            template<typename T>
            bool sendTo(const std::string& host, unsigned short port, ProtocolType msgType, const T& data);

        public: //SIGNALs
            PROTOCOL_SIGNAL(RequestTempVideo) onRequestTempVideoMessage;
            PROTOCOL_SIGNAL(RequestSetVideo) onRequestSetVideoMessage;
            PROTOCOL_SIGNAL(RequestInit) onIvaRequestInitMessage;
            PROTOCOL_SIGNAL(void) onMonitorChangedMessage;
            PROTOCOL_SIGNAL(FVMChannelChanged) onChannelChangedMessage;
            PROTOCOL_SIGNAL(RequestVideoPause) onVideoPauseMessage;
            PROTOCOL_SIGNAL(RequestVideoResume) onVideoResumeMessage;
            PROTOCOL_SIGNAL(FVMChangedId) onFvmChangedMessage;
            PROTOCOL_SIGNAL(SetPosition) onSetPosition;
            PROTOCOL_SIGNAL(RequestRemoteChanged) onRemoteChangedMessage; //前端资源变化 GUM->FVM
            PROTOCOL_SIGNAL(RequestRTSPChanged) onRTSPChangedMessage;
            PROTOCOL_SIGNAL(EventOccurInfo) onEventOccurInfo;
            PROTOCOL_SIGNAL(EventRemoveInfo) onEventRemoveInfo;
            PROTOCOL_SIGNAL(RequestDayNightInfo) onRequestDayNightInfo;
            PROTOCOL_SIGNAL(RequestVideoRet) onRequestVideoRet;
            PROTOCOL_SIGNAL(RequestStopVideoRet) onRequestStopVideoRet;
            PROTOCOL_SIGNAL(RequestPtzOperRet) onRequestPtzOperRet;
            PROTOCOL_SIGNAL(PostRemoteStatus) onRemoteStatusMessage;        //前端状态变化 GUM->FVM
            PROTOCOL_SIGNAL(VideoQuaAlarmConf) onIvaVideoQuaAlarm;
            PROTOCOL_SIGNAL(VideoQuaRecovery) onIvaVideoRecovery;
            PROTOCOL_SIGNAL(PtzControl) onPtzControl;
            PROTOCOL_SIGNAL(PresetControl) onPresetControl; 
            PROTOCOL_SIGNAL(FocusEvent) onFocusEvent;

        private:
            /*
             * 消息数据处理
             * @param msgData 消息内容
             */
            void handleProtocolReceived(std::string& msgData);
        };
    }
    typedef boost::serialization::singleton<protocol::ProtocolManager> SingletonProtocolManager;
    #define PROTOCOL_MANAGER SingletonProtocolManager::get_mutable_instance()
}
#include "protocol_sender.ixx"








