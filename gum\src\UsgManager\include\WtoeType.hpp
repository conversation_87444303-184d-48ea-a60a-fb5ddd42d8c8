
#ifndef WTOETYPE_HPP_
#define WTOETYPE_HPP_

namespace usg
{

// enum EItemType
// {
//     EITEMTYPE_GROUP = 0,      ///< 组结点类型
//     EITEMTYPE_MEDIA = 1,      ///< 媒体资源结点类型
//     EITEMTYPE_ALARM = 2,      ///< 告警资源结点类型
// };

    enum EFormatType
    {
        //   EFORMATTYPE_CIF,
        //EFORMATTYPE_2CIF,
        //   EFORMATTYPE_4CIF, // D1
        //   EFORMATTYPE_720P,
        //   EFORMATTYPE_1080P,
        EFORMATTYPE_QVGA    = 0, ///< qvga  320*240
        EFORMATTYPE_CIF     = 1, ///< cif   352*288
        EFORMATTYPE_VGA     = 2, ///< vga   640*480
        EFORMATTYPE_4CIF    = 3, ///< 4cif  704*576
        EFORMATTYPE_SVGA    = 4, ///< svga  800*600
        EFORMATTYPE_XGA     = 5, ///< xga   1024*768
        EFORMATTYPE_720P    = 6, ///< 720p  1280*720
        EFORMATTYPE_UVGA    = 7, ///< uvga  1600*1200
        EFORMATTYPE_1080P   = 8, ///< 1080p 1920*1080
    };
    enum EVideoType
    {
        EVIDEOTYPE_H_264,
    };
    enum EAudioType
    {
        EAUDIOTYPE_G_711A,
        EAUDIOTYPE_G_711,
        EAUDIOTYPE_G_722,
    };
    enum ESocketType
    {
        ESOCKETTYPE_UDP,
        ESOCKETTYPE_TCP_PASSIVE,
        ESOCKETTYPE_TCP_ACTIVE,
    };


    enum EPtzCommand
    {
        EPTZCOMMAND_UP = 0,
        EPTZCOMMAND_DOWN,
        EPTZCOMMAND_LEFT,
        EPTZCOMMAND_RIGHT,
        EPTZCOMMAND_STOP,

        EPTZCOMMAND_FOCUSNEAR,
        EPTZCOMMAND_FOCUSFAR,
        EPTZCOMMAND_FOCUSSTOP,

        EPTZCOMMAND_ZOOMIN,
        EPTZCOMMAND_ZOOMOUT,

        EPTZCOMMAND_APERTUREWIDE,
        EPTZCOMMAND_APERTURETELE,

        EPTZCOMMAND_WIPERON,
        EPTZCOMMAND_WIPEROFF,

        EPTZCOMMAND_LEDON,
        EPTZCOMMAND_LEDOFF,

        //预置位
        EPTZCOMMAND_SWITCH,
        EPTZCOMMAND_ADD,
        EPTZCOMMAND_DEL,

        //巡航
        EPTZCOMMAND_CRUISEADD,
        EPTZCOMMAND_CRUISEDEL,
        EPTZCOMMAND_CRUISESPEED,
        EPTZCOMMAND_CRUISESTIME,
        EPTZCOMMAND_CRUISESSTART,

        //扫描
        EPTZCOMMAND_SCANOPT,
        EPTZCOMMAND_SCANTIME,

    };

    enum EAlarmType
    {
        EALARMTYPE_DETECT,
        EALARMTYPE_VDETECT,
        EALARMTYPE_VLOST,
        EALARMTYPE_VHIDE,
        EALARMTYPE_EVENTONVIDEO,
        EALARMTYPE_OTH,
    };

//目录推送操作类型
    const static std::string CATALOG_OPER_ADD = "ADD";
    const static std::string CATALOG_OPER_DEL = "DEL";
    const static std::string CATALOG_OPER_MOD = "MOD";

    const static std::string PTZ_STRING_UP  = "08";
    const static std::string PTZ_STRING_DOWN = "04";
    const static std::string PTZ_STRING_LEFT = "02";
    const static std::string PTZ_STRING_RIRGT= "01";
    const static std::string PTZ_STRING_STOP  = "000000"; //停止转动
    const static std::string PTZ_STRING_SWITCHPREPOSITION = "82";
    const static std::string PTZ_STRING_FOCUSNEAR = "20";
    const static std::string PTZ_STRING_FOCUSFAR= "10";
    const static std::string PTZ_STRING_ZOOMOUT  = "44";
    const static std::string PTZ_STRING_ZOOMIN = "48";
    const static std::string PTZ_STRING_APERTUREWIDE = "41";
    const static std::string PTZ_STRING_APERTURETELE= "42";
    const static std::string PTZ_STRING_WIPERON  = "8C0100";
    const static std::string PTZ_STRING_WIPEROFF = "8D0100";
    const static std::string PTZ_STRING_LEDON = "8C0200";
    const static std::string PTZ_STRING_LEDOFF= "8D0200";
    const static std::string PTZ_STRING_STOP_1  = "400000";//停止镜头动作
}

#endif
