#include <iostream>
#include<boost/date_time/posix_time/posix_time.hpp>
#include "RegistSession.hpp"
#include "SipPoolGuard.hpp"
namespace usg {

    const std::string AUTH = "Capability algorithm=\"A:RSA/ECB/PKCS1,RSA/CBC/PKCS1;H:SHA1,MD5,SHA256;S:DES/ECB/PKCS5,DES/CBC/PKCS5,3DES/ECB/PKCS5,3DES/CBC/PKCS5,SCB2\"";

    CRegistSession::CRegistSession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from )
            : m_pool( pool ), m_endPoint( endPoint ), m_handler( 0 ), m_from( from ),m_xmlType( "DDCP" ),m_iExpries( 60 )
    {
        m_auth = AUTH;
    }

    CRegistSession::CRegistSession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from, const std::string& xmlType )
            : m_pool( pool ), m_endPoint( endPoint ), m_handler( 0 ), m_from( from ),m_xmlType( xmlType ),m_iExpries( 60 )
    {
        m_auth = AUTH;
    }
    CRegistSession::~CRegistSession()
    {
    }

    CRegistSession *CRegistSession::setHandler( ICtxHandler *handler )
    {
        m_handler = handler;
        return this;
    }

    void CRegistSession::setExpries( uint32_t iExpries )
    {
        m_iExpries = iExpries;
    }


    void CRegistSession::setUserNameAndPasswd( std::string &userName, std::string &passwd )
    {
        m_userName = userName;
        m_password = passwd;
    }


// bool CRegistSession::regist( std::string &sid, const std::string &sipUri )
// {
// 	return regist_i( sid, sipUri, m_iExpries );
// }

// bool CRegistSession::unregist( std::string &sid, const std::string &sipUri )
// {
// 	setExpries(0);
// 	return regist_i( sid, sipUri, 0 );
// }

    void CRegistSession::removeAddFromToHeader(  pjsip_hdr_e type, pjsip_rx_data * rdata, pjsip_tx_data * tdata )
    {
        pjsip_from_hdr *fromto = (pjsip_from_hdr *)pjsip_msg_find_hdr( tdata->msg, type, 0 );

        pjsip_sip_uri *rxfromto;
        if ( type == PJSIP_H_FROM )
        {
            rxfromto = (pjsip_sip_uri *)rdata->msg_info.from->uri;
        }
        else
        {
            rxfromto = (pjsip_sip_uri *)rdata->msg_info.to->uri;
        }

        std::string oid( rxfromto->host.ptr, rxfromto->host.slen );

        //上级获取注册下级的设备编码+ip地址+端口号
        std::string addr(rdata->msg_info.via->sent_by.host.ptr, rdata->msg_info.via->sent_by.host.slen);
        int port = rdata->msg_info.via->sent_by.port;
        std::string strport = boost::lexical_cast<std::string>(port);
        addr += ":";
        addr += boost::lexical_cast<std::string>(port);

        //添加 type 头字段
        pjsip_sip_uri *url;
        url = pjsip_sip_uri_create( tdata->pool, 0 );
        pj_strdup2( tdata->pool, &url->user, oid.c_str() );
        pj_strdup2( tdata->pool, &url->host, addr.c_str() );
        //url->port = port;
        fromto->uri = ( pjsip_uri* )url;
    }

    bool CRegistSession::answer( pjsip_rx_data *rdata, int status )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        pjsip_tx_data *tdata = 0;
        if ( PJ_SUCCESS != pjsip_endpt_create_response( m_endPoint, rdata, status, 0, &tdata ) )
            return false;

        if (401 == status)
        {
            //添加 WWW-Authenticate 头字段
            const pj_str_t header = pj_str((char*)"WWW-Authenticate");
            const std::string NONCE = "6fe9ba44a76be22a";
            const std::string REALM = m_sipUri.substr(0, 8);
            std::string authstr = "Digest realm=\"" + REALM + "\",nonce=\"" + NONCE + "\"";
            pj_str_t authvalue = { (char *)authstr.c_str(), (pj_ssize_t)authstr.length() };

            pjsip_generic_string_hdr *auth = pjsip_generic_string_hdr_create( tdata->pool, &header, &authvalue);
            pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)auth );
        }
        if (200 == status)
        {
            //添加data头字段
            const pj_str_t header = pj_str((char*)"Date");
            const boost::posix_time::ptime time = boost::posix_time::microsec_clock::local_time();
            std::string strTime = boost::posix_time::to_iso_string(time);
            strTime.replace(4,0,std::string("-"));
            strTime.replace(7,0,std::string("-"));
            int pos = strTime.find('T');
            strTime.replace(pos + 3,0,std::string(":"));
            strTime.replace(pos + 6,0,std::string(":"));
            strTime = strTime.substr(0,strlen("YYYY-MM-DDTHH:MM:SS.sss"));

            pj_str_t datavalue = { (char *)strTime.c_str(), (pj_ssize_t)strTime.length() };
            pjsip_generic_string_hdr *date = pjsip_generic_string_hdr_create( tdata->pool, &header, &datavalue);
            pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)date );

            //添加Expires头字段
            pjsip_expires_hdr* expriesHdr = pjsip_expires_hdr_create( tdata->pool, m_iExpries );
            if ( expriesHdr == NULL )
                return false;
            pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)expriesHdr );

            //添加contact头字段
            pjsip_contact_hdr* contactHdr = pjsip_contact_hdr_create( tdata->pool );
            if ( contactHdr == NULL )
                return false;
            pjsip_contact_hdr *contact = (pjsip_contact_hdr *)pjsip_msg_find_hdr( rdata->msg_info.msg, PJSIP_H_CONTACT, 0 );
            contactHdr->uri = contact->uri;
            pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)contactHdr );
        }

        //removeAddFromToHeader( PJSIP_H_FROM, rdata, tdata );

        return PJ_SUCCESS == pjsip_endpt_send_response2( m_endPoint, rdata, tdata, 0, 0 );
    }

    bool CRegistSession::onRegist( pjsip_rx_data *rdata )
    {
        if ( !rdata->msg_info.cseq )
            return false;

        //add by hxu 加入发送端的标识，用于判断下级是否合法
        /*
        * 获取uri.
        */
        if ( !PJSIP_URI_SCHEME_IS_SIP( rdata->msg_info.msg->line.req.uri ) ) return false;

        //pjsip_sip_uri *uri = (pjsip_sip_uri *)rdata->msg_info.msg->line.req.uri;
        //std::string oid( uri->user.ptr, uri->user.slen );
        //if ( oid.empty() ) return false;
        pjsip_sip_uri *from = (pjsip_sip_uri *)rdata->msg_info.from->uri;
        if ( !from )
        {
            return false;
        }
        std::string oid( from->host.ptr, from->host.slen );
        if ( oid.empty() ) return false;

        //上级获取sip设备编码
        m_sipUri = oid;

        //上级获取注册下级的设备编码+ip地址+端口号
        std::string addr(rdata->msg_info.via->sent_by.host.ptr, rdata->msg_info.via->sent_by.host.slen);
        int port = rdata->msg_info.via->sent_by.port;
        from->port = port;
        std::string url;
        url += oid;
        url += "@";
        url += addr;
        url += ":";
        url += boost::lexical_cast<std::string>(port);

        //add by hxu 获取下级注册时的超时时间，饱和时需要
        int iexpires = 60;
        pjsip_expires_hdr *expires = (pjsip_expires_hdr *)pjsip_msg_find_hdr( rdata->msg_info.msg, PJSIP_H_EXPIRES, 0 );
        if ( expires )
        {
            if ( expires->ivalue >= 0 )
            {
                iexpires = expires->ivalue;
            }
            setExpries(iexpires);
        }

        /*
         * 获取其他参数.
         */
        std::string sid = createSid( rdata->msg_info.cid, rdata->msg_info.cseq );///< 创建会话id， cid号+cseq号
        if( m_handler )
        {
            pjsip_authorization_hdr *auth = (pjsip_authorization_hdr *)pjsip_msg_find_hdr( rdata->msg_info.msg, PJSIP_H_AUTHORIZATION, 0 );
            if( !auth )
            {
                if ( iexpires > 0 )
                    return answer( rdata, 401 );
                else
                {
                    if ( m_handler->commitRegist( sid,url, iexpires ) )
                    {
                        return answer( rdata, 200 );
                    }
                    else
                    {
                        return answer( rdata, 400 );
                    }
                }
            }

            std::string auth_str;
            auth_str.assign(auth->scheme.ptr);
            if( ( std::string::npos == auth_str.find( "Digest" ) )
                || ( std::string::npos == auth_str.find( "username" ) )
                || ( std::string::npos == auth_str.find( "realm" ) )
                || ( std::string::npos == auth_str.find( "nonce" ) )
                || ( std::string::npos == auth_str.find( "response" ) ) )
            {
                return answer( rdata, 401 );
            }

            if ( m_handler->commitRegist( sid,url, iexpires ) )
            {
                return answer( rdata, 200 );
            }
            else
            {
                return answer( rdata, 400 );
            }
        }

        return false;
    }

    bool parseLine( char* inputLine, char*& nextLine )
    {
        // Begin by finding the start of the next line (if any):
        nextLine = NULL;
        for ( char* ptr = inputLine; *ptr != '\0'; ++ptr )
        {
            if ( *ptr == '\r' || *ptr == '\n' )
            {
                // We found the end of the line
                ++ptr;
                while ( *ptr == '\r' || *ptr == '\n' )
                    ++ptr;
                nextLine = ptr;
                if ( nextLine[0] == '\0' )
                    nextLine = NULL; // special case for end
                break;
            }
        }

        // Then, check that this line is a SDP line of the form <char>=<etc>
        // (However, we also accept blank lines in the input.)
        if ( inputLine[0] == '\r' || inputLine[0] == '\n' )
            return true;

// 	if ( /*strlen(inputLine) < 2 || */inputLine[1] != '='
// 		|| inputLine[0] < 'a' || inputLine[0] > 'z' )
// 	{
// 		return false;
// 	}

        return true;
    }

    bool CRegistSession::onAnswer( pjsip_rx_data *rdata )
    {
        if ( !m_handler || !rdata->msg_info.msg || !rdata->msg_info.cseq )
            return false;
        /*
         * 获取其他参数.
         */
        std::string sid = createSid( rdata->msg_info.cid, rdata->msg_info.cseq );

        if ( rdata->msg_info.msg->line.status.code == 401 )
        {
            m_auth = cretatAuthorizationValue( rdata );

            if ( m_iExpries == 0 )
            {
                return regist_i( sid, m_sipUri, 0 );;
            }
        }

        int status = rdata->msg_info.msg->line.status.code / 100;
        switch ( status )
        {
            case 2:
            {
                char *ptr = 0;
                size_t len = 0;

                char *preq = rdata->msg_info.msg->line.req.method.name.ptr;
                char *nextreq = 0;

                while(preq && parseLine( preq, nextreq ) )
                {
                    if( 0 == ::strnicmp( preq, "Date:", 5 ) )
                    {
                        ptr = preq;
                        len = nextreq - preq;
                        break;
                    }

                    preq = nextreq;
                }

                return m_handler->commitAnswer( sid, ptr, len, true, IRegistSession::SESSION_TYPE );
            }
            case 4:
            {
                //如果注册消息返回为401，则立刻重新注册；如果为其他错误，则等待60s再重新注册
                if( rdata->msg_info.msg->line.status.code != 401 )
                {
                    //做一个特殊处理：如果接受到401错误，则将type乘以10，表示注册返回401
                    return m_handler->commitAnswer( sid, 0, 0, false, (IRegistSession::SESSION_TYPE * 10) );
                }
                else
                {
                    return m_handler->commitAnswer( sid, 0, 0, false, IRegistSession::SESSION_TYPE );
                }
            }

            default:
                break;
        }

        return true;
    }

    std::string CRegistSession::createSid( pjsip_cid_hdr *cid, pjsip_cseq_hdr *cseq )
    {
        if ( !cid || !cseq ) return std::string();

        std::string sid( cid->id.ptr, cid->id.slen );

        char buf[16] = { 0 };
        sprintf( buf, "%d", cseq->cseq );
        return sid += buf;
    }

    std::string CRegistSession::cretatAuthorizationValue( pjsip_rx_data *rdata )
    {
        pjsip_authorization_hdr *auth;
        auth = (pjsip_authorization_hdr*) pjsip_msg_find_hdr(rdata->msg_info.msg, PJSIP_H_WWW_AUTHENTICATE, NULL);

        pj_str_t response;
        response.ptr = new char[32];
        memset( response.ptr, 0, 32 );
        response.slen = 32;

        pj_str_t str_nonce = auth->credential.digest.nonce;
        pj_str_t str_nc; str_nc.slen = 0;
        pj_str_t str_cnonce; str_cnonce.slen = 0;
        pj_str_t str_qop; str_qop.slen = 0;
        pj_str_t str_uri = { (char *)m_sipUri.c_str(), (pj_ssize_t)m_sipUri.length() };
        pj_str_t str_usr = { (char *)m_userName.c_str(), (pj_ssize_t)m_userName.length() };
        pj_str_t str_pwd = { (char *)m_password.c_str(), (pj_ssize_t)m_password.length() };
        pj_str_t str_realm = auth->credential.digest.realm;
        pjsip_cred_info s_cred_info;
        s_cred_info.realm = str_realm;
        s_cred_info.scheme.ptr = (char*)"digest"; s_cred_info.scheme.slen = strlen("digest");
        s_cred_info.username = str_usr;
        s_cred_info.data_type = 0;
        s_cred_info.data =str_pwd;
        pj_str_t str_method = {(char*)"REGISTER", strlen("REGISTER")};
        pjsip_auth_create_digest( &response, &str_nonce, &str_nc, &str_cnonce, &str_qop,
                                  &str_uri, &str_realm, &s_cred_info, &str_method );


        std::string strAuthValue = "Digest username=\"" + std::string(s_cred_info.username.ptr, s_cred_info.username.slen) +
                                   "\",realm=\""		+ std::string(str_realm.ptr, str_realm.slen) +
                                   "\",nonce=\""		+ std::string(str_nonce.ptr, str_nonce.slen) +
                                   "\",uri=\""			+ m_sipUri									 +
                                   "\",response=\""		+ std::string(response.ptr, 32)	 +
                                   "\",algorithm=MD5";

        if ( response.ptr)
        {
            delete [] response.ptr;
            response.ptr = NULL;
        }
        return strAuthValue;
    }

    bool CRegistSession::regist_i( std::string &sid, const std::string &sipUri,const uint32_t iExpries )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;
        //CSipPoolGuard poolGuard;
        //pj_pool_t * pool = poolGuard.getPool();
        //if ( NULL == pool )
        //{
        //    return false;
        //}
        if ( sipUri.empty() || m_from.empty() )
        {
            return false;
        }

        m_sipUri = sipUri;

        pj_str_t uri = { (char *)sipUri.c_str(), (pj_ssize_t)sipUri.length() };
        pj_str_t from = { (char *)m_from.c_str(), (pj_ssize_t)m_from.length() };
        pjsip_tx_data *tdata = 0;

        if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, pjsip_get_register_method(), &uri, &from, &from, &from, 0, -1, 0, &tdata ) )
            return false;

        //pjsip_expires_hdr* expriesHdr = pjsip_expires_hdr_create( pool, 60 );
        pjsip_expires_hdr* expriesHdr = pjsip_expires_hdr_create( tdata->pool, iExpries );
        //pjsip_expires_hdr* expriesHdr = pjsip_expires_hdr_create( tdata->pool, 60 );
        if ( expriesHdr == NULL )
            return false;
        pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)expriesHdr );

        if ( !m_auth.empty() )
        {
            const pj_str_t hauthorization = pj_str((char*)"Authorization");

            pj_str_t hauthvalue = { (char *)m_auth.c_str(), (pj_ssize_t)m_auth.length() };

            pjsip_generic_string_hdr *auth;
            auth = pjsip_generic_string_hdr_create( tdata->pool, &hauthorization, &hauthvalue);

            pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)auth );
        }
        m_auth = AUTH;

        pj_str_t type = { (char*)"Application", 11 };
        std::string xmlType = m_xmlType;
        if ( m_xmlType == "MANSCDP" )
            xmlType += "+xml";
        pj_str_t subtype = { (char *)xmlType.c_str(), (pj_ssize_t)xmlType.length() };
        std::string tmp;
        pj_str_t text = { (char *)tmp.c_str(), (pj_ssize_t)tmp.length() };

        //tdata->msg->body = pjsip_msg_body_create( pool, &type, &subtype, &text );
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, &type, &subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;

        /*
         * 插入dialog记录.
         */
        pjsip_cseq_hdr *cseq = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CSEQ, 0 );
        pjsip_cid_hdr  *cid  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CALL_ID, 0 );

        sid = createSid( cid, cseq );

        return PJ_SUCCESS == pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 );
    }

    uint32_t CRegistSession::getExpries()
    {
        return m_iExpries;
    }

}
