#include <boost/uuid/uuid.hpp>
#include <boost/uuid/uuid_io.hpp>
#include <boost/uuid/uuid_generators.hpp>
#include <wtoe/TestSupport/TestSupportExp.hpp>

#include "UsgManager/include/UsgManagerExp.hpp"
#include "UsgManager.hpp"
#include "UsgManager/src/ResManager.hpp"
#include "include/file_info.hpp"
#include "include/debug_info.hpp"
#include "ailog.h"
#include "boost/locale/encoding.hpp"

namespace usg
{

    CResManager::CResManager( uint32_t dbId, const std::string& szSipId, const std::string& szAddr, EPackerType type )
    :m_bKeepExit( true ), m_currentChannel(0)
    , m_iDbId( dbId ), m_szSipId( szSipId ), m_szRemoteAddr( szAddr )
    , m_timeStamp( 2 ),m_packerType( type )
    , m_isRegist( false ), m_KeepaliveOk(false), m_expries(30), m_isSubscribeCatalog( false )
    ,m_inviteSession( 0 ),m_notifySession( 0 ),m_registSession( 0 ),m_ddcpDoSession( 0 )
    ,m_funcInsertVideo(0)
    ,m_funcUpdateRemoteStatus(0)
    ,m_funcUpdateVideoName(0)
    {
    }

    CResManager::~CResManager()
    {

    }

    bool CResManager::init(FUNC_INSERTVIDEO_CALLBACK func1,
                           FUNC_UPDATEREMOTESTATUS_CALLBACK func2,
                           FUNC_UPDATEVIDEONAME_CALLBACK func3)
    {
        m_funcInsertVideo = func1;
        m_funcUpdateRemoteStatus = func2;
        m_funcUpdateVideoName = func3;
        return true;
    }

    bool CResManager::getResInfoByChannel( const uint16_t channel, SResInfo& info )
    {
        //readLock rlock( m_sipResInfoLock );
        ResContainer::index<RESCHANNEL>::type& indexOfChannel = m_resContainer.get<RESCHANNEL>();
        ResContainer::index<RESCHANNEL>::type::iterator it = indexOfChannel.find( channel );
        if ( it != indexOfChannel.end() )
        {
            info = (*it);
            return true;
        }
        return false;
    }

    bool CResManager::getResInfoBySipCode( const std::string& code, SResInfo& info )
    {
        //readLock rlock( m_sipResInfoLock );
        ResContainer::index<RESSIPCODE>::type& indexOfCode = m_resContainer.get<RESSIPCODE>();
        ResContainer::index<RESSIPCODE>::type::iterator it = indexOfCode.find( code );
        if ( it != indexOfCode.end() )
        {
            info = (*it);
            return true;
        }
        return false;
    }
    bool CResManager::getResInfoByUuid( const boost::uuids::uuid& id, SResInfo& info )
    {
        //readLock rlock( m_sipResInfoLock );
        ResContainer::index<RESID>::type& indexOfId = m_resContainer.get<RESID>();
        ResContainer::index<RESID>::type::iterator it = indexOfId.find( id );
        if ( it != indexOfId.end() )
        {
            info = (*it);
            return true;
        }
        return false;
    }

    bool CResManager::onReceiveCatalog( const SCatalog& info )
    {
        writeLock wlock( m_sipResInfoLock );
        onReceiveCatalogInner( info );
        return true;
    }

    bool CResManager::onAddRes( const SResInfo& resInfo )
    {
        std::string utfName = boost::locale::conv::between(resInfo.resName, "UTF-8", "GB2312");
        ResContainer::index<RESSIPCODE>::type& indexOfCode = m_resContainer.get<RESSIPCODE>();
        ResContainer::index<RESSIPCODE>::type::iterator it = indexOfCode.find( resInfo.sipResCode );
        //已经存在，但又是新增的状态,可能是下级断线重连后重新后的，所以这里要判断是否已经上报过
        if ( it != indexOfCode.end() )
        {
            SResInfo& tmpInfo =  const_cast<SResInfo&>(*it);
            tmpInfo.hasPtz = resInfo.hasPtz;
            tmpInfo.resIP = resInfo.resIP;
            tmpInfo.resStatus = true; //resInfo.resStatus;
            tmpInfo.masterImageSize = resInfo.masterImageSize;
            tmpInfo.subImageSize = resInfo.subImageSize;
            if ( tmpInfo.resName != utfName ) {
                tmpInfo.resName = utfName;
                if (m_funcUpdateVideoName)
                    m_funcUpdateVideoName(m_iDbId, tmpInfo.sipResCode, tmpInfo.resName);
            }
        }
        else
        {
            SResInfo addResInfo = resInfo;
            boost::uuids::uuid resId = boost::uuids::random_generator()();
            addResInfo.resId = resId;
            addResInfo.channelIndex = -1;  //表示新增
            addResInfo.resStatus = true;
            if ( m_funcInsertVideo )
                m_funcInsertVideo( m_iDbId, resInfo.sipResCode, utfName, addResInfo.channelIndex );
            //判断这个channelIndex是否已经在表中，若是，更新这个container
            ResContainer::index<RESCHANNEL>::type& indexOfCode = m_resContainer.get<RESCHANNEL>();
            ResContainer::index<RESCHANNEL>::type::iterator it = indexOfCode.find( addResInfo.channelIndex );
            if ( it != indexOfCode.end() )
            {
                SResInfo& tmpInfo =  const_cast<SResInfo&>(*it);
                tmpInfo.sipResCode = resInfo.sipResCode;
                tmpInfo.resName = utfName;
            }
            else
                m_resContainer.insert( addResInfo );
        }
        return true;
    }

    bool CResManager::onDelRes( const SResInfo& resInfo )
    {
        ResContainer::index<RESSIPCODE>::type& indexOfCode = m_resContainer.get<RESSIPCODE>();
        ResContainer::index<RESSIPCODE>::type::iterator it = indexOfCode.find( resInfo.sipResCode );
        if ( it == indexOfCode.end() )
        {
            return true;
        }
        SResInfo tmpInfo = (*it);
        indexOfCode.erase( it );
        //直接调用更新操作
//	m_frontMgr->delVideoChannel( tmpInfo.channelIndex );
        return true;
    }

    bool CResManager::onModifyRes( const SResInfo& resInfo )
    {
        ResContainer::index<RESSIPCODE>::type& indexOfCode = m_resContainer.get<RESSIPCODE>();
        ResContainer::index<RESSIPCODE>::type::iterator it = indexOfCode.find( resInfo.sipResCode );
        if ( it != indexOfCode.end() )
        {
            //此处的修改，因为不涉及到索引字段的修改，所以直接去掉CONST属性使用
            SResInfo& tmpInfo =  const_cast<SResInfo&>(*it);
            tmpInfo.hasPtz = resInfo.hasPtz;
            tmpInfo.resName = resInfo.resName;
            tmpInfo.resStatus = resInfo.resStatus;
            tmpInfo.masterImageSize = resInfo.masterImageSize;
            tmpInfo.subImageSize = resInfo.subImageSize;
        }
        return true;
    }

    bool CResManager::OnSgResNotify(  const std::map<boost::uuids::uuid, SResInfo >& mapRes )
    {
//     //获取客户端注册的通知对象
//     boost::shared_ptr<IGb28181> gb28181;
//     if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, gb28181 ) )
//         return false;
//
// 	boost::shared_ptr< CUsgManager > sg = boost::dynamic_pointer_cast< CUsgManager >( gb28181 );
// 	if( sg == 0 ) return false;
//
//     ISgResNotify *notify = sg->getNotify();
//
//     if( notify == 0 )
//     {
//         return false;
//     }
//
//     uint32_t t;
//     getTimeStamp( t );
//     //发送调度结果通知
//     return notify->sgResNotify( mapRes, t );
        return true;
    }

    bool CResManager::fini()
    {
        ai::LogInfo << "CResManager::FINI " << m_szSipId << " BEGIN";
        {
            lock_type l( m_mutexReg );
            m_isRegist = false;
            m_expries = 0;
            m_keepAliveTime = getCurTime() - boost::posix_time::seconds( 100000 );
            m_keepaliveCond.notify_one();
        }
        if ( !m_bKeepExit )
        {
            m_bKeepExit = true;
            m_keepThread.join();
        }
        m_registerCond.notify_one();

        ai::LogInfo<< "CResManager::FINI " << m_szSipId <<" KEEP EXIT";
        {
            writeLock wlock( m_sipResInfoLock );
            m_resContainer.clear();
        }

// 	updateLowerStatus( false );

        return true;
    }

    bool CResManager::getAllResInfo( std::map<boost::uuids::uuid, SResInfo >& mapRes, uint32_t& timeStamp )
    {
        //readLock rlock( m_sipResInfoLock );
        ResContainer::index<RESID>::type& indexOfId = m_resContainer.get<RESID>();
        ResContainer::index<RESID>::type::iterator it = indexOfId.begin(),ite = indexOfId.end();
        for ( ; it != ite; ++it )
        {
            mapRes[ (*it).resId ] = (*it);
        }
        getTimeStamp( timeStamp );
        return true;
    }

    uint32_t CResManager::getTimeStamp( uint32_t& timeStamp )
    {
        timeStamp = m_timeStamp++;
        return timeStamp;
    }

    bool CResManager::updateLowerStatus( bool online )
    {
        if ( m_funcUpdateRemoteStatus )
            m_funcUpdateRemoteStatus( m_iDbId, online );
        return true;
    }

    bool CResManager::onLinkDown()
    {
        updateLowerStatus( false );
        std::map<boost::uuids::uuid, SResInfo > mapRes;
        {
            //writeLock wlock( m_sipResInfoLock );
            ResContainer::index<RESID>::type& indexOfId = m_resContainer.get<RESID>();
            ResContainer::index<RESID>::type::iterator it = indexOfId.begin(), ite = indexOfId.end();
            for ( ;it != ite; ++it )
            {
                //此处的修改，因为不涉及到索引字段的修改，所以直接去掉CONST属性使用
                SResInfo& tmpInfo =  const_cast<SResInfo&>(*it);
                tmpInfo.resStatus = false;
                mapRes[tmpInfo.resId] = tmpInfo;
            }
        }
        return OnSgResNotify( mapRes );
    }

    bool CResManager::onReceiveCatalogInner( const SCatalog& info )
    {
        uint16_t size = info.subItems.size();
        for ( uint16_t i = 0; i < size; ++i )
        {
		    // 过滤目录
            if (info.subItems[i].parental == "1") {
                continue;
            }
            SResInfo resinfo;
            resinfo.packerType = m_packerType;
            resinfo.sipResCode = info.subItems[i].sipResCode;
            resinfo.resName = info.subItems[i].name;
            if ( resinfo.resName.empty() )
                resinfo.resName = resinfo.sipResCode;
            //		continue;

            resinfo.resIP = info.subItems[i].ip;
            resinfo.hasPtz = info.subItems[i].hasPtz;
            resinfo.resStatus = info.subItems[i].resStatus;
            if ( info.subItems[i].isHD )
            {
                resinfo.masterImageSize = EMEDIAIMAGESIZE_1080P;
                resinfo.subImageSize = EMEDIAIMAGESIZE_4CIF;
            }
            else
                resinfo.masterImageSize = EMEDIAIMAGESIZE_4CIF;

            if ( info.subItems[i].operatorType == CATALOG_OPER_ADD )
            {
                onAddRes( resinfo );
            }
            else if ( info.subItems[i].operatorType == CATALOG_OPER_DEL )
            {
                onDelRes( resinfo );
            }
            else if ( info.subItems[i].operatorType == CATALOG_OPER_MOD )
            {
                onModifyRes( resinfo );
            }
        }
//
//     std::map<boost::uuids::uuid, SResInfo > mapRes;
// 	{
// 		ResContainer::index<RESID>::type& indexOfId = m_resContainer.get<RESID>();
// 		ResContainer::index<RESID>::type::iterator it = indexOfId.begin(),ite = indexOfId.end();
//
// 		for ( ; it != ite; ++it )
// 		{
// 			mapRes[ (*it).resId ] = (*it);
// 		}
// 	}
//
//     if ( !saveSipResDb() )
//     {
//         std::cout << " saveSipResDb fail. -----------" << std::endl;
//     }
//
//     return OnSgResNotify( mapRes );
        return true;
    }

    void CResManager::keepAliveThread()
    {
        char chBuf[255];
        sprintf(chBuf,"%ld:%d:28P:Alive", wtoe_getThreadid(), m_iDbId );
        show_thread( chBuf );
        ai::LogInfo << "Thread: " << chBuf;

        //保活
        while ( !m_bKeepExit )
        {
            for ( int i = 0; i < 10; i++ )
            {
                sleepMillion( 100);
                if ( m_bKeepExit )
                    break;
            }
            if ( !isRegist() )
                break;
            //查看保活状态
            if ( isAlive() )
                continue;
            if (!m_bKeepExit)
            {
                setRegist(false);
                setSubscribeCatalog(false);
                onLinkDown();
                break;
            }
        }
        //m_bKeepExit = true;
        ai::LogInfo << "Thread: " << chBuf << " EXIT";
    }

    bool CResManager::addVideo( const std::string& szResCode, const std::string& szName, uint32_t id )
    {
        ResContainer::index<RESSIPCODE>::type& indexOfCode = m_resContainer.get<RESSIPCODE>();
        ResContainer::index<RESSIPCODE>::type::iterator it = indexOfCode.find( szResCode );
        //已经存在，但又是新增的状态,可能是下级断线重连后重新后的，所以这里要判断是否已经上报过
        if ( it != indexOfCode.end() )
            return true;
        SResInfo info;
        info.sipResCode = szResCode;
        info.packerType = m_packerType;
        info.channelIndex = id;
        info.resName = szName;
        info.resId = boost::uuids::random_generator()();
        info.hasPtz = true;///< gb28181协议没有要求，默认为有云台//( tmp != 0 );
        info.resStatus = false; //认为不在线
        info.masterImageSize = EMEDIAIMAGESIZE_1080P;///< gb28181无法传，所以默认为1080P//(EMediaImageSize)tmp;
        info.subImageSize = EMEDIAIMAGESIZE_4CIF;///< gb28181无法传，所以默认为4cif//(EMediaImageSize)tmp;
        m_resContainer.insert( info );
        return true;
    }

    bool CResManager::getVideoStatus( const std::string& szResCode )
    {
        SResInfo info;
        if ( !getResInfoBySipCode( szResCode, info ) )
            return false;
        return info.resStatus;
    }

    void CResManager::onRegist(  int expires )
    {
        {
            lock_type l( m_mutexReg );
            bool oldRegist = m_isRegist;
            m_isRegist = ( expires != 0 );
            m_keepAliveTime = boost::posix_time::second_clock::local_time();
            m_keepaliveCond.notify_one();
            if (oldRegist == m_isRegist)
                return;
        }
        if ( expires == 0 ) //设备主动下线
        {
            m_expries = 0;
            //退出保活线程
            if (!m_bKeepExit)
            {
                m_bKeepExit = true;
                m_keepThread.join();
            }
            m_isSubscribeCatalog = true;
            onLinkDown();
        }
        else
        {
            m_expries = expires / 3;
            m_isSubscribeCatalog = false;
            //开启保活线程
            if (m_bKeepExit)
            {
                m_bKeepExit = false;
                m_keepThread = boost::thread(boost::bind(&CResManager::keepAliveThread, this));
            }

            updateLowerStatus( true );
        }
        m_registerCond.notify_all();
    }

    int CResManager::getExpires()
    {
        return m_expries;
    }

    void CResManager::keepAlive()
    {
        lock_type l( m_mutexKeepalive );
        m_keepAliveTime = boost::posix_time::second_clock::local_time();
    }

    bool CResManager::isAlive()
    {
        lock_type l( m_mutexKeepalive );
        boost::posix_time::time_duration td = boost::posix_time::second_clock::local_time() - m_keepAliveTime;
        if( td.total_seconds() > 60 * 3 )///< 保活时间检查超出要求，认为设备下线
        {
            ai::LogDebug << m_szSipId << " td.total_seconds() > 60 * 3 : lowersg lost keepalive";
            return false;
        }
//	if ( m_bKeepExit )
//		return false;
        if ( m_keepaliveCond.timed_wait( l, boost::posix_time::seconds( 30 ) ) )///< 有通知，使其不再等待
        {
            if ( m_expries == 0 ) // 设备主动下线
            {
                ai::LogInfo << m_szSipId.c_str() << " Exit Alive";
                return false;
            }
        }

        return true;
    }

    bool CResManager::isRegist()
    {
        lock_type l( m_mutexReg );
        return m_isRegist;
    }

    void CResManager::setRegist( bool bValue )
    {
        lock_type l( m_mutexReg );
        m_isRegist = false;
    }

    void CResManager::waitRegist()
    {
        lock_type l( m_mutexReg );
        m_registerCond.wait( l );
    }

    std::string CResManager::getSipId()
    {
        return m_szSipId;
    }

    std::string CResManager::getSipUrl(std::string res)
    {
        return "sip:" + (res == "" ? m_szSipId : res) + "@" + m_szRemoteAddr;
    }

    std::string CResManager::getRemoteAddr()
    {
        return m_szRemoteAddr;
    }

    bool CResManager::isSubscribeCatalog()
    {
        return m_isSubscribeCatalog;
    }

    void CResManager::setSubscribeCatalog( bool bValue )
    {
        m_isSubscribeCatalog = bValue;
    }

}