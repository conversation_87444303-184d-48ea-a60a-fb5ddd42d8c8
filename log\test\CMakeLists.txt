cmake_minimum_required(VERSION 3.5.2 )
project(test-log)
set(APP_NAME "test_log")
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR})

add_compile_options(-std=c++11 -fPIC -fstack-protector-all -Wall -DBOOST_LOG_DYN_LINK)
# boost
set(BOOST_HOME "/opt/boost")
set(BOOST_LIB
        boost_log
        boost_log_setup )
#头文件
include_directories(
        ${PROJECT_SOURCE_DIR}/../include/
        ${BOOST_HOME}/include/
)

link_directories(
        ${PROJECT_SOURCE_DIR}/../../out/lib
        ${BOOST_HOME}/lib/
)

add_executable(${APP_NAME} main.cpp )
target_link_libraries( ${APP_NAME} ai_log ${BOOST_LIB} )

add_subdirectory(../ ailog_binary_dir)
message(STATUS "lib ai_log =  ${PROJECT_SOURCE_DIR}/../out/lib/")