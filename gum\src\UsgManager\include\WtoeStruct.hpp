
#ifndef WTOESTRUCT_HPP_
#define WTOESTRUCT_HPP_

#include <map>
#include <string>
#include <vector>
#include <stdint.h>
#include <boost/uuid/uuid.hpp>
#include <ace/INET_Addr.h>

#include "WtoeType.hpp"

namespace usg
{

//---------------可以明确的功能,按WTOE的特性,定义的结构体.---------------

// 1. 注册没有信息.

// 2. 推送目录: 发请求, 收回应.
    struct SCatalog
    {
        struct SItem
        {
            std::string name;
            std::string sipResCode;
            std::string operatorType;
            std::string ip;
            bool resStatus;
            bool isHD;
            bool  hasPtz;
            std::string parental;
        };
//       std::string parentAddr;
//       std::string parentName;
        uint16_t	num;	//目录总数
        bool 		endflg;	//标记逐条推送的目录是否完毕
        std::vector< SItem > subItems;
        std::string deviceId;
    };

    struct SCatalogResponse
    {
        bool isOk;
    };

// 3. 心跳信息: 发请求, 收回应.
// struct SKeepalive
// {
//     // 无
// };
// struct SKeepaliveResponse
// {
//     bool isOk;
// };

// 4. 实时播放: 收请求, 发回应.
    struct SRealMedia
    {
        std::vector< EFormatType > supportFormatTypes;
        std::vector< EVideoType > supportVideoTypes;
        std::vector< EAudioType > supportAudioTypes;
        ESocketType socketType;
        uint32_t sockAddr;
        uint16_t sockPort;
    };
    struct SRealMediaResponse
    {
        EFormatType formatType;
        EVideoType videoType;
        EAudioType audioType;
        uint16_t bitRate;
        ESocketType socketType; // TCP, UDP
        uint32_t sockAddr;
        uint16_t sockPort; // 210.98.45.200 UDP 2360
    };

// 5. 历史文件列表查询: 收请求,发回应.
    struct SHistoryList
    {
        size_t fromIndex;
        size_t toIndex; // 查询[startTime,endTime]的时间段,并取其中的第[from, to]个.
        uint32_t startTime;
        uint32_t endTime;
    };
    struct SHistoryListResponse
    {
        struct SItem
        {
            uint32_t startTime;
            uint32_t endTime;
            std::string fileName;
        };

        bool isOk;
        size_t allItemSize; // 总共个数.注意:files中是本次传送的个数.
        size_t fromIndex;
        size_t toIndex; // 本次传送的是第[ from, to ]条信息.
        std::vector< SItem > files;
    };

// 6. 历史视频播放: 收请求,发回应.
    struct SHistoryMedia
    {
        uint32_t startTime;
        uint32_t endTime;
    };
    struct SHistoryMediaResponse
    {
        bool isOk;
        std::string playUrl; //如 rtsp://10.116.172.8:554/path
    };

// 7. 云台控制: 收请求,发回应.
    struct SPtzCommand
    {
        EPtzCommand command;
        int commandParam1; // 云台速度.
        int commandParam2; // 预置编号.
    };
    struct SPtzCommandResponse
    {
        bool isOk;
    };

    struct SRebootCommandResponse
    {
        bool isOk;
    };

    struct SubscribeCommandResponse
    {
        bool isOk;
    };

    struct SBroadcastResponse
    {
        bool isOk;
    };

// 8. 预置位信息查询: 收请求,发回应.
    struct SPresetList
    {
        size_t fromIndex;
        size_t toIndex; // [ from, to ]
    };
    struct SPresetListResponse
    {
        struct SItem
        {
            int presetId; // id不是[ from, to ],可以随便跳跃.
            std::string presetName;
        };

        bool isOk;
        size_t allItemSize; // 总共个数.注意:files中是本次传送的个数.
        size_t fromIndex;
        size_t toIndex; // [ from, to ]
        std::vector< SItem > presets;
    };

//
// 9. 告警预订: 收请求,发回应.
    struct SAlarmSubscribe
    {
        boost::uuids::uuid resId;
        int alarmLevel;
        EAlarmType alarmType;
    };
// struct SAlarmSubscribeResponse
// {
//     bool isOk;
// };
//
// // 10. 告警预订成功与否的通知: 发请求,收回应.
// struct SAlarmSubscribeStatus
// {
//     boost::uuids::uuid resId;
//     bool isActive;
// };
// struct SAlarmSubscribeStatusResponse
// {
//     // 无
// };
//
// 11. 告警上报: 发请求,收回应.
    struct SAlarmNotify
    {
        boost::uuids::uuid resId;
        int alarmLevel;
        EAlarmType alarmType;
        bool alarmStatus;
        std::string alarmData;
        uint32_t startTime;
    };
    struct SAlarmNotifyResponse
    {
        bool isOk;
    };

// 12. 查询设备信息
    struct SDeviceInfo
    {
    };

    struct SDeviceInfoResponse
    {
        bool isOk;
        bool online;
        bool status;

        // gb28181所需要的内容
        std::string resAddr;
        std::string deviceType;
        std::string manufacturer;
        std::string model;
        std::string firmware;
        uint32_t	maxCamera;
        uint32_t	maxAlarm;

    };

// 13. 编码器参数设置
    struct SEncoderSet
    {
        EFormatType format;
        int framerate;
        int bitrate;
        //bool priority;//0.图像质量优先 1.帧率优先
        int gop;//编码器IPPP编码结构中I帧间隔
        //EImageQualityType image_quality;
    };
    struct SEncoderSetResponse
    {
        bool result;
    };

    // 14. 查询设备状态
    struct SDeviceStatusResponse
    {
        //暂不支持告警
        //struct SAlarmItem
        //{
        //	boost::uuids::uuid	alarmUuid;		// 告警UUID
        //	EAlarmStatus		alarmStatus;	// 告警状态
        //};
        //
        //std::vector< SAlarmItem > alarmStatusList;	// 告警状态列表

        std::string resAddr;					// 设备ID
        bool result;
        bool isEncoder;						// 是否编码
        bool isRecord;							// 是否录像
        bool isOnline;							// 是否在线
        bool isNormal;							// 是否正常工作
        std::string strFaultReason;			// 故障原因
    };

    struct SDeviceConfigBasicParam
    {
        std::string name;
        std::string deviceid;
        std::string sipserverid;
        std::string sipserverip;
        long sipserverport;
        std::string domainname;
        int expiration;
        std::string password;
        int heartBeatInterval;
        int heartBeatCount;
    };

    struct SDeviceConfigVideoParam
    {
        std::string streamname;
        std::string streamFormat;
        std::string resolution;
        std::string frameRate;
        std::string bitRateType;
        std::string videoBitRate;
    };

    struct SDeviceConfigAudioParam
    {
        std::string streamname;
        std::string audioFormat;
        std::string audioBitRate;
        std::string samplingRate;
    };

    struct SAlarmSetParam
    {
        std::string resId;
        int startAlarmPriority;
        int endAlarmPriority;
        std::string alarmMethod;
        uint32_t startTime;
        uint32_t endTime;
    };

    struct SAlarmParam
    {
        std::string resId;
        int priority;
        int method;
        uint32_t time;
        std::string description;
        float longitude;
        float latitude;
    };


}

#endif
