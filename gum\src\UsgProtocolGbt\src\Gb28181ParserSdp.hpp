
#ifndef DB28181PARSERSDP_HPP_
#define DB28181PARSERSDP_HPP_

#include <string>
#include "StructExchange.hpp"

namespace usg
{
    struct IWtoeSipFunc;
}

namespace gb28181
{
    class CConfigFile;

    class CGb28181ParserSdp
    {
    public:
        CGb28181ParserSdp( usg::IWtoeSipFunc* wsf,
                           CConfigFile *configFile,
                           const std::string &sid,
                           const std::string &resAddr,
                           const std::string &gbtStr ,
                           const bool answer);

        ~CGb28181ParserSdp();

        // 是否构造成功.
        bool isOk();

        int getInviteType();

        std::string getResultStr();
        bool parserReceivePkg();
        std::string getFormat();
        std::string getSrcAddr();

        bool trans(uint8_t imageSize, gb28181::EFormatType &type);
        bool trans(uint8_t imageSize, usg::EFormatType &type);
        bool trans(const std::string &videoTypeStr, gb28181::EVideoType &type);
        bool trans(const std::string &videoTypeStr, gb28181::EAudioType &type);

        bool request_func_play(ACE_INET_Addr &addr, std::string &resAddr,std::string &format, bool &isVideo, std::vector<std::string> &videoTypes); //请求实时点播
        bool request_func_playback(ACE_INET_Addr &addr, std::string &resAddr, uint32_t beginTime, uint32_t endTime); //请求回放
        bool request_func_download(ACE_INET_Addr &addr, std::string &resAddr, uint32_t beginTime, uint32_t endTime); //请求下载

        bool response_func_play();
        bool response_func_playback(ACE_INET_Addr &addr, std::string &resAddr, uint32_t beginTime);
        bool response_func_download(ACE_INET_Addr &addr, std::string &resAddr, uint32_t beginTime);

        // 历史流与下载相同
        SHistoryMediaResponse getHistoryMediaInfo();

    public:
        char* strDupSize( const char* str );
        void strDel( char*& str );

        bool parseSDPLine( const char* inputLine, const char*& nextLine );
        bool parseSDPLine_v( const char* sdpLine, std::string &ver );
        bool parseSDPLine_o( const char* sdpLine, std::string &addr );
        bool parseSDPLine_s( const char* sdpLine, std::string &optStr );
        bool parseSDPLine_t( const char* sdpLine, uint32_t &beginTime, uint32_t &endTime );
        bool parseSDPLine_c( const char* sdpLine, std::string &addr );
        bool parseSDPLine_f( const char* sdpLine, std::string &format );
        bool parseSDPLine_m( const char* sdpLine, bool &isVideo, uint16_t &port );
        bool parseSDPLine_u( const char* sdpLine, std::string &rescode);
        bool parseSDPAttribute_rtpmap( const char* sdpLine, std::string &videoType);

        bool mkResultStr( usg::SRealMediaResponse &msg_response, std::string &resultStr );
        bool mkResultStr( usg::SHistoryMediaResponse &msg_response, std::string &resultStr );
        bool replace_o( std::string &line, std::string addr );
        bool replace_c( std::string &line, std::string addr );
        bool replace_m( std::string &line, uint16_t port );
        bool replace_a( std::string &line ,bool real = true);
        bool replace_s( std::string &line );
        bool replace_a_speed( std::string &line );

        bool parseAddrStr(std::string url, std::string &addrStr);

        bool request_func_gengealImple( ACE_INET_Addr &addr, std::string &resAddr, uint32_t beginTime, uint32_t endTime );

    private:
        usg::IWtoeSipFunc* m_wsf;
        const std::string m_sid;
        const std::string m_resAddr;
        const std::string m_gbtStr;
        const bool   m_answer;
        CConfigFile *m_configFile;
        CStructExchange m_structExchange;
        bool m_isOk;

        std::string		m_result;
        std::string		m_palyerURL;
        SHistoryMediaResponse	m_historyMedia;

        std::string     m_format; //缓存实时播放时的格式信息（视频尺寸、码率等）

        std::string     m_srcAddr;

        int             m_inviteType; //0:实时,1:回放,2:下载
    };

};

#endif
