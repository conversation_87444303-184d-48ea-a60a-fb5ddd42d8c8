#include "UsgManager/include/UsgManagerExp.hpp"
#include "UsgSipStack/include/UsgSipStack.hpp"

#include "UsgSipUdpService.hpp"

namespace usg {

bool registPjThread( )
{
	long *ptrdes = 0;
	if( PJ_TRUE != pj_thread_is_registered() )
	{
		ptrdes = new long[PJ_THREAD_DESC_SIZE];
		pj_thread_desc *tmp = ( pj_thread_desc* )ptrdes;
		::memset( tmp, 0, sizeof( pj_thread_desc ) );
		pj_thread_t *this_thread = 0;
		if( PJ_SUCCESS != pj_thread_register( 0, *tmp, &this_thread ) )
		{
			std::cerr << "registPjThread pj_thread_register false" << std::endl;
			return false;
		}
		boost::shared_ptr<IUsgManager> sg;
		wtoe::IMainApplication* pMain = wtoe::getMainApp();
		if ( !pMain )  return false;
		if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

		sg->addPJThreadDes( ptrdes );
	}

	return true;
}

CUsgSipStack::CUsgSipStack()
{

}

CUsgSipStack::~CUsgSipStack()
{

}

bool CUsgSipStack::startupImpl()
{
    pj_init();

    pjlib_util_init();
    
    pj_log_set_level( 1 );

    pjsip_cfg()->endpt.allow_port_in_fromto_hdr = PJ_TRUE;

    return true;
}

bool CUsgSipStack::shutdownImpl()
{
	if( m_sipService )
		m_sipService.reset();

	pj_shutdown();

	return true;
}

boost::shared_ptr< IUsgSipUdpService > CUsgSipStack::createUdpSipService( const std::string &lAddr, uint16_t lPort, const std::string &lCode,const std::string& xmlType /*= "DDCP" */ )
{
	if( m_sipService == 0 )
		m_sipService.reset( new CUsgSipUdpService( lAddr, lPort, lCode, xmlType ) );

	return m_sipService;
}

void CUsgSipStack::setLevel( uint16_t level )
{
	pj_log_set_level( level );
}

CUsgSipStack *CUsgSipStack::instance() {
    static CUsgSipStack ss;
    return &ss;
}

}
