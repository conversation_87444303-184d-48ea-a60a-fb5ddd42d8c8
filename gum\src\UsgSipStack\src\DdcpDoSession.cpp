#include <iostream>

#include "DdcpDoSession.hpp"
#include "SipPoolGuard.hpp"

namespace usg {

    namespace {

        static pjsip_method do_method = { PJSIP_OTHER_METHOD, { (char*)"DO", 2 } };
        static pjsip_method info_method = { PJSIP_OTHER_METHOD, { (char*)"INFO", 4 } };
        static pjsip_method message_method = { PJSIP_OTHER_METHOD, { (char*)"MESSAGE", 7 } };

    }

    CDdcpDoSession::CDdcpDoSession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from )
            : m_pool( pool ), m_endPoint( endPoint ), m_handler( 0 ), m_from( from ),m_xmlType( "DDCP" )
    {

    }

    CDdcpDoSession::CDdcpDoSession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from,const std::string& xmlType )
            : m_pool( pool ), m_endPoint( endPoint ), m_handler( 0 ), m_from( from ),m_xmlType( xmlType )
    {

    }
    CDdcpDoSession::~CDdcpDoSession()
    {
    }

    CDdcpDoSession *CDdcpDoSession::setHandler( ICtxHandler *handler )
    {
        m_handler = handler;
        return this;
    }

    bool CDdcpDoSession::ddcpDo( std::string &sid, const std::string &sipUri, const std::string &result )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        //CSipPoolGuard poolGuard;
        //pj_pool_t * pool = poolGuard.getPool();
        //if ( NULL == pool )
        //{
        //    return false;
        //}

        pjsip_tx_data *tdata = 0;
        if ( sipUri.empty() || m_from.empty() )
        {
            return false;
        }
        pj_str_t uri = { (char *)sipUri.c_str(), (pj_ssize_t)sipUri.length() };
        pj_str_t from = { (char *)m_from.c_str(), (pj_ssize_t)m_from.length() };

        if ( m_xmlType == "DDCP" )
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, &do_method, &uri, &from, &uri, &from, 0, -1, 0, &tdata ) )
                return false;
        }
        else if ( m_xmlType == "MANSCDP" )
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, &message_method, &uri, &from, &uri, &from, 0, -1, 0, &tdata ) )
                return false;
        }
        else
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, &info_method, &uri, &from, &uri, &from, 0, -1, 0, &tdata ) )
                return false;
        }

        pj_str_t type = { (char*)"Application", 11 };
        std::string xmlType = m_xmlType;
        if ( m_xmlType == "MANSCDP" )
            xmlType += "+xml";
        pj_str_t subtype = { (char *)xmlType.c_str(), (pj_ssize_t)xmlType.length() };
        //pj_str_t subtype = { (char *)m_xmlType.c_str(), m_xmlType.length() };
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
        //tdata->msg->body = pjsip_msg_body_create( pool, &type, &subtype, &text );
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, &type, &subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;

        /*
         * 插入dialog记录.
         */
        pjsip_cseq_hdr *cseq = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CSEQ, 0 );
        pjsip_cid_hdr  *cid  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CALL_ID, 0 );

        sid = createSid( cid, cseq );

        return PJ_SUCCESS == pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 );
    }

    bool CDdcpDoSession::ddcpDo( std::string &sid, const std::string &sipUri, const std::string &result, int cseqid )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        //CSipPoolGuard poolGuard;
        //pj_pool_t * pool = poolGuard.getPool();
        //if ( NULL == pool )
        //{
        //    return false;
        //}

        pjsip_tx_data *tdata = 0;
        if ( sipUri.empty() || m_from.empty() )
        {
            return false;
        }
        pj_str_t uri = { (char *)sipUri.c_str(), (pj_ssize_t)sipUri.length() };
        pj_str_t from = { (char *)m_from.c_str(), (pj_ssize_t)m_from.length() };

        if ( m_xmlType == "DDCP" )
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, &do_method, &uri, &from, &uri, &from, 0, -1, 0, &tdata ) )
                return false;
        }
        else if ( m_xmlType == "MANSCDP" )
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, &message_method, &uri, &from, &uri, &from, 0, -1, 0, &tdata ) )
                return false;
        }
        else
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, &info_method, &uri, &from, &uri, &from, 0, -1, 0, &tdata ) )
                return false;
        }

        pj_str_t type = { (char*)"Application", 11 };
        std::string xmlType = m_xmlType;
        if ( m_xmlType == "MANSCDP" )
            xmlType += "+xml";
        pj_str_t subtype = { (char *)xmlType.c_str(), (pj_ssize_t)xmlType.length() };
        //pj_str_t subtype = { (char *)m_xmlType.c_str(), m_xmlType.length() };
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
        //tdata->msg->body = pjsip_msg_body_create( pool, &type, &subtype, &text );
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, &type, &subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;

        /*
         * 插入dialog记录.
         */
        pjsip_cseq_hdr *cseq = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CSEQ, 0 );
        cseq->cseq = cseqid;
        pjsip_cid_hdr  *cid  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CALL_ID, 0 );

        sid = createSid( cid, cseq );

        return PJ_SUCCESS == pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 );
    }

    bool CDdcpDoSession::ddcpDo( std::string &sid, const std::string &sipUri, const std::string &result, int cseqid, const std::string callid )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        //CSipPoolGuard poolGuard;
        //pj_pool_t * pool = poolGuard.getPool();
        //if ( NULL == pool )
        //{
        //    return false;
        //}

        pjsip_tx_data *tdata = 0;
        if ( sipUri.empty() || m_from.empty() )
        {
            return false;
        }
        pj_str_t uri = { (char *)sipUri.c_str(), (pj_ssize_t)sipUri.length() };
        pj_str_t from = { (char *)m_from.c_str(), (pj_ssize_t)m_from.length() };
        pj_str_t pjcallid = { ( char *)callid.c_str(), (pj_ssize_t)callid.length() };

        if ( m_xmlType == "DDCP" )
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, &do_method, &uri, &from, &uri, &from, 0, -1, 0, &tdata ) )
                return false;
        }
        else if ( m_xmlType == "MANSCDP" )
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, &message_method, &uri, &from, &uri, &from, &pjcallid, -1, 0, &tdata ) )
                return false;
        }
        else
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, &info_method, &uri, &from, &uri, &from, 0, -1, 0, &tdata ) )
                return false;
        }

        pj_str_t type = { (char*)"Application", 11 };
        std::string xmlType = m_xmlType;
        if ( m_xmlType == "MANSCDP" )
            xmlType += "+xml";
        pj_str_t subtype = { (char *)xmlType.c_str(), (pj_ssize_t)xmlType.length() };
        //pj_str_t subtype = { (char *)m_xmlType.c_str(), m_xmlType.length() };
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
        //tdata->msg->body = pjsip_msg_body_create( pool, &type, &subtype, &text );
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, &type, &subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;

        /*
         * 插入dialog记录.
         */
        pjsip_cseq_hdr *cseq = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CSEQ, 0 );
/*	cseq->cseq = cseqid;*/
        pjsip_cid_hdr  *cid  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CALL_ID, 0 );

        sid = createSid( cid, cseq );

        return PJ_SUCCESS == pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 );
    }

    bool CDdcpDoSession::ddcpDo( std::string &sid, const std::string &callid, const std::string &sipUri, const std::string &result )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        pjsip_tx_data *tdata = 0;
        if ( sipUri.empty() || m_from.empty() )
        {
            return false;
        }
        pj_str_t uri = { (char *)sipUri.c_str(), (pj_ssize_t)sipUri.length() };
        pj_str_t from = { (char *)m_from.c_str(), (pj_ssize_t)m_from.length() };
        pj_str_t pjcallid = { ( char *)callid.c_str(), (pj_ssize_t)callid.length() };

        if ( m_xmlType == "DDCP" )
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, &do_method, &uri, &from, &uri, &from, &pjcallid, -1, 0, &tdata ) )
                return false;
        }
        else if ( m_xmlType == "MANSCDP" )
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, &message_method, &uri, &from, &uri, &from, &pjcallid, -1, 0, &tdata ) )
                return false;
        }
        else
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, &info_method, &uri, &from, &uri, &from, &pjcallid, -1, 0, &tdata ) )
                return false;
        }

        pj_str_t type = { (char*)"Application", 11 };
        std::string xmlType = m_xmlType;
        if ( m_xmlType == "MANSCDP" )
            xmlType += "+xml";
        pj_str_t subtype = { (char *)xmlType.c_str(), (pj_ssize_t)xmlType.length() };
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, &type, &subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;

        /*
         * 插入dialog记录.
         */
        //pjsip_cseq_hdr *cseq = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CSEQ, 0 );
        //pjsip_cid_hdr  *cid  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CALL_ID, 0 );

//    sid = createSid( cid, cseq );

        return PJ_SUCCESS == pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 );
    }

    bool CDdcpDoSession::answer( pjsip_rx_data *rdata, int status, const std::string &result )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        //CSipPoolGuard poolGuard;
        //pj_pool_t * pool = poolGuard.getPool();
        //if ( NULL == pool )
        //{
        //    return false;
        //}
        pjsip_tx_data *tdata = 0;
        if ( PJ_SUCCESS != pjsip_endpt_create_response( m_endPoint, rdata, status, 0, &tdata ) )
            return false;

        pjsip_uri *uri = rdata->msg_info.msg->line.req.uri;
        //pjsip_contact_hdr* contactHdr = pjsip_contact_hdr_create( pool );
        pjsip_contact_hdr* contactHdr = pjsip_contact_hdr_create( tdata->pool );
        if ( contactHdr == NULL )
            return false;
        contactHdr->uri = uri;
        //contactHdr->q1000 = 500;
        //contactHdr->expires = 3600;
        pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)contactHdr );

        if( result.empty() )
        {
            return PJ_SUCCESS == pjsip_endpt_send_response2( m_endPoint, rdata, tdata, 0, 0 );
        }

        pj_str_t *type = &rdata->msg_info.ctype->media.type;
        pj_str_t *subtype = &rdata->msg_info.ctype->media.subtype;
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
        //tdata->msg->body = pjsip_msg_body_create( pool, type, subtype, &text );
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, type, subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;
        return PJ_SUCCESS == pjsip_endpt_send_response2( m_endPoint, rdata, tdata, 0, 0 );
    }

    bool CDdcpDoSession::onDdcpDo( pjsip_rx_data *rdata )
    {
        if ( !rdata->msg_info.msg || !rdata->msg_info.msg->body || !rdata->msg_info.cseq )
            return false;
        /*
         * 获取uri.
         */
        if ( !PJSIP_URI_SCHEME_IS_SIP( rdata->msg_info.msg->line.req.uri ) ) return false;

        pjsip_sip_uri *uri = (pjsip_sip_uri *)rdata->msg_info.msg->line.req.uri;
        std::string oid( uri->user.ptr, uri->user.slen );
        if ( oid.empty() ) return false;
        /*
         * 获取其他参数.
         */
        const char *sub = rdata->msg_info.msg->body->content_type.subtype.ptr;
        const char *ptr = (const char *)rdata->msg_info.msg->body->data;
        size_t      len = rdata->msg_info.msg->body->len;
        std::string sid = createSid( rdata->msg_info.cid, rdata->msg_info.cseq );

        /*
         * 判断正文是否是所需要的格式.
         */
        if ( 0 == ::strnicmp( sub, "DDCP", 4 ) ||
             0 == ::strnicmp( sub, "XML", 3 ) ||
             0 == ::strnicmp( sub, "IVS_XML", 7 ) ||
             0 == ::strnicmp( sub, "MANSRTSP", 8) )
        {
            if ( m_handler )
            {
                std::string result;
                if ( m_handler->commitDdcpDo( sid, oid, ptr, len, result ) )
                {
                    if( 0 == ::strnicmp( sub, "MANSRTSP", 8) )
                        return answerRtsp( rdata, 200, result );
                    else
                        return answer( rdata, 200, result );
                }
                else
                {
                    return answer( rdata, 400, result );
                }
            }
            return false;
        }
        else if ( 0 == ::strnicmp( sub, "MANSCDP", 7 ) )
        {
            if ( m_handler )
            {
                std::vector<std::string> resultVec;
                if ( m_handler->commitDdcpDo( sid, oid, ptr, len, resultVec ) )
                {
                    // 下级回复200 OK
                    if( !answer( rdata, 200, "" ) )
                    {
                        std::cerr << "!answer( rdata, 200 )" << std::endl;
                        return false;
                    }

                    if( !resultVec.empty() )
                    {
                        if( ( *resultVec.begin() ) == "CatalogQueryCmad" )
                        {
                            std::string callid( rdata->msg_info.cid->id.ptr, rdata->msg_info.cid->id.slen );
                            m_queryCatalog[callid] = resultVec;
                            ddcpDo(sid, m_catalogSipUrl, resultVec[1], rdata->msg_info.cseq->cseq, callid );
                            m_queryCatalog[callid].erase( m_queryCatalog[callid].begin() );
                            m_queryCatalog[callid].erase( m_queryCatalog[callid].begin() );
                        }
                        else
                        {
                            // 下级回复查询信息给上级
                            for(size_t i=0;i<resultVec.size();i++)
                            {
                                std::string callid( rdata->msg_info.cid->id.ptr, rdata->msg_info.cid->id.slen );
                                ddcpDo(sid, m_catalogSipUrl, resultVec[i],rdata->msg_info.cseq->cseq,callid);
                            }
                        }
                    }
                }
                else
                {
                    return answer( rdata, 400, "" );
                }
            }
            return false;
        }

        return true;
    }

    bool CDdcpDoSession::onAnswer( pjsip_rx_data *rdata )
    {
        if ( !m_handler || !rdata->msg_info.msg || !rdata->msg_info.cseq )
            return false;
        /*
         * 获取其他参数.
         */
        std::string sid = createSid( rdata->msg_info.cid, rdata->msg_info.cseq );

        int status = rdata->msg_info.msg->line.status.code / 100;
        switch ( status )
        {
            case 2:
            {
                char *ptr = 0;
                size_t len = 0;
                if ( rdata->msg_info.msg->body )
                {
                    ptr = (char *)rdata->msg_info.msg->body->data;
                    len = rdata->msg_info.msg->body->len;
                }

                //处理目录查询发送
                return m_handler->commitAnswer( sid, ptr, len, true, IDdcpDoSession::SESSION_TYPE );
            }
            case 4:
                return m_handler->commitAnswer( sid, 0, 0, false, IDdcpDoSession::SESSION_TYPE );

            default:
                break;
        }

        return true;
    }

    std::string CDdcpDoSession::createSid( pjsip_cid_hdr *cid, pjsip_cseq_hdr *cseq )
    {
        if ( !cid || !cseq ) return std::string();

        std::string sid( cid->id.ptr, cid->id.slen );

        //char buf[16] = { 0 };
        //sprintf( buf, "%d", cseq->cseq );
        return sid; //+= buf;
    }

    bool CDdcpDoSession::ddcpDoByCallId( const pjsip_uri *target, const pjsip_from_hdr *from, const pjsip_to_hdr *to, const pjsip_cid_hdr *call_id, int cseq, const std::string &result )
    {
        return ddcpDoByCallId(target,from,to,call_id,0, cseq,m_xmlType,result);
    }

    bool CDdcpDoSession::ddcpDoByCallId( const pjsip_uri *target, const pjsip_from_hdr *from, const pjsip_to_hdr *to, const pjsip_cid_hdr *call_id, const pjsip_contact_hdr *contact,int cseq, const std::string &xmlType, const std::string &result )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        std::string strXmlType = xmlType;
        pjsip_tx_data *tdata = 0;
        if ( strXmlType == "DDCP" )
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request_from_hdr( m_endPoint,&do_method, target, from, to, contact, call_id, cseq, 0, &tdata ) )
                return false;
        }
        else if ( strXmlType == "MANSCDP" )
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request_from_hdr( m_endPoint,&message_method, target, from, to, contact, call_id, cseq, 0, &tdata ) )
                return false;
        }
        else
        {
            if ( PJ_SUCCESS != pjsip_endpt_create_request_from_hdr( m_endPoint,&info_method, target, from, to, contact, call_id, cseq, 0, &tdata ) )
                return false;
        }

//     pjsip_expires_hdr* expriesHdr = pjsip_expires_hdr_create( tdata->pool, 60 );
//     pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)expriesHdr );

        pj_str_t type = { (char*)"Application", 11 };
        if ( strXmlType == "MANSCDP" )
            strXmlType += "+xml";
        pj_str_t subtype = { (char *)strXmlType.c_str(), (pj_ssize_t)strXmlType.length() };
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, &type, &subtype, &text );

        return PJ_SUCCESS == pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 );
    }

    bool CDdcpDoSession::setSipUrl( const std::string catalogSipUrl )
    {
        m_catalogSipUrl = catalogSipUrl;
        return true;
    }

    bool CDdcpDoSession::answerRtsp( pjsip_rx_data *rdata, int status, const std::string &result )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        //CSipPoolGuard poolGuard;
        //pj_pool_t * pool = poolGuard.getPool();
        //if ( NULL == pool )
        //{
        //    return false;
        //}
        pjsip_tx_data *tdata = 0;
        if ( PJ_SUCCESS != pjsip_endpt_create_response( m_endPoint, rdata, status, 0, &tdata ) )
            return false;

        pjsip_uri *uri = rdata->msg_info.to->uri;
        //pjsip_contact_hdr* contactHdr = pjsip_contact_hdr_create( pool );
        pjsip_contact_hdr* contactHdr = pjsip_contact_hdr_create( tdata->pool );
        if ( contactHdr == NULL )
            return false;
        contactHdr->uri = uri;
        //contactHdr->q1000 = 500;
        //contactHdr->expires = 3600;
        pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)contactHdr );

        pj_str_t *type = &rdata->msg_info.ctype->media.type;
        pj_str_t *subtype = &rdata->msg_info.ctype->media.subtype;
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
        //tdata->msg->body = pjsip_msg_body_create( pool, type, subtype, &text );
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, type, subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;
        return PJ_SUCCESS == pjsip_endpt_send_response2( m_endPoint, rdata, tdata, 0, 0 );
    }

}
