/**
 * Project FVM
 */
#pragma once

#include <memory>
#include <vector>
#include "stream/input/stream_input.h"
#include "stream/output/stream_output.h"

/**
 * @brief: 视频流管道 （可作为临时通道、或派生检测通道）
 */
namespace fvm::stream {

    /**
     * @brief 临时推流通道类型
     */
    enum class ChannelType
    {
        Detect,             //!< 检测通道
        TempRequest,        //!< web端 视频查看
        TempSetArea,        //!< web端 设置检测区
    };

    /**
     * @brief 预置位坐标信息
     */
    struct PresetPosition
    {
        double x = 0;
        double y = 0;
        double z = 0;
    };

    typedef boost::signals2::signal<void()> OnTimeProgramsTimerExpired;
    /**
     * 流管道
     */
    class StreamPipe {
    public:
        /**
         * 初始化输入器
         * @param StreamInput
         */
        virtual void initInput(StreamInputPtr input);

        /**
         * 初始化输出列表
         * @param StreamOutputList
         */
        virtual void initOutputs(StreamOutputList outputs);

        //virtual StreamInputPtr getInput(){return inputStream;};

        /**
         * 增加视频输出
         * @param StreamOutput
         */
        virtual bool addOutput(StreamOutputPtr output);

        /**
         * 获取视频输出
         * @param StreamOutput
         */
        virtual StreamOutputPtr getOutput(StreamOuputType outputType, int port = 0);

         /**
         * 获取视频输入
         * @param 
         */
        virtual StreamInputPtr getInput();

        /**
         * 根据输出类型删除这个输出
         * @param StreamOutput
         */
        virtual void removeOutputTarget(OutputTargetType outputType);

        /**
         * 开始管道
         */
        virtual void startPipe();

        /**
         * 停止管道
         * @param waitFinish 是否等待停止
         * @param dispose 是否销毁管道的输入输出
         */
        virtual void stopPipe(bool waitFinish = false, bool dispose = false);

        /**
         * 获取通道流状态
         * @param detailed 是否显示详细
         */
        std::string getStreamStatus(bool detailed = false);

        /**
         * 调用当前视频的预置位
         */
        void asyncCallCameraPreset( int presetId, int actPresetId);

        /**
         * 获取当前视频的id
         */
         int getInputVideoId();
         /**
          * 获取当前视频的前端id
          */
         int getInputVideoServerId();

        /**
         * 往数据库中写预置位对应的基准坐标值
         */
        bool writePresetPosition(int presetId, PresetPosition& position);

    public: // SIGNALs
         OnTimeProgramsTimerExpired onTimeProgramsTimerExpired;  //!< 时间切换方案定时器到时通知

    private:

        /**
         * 切换输入
         * @param input
         */
        void switchInput(StreamInputPtr input);

        /**
         * 移除输出
         * @param output
         */
        void removeOutput(StreamOutputPtr output);

        /**
         * 收到输入流状态变化回调
         * @param status
         */
        void inputStreamStatusChanged(StreamElementStatus status);
        /**
         * 收到视频流数据包
         * @param packet
         */
        void streamDataReceived(PacketDataPtr packet);

        /**
         * 收到码流相关信息
         * @param codec
         */
        void streamCodecInfoRetrieved(CodecInfoPtr codec);


    protected:
        // 当前输入
        StreamInputPtr inputStream = nullptr;
        // 等待切换的 输入列表
        StreamInputList scheduledInputStreams;

        // 当前输出
        StreamOutputList outputStreams;

        // 当前码流相关信息
        CodecInfoPtr codecInfo;

        boost::fibers::mutex mutexInput;
        boost::fibers::mutex mutexOutput;
    };

    typedef std::shared_ptr<StreamPipe> StreamPipePtr;
}