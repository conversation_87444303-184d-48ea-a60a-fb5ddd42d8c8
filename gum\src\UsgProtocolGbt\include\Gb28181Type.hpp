
#ifndef GB28181TYPE_HPP_
#define GB28181TYPE_HPP_

#include <string>
#include <stdlib.h>

#include <ace/INET_Addr.h>
#include <boost/bimap.hpp>
#include <boost/regex.hpp>
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>
#include <boost/format.hpp>

namespace gb28181
{

/**
 DeviceAddressType
 设备地址类型 字符串, 内部为0-9的数字, 定长18
 排列顺序从左至右依次 ：六位数字码为三级域代码，两位数字码为四级域代码，
 两位数字码为接入类型码，六位数字码为区域码，两位数字码为类型码
 */

// PrivilegeType 用户权限类型 // 字符串, 以%开头加1或2个16进制数为一组, 如%01,这样的组为1到多个, 如%01%02%a.按两位补0或不补都可以.

// IpAddressType IP地址类型 // 即常见的点分十进制方式.

// CatalogType 目录结构类型

// VideoType 视频编码类型 // 字符串,定义为: MPEG-4 和 H.264

// AudioType 音频编码类型 // 字符串,定义为: G.711A G.722.1 G.723.1 G.729 AMR-NB [MPEG2 L2最后这个,它中间有空格]

// FormatType 码流格式类型 // 字符串,定义为: QCIF QVGA CIF 2CIF DCIF D1 720p 1080i 1080p UXGA OTH

// ControlCode 控制码类型 // 字符串,数字0-9或字母(大小写都可以), 定长为6. 第1、2个字符代表命令码，第3、4个字符代表数据1，第5、6个字符代表数据2.

// DecoderTagType 解码插件标签类型 // 字符串,数字0-9或字母(大小写都可以), 长度3到10. 加[_V]加一位十进制数,加[.],加2位十进制数(不足要补0),如 abc123ABC0_V1.02

// ResType 资源类型 // 字符串, 2位十进制数(不足要补0).

// ResSubType 资源子类型 // 字符串, 2位十进制数(不足要补0).

// OperateType 操作类型 // 字符串, ADD添加共享 DEL取消共享 MOD修改共享 OTH保留

// AlarmType 报警类型 // 字符串, Detect VDetect VLost VHide EventOnVideo OTH

/**
 * 可用来进行10进制的数字值转换.
 * 注意:不能用来转换16进制的字符串.
 */
    template< typename FROM_T, typename TO_T >
    inline bool valueToLexical( const FROM_T &f, TO_T &t )
    {
        try
        {
            t = boost::lexical_cast< TO_T >( f );
            return true;
        }
        catch( boost::bad_lexical_cast & /*a*/ )
        {
        }
        catch( ... )
        {
        }
        return false;
    }
// 注意,如果有a-f,则转换成大写.
    inline bool valueToHexStr( const uint8_t f, std::string &t )
    {
        uint16_t tmp = f;
        boost::format fmt( "%02X" );
        fmt% tmp ;
        t = fmt.str();
        return true;
    }
    inline bool valueToHexStr( const uint32_t f, std::string &t )
    {
        uint32_t tmp = f & 0x00ffffff;
        boost::format fmt( "%06X" );
        fmt% tmp ;
        t = fmt.str();
        return true;
    }
    inline bool valueFromHexStr( const std::string &f, uint16_t &t )
    {
        if( f.empty() )
            return false;
        unsigned long tmp = 0;
        tmp = strtoul( f.c_str(), 0, 16 );
        if( tmp > 0xff )
            return false;
        t = tmp & 0xff;
        return true;
    }
//////////////////////////////////////////////////////////////////////////

    inline bool isValidDeviceAddressType( const std::string &str )
    {
        return true;
        static boost::regex reg("\\d{18}");
        return boost::regex_match( str, reg );
    }

    inline bool isValidPrivilegeType( const std::string &str )
    {
        return true;
        static boost::regex reg("(%(\\d|[a-f]|[A-F]){1,2}){1,}");
        return boost::regex_match( str, reg );
    }

    inline bool isValidIpAddressType( const std::string &str )
    {
        static boost::regex reg(
                "(25[0-5]|" // 25x
                "2[0-4][0-9]|" // 2xx
                "1[0-9][0-9]|" // 1xx
                "[1-9][0-9]|" // xx
                "[0-9])[.]" // x
                "(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])[.]"
                "(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])[.]"
                "(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])" );
        return boost::regex_match( str, reg );
    }

    enum EVideoType
    {
        EVIDEOTYPE_MPEG_2,
        EVIDEOTYPE_MPEG_4,
        EVIDEOTYPE_H_264,
        EVIDEOTYPE_SAVC,
    };
    extern boost::bimap< std::string, int > s_videoType;
// inline bool isValidVideoType( const std::string &str )
// {
//     boost::bimap< std::string, int >::left_const_iterator iter = s_videoType.left.find( str );
//     if( iter == s_videoType.left.end() )
//         return false;
//     return true;
// }
    inline bool strToVideoType( const std::string &str, EVideoType &type )
    {
        boost::bimap< std::string, int >::left_const_iterator iter = s_videoType.left.find( str );
        if( iter == s_videoType.left.end() )
            return false;
        type = (EVideoType)(*iter).second;
        return true;
    }
    inline bool strFromVideoType( const EVideoType type, std::string &str )
    {
        boost::bimap< std::string, int >::right_const_iterator iter = s_videoType.right.find( type );
        if( iter == s_videoType.right.end() )
            return false;
        str = (*iter).second;
        return true;
    }
// 空=回成功,调用者判断空ts是否合法. 后面其它几个此种函数也是此原则.
    inline bool strToVideoType_s( const std::string &str, std::vector< EVideoType > &ts )
    {
        std::vector< std::string > tmp;
        boost::algorithm::split( tmp, str, boost::is_space(), boost::token_compress_on ); // token_compress_off不去掉中间连写的分隔符, token_compress_on是去中间连写的分隔符,如多个空格不看作一个有效串.

        ts.clear();
        std::vector< std::string >::const_iterator it( tmp.begin() );
        std::vector< std::string >::const_iterator end( tmp.end() );
        for( ; it != end; ++it )
        {
            if( (*it).empty() )
                continue;
            EVideoType t;
            if( !strToVideoType( (*it), t ) )
            {
                ts.clear();
                return false;
            }
            ts.push_back( t );
        }
        return true;
    }

    inline bool videoType_sToStr( const std::vector< EVideoType > & ts, std::string &str  )
    {
        str.clear();
        std::vector< EVideoType >::const_iterator it( ts.begin() );
        std::vector< EVideoType >::const_iterator end( ts.end() );
        std::string tmpStr;
        for( ; it != end; ++it )
        {
// 		boost::bimap< std::string, int >::right_const_iterator iter = s_videoType.right.find( (*it) );
// 		if( iter == s_videoType.right.end() )
// 			return false;
// 		str += (*iter).second;
            tmpStr.clear();
            strFromVideoType((*it),tmpStr);
            str = str+ tmpStr + " ";
        }
        str.erase(str.find_last_not_of(" ") + 1);
        return true;
    }

    enum EAudioType
    {
        EAUDIOTYPE_G_711,
        EAUDIOTYPE_G_722,
        EAUDIOTYPE_G_723_1,
        EAUDIOTYPE_G_729,
    };
    extern boost::bimap< std::string, int > s_audioType;
// inline bool isValidAudioType( const std::string &str )
// {
//     boost::bimap< std::string, int >::left_const_iterator iter = s_audioType.left.find( str );
//     if( iter == s_audioType.left.end() )
//         return false;
//     return true;
// }
    inline bool strToAudioType( const std::string &str, EAudioType &type )
    {
        boost::bimap< std::string, int >::left_const_iterator iter = s_audioType.left.find( str );
        if( iter == s_audioType.left.end() )
            return false;
        type = (EAudioType)(*iter).second;
        return true;
    }
    inline bool strFromAudioType( const EAudioType type, std::string &str )
    {
        boost::bimap< std::string, int >::right_const_iterator iter = s_audioType.right.find( type );
        if( iter == s_audioType.right.end() )
            return false;
        str = (*iter).second;
        return true;
    }
    inline bool strToAudioType_s( const std::string &str, std::vector< EAudioType > &ts )
    {
        //
        // 注意:在AudioType中,有字符串EAUDIOTYPE_MPEG2_L2(对应"MPEG2 L2")它中间是有空格的.
        // 按此split方式,则会分析成两个,
        // 由于WTOE根本不支持此模式,所以此处暂未处理,即如果有此类型,不会被分析出来.
        // comment by yy.
        //
        std::vector< std::string > tmp;
        boost::algorithm::split( tmp, str, boost::is_space(), boost::token_compress_on ); // token_compress_off不去掉中间连写的分隔符, token_compress_on是去中间连写的分隔符,如多个空格不看作一个有效串.

        ts.clear();
        std::vector< std::string >::const_iterator it( tmp.begin() );
        std::vector< std::string >::const_iterator end( tmp.end() );
        for( ; it != end; ++it )
        {
            if( (*it).empty() )
                continue;
            if( (*it) == "MPEG2" || (*it) == "L2" )
                continue;
            EAudioType t;
            if( !strToAudioType( (*it), t ) )
            {
                ts.clear();
                return false;
            }
            ts.push_back( t );
        }
        return true;
    }

    inline bool  audioType_sToStr(  const std::vector< EAudioType > &ts ,std::string &str)
    {

        str.clear();
        std::vector< EAudioType >::const_iterator it( ts.begin() );
        std::vector< EAudioType >::const_iterator end( ts.end() );
        std::string tmpStr;
        for( ; it != end; ++it )
        {
// 		boost::bimap< std::string, int >::right_const_iterator iter = s_audioType.right.find( type );
// 		if( iter == s_audioType.right.end() )
// 			return false;
            //str += (*iter).second;
            tmpStr.clear();
            strFromAudioType( (*it), tmpStr);
            str = str+ tmpStr + " ";

        }
// 	trim( str );
        str.erase(str.find_last_not_of(" ") + 1);
        return true;
    }


    enum EFormatType
    {
        EFORMATTYPE_QCIF,
        EFORMATTYPE_CIF,
        EFORMATTYPE_2CIF,
        EFORMATTYPE_DCIF,
        EFORMATTYPE_D1, // 4cif
        EFORMATTYPE_4CIF,
        EFORMATTYPE_16CIF,
    };
    extern boost::bimap< std::string, int > s_formatType;
// inline bool isValidFormatType( const std::string &str )
// {
//     boost::bimap< std::string, int >::left_const_iterator iter = s_formatType.left.find( str );
//     if( iter == s_formatType.left.end() )
//         return false;
//     return true;
// }
    inline bool strToFormatType( const std::string &str, EFormatType &type )
    {
        boost::bimap< std::string, int >::left_const_iterator iter = s_formatType.left.find( str );
        if( iter == s_formatType.left.end() )
            return false;
        type = (EFormatType)(*iter).second;
        return true;
    }
    inline bool strFromFormatType( const EFormatType type, std::string &str )
    {
        boost::bimap< std::string, int >::right_const_iterator iter = s_formatType.right.find( type );
        if( iter == s_formatType.right.end() )
            return false;
        str = (*iter).second;
        return true;
    }
    inline bool strToFromatType_s( const std::string &str, std::vector< EFormatType > &ts )
    {
        std::vector< std::string > tmp;
        boost::algorithm::split( tmp, str, boost::is_space(), boost::token_compress_on ); // token_compress_off不去掉中间连写的分隔符, token_compress_on是去中间连写的分隔符,如多个空格不看作一个有效串.

        ts.clear();
        std::vector< std::string >::const_iterator it( tmp.begin() );
        std::vector< std::string >::const_iterator end( tmp.end() );
        for( ; it != end; ++it )
        {
            if( (*it).empty() )
                continue;
            EFormatType t;
            if( !strToFormatType( (*it), t ) )
            {
                ts.clear();
                return false;
            }
            ts.push_back( t );
        }
        return true;
    }

    inline bool fromatType_sToStr( const std::vector< EFormatType > &ts,std::string &str  )
    {

        str.clear();
        std::vector< EFormatType >::const_iterator it( ts.begin() );
        std::vector< EFormatType >::const_iterator end( ts.end() );
        std::string tmpStr;
        for( ; it != end; ++it )
        {
            tmpStr.clear();
            strFromFormatType((*it),tmpStr);
            str = str+ tmpStr + " ";
        }
        str.erase(str.find_last_not_of(" ") + 1);
        return true;
    }

    inline bool isValidDecoderTagType( const std::string &str )
    {
        return true;
        static boost::regex reg("(\\d|[a-f]|[A-F]){3,10}_V\\d\\.\\d{2}");
        return boost::regex_match( str, reg );
    }

    inline bool isValidResType( const std::string &str )
    {
        return true;
        static boost::regex reg("\\d{2}");
        return boost::regex_match( str, reg );
    }

    inline bool isValidResSubType( const std::string &str )
    {
        return isValidResType( str );
    }

    enum EOperateType
    {
        EOPERATETYPE_ADD,
        EOPERATETYPE_DEL,
        EOPERATETYPE_MOD,
        EOPERATETYPE_OTH,
    };
    extern boost::bimap< std::string, int > s_operateType;
// inline bool isValidOperateType( const std::string &str )
// {
//     boost::bimap< std::string, int >::left_const_iterator iter = s_operateType.left.find( str );
//     if( iter == s_operateType.left.end() )
//         return false;
//     return true;
// }
    inline bool strToOperateType( const std::string &str, EOperateType &type )
    {
        boost::bimap< std::string, int >::left_const_iterator iter = s_operateType.left.find( str );
        if( iter == s_operateType.left.end() )
            return false;
        type = (EOperateType)(*iter).second;
        return true;
    }
    inline bool strFromOperateType( const EOperateType type, std::string &str )
    {
        boost::bimap< std::string, int >::right_const_iterator iter = s_operateType.right.find( type );
        if( iter == s_operateType.right.end() )
            return false;
        str = (*iter).second;
        return true;
    }

    enum EAlarmType
    {
        EALARMTYPE_DETECT,
        EALARMTYPE_VDETECT,
        EALARMTYPE_VLOST,
        EALARMTYPE_VHIDE,
        EALARMTYPE_EVENTONVIDEO,
        EALARMTYPE_OTH,
    };
    extern boost::bimap< std::string, int > s_alarmType;
// inline bool isValidAlarmType( const std::string &str )
// {
//     boost::bimap< std::string, int >::left_const_iterator iter = s_alarmType.left.find( str );
//     if( iter == s_alarmType.left.end() )
//         return false;
//     return true;
// }
    inline bool strToAlarmType( const std::string &str, EAlarmType &type )
    {
        boost::bimap< std::string, int >::left_const_iterator iter = s_alarmType.left.find( str );
        if( iter == s_alarmType.left.end() )
            return false;
        type = (EAlarmType)(*iter).second;
        return true;
    }
    inline bool strFromAlarmType( const EAlarmType type, std::string &str )
    {
        boost::bimap< std::string, int >::right_const_iterator iter = s_alarmType.right.find( type );
        if( iter == s_alarmType.right.end() )
            return false;
        str = (*iter).second;
        return true;
    }

    enum ESocketType
    {
        ESOCKETTYPE_UDP,
        ESOCKETTYPE_TCP_PASSIVE,
        ESOCKETTYPE_TCP_ACTIVE,
    };
    extern boost::bimap< std::string, int > s_socketType;
// inline bool isValidSocketType( const std::string &str )
// {
//     boost::bimap< std::string, int >::left_const_iterator iter = s_socketType.left.find( str );
//     if( iter == s_socketType.left.end() )
//         return false;
//     return true;
// }
    inline bool strToSocketType( const std::string &str, ESocketType &type )
    {
        boost::bimap< std::string, int >::left_const_iterator iter = s_socketType.left.find( str );
        if( iter == s_socketType.left.end() )
            return false;
        type = (ESocketType)(*iter).second;
        return true;
    }
    inline bool strFromSocketType( const ESocketType type, std::string &str )
    {
        boost::bimap< std::string, int >::right_const_iterator iter = s_socketType.right.find( type );
        if( iter == s_socketType.right.end() )
            return false;
        str = (*iter).second;
        return true;
    }
    inline bool strToSocketUrl( const std::string &str, ESocketType &st, uint32_t &addr, uint16_t &port )
    {
        // ************* UDP 2360
        std::string strTmp = str;
        boost::trim( strTmp );
        std::vector< std::string > tmp;
        boost::algorithm::split( tmp, strTmp, boost::is_space(), boost::token_compress_on ); // token_compress_off不去掉中间连写的分隔符, token_compress_on是去中间连写的分隔符,如多个空格不看作一个有效串.

        // 第一段应该是IP地址. 第二段应该是UDP/TCP. 第三段应该是端口.
        if( tmp.size() != 3 )
            return false;

        if( !isValidIpAddressType( tmp[0] ) )
            return false;

        if( !strToSocketType( tmp[1], st ) )
            return false;

        if( !valueToLexical<std::string, uint16_t>( tmp[2], port ) )
            return false;

        ACE_INET_Addr tmpAddr;
        if( 0 != tmpAddr.set( u_short(0), tmp[0].c_str() ) )
            return false;
        addr = tmpAddr.get_ip_address();

        return true;
    }
    inline bool strFromSocketUrl( const ESocketType st, const uint32_t addr, const uint16_t port, std::string &str )
    {
        std::string t;
        if( !strFromSocketType( st, t ) )
            return false;

        std::string p;
        if( !valueToLexical< uint16_t, std::string >( port, p ) )
            return false;

        std::string a;
        ACE_INET_Addr tmpAddr;
        if( 0 != tmpAddr.set( u_short(0), addr ) )
            return false;
        a = tmpAddr.get_host_addr();

        str = a + " " + t + " " + p;
        return true;
    }


    enum EPtzCommand
    {
        EPTZCOMMAND_UP = 0x08000000,
        EPTZCOMMAND_DOWN = 0x04000000,
        EPTZCOMMAND_LEFT = 0x02000000,
        EPTZCOMMAND_RIGHT =0x01000000,
        EPTZCOMMAND_STOP = 0x00000000,

        EPTZCOMMAND_FOCUSNEAR = 0x42000000,
        EPTZCOMMAND_FOCUSFAR = 0x41000000,
        EPTZCOMMAND_FOCUSSTOP = 0x40000000,

        EPTZCOMMAND_ZOOMIN = 0x10000000,
        EPTZCOMMAND_ZOOMOUT = 0x20000000,

        EPTZCOMMAND_APERTUREWIDE = 0x44000000,
        EPTZCOMMAND_APERTURETELE = 0x48000000,

        //预置位
        EPTZCOMMAND_PREPOSITIONADD = 0x81000000,
        EPTZCOMMAND_PREPOSITIONSWITCH = 0x82000000,
        EPTZCOMMAND_PREPOSITIONDEL = 0x83000000,

        //巡航
        EPTZCOMMAND_CRUISEADD = 0x84000000,
        EPTZCOMMAND_CRUISEDEL = 0x85000000,
        EPTZCOMMAND_CRUISESPEED = 0x86000000,
        EPTZCOMMAND_CRUISESTIME = 0x87000000,
        EPTZCOMMAND_CRUISESSTART = 0x88000000,

        //扫描
        EPTZCOMMAND_SCANSTART = 0x89000000,
        EPTZCOMMAND_SCANLEFT = 0x89000100,
        EPTZCOMMAND_SCANRIGHT = 0x89000200,
        EPTZCOMMAND_SCANTIME = 0x8A000000,	//设置自动扫描速度指令
    };


// 如果云台的命令,就是这个字段.
    inline bool isValidControlCode( const std::string &str )
    {
        return true;
        static boost::regex reg("((\\d|[a-f]|[A-F]){16})");
        return boost::regex_match( str, reg );
    }
    inline bool strToControlCode( const std::string &str, uint32_t &cmd, uint16_t &param1, uint16_t &param2 )
    {
        if( !isValidControlCode( str ) )
            return false;

        //获取PTZ控制指令
        uint8_t cmdHex[8] = {0};
        //字符串转成十六进制数
        for (int i = 0; i < 8; ++i)
        {
            uint16_t hex;
            std::string tmp = str.substr(i * 2, 2);
            if( !valueFromHexStr( tmp, hex ))
                return false;
            cmdHex[i] = (uint8_t)hex;
        }

        cmd = (cmdHex[3] << 24);
        if (cmdHex[3] == 0x89)
            cmd = cmd + (cmdHex[5] << 8);

        param1 = cmdHex[4];
        param2 = (cmdHex[5] << 8) + cmdHex[6];
        return true;
    }
    inline bool strFromControlCode( const uint8_t cmnd, const uint8_t p1, const uint8_t p2, std::string &str )
    {
        std::string ret;
        std::string tmp;

        if( !valueToHexStr( cmnd, tmp ) )
            return false;
        ret = tmp;

        if( !valueToHexStr( p1, tmp ) )
            return false;
        ret = ret + tmp;

        if( !valueToHexStr( p2, tmp ) )
            return false;
        ret = ret + tmp;

        str = ret;
        //str = "0x" + ret;
        return true;
    }


//目录推送操作类型
// const static std::string CATALOG_OPER_ADD = "ADD";
// const static std::string CATALOG_OPER_DEL = "DEL";
// const static std::string CATALOG_OPER_MOD = "MOD";
//
// const static std::string PTZ_STRING_UP  = "08";
// const static std::string PTZ_STRING_DOWN = "04";
// const static std::string PTZ_STRING_LEFT = "02";
// const static std::string PTZ_STRING_RIRGT= "01";
// const static std::string PTZ_STRING_STOP  = "000000"; //停止转动
// const static std::string PTZ_STRING_SWITCHPREPOSITION = "82";
// const static std::string PTZ_STRING_FOCUSNEAR = "20";
// const static std::string PTZ_STRING_FOCUSFAR= "10";
// const static std::string PTZ_STRING_ZOOMOUT  = "44";
// const static std::string PTZ_STRING_ZOOMIN = "48";
// const static std::string PTZ_STRING_APERTUREWIDE = "41";
// const static std::string PTZ_STRING_APERTURETELE= "42";
// const static std::string PTZ_STRING_WIPERON  = "8C0100";
// const static std::string PTZ_STRING_WIPEROFF = "8D0100";
// const static std::string PTZ_STRING_LEDON = "8C0200";
// const static std::string PTZ_STRING_LEDOFF= "8D0200";
// const static std::string PTZ_STRING_STOP_1  = "400000";//停止镜头动作

//将国标的云台控制指令转换为字符串

/************************************************************************/
/* 其它的类型判断                                                       */
/************************************************************************/

    inline bool isValidBoolResultType( const std::string &str )
    {
        static boost::regex reg("([0-1]){1}");
        return boost::regex_match( str, reg );
    }
    inline bool strToBoolResult( const std::string &str, bool &b )
    {
        if( !isValidBoolResultType( str ) )
        {
            return false;
        }
        int tmp = 0;
        if( !valueToLexical<std::string, int>( str, tmp ) )
        {
            return false;
        }

        // The value space of xsd:boolean is true and false. Its lexical space accepts true, false, and also 1 (for true) and 0 (for false).
        if( tmp == 0 )
            b = false;
        else if( tmp == 1 )
            b = true;
        else
            return false;
        return true;
    }
    inline void strFromBoolResult( const bool b, std::string &str )
    {
        if( b )
        {
            str = "OK";
            return;
        }
        str = "ERROR";
    }

    inline bool ptzCommandTypeToString(uint32_t cmd, uint16_t param1, uint16_t param2, std::string &str)
    {
        //云台命令
        const uint8_t HEAD_BYTE = 0xA5;			//首字节
        const uint8_t VERSION_BYTE = 0x01;		//版本信息字节
        const uint8_t ADDR_BYTE = 0x00;			//地址码
        std::string tmp;

        uint8_t ptzCmd[8] = {0};
        ptzCmd[0] = HEAD_BYTE;
        ptzCmd[1] = (VERSION_BYTE << 4) + (((ptzCmd[0] & 0xf0) >> 4) + (ptzCmd[0] & 0x0f) + VERSION_BYTE)%16;
        ptzCmd[2] = ADDR_BYTE;
        ptzCmd[3] = cmd >> 24;
        ptzCmd[4] = param1 & 0x00ff;
        ptzCmd[5] = (param2 & 0xff00) >> 8;
        ptzCmd[6] = (param2 & 0x00f0) + ((ADDR_BYTE & 0xf0) >> 4);
        ptzCmd[7] = (ptzCmd[0] + ptzCmd[1] + ptzCmd[2]+ ptzCmd[3]+ ptzCmd[4]+ ptzCmd[5]+ ptzCmd[6]+ ptzCmd[7])%256;

        for (uint8_t i = 0; i < 8; ++i)
        {
            if ( !valueToHexStr( (uint8_t)ptzCmd[i], tmp ) )
                return false;
            str += tmp;
        }
        return true;
    }

}


#endif
