
#ifndef GB28181STRUCT_HPP_
#define GB28181STRUCT_HPP_

#include <string>
#include <stdint.h>
#include <vector>
#include <boost/uuid/uuid.hpp>
#include <boost/uuid/uuid_generators.hpp>
#include <boost/shared_ptr.hpp>

#include "Gb28181Type.hpp"

namespace gb28181
{

    struct SAccess
    {
        std::string ip;
        uint16_t    port;
        std::string addrCode;
        std::string usrName;
        std::string password;
    };

// 1. 注册没有信息.

// 2. 推送目录: 发请求, 收回应.

    struct SCatalog
    {
        struct SItem
        {
            boost::uuids::uuid resUuid;
            std::string devid;
            uint8_t     itemMasterImageSize; // imageSize: CIF=0, 4CIF=1, 720P=2, 1080P=3.
            uint8_t     itemSubImageSize;
            std::string itemName;
            std::string itemAddr;
            std::string itemResType;         //00 --- 媒体源， 01 --- 告警源
            //std::string itemCoding;
            std::string itemResSubType;
            std::string itemPrivilege;
            std::string itemStatus;
            std::string itemLongitude;  //经度
            std::string itemLatitude;   //纬度
            std::string itemRoadway;
            std::string itemPileNo;
            int16_t itemSubnum;
            std::string itemElevation;
            std::string itemDecoderTag;
            EOperateType itemOperateType;

            //for gb28181
            std::string manufacturer; //设备厂商
            std::string model;        //设备型号
            std::string owner;        //设备归属
            std::string civilcode;    //行政区域
            std::string address;      //安装地址
            std::string parental;             //是否有子设备；1--有，0--没有
            std::string registerway;          //注册方式；
            std::string secrecy;              //保密属性；0--不涉密，1--涉密
            long channels;
        };
        std::string parentAddr;
        std::string name;
        std::string coding;
        std::string status;
        std::string sn;
        int16_t subNum; // 虽然它的值可以由items.size()得出,但还是忠实的将协议的内容转换出来.
        std::vector< SItem > subList;
    };
    struct SCatalogResponse
    {
        //std::string status;
        bool result;
        std::string sn;
    };

// 3. 心跳信息: 发请求, 收回应.
// struct SKeepalive
// {
//     // 无具体内容,但有xml结构.
// };
// struct SKeepaliveResponse
// {
//     bool result;
// };

// 4. 实时播放: 收请求, 发回应.
    struct SRealMedia
    {
        std::string resAddr;

        std::string privilege;
        std::vector< EFormatType> supportFormatTypes;
        std::vector< EVideoType > supportVideoTypes;
        std::vector< EAudioType > supportAudioTypes;
        int maxBitrate; // 最小值32
        // ************* UDP 2360
        ESocketType socketType;
        uint32_t sockAddr;
        uint16_t sockPort;
    };
    struct SRealMediaResponse
    {
        std::string resAddr;

        EFormatType formatType;
        EVideoType  videoType;
        EAudioType  audioType;
        int bitrate; // 最小值32
        // ************* UDP 2360
        ESocketType socketType;//国标回复结构中没有socket字段
        uint32_t sockAddr;
        uint16_t sockPort;

        //  std::string decoderTag;
    };

// 5. 历史文件列表查询: 收请求,发回应.
    struct SFileList
    {
        std::string resAddr;

        std::string filepath;
        std::string address;
        std::string recordId;
        std::string secrecy;
        int fileType;
        std::string beginTime; // 20051110T132050Z 其中T是表示时间段开始的必需字符，而TZD则是表示时区，Z是表是格林威治标准时间
        std::string endTime;
    };
    struct SFileListResponse
    {
        struct SItem
        {
            std::string itemName;
            std::string itemBeginTime;
            std::string itemEndTime;
            int itemFileSize;
        };

        std::string resAddr;
        std::string filepath;
        std::string address;
        std::string recordId;
        std::string secrecy;
        std::string sn;
        std::string resName;

        bool result;
        int realFileNum; // 总共个数.注意:files中是本次传送的个数.
        int sendFileNum; //发送个数
        std::vector< SItem > fileInfolist;
    };

// 6. 历史视频播放: 收请求,发回应.
    struct SHistoryMedia
    {
        std::string resAddr;

        std::string privilege;
        int fileType;
        std::string name;
        std::string beginTime;
        std::string endTime;
        int rtpPort;
        int maxBitrate; // 最小32
    };
    struct SHistoryMediaResponse
    {
        std::string resAddr;
        std::string playUrl;		//如 rtsp://************:554/path
        std::string	receiveAddr;	// 视频接收端地址
        bool		result;
        int			bitrate;
        int32_t		beginTime;
        int32_t		endTime;

    };

// 7. 云台控制: 收请求,发回应.
    struct SPtzCommand
    {
        std::string resAddr;

        std::string sn;
        uint32_t command;
        uint16_t commandParam1;
        uint16_t commandParam2;
    };
    struct SPtzCommandResponse
    {
        std::string resAddr;

        std::string sn;
        bool result;
        uint16_t command;
        uint16_t commandParam1;
        uint16_t commandParam2;
    };




// 9. 告警预订: 收请求,发回应. ---------------告警相关的现在还没有,所以按DB33定的.---------------
    struct SAlarmSubscribe
    {
        std::string privilege;
        std::string resAddr;
        int alarmLevel;
        EAlarmType alarmType;
    };
// struct SAlarmSubscribeResponse
// {
//     bool result;
// };

// 10. 告警预订成功与否的通知: 发请求,收回应.
// 暂未实现.

// 11. 告警上报: 发请求,收回应.
    struct SAlarmNotify
    {
        std::string resAddr;
        int alarmLevel;
        EAlarmType alarmType;
        bool alarmStatus;
        std::string alarmData; // 就是一段关于告警的数据,字义为字符串.
        std::string beginTime;
    };
// struct SAlarmNotifyResponse
// {
//     bool result;
// };


    struct SKeepalive
    {
        // 无具体内容,但有xml结构.
    };


    struct SPresetList
    {
        std::string resAddr;

        std::string privilege;
        int fromIndex;
        int toIndex; // [ from, to ]
    };

    struct SDeviceReboot
    {
        std::string sn;
    };

    struct SRecordContronl
    {
        std::string sn;
        //true--start, false--stop
        bool flag;
    };

    struct SRecordContronlResponse
    {
        std::string devid;
        std::string sn;
        bool flag;
        bool result;
    };

    struct SGuardContronl
    {
        std::string sn;
        bool flag;
    };

    struct SGuardContronlResponse
    {
        std::string devid;
        std::string sn;
        bool flag;
        bool result;
    };

    struct SAlarmReset
    {
        std::string sn;
    };

    struct SAlarmResetResponse
    {
        std::string devid;
        std::string sn;
        bool result;
    };


    struct SDeviceCatalog
    {
        std::string sn;
    };

    struct SDeviceInfo
    {
        std::string sn;
    };

    struct SDeviceStatus
    {
        std::string sn;
    };


    struct SPresetListResponse
    {
        struct SItem
        {
            int itemValue; // id不是[ from, to ],可以随便跳跃.
            std::string itemDescription;
        };
        std::string resAddr;
        bool result;
        int realNum; // 总共个数.注意:files中是本次传送的个数.
        int fromIndex;
        int toIndex; // [ from, to ]
        std::vector< SItem > presetInfoList;
    };

    enum EAlarmStatus
    {
        EALARMSTATUS_ACTIVED   = 0,  ///< 活跃
        EALARMSTATUS_INACTIVED       ///< 清除
    };

    struct SDeviceStatusResponse
    {
        //暂不支持告警
        //struct SAlarmItem
        //{
        //	boost::uuids::uuid	alarmUuid;		// 告警UUID
        //	EAlarmStatus		alarmStatus;	// 告警状态
        //};
        //
        //std::vector< SAlarmItem > alarmStatusList;	// 告警状态列表

        std::string resAddr;					// 设备ID
        std::string sn;                         // SN
        bool result;
        bool isEncoder;							// 是否编码
        bool isRecord;							// 是否录像
        bool isOnline;							// 是否在线
        bool isNormal;							// 是否正常工作
        std::string strFaultReason;				// 故障原因
    };

    struct SDeviceAlarmStatusResponse
    {
        //暂不支持告警
        //struct SAlarmItem
        //{
        //	boost::uuids::uuid	alarmUuid;		// 告警UUID
        //	EAlarmStatus		alarmStatus;	// 告警状态
        //};
        //
        //std::vector< SAlarmItem > alarmStatusList;	// 告警状态列表

        std::string resAddr;					// 设备ID
        std::string sn;                         // SN
        bool isOnline;							// 是否在线
        bool isNormal;							// 是否正常工作
        std::string strFaultReason;				// 故障原因
        bool isDuty;
        bool isAlarm;
    };

    struct SDeviceInfoResponse
    {
        std::string sn;
        std::string resAddr;
        std::string deviceType;		//未使用
        std::string manufacturer;
        std::string model;
        std::string firmware;
        uint32_t	maxCamera;	//未使用
        uint32_t	maxAlarm;	//Channel
    };

    struct SHistoryRtspInfo
    {
        uint16_t	itype;			// 0 表示播放，1表示暂停
        double		dscale;			// 仅当itype = 0 时有效 正常播放速率为1.0
        uint32_t	dRangeStart;	// 开始时间段
        uint32_t	dRangeEnd;		// 结束时间段
    };

    struct SDeviceConfigQueryBasicParam
    {

        std::string name;
        std::string deviceid;
        std::string sipserverid;
        std::string sipserverip;
        std::string sipserverport;
        std::string domainName;
        std::string expiration;
        std::string password;
        std::string heartBeatInterval;
        std::string heartBeatCount;
    };

    struct SVideoParamOpt
    {
        std::string videoFormatOpt;
        std::string resolutionOpt;
        std::string frameRateOpt;
        std::string bitRateTypeOpt;
        std::string videoBitRateOpt;
        std::string downloadSpeedOpt;
    };

    struct SVideoParamAttribute
    {

        std::string streamName;
        std::string videoFormat;
        std::string resolution;
        std::string frameRate;
        std::string bitRateType;
        std::string videoBitRate;
    };

    struct SAudioParamOpt
    {
        std::string audioFormatOpt;
        std::string audioBitRateOpt;
        std::string samplingRateOpt;
    };

    struct SAideoParamAttribute
    {
        std::string streamName;
        std::string audioFormat;
        std::string audioBitRate;
        std::string samplingRate;
    };

    struct SDeviceConfigQueryResult
    {
        bool isOk;
        boost::shared_ptr< SDeviceConfigQueryBasicParam > basicParam;
        boost::shared_ptr< SVideoParamOpt > videoParamOpt;
        std::vector< SVideoParamAttribute > videoParamConfigs;
        boost::shared_ptr< SAudioParamOpt > audioParamOpt;
        std::vector< SAideoParamAttribute > audioParamConfigs;
    };

    struct SAlarmInfo
    {
        std::string alarmPriority;
        std::string alarmMethod;
        std::string alarmTime;
    };

    struct SAlarmSubscribeParam
    {
        std::string resId;
        int startAlarmPriority;
        int endAlarmPriority;
        std::string alarmMethod;
        uint32_t startTime;
        uint32_t endTime;
    };

    struct SAlarmParam
    {
        std::string resId;
        int priority;
        int method;
        uint32_t time;
        std::string description;
        float longitude;
        float latitude;
    };

}


#endif
