#ifndef DATAMGR_HPP_
#define DATAMGR_HPP_

#include "wtoe/PackageManager/PackageManagerExp.hpp"
#include "DataMgr/include/DataMgrCfg.hpp"
#include "DataMgr/include/DataMgrItf.hpp"

namespace gum
{
class CDataMgr : public IDataMgr, public wtoe::IServiceEntry
{
public:
	CDataMgr();
	virtual ~CDataMgr();

	virtual bool init(const std::string& ip, const std::string& db);
    virtual bool fini();
	virtual void showVersion();
    virtual void showStatus( int remoteId = 0, const std::string& szKey = "" );

protected:
    virtual bool startupImpl();
    virtual bool shutdownImpl();

private:


};

} //namespace gum

#endif // DATAMGR_HPP_


