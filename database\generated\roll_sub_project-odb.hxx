// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#ifndef ROLL_SUB_PROJECT_ODB_HXX
#define ROLL_SUB_PROJECT_ODB_HXX

#include <odb/version.hxx>

#include <odb/pre.hxx>

#include "roll_sub_project.h"

#include <memory>
#include <cstddef>
#include <utility>

#include <odb/core.hxx>
#include <odb/traits.hxx>
#include <odb/callback.hxx>
#include <odb/wrapper-traits.hxx>
#include <odb/pointer-traits.hxx>
#include <odb/container-traits.hxx>
#include <odb/no-op-cache-traits.hxx>
#include <odb/result.hxx>
#include <odb/simple-object-result.hxx>

#include <odb/details/unused.hxx>
#include <odb/details/shared-ptr.hxx>

namespace odb
{
  // RollSubProject
  //
  template <>
  struct class_traits< ::db::RollSubProject >
  {
    static const class_kind kind = class_object;
  };

  template <>
  class access::object_traits< ::db::RollSubProject >
  {
    public:
    typedef ::db::RollSubProject object_type;
    typedef ::db::RollSubProject* pointer_type;
    typedef odb::pointer_traits<pointer_type> pointer_traits;

    static const bool polymorphic = false;

    typedef long unsigned int id_type;

    static const bool auto_id = true;

    static const bool abstract = false;

    static id_type
    id (const object_type&);

    typedef
    no_op_pointer_cache_traits<pointer_type>
    pointer_cache_traits;

    typedef
    no_op_reference_cache_traits<object_type>
    reference_cache_traits;

    static void
    callback (database&, object_type&, callback_event);

    static void
    callback (database&, const object_type&, callback_event);
  };
}

#include <odb/details/buffer.hxx>

#include <odb/mysql/version.hxx>
#include <odb/mysql/forward.hxx>
#include <odb/mysql/binding.hxx>
#include <odb/mysql/mysql-types.hxx>
#include <odb/mysql/query.hxx>

namespace odb
{
  // RollSubProject
  //
  template <typename A>
  struct query_columns< ::db::RollSubProject, id_mysql, A >
  {
    // id
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    id_type_;

    static const id_type_ id;

    // rollProjId
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    rollProjId_type_;

    static const rollProjId_type_ rollProjId;

    // startTime
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    startTime_type_;

    static const startTime_type_ startTime;

    // programId
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    programId_type_;

    static const programId_type_ programId;

    // isDel
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        bool,
        mysql::id_long >::query_type,
      mysql::id_long >
    isDel_type_;

    static const isDel_type_ isDel;

    // isEnable
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        bool,
        mysql::id_long >::query_type,
      mysql::id_long >
    isEnable_type_;

    static const isEnable_type_ isEnable;
  };

  template <typename A>
  const typename query_columns< ::db::RollSubProject, id_mysql, A >::id_type_
  query_columns< ::db::RollSubProject, id_mysql, A >::
  id (A::table_name, "`id`", 0);

  template <typename A>
  const typename query_columns< ::db::RollSubProject, id_mysql, A >::rollProjId_type_
  query_columns< ::db::RollSubProject, id_mysql, A >::
  rollProjId (A::table_name, "`project_id`", 0);

  template <typename A>
  const typename query_columns< ::db::RollSubProject, id_mysql, A >::startTime_type_
  query_columns< ::db::RollSubProject, id_mysql, A >::
  startTime (A::table_name, "`start_time`", 0);

  template <typename A>
  const typename query_columns< ::db::RollSubProject, id_mysql, A >::programId_type_
  query_columns< ::db::RollSubProject, id_mysql, A >::
  programId (A::table_name, "`program_id`", 0);

  template <typename A>
  const typename query_columns< ::db::RollSubProject, id_mysql, A >::isDel_type_
  query_columns< ::db::RollSubProject, id_mysql, A >::
  isDel (A::table_name, "`is_del`", 0);

  template <typename A>
  const typename query_columns< ::db::RollSubProject, id_mysql, A >::isEnable_type_
  query_columns< ::db::RollSubProject, id_mysql, A >::
  isEnable (A::table_name, "`is_enable`", 0);

  template <typename A>
  struct pointer_query_columns< ::db::RollSubProject, id_mysql, A >:
    query_columns< ::db::RollSubProject, id_mysql, A >
  {
  };

  template <>
  class access::object_traits_impl< ::db::RollSubProject, id_mysql >:
    public access::object_traits< ::db::RollSubProject >
  {
    public:
    struct id_image_type
    {
      unsigned long long id_value;
      my_bool id_null;

      std::size_t version;
    };

    struct image_type
    {
      // id
      //
      unsigned long long id_value;
      my_bool id_null;

      // rollProjId
      //
      unsigned long long rollProjId_value;
      my_bool rollProjId_null;

      // startTime
      //
      details::buffer startTime_value;
      unsigned long startTime_size;
      my_bool startTime_null;

      // programId
      //
      unsigned long long programId_value;
      my_bool programId_null;

      // isDel
      //
      int isDel_value;
      my_bool isDel_null;

      // isEnable
      //
      int isEnable_value;
      my_bool isEnable_null;

      std::size_t version;
    };

    struct extra_statement_cache_type;

    using object_traits<object_type>::id;

    static id_type
    id (const id_image_type&);

    static id_type
    id (const image_type&);

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&,
          mysql::statement_kind);

    static void
    bind (MYSQL_BIND*, id_image_type&);

    static bool
    init (image_type&,
          const object_type&,
          mysql::statement_kind);

    static void
    init (object_type&,
          const image_type&,
          database*);

    static void
    init (id_image_type&, const id_type&);

    typedef mysql::object_statements<object_type> statements_type;

    typedef mysql::query_base query_base_type;

    static const std::size_t column_count = 6UL;
    static const std::size_t id_column_count = 1UL;
    static const std::size_t inverse_column_count = 0UL;
    static const std::size_t readonly_column_count = 0UL;
    static const std::size_t managed_optimistic_column_count = 0UL;

    static const std::size_t separate_load_column_count = 0UL;
    static const std::size_t separate_update_column_count = 0UL;

    static const bool versioned = false;

    static const char persist_statement[];
    static const char find_statement[];
    static const char update_statement[];
    static const char erase_statement[];
    static const char query_statement[];
    static const char erase_query_statement[];

    static const char table_name[];

    static void
    persist (database&, object_type&);

    static pointer_type
    find (database&, const id_type&);

    static bool
    find (database&, const id_type&, object_type&);

    static bool
    reload (database&, object_type&);

    static void
    update (database&, const object_type&);

    static void
    erase (database&, const id_type&);

    static void
    erase (database&, const object_type&);

    static result<object_type>
    query (database&, const query_base_type&);

    static unsigned long long
    erase_query (database&, const query_base_type&);

    public:
    static bool
    find_ (statements_type&,
           const id_type*);

    static void
    load_ (statements_type&,
           object_type&,
           bool reload);
  };

  template <>
  class access::object_traits_impl< ::db::RollSubProject, id_common >:
    public access::object_traits_impl< ::db::RollSubProject, id_mysql >
  {
  };

  // RollSubProject
  //
}

#include "roll_sub_project-odb.ixx"

#include <odb/post.hxx>

#endif // ROLL_SUB_PROJECT_ODB_HXX
