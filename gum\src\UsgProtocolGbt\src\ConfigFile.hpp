
#ifndef CONFIGFILE_HPP_
#define CONFIGFILE_HPP_


#include <string>
#include <map>

#include <boost/shared_ptr.hpp>
#include <boost/thread/mutex.hpp>

#include "../include/Gb28181Struct.hpp"

struct pj_xml_node;
struct pj_pool_t;

namespace gb28181
{

    class CConfigFile
    {
    public:
        CConfigFile();

        bool init( const std::string &relativePath );

    public:
        bool getResUuid( const std::string &addrCode, boost::uuids::uuid &resUid );
        bool getDecoderTag( const std::string &addrCode, std::string &dt );
        bool getSubImageSize( const std::string &addrCode, uint8_t &imageSize );
        bool getMasterImageSize( const std::string &addrCode, uint8_t &imageSize );
        bool getResName( const std::string &addrCode, std::string &resName );

        bool getResAddrCode( const boost::uuids::uuid &resUid, std::string &addrCode );

        // from从1开始.
        bool getResMulti( const size_t from, const size_t to, std::vector< SCatalog::SItem > &ress );
        void setCatalogs( const std::vector< SCatalog::SItem > &res );
        bool getItem( const std::string &addrCode, SCatalog::SItem &item );
        size_t getResSize();

        SAccess getRemote();
        SAccess getLocal();

        void updataItem( const SCatalog::SItem &item );

    private:
        bool loadAccess( const std::string &accessFile );
        bool loadRes( const std::string &resFile );

        bool pOneAccess( pj_pool_t *pool, pj_xml_node *p, SAccess &sa, bool isHasUserNamdAndPasswd = false );
        bool pOneItem( pj_pool_t *pool, pj_xml_node *p, SCatalog::SItem &oneItem );

    private:
        SAccess m_remoteAccess;
        SAccess m_localAccess;

        std::vector< SCatalog::SItem > m_resEx; // 初始创建,以后不修改.
        std::map< std::string, int > m_resAddrCodeMap; // 用resAddrCode映射到Res在m_resEx下的下标.
        std::map< boost::uuids::uuid, int > m_resUuidMap; // 用resUuid映射到Res在m_resEx下的下标.

        boost::mutex m_mutex;
    };

}

#endif
