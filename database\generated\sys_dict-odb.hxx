// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#ifndef SYS_DICT_ODB_HXX
#define SYS_DICT_ODB_HXX

#include <odb/version.hxx>

#include <odb/pre.hxx>

#include "sys_dict.h"

#include <memory>
#include <cstddef>
#include <utility>

#include <odb/core.hxx>
#include <odb/traits.hxx>
#include <odb/callback.hxx>
#include <odb/wrapper-traits.hxx>
#include <odb/pointer-traits.hxx>
#include <odb/container-traits.hxx>
#include <odb/no-op-cache-traits.hxx>
#include <odb/result.hxx>
#include <odb/simple-object-result.hxx>

#include <odb/details/unused.hxx>
#include <odb/details/shared-ptr.hxx>

namespace odb
{
  // SysDict
  //
  template <>
  struct class_traits< ::db::SysDict >
  {
    static const class_kind kind = class_object;
  };

  template <>
  class access::object_traits< ::db::SysDict >
  {
    public:
    typedef ::db::SysDict object_type;
    typedef ::db::SysDict* pointer_type;
    typedef odb::pointer_traits<pointer_type> pointer_traits;

    static const bool polymorphic = false;

    typedef long unsigned int id_type;

    static const bool auto_id = true;

    static const bool abstract = false;

    static id_type
    id (const object_type&);

    typedef
    no_op_pointer_cache_traits<pointer_type>
    pointer_cache_traits;

    typedef
    no_op_reference_cache_traits<object_type>
    reference_cache_traits;

    static void
    callback (database&, object_type&, callback_event);

    static void
    callback (database&, const object_type&, callback_event);
  };
}

#include <odb/details/buffer.hxx>

#include <odb/mysql/version.hxx>
#include <odb/mysql/forward.hxx>
#include <odb/mysql/binding.hxx>
#include <odb/mysql/mysql-types.hxx>
#include <odb/mysql/query.hxx>

namespace odb
{
  // SysDict
  //
  template <typename A>
  struct query_columns< ::db::SysDict, id_mysql, A >
  {
    // id
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    id_type_;

    static const id_type_ id;

    // dictName
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    dictName_type_;

    static const dictName_type_ dictName;

    // dictType
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    dictType_type_;

    static const dictType_type_ dictType;

    // dictCode
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    dictCode_type_;

    static const dictCode_type_ dictCode;

    // dictCodeCnValue
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    dictCodeCnValue_type_;

    static const dictCodeCnValue_type_ dictCodeCnValue;

    // dictCodeEnValue
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    dictCodeEnValue_type_;

    static const dictCodeEnValue_type_ dictCodeEnValue;
  };

  template <typename A>
  const typename query_columns< ::db::SysDict, id_mysql, A >::id_type_
  query_columns< ::db::SysDict, id_mysql, A >::
  id (A::table_name, "`id`", 0);

  template <typename A>
  const typename query_columns< ::db::SysDict, id_mysql, A >::dictName_type_
  query_columns< ::db::SysDict, id_mysql, A >::
  dictName (A::table_name, "`name`", 0);

  template <typename A>
  const typename query_columns< ::db::SysDict, id_mysql, A >::dictType_type_
  query_columns< ::db::SysDict, id_mysql, A >::
  dictType (A::table_name, "`type`", 0);

  template <typename A>
  const typename query_columns< ::db::SysDict, id_mysql, A >::dictCode_type_
  query_columns< ::db::SysDict, id_mysql, A >::
  dictCode (A::table_name, "`code`", 0);

  template <typename A>
  const typename query_columns< ::db::SysDict, id_mysql, A >::dictCodeCnValue_type_
  query_columns< ::db::SysDict, id_mysql, A >::
  dictCodeCnValue (A::table_name, "`cn_value`", 0);

  template <typename A>
  const typename query_columns< ::db::SysDict, id_mysql, A >::dictCodeEnValue_type_
  query_columns< ::db::SysDict, id_mysql, A >::
  dictCodeEnValue (A::table_name, "`en_value`", 0);

  template <typename A>
  struct pointer_query_columns< ::db::SysDict, id_mysql, A >:
    query_columns< ::db::SysDict, id_mysql, A >
  {
  };

  template <>
  class access::object_traits_impl< ::db::SysDict, id_mysql >:
    public access::object_traits< ::db::SysDict >
  {
    public:
    struct id_image_type
    {
      unsigned long long id_value;
      my_bool id_null;

      std::size_t version;
    };

    struct image_type
    {
      // id
      //
      unsigned long long id_value;
      my_bool id_null;

      // dictName
      //
      details::buffer dictName_value;
      unsigned long dictName_size;
      my_bool dictName_null;

      // dictType
      //
      details::buffer dictType_value;
      unsigned long dictType_size;
      my_bool dictType_null;

      // dictCode
      //
      int dictCode_value;
      my_bool dictCode_null;

      // dictCodeCnValue
      //
      details::buffer dictCodeCnValue_value;
      unsigned long dictCodeCnValue_size;
      my_bool dictCodeCnValue_null;

      // dictCodeEnValue
      //
      details::buffer dictCodeEnValue_value;
      unsigned long dictCodeEnValue_size;
      my_bool dictCodeEnValue_null;

      std::size_t version;
    };

    struct extra_statement_cache_type;

    using object_traits<object_type>::id;

    static id_type
    id (const id_image_type&);

    static id_type
    id (const image_type&);

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&,
          mysql::statement_kind);

    static void
    bind (MYSQL_BIND*, id_image_type&);

    static bool
    init (image_type&,
          const object_type&,
          mysql::statement_kind);

    static void
    init (object_type&,
          const image_type&,
          database*);

    static void
    init (id_image_type&, const id_type&);

    typedef mysql::object_statements<object_type> statements_type;

    typedef mysql::query_base query_base_type;

    static const std::size_t column_count = 6UL;
    static const std::size_t id_column_count = 1UL;
    static const std::size_t inverse_column_count = 0UL;
    static const std::size_t readonly_column_count = 0UL;
    static const std::size_t managed_optimistic_column_count = 0UL;

    static const std::size_t separate_load_column_count = 0UL;
    static const std::size_t separate_update_column_count = 0UL;

    static const bool versioned = false;

    static const char persist_statement[];
    static const char find_statement[];
    static const char update_statement[];
    static const char erase_statement[];
    static const char query_statement[];
    static const char erase_query_statement[];

    static const char table_name[];

    static void
    persist (database&, object_type&);

    static pointer_type
    find (database&, const id_type&);

    static bool
    find (database&, const id_type&, object_type&);

    static bool
    reload (database&, object_type&);

    static void
    update (database&, const object_type&);

    static void
    erase (database&, const id_type&);

    static void
    erase (database&, const object_type&);

    static result<object_type>
    query (database&, const query_base_type&);

    static unsigned long long
    erase_query (database&, const query_base_type&);

    public:
    static bool
    find_ (statements_type&,
           const id_type*);

    static void
    load_ (statements_type&,
           object_type&,
           bool reload);
  };

  template <>
  class access::object_traits_impl< ::db::SysDict, id_common >:
    public access::object_traits_impl< ::db::SysDict, id_mysql >
  {
  };

  // SysDict
  //
}

#include "sys_dict-odb.ixx"

#include <odb/post.hxx>

#endif // SYS_DICT_ODB_HXX
