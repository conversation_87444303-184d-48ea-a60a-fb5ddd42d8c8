cmake_minimum_required (VERSION 3.5.2)

project(ai-log)
## 目标生成
set(TARGET_LIBRARY "ai_log")
# 输出目录
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/../out/lib)

add_compile_options(-std=c++17 -fPIC -fstack-protector-all -Wall -DBOOST_LOG_DYN_LINK)

# boost
set(BOOST_HOME "/opt/boost")
set(BOOST_LIB
        boost_log
        boost_log_setup
        boost_filesystem
        )

# 头文件
include_directories(
        ${BOOST_HOME}/include/
)

# 库路径
link_directories(
        ${BOOST_HOME}/lib/
)

FILE(GLOB src "src/*.cpp")

#file(GLOB_RECURSE sources CONFIGURE_DEPENDS "*.cpp")
SET(ALL_SRC ${include} ${src} )

# 生成动态库
ADD_LIBRARY(${TARGET_LIBRARY} SHARED ${ALL_SRC})
target_link_libraries(${TARGET_LIBRARY} pthread ${BOOST_LIB} )