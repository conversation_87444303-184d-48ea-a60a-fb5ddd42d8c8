/**
 * Project FVM
 */

#pragma once
#include "platform_puller.h"
#include "platform_puller.h"
#include "stream/element/circular_queue.h"

/**
 * @brief: MPC输入
 */
namespace fvm::stream
{
    class SDKMPCPuller : public PlatformPuller{
    public:
        SDKMPCPuller(FrontPlatformPtr platform);
        // （新增）3D定位
        bool focusRect(int xTop, int yTop, int xBottom, int yBottom) override;
        bool callCameraPreset(int presetId) override;
        bool saveCameraPreset(int presetId) override;

    private:
        void initOptions() override;
        bool open() override;
        void close() override;
        void process() override;

        void dealData(uint8_t* buf, int buf_size);

        /**
        * @brief 控制云台
        * @param[in] action:  云台动作 1：向左 2：向右 3：向上 4：向下 5：变倍短 6：变倍长 7：聚焦近 8：聚焦远 9：光圈小 10：光圈大 11：灯光关 12：灯光开
        * @param[in] step:  步长  主要用于控制云台转动方向的操作，1-8为步长值，1为最小步长，8为最大步长
        */
        bool controlPtz(int action, int step);
    private:
        uint8_t* ioContextBuffer = nullptr;
        AVIOContext* pb = nullptr;
        CCircularQueue streamQueue;
    };
}