
#include "../include/command_process.h"
#include "console.h"
#include <pthread.h>

#include <termio.h>
#include <cstdio>
#include <unistd.h>
#include <cstring>



using namespace std;

namespace cli{

    CommandProcess::~CommandProcess()
    {
        stop();
    }

    /**
     * @brief      分割字符串
     * @param[in]  input: 输入字符串 delim: 分割符，默认通过空格进行分割
     * @return     分割后的字符串数组
     */
    static std::vector<std::string> stringSplit(const std::string& input, const std::string& delim = " ")
    {
        std::vector<std::string> output;
        size_t pos = 0;
        size_t len = input.length();
        size_t delimLen = delim.length();

        if (delimLen == 0)
            return output;

        while (pos < len)
        {
            const auto findPos = input.find(delim, pos);
            if (findPos != std::string::npos)
            {
                string substr = input.substr(pos, findPos - pos);
                if (!substr.empty())                         //!< 只保存分隔符后的非空字符串
                    output.emplace_back(substr);
                pos = findPos + delimLen;
            }
            else
            {
                output.emplace_back(input.substr(pos, len - pos));
                break;
            }
        }
        return output;
    }

    /**
     * @brief      修剪掉字符串头部空白字符或其他字符
     * @param[in]  str: 输入字符串 trim: 需要删除的字符
     * @return     修剪后的字符串
     */
    static std::string trimHeadSpace(const std::string& str, const std::string& trim = " \t\r\n")
    {
        std::string::size_type first = str.find_first_not_of(trim);
        std::string::size_type last = str.size();

        if (first == std::string::npos)
        {
            return "";
        }

        return std::move(str.substr(first, last - first + 1));
    }

    /**
     * @brief      修剪掉字符串头部空白字符或其他字符
     * @param[in]  str: 输入字符串 trim: 需要删除的字符
     * @return     修剪后的字符串
     */
    static std::string trimSpace(const std::string& str, const std::string& trim = " \t\r\n")
    {
        std::string::size_type first = str.find_first_not_of(trim);
        std::string::size_type last = str.find_last_not_of(trim);

        if (first == std::string::npos || last == std::string::npos)
        {
            return "";
        }

        return std::move(str.substr(first, last - first + 1));
    }


    bool CommandProcess::execCommand(const std::string& funcName, const consoleParamList& params)
    {
        std::lock_guard<std::mutex> lock(commandsLock);
        auto it = std::find_if(commands.begin(),commands.end(),[&](const Command& cmd){
            return cmd.name == funcName;
        });
        if (it == commands.end()){
            std::cerr << "command " << funcName << " not found !" << endl;
            return false;
        }
        consoleFunction consoleFunc = it->func;
        commandsLock.unlock();

        consoleFunc(params);
        return true;
    }

    void CommandProcess::addCommand(const std::string& funcName, const consoleFunction& func, const std::string& description)
    {
        Command command{funcName,func, description};
        std::lock_guard<std::mutex> lock(commandsLock);
        commands.emplace_back(command);
    }

    std::vector<Command> CommandProcess::getCommands(){
        std::lock_guard<std::mutex> lock(commandsLock);
        return commands;
    }

    void CommandProcess::removeCommand(const std::string& funcName)
    {
        std::lock_guard<std::mutex> lock(commandsLock);
        auto it = std::find_if(commands.begin(),commands.end(),[&](const Command& cmd){
            return cmd.name == funcName;
        });
        if (it == commands.end()){
            return;
        }
        commands.erase(it);
    }

#if 0
    void CommandProcess::consoleRun()
    {
        std::string inputStr = {};
        int ch = -1;

        while (running)
        {
            int inputCh = std::getchar();
            if ('>' != inputCh)       //!< 通过enter确定输入结果
            {
               continue;
            }

            inputCh =  cli::getChar();   //!<  清除一下输入缓冲区数据
            printf("\033[42m\033[1m>input mode............\033[0m \n");
            fflush(stdout);
            while (running)
            {
                ch =  cli::getChar(); //!< 输入不需要通过enter确认

                if (-1 == ch)
                {
                    std::cout << "input error" << std::endl;
                    inputStr.clear();
                    continue;
                }
                else if ('\b' == ch) //!< BACKSPACE
                {
                    if (!inputStr.empty())
                        inputStr.pop_back();
                }
                else if ('\n' == ch) //!< ENTER  输入完后确认
                {
                    //std::cout << "input command: " << inputStr <<  std::endl;
                    clearLine();
                    handleInputCommand(inputStr);
                    inputStr.clear();
                    continue;
                }
                else if (27 == ch)   //!< ESCAPE 退出输入模式
                {
                    printf("\033[42m\033[1m>exit input mode........\033[0m\n");
                    fflush(stdout);
                    inputStr.clear();
                    break;
                }
                else
                {
                    inputStr.push_back((char)ch);
                }

                clearLine();
                if (inputStr.empty())
                    continue;
                setCursorEnable(true);
                printf("\033[42m\033[1m>: %s\r\033[0m", inputStr.c_str());
                fflush(stdout);

            }
        }
    }
#endif
    void CommandProcess::consoleRun()
    {
        std::string inputStr = {};
        int ch = -1;

        while (running)
        {
            int inputCh = std::getchar();
            if ('>' != inputCh)       //!< 通过enter确定输入结果
            {
                continue;
            }

            printf("\r\n\033[42m\033[1m>input mode............\033[0m \n");
            fflush(stdout);
            inputCh = std::getchar();
            while (running)
            {
                inputStr.clear();
                std::getline(std::cin, inputStr);
                clearLine();
                if (inputStr.empty())
                    continue;

                if ((inputStr == "exit") || (inputStr == "<")) //!< 退出输入模式
                {
                    printf("\033[42m\033[1m>exit input mode........\033[0m\n");
                    fflush(stdout);
                    inputStr.clear();
                    break;
                }

                setCursorEnable(true);
                printf("\033[42m\033[1m>: %s\n\033[0m", inputStr.c_str());
                fflush(stdout);
                clearLine();
                handleInputCommand(inputStr);
                inputStr.clear();
            }
        }
    }

    bool CommandProcess::handleInputCommand(const std::string& inputStr)
    {
        bool ret = false;
        try {

            if (inputStr.empty())
                return false;

            std::string input = trimHeadSpace(inputStr); //!< 删除头部空白字符

            //! 将用户输入的字符串拆分成 命令 和 参数
            vector<string> inputSplit = stringSplit(input, " ");
            if (inputSplit.empty())
            {
                std::cout << "input command error " << std::endl;
                return false;
            }

            string commandName = inputSplit.at(0);
            std::vector<std::string> params = {};
            params.assign(inputSplit.begin() + 1, inputSplit.end());

            ret = execCommand(commandName, params);

        }
        catch (std::invalid_argument&) {
            std::cerr <<"command has invalid argument: " << inputStr << std::endl;
            return false;
        }
        catch (std::out_of_range&) {
            std::cerr << " command out of range: " << inputStr << std::endl;
            return false;
        }
        catch (std::exception& e){
            std::cerr <<"caught " << e.what() << "command parse error: " << inputStr << std::endl;
            return false;
        }
        catch (...){
            std::cerr <<"caught an anonymous exception, command parse error: " << inputStr << std::endl;
            return false;
        }
        if (!ret)
            return false;


        return true;

    }

    void CommandProcess::start()
    {
        if (running)
            return;

        tcgetattr(0, &rawTermios);            //!< 保存现场
        running = true;
        consoleThread = std::thread(&CommandProcess::consoleRun, this);
    }

    void CommandProcess::stop()
    {
        if (!running)
            return;

        running = false;
        pthread_cancel(consoleThread.native_handle());
        if (consoleThread.joinable())
            consoleThread.join();

        tcsetattr(STDIN_FILENO, TCSANOW, &rawTermios); //!< kill线程后恢复现场
    }
}