#include "UsgManager/src/SipPtzController.hpp"
#include "UsgManager.hpp"

namespace usg
{

CSipPtzController::CSipPtzController()
{

}

CSipPtzController::~CSipPtzController()
{

}

bool CSipPtzController::queryPreposition( const boost::uuids::uuid &resId, std::map< uint8_t, std::string > &infos )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;

    return csg->onqueryPreposition( resId, infos );
}

bool CSipPtzController::up( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;

    return csg->onReceivePtzCommand( EPTZCOMMAND_UP, resId, speed );
}

bool CSipPtzController::down( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;

    return csg->onReceivePtzCommand( EPTZCOMMAND_DOWN, resId, speed );
}

bool CSipPtzController::left( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;
	
	return csg->onReceivePtzCommand( EPTZCOMMAND_LEFT, resId, speed );
}

bool CSipPtzController::right( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;

    return csg->onReceivePtzCommand( EPTZCOMMAND_RIGHT, resId, speed );
}

bool CSipPtzController::stop( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;

    return csg->onReceivePtzCommand( EPTZCOMMAND_STOP, resId, speed );
}

bool CSipPtzController::switchPreposition( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId, const uint8_t index )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;
       
	return csg->onReceivePtzCommand( EPTZCOMMAND_SWITCH, resId, index );
}

bool CSipPtzController::focusNear( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;

    return csg->onReceivePtzCommand( EPTZCOMMAND_FOCUSNEAR, resId, 1 );
}

bool CSipPtzController::focusFar( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;
        
	return csg->onReceivePtzCommand( EPTZCOMMAND_FOCUSFAR, resId, 1 );
}

bool CSipPtzController::zoomOut( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;
        
	return csg->onReceivePtzCommand( EPTZCOMMAND_ZOOMOUT, resId, 1 );
}

bool CSipPtzController::zoomIn( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;

    return csg->onReceivePtzCommand( EPTZCOMMAND_ZOOMIN, resId, 1 );
}

bool CSipPtzController::apertureWide( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;
        
	return csg->onReceivePtzCommand( EPTZCOMMAND_APERTUREWIDE, resId, 1 );
}

bool CSipPtzController::apertureTele( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;
        
	return csg->onReceivePtzCommand( EPTZCOMMAND_APERTURETELE, resId, 1 );
}

bool CSipPtzController::wiperOn( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;
       
	return csg->onReceivePtzCommand( EPTZCOMMAND_WIPERON, resId, 1 );
}

bool CSipPtzController::wiperOff( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;
        
	return csg->onReceivePtzCommand( EPTZCOMMAND_WIPEROFF, resId, 1 );
}

bool CSipPtzController::ledOn( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;
       
	return csg->onReceivePtzCommand( EPTZCOMMAND_LEDON, resId, 0 );
}

bool CSipPtzController::ledOff( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;
        
	return csg->onReceivePtzCommand( EPTZCOMMAND_LEDOFF, resId, 0 );
}

bool CSipPtzController::canControlPtz( const boost::uuids::uuid &resId, bool &retbool )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;
        
	return csg->canControlPtz(  resId, retbool );
}

bool CSipPtzController::init()
{
    return true;
}

bool CSipPtzController::fini()
{
    return true;
}

bool CSipPtzController::focusStop( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId )
{
	boost::shared_ptr< IUsgManager > sg;
	if( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	if( sg == 0 ) return false;

	boost::shared_ptr< CUsgManager > csg = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( csg == 0 ) return false;

	return csg->onReceivePtzCommand( EPTZCOMMAND_FOCUSSTOP, resId, 1 );
}

}