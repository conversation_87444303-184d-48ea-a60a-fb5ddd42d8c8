// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#include <odb/pre.hxx>

#include "process_config-odb.hxx"

#include <cassert>
#include <cstring>  // std::memcpy


#include <odb/mysql/traits.hxx>
#include <odb/mysql/database.hxx>
#include <odb/mysql/transaction.hxx>
#include <odb/mysql/connection.hxx>
#include <odb/mysql/statement.hxx>
#include <odb/mysql/statement-cache.hxx>
#include <odb/mysql/simple-object-statements.hxx>
#include <odb/mysql/container-statements.hxx>
#include <odb/mysql/exceptions.hxx>
#include <odb/mysql/simple-object-result.hxx>
#include <odb/mysql/enum.hxx>

namespace odb
{
  // ProcessConfig
  //

  struct access::object_traits_impl< ::db::ProcessConfig, id_mysql >::extra_statement_cache_type
  {
    extra_statement_cache_type (
      mysql::connection&,
      image_type&,
      id_image_type&,
      mysql::binding&,
      mysql::binding&)
    {
    }
  };

  access::object_traits_impl< ::db::ProcessConfig, id_mysql >::id_type
  access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  id (const id_image_type& i)
  {
    mysql::database* db (0);
    ODB_POTENTIALLY_UNUSED (db);

    id_type id;
    {
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        id,
        i.id_value,
        i.id_null);
    }

    return id;
  }

  access::object_traits_impl< ::db::ProcessConfig, id_mysql >::id_type
  access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  id (const image_type& i)
  {
    mysql::database* db (0);
    ODB_POTENTIALLY_UNUSED (db);

    id_type id;
    {
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        id,
        i.id_value,
        i.id_null);
    }

    return id;
  }

  bool access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // id
    //
    t[0UL] = 0;

    // processId
    //
    t[1UL] = 0;

    // processPort
    //
    t[2UL] = 0;

    // startChannel
    //
    t[3UL] = 0;

    // endChannel
    //
    t[4UL] = 0;

    // networkCard
    //
    if (t[5UL])
    {
      i.networkCard_value.capacity (i.networkCard_size);
      grew = true;
    }

    // gpuCard
    //
    t[6UL] = 0;

    // fvmPort
    //
    t[7UL] = 0;

    // streamType
    //
    t[8UL] = 0;

    // fvmStatus
    //
    t[9UL] = 0;

    // fvmUpdateTime
    //
    if (t[10UL])
    {
      i.fvmUpdateTime_value.capacity (i.fvmUpdateTime_size);
      grew = true;
    }

    // ivaStatus
    //
    t[11UL] = 0;

    // ivaUpdateTime
    //
    if (t[12UL])
    {
      i.ivaUpdateTime_value.capacity (i.ivaUpdateTime_size);
      grew = true;
    }

    return grew;
  }

  void access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i,
        mysql::statement_kind sk)
  {
    ODB_POTENTIALLY_UNUSED (sk);

    using namespace mysql;

    std::size_t n (0);

    // id
    //
    if (sk != statement_update)
    {
      b[n].buffer_type = MYSQL_TYPE_LONGLONG;
      b[n].is_unsigned = 1;
      b[n].buffer = &i.id_value;
      b[n].is_null = &i.id_null;
      n++;
    }

    // processId
    //
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.processId_value;
    b[n].is_null = &i.processId_null;
    n++;

    // processPort
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.processPort_value;
    b[n].is_null = &i.processPort_null;
    n++;

    // startChannel
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.startChannel_value;
    b[n].is_null = &i.startChannel_null;
    n++;

    // endChannel
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.endChannel_value;
    b[n].is_null = &i.endChannel_null;
    n++;

    // networkCard
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.networkCard_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.networkCard_value.capacity ());
    b[n].length = &i.networkCard_size;
    b[n].is_null = &i.networkCard_null;
    n++;

    // gpuCard
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.gpuCard_value;
    b[n].is_null = &i.gpuCard_null;
    n++;

    // fvmPort
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.fvmPort_value;
    b[n].is_null = &i.fvmPort_null;
    n++;

    // streamType
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.streamType_value;
    b[n].is_null = &i.streamType_null;
    n++;

    // fvmStatus
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.fvmStatus_value;
    b[n].is_null = &i.fvmStatus_null;
    n++;

    // fvmUpdateTime
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.fvmUpdateTime_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.fvmUpdateTime_value.capacity ());
    b[n].length = &i.fvmUpdateTime_size;
    b[n].is_null = &i.fvmUpdateTime_null;
    n++;

    // ivaStatus
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.ivaStatus_value;
    b[n].is_null = &i.ivaStatus_null;
    n++;

    // ivaUpdateTime
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.ivaUpdateTime_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.ivaUpdateTime_value.capacity ());
    b[n].length = &i.ivaUpdateTime_size;
    b[n].is_null = &i.ivaUpdateTime_null;
    n++;
  }

  void access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  bind (MYSQL_BIND* b, id_image_type& i)
  {
    std::size_t n (0);
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.id_value;
    b[n].is_null = &i.id_null;
  }

  bool access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  init (image_type& i,
        const object_type& o,
        mysql::statement_kind sk)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (sk);

    using namespace mysql;

    bool grew (false);

    // id
    //
    if (sk == statement_insert)
    {
      long unsigned int const& v =
        o.id;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.id_value, is_null, v);
      i.id_null = is_null;
    }

    // processId
    //
    {
      long unsigned int const& v =
        o.processId;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.processId_value, is_null, v);
      i.processId_null = is_null;
    }

    // processPort
    //
    {
      int const& v =
        o.processPort;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.processPort_value, is_null, v);
      i.processPort_null = is_null;
    }

    // startChannel
    //
    {
      int const& v =
        o.startChannel;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.startChannel_value, is_null, v);
      i.startChannel_null = is_null;
    }

    // endChannel
    //
    {
      int const& v =
        o.endChannel;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.endChannel_value, is_null, v);
      i.endChannel_null = is_null;
    }

    // networkCard
    //
    {
      ::std::string const& v =
        o.networkCard;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.networkCard_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.networkCard_value,
        size,
        is_null,
        v);
      i.networkCard_null = is_null;
      i.networkCard_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.networkCard_value.capacity ());
    }

    // gpuCard
    //
    {
      int const& v =
        o.gpuCard;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.gpuCard_value, is_null, v);
      i.gpuCard_null = is_null;
    }

    // fvmPort
    //
    {
      int const& v =
        o.fvmPort;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.fvmPort_value, is_null, v);
      i.fvmPort_null = is_null;
    }

    // streamType
    //
    {
      int const& v =
        o.streamType;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.streamType_value, is_null, v);
      i.streamType_null = is_null;
    }

    // fvmStatus
    //
    {
      int const& v =
        o.fvmStatus;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.fvmStatus_value, is_null, v);
      i.fvmStatus_null = is_null;
    }

    // fvmUpdateTime
    //
    {
      ::std::string const& v =
        o.fvmUpdateTime;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.fvmUpdateTime_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.fvmUpdateTime_value,
        size,
        is_null,
        v);
      i.fvmUpdateTime_null = is_null;
      i.fvmUpdateTime_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.fvmUpdateTime_value.capacity ());
    }

    // ivaStatus
    //
    {
      int const& v =
        o.ivaStatus;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.ivaStatus_value, is_null, v);
      i.ivaStatus_null = is_null;
    }

    // ivaUpdateTime
    //
    {
      ::std::string const& v =
        o.ivaUpdateTime;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.ivaUpdateTime_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.ivaUpdateTime_value,
        size,
        is_null,
        v);
      i.ivaUpdateTime_null = is_null;
      i.ivaUpdateTime_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.ivaUpdateTime_value.capacity ());
    }

    return grew;
  }

  void access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  init (object_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    // id
    //
    {
      long unsigned int& v =
        o.id;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.id_value,
        i.id_null);
    }

    // processId
    //
    {
      long unsigned int& v =
        o.processId;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.processId_value,
        i.processId_null);
    }

    // processPort
    //
    {
      int& v =
        o.processPort;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.processPort_value,
        i.processPort_null);
    }

    // startChannel
    //
    {
      int& v =
        o.startChannel;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.startChannel_value,
        i.startChannel_null);
    }

    // endChannel
    //
    {
      int& v =
        o.endChannel;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.endChannel_value,
        i.endChannel_null);
    }

    // networkCard
    //
    {
      ::std::string& v =
        o.networkCard;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.networkCard_value,
        i.networkCard_size,
        i.networkCard_null);
    }

    // gpuCard
    //
    {
      int& v =
        o.gpuCard;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.gpuCard_value,
        i.gpuCard_null);
    }

    // fvmPort
    //
    {
      int& v =
        o.fvmPort;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.fvmPort_value,
        i.fvmPort_null);
    }

    // streamType
    //
    {
      int& v =
        o.streamType;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.streamType_value,
        i.streamType_null);
    }

    // fvmStatus
    //
    {
      int& v =
        o.fvmStatus;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.fvmStatus_value,
        i.fvmStatus_null);
    }

    // fvmUpdateTime
    //
    {
      ::std::string& v =
        o.fvmUpdateTime;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.fvmUpdateTime_value,
        i.fvmUpdateTime_size,
        i.fvmUpdateTime_null);
    }

    // ivaStatus
    //
    {
      int& v =
        o.ivaStatus;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.ivaStatus_value,
        i.ivaStatus_null);
    }

    // ivaUpdateTime
    //
    {
      ::std::string& v =
        o.ivaUpdateTime;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.ivaUpdateTime_value,
        i.ivaUpdateTime_size,
        i.ivaUpdateTime_null);
    }
  }

  void access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  init (id_image_type& i, const id_type& id)
  {
    {
      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.id_value, is_null, id);
      i.id_null = is_null;
    }
  }

  const char access::object_traits_impl< ::db::ProcessConfig, id_mysql >::persist_statement[] =
  "INSERT INTO `wn_process_config` "
  "(`id`, "
  "`process_id`, "
  "`process_port`, "
  "`start_channel`, "
  "`end_channel`, "
  "`network_card`, "
  "`gpu_card`, "
  "`fvm_port`, "
  "`streamType`, "
  "`fvm_status`, "
  "`fvm_update_time`, "
  "`iva_status`, "
  "`iva_update_time`) "
  "VALUES "
  "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

  const char access::object_traits_impl< ::db::ProcessConfig, id_mysql >::find_statement[] =
  "SELECT "
  "`wn_process_config`.`id`, "
  "`wn_process_config`.`process_id`, "
  "`wn_process_config`.`process_port`, "
  "`wn_process_config`.`start_channel`, "
  "`wn_process_config`.`end_channel`, "
  "`wn_process_config`.`network_card`, "
  "`wn_process_config`.`gpu_card`, "
  "`wn_process_config`.`fvm_port`, "
  "`wn_process_config`.`streamType`, "
  "`wn_process_config`.`fvm_status`, "
  "`wn_process_config`.`fvm_update_time`, "
  "`wn_process_config`.`iva_status`, "
  "`wn_process_config`.`iva_update_time` "
  "FROM `wn_process_config` "
  "WHERE `wn_process_config`.`id`=?";

  const char access::object_traits_impl< ::db::ProcessConfig, id_mysql >::update_statement[] =
  "UPDATE `wn_process_config` "
  "SET "
  "`process_id`=?, "
  "`process_port`=?, "
  "`start_channel`=?, "
  "`end_channel`=?, "
  "`network_card`=?, "
  "`gpu_card`=?, "
  "`fvm_port`=?, "
  "`streamType`=?, "
  "`fvm_status`=?, "
  "`fvm_update_time`=?, "
  "`iva_status`=?, "
  "`iva_update_time`=? "
  "WHERE `id`=?";

  const char access::object_traits_impl< ::db::ProcessConfig, id_mysql >::erase_statement[] =
  "DELETE FROM `wn_process_config` "
  "WHERE `id`=?";

  const char access::object_traits_impl< ::db::ProcessConfig, id_mysql >::query_statement[] =
  "SELECT "
  "`wn_process_config`.`id`, "
  "`wn_process_config`.`process_id`, "
  "`wn_process_config`.`process_port`, "
  "`wn_process_config`.`start_channel`, "
  "`wn_process_config`.`end_channel`, "
  "`wn_process_config`.`network_card`, "
  "`wn_process_config`.`gpu_card`, "
  "`wn_process_config`.`fvm_port`, "
  "`wn_process_config`.`streamType`, "
  "`wn_process_config`.`fvm_status`, "
  "`wn_process_config`.`fvm_update_time`, "
  "`wn_process_config`.`iva_status`, "
  "`wn_process_config`.`iva_update_time` "
  "FROM `wn_process_config`";

  const char access::object_traits_impl< ::db::ProcessConfig, id_mysql >::erase_query_statement[] =
  "DELETE FROM `wn_process_config`";

  const char access::object_traits_impl< ::db::ProcessConfig, id_mysql >::table_name[] =
  "`wn_process_config`";

  void access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  persist (database& db, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    callback (db,
              static_cast<const object_type&> (obj),
              callback_event::pre_persist);

    image_type& im (sts.image ());
    binding& imb (sts.insert_image_binding ());

    if (init (im, obj, statement_insert))
      im.version++;

    im.id_value = 0;

    if (im.version != sts.insert_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_insert);
      sts.insert_image_version (im.version);
      imb.version++;
    }

    {
      id_image_type& i (sts.id_image ());
      binding& b (sts.id_image_binding ());
      if (i.version != sts.id_image_version () || b.version == 0)
      {
        bind (b.bind, i);
        sts.id_image_version (i.version);
        b.version++;
      }
    }

    insert_statement& st (sts.persist_statement ());
    if (!st.execute ())
      throw object_already_persistent ();

    obj.id = id (sts.id_image ());

    callback (db,
              static_cast<const object_type&> (obj),
              callback_event::post_persist);
  }

  void access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  update (database& db, const object_type& obj)
  {
    ODB_POTENTIALLY_UNUSED (db);

    using namespace mysql;
    using mysql::update_statement;

    callback (db, obj, callback_event::pre_update);

    mysql::transaction& tr (mysql::transaction::current ());
    mysql::connection& conn (tr.connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    id_image_type& idi (sts.id_image ());
    init (idi, id (obj));

    image_type& im (sts.image ());
    if (init (im, obj, statement_update))
      im.version++;

    bool u (false);
    binding& imb (sts.update_image_binding ());
    if (im.version != sts.update_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_update);
      sts.update_image_version (im.version);
      imb.version++;
      u = true;
    }

    binding& idb (sts.id_image_binding ());
    if (idi.version != sts.update_id_image_version () ||
        idb.version == 0)
    {
      if (idi.version != sts.id_image_version () ||
          idb.version == 0)
      {
        bind (idb.bind, idi);
        sts.id_image_version (idi.version);
        idb.version++;
      }

      sts.update_id_image_version (idi.version);

      if (!u)
        imb.version++;
    }

    update_statement& st (sts.update_statement ());
    if (st.execute () == 0)
      throw object_not_persistent ();

    callback (db, obj, callback_event::post_update);
    pointer_cache_traits::update (db, obj);
  }

  void access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  erase (database& db, const id_type& id)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    id_image_type& i (sts.id_image ());
    init (i, id);

    binding& idb (sts.id_image_binding ());
    if (i.version != sts.id_image_version () || idb.version == 0)
    {
      bind (idb.bind, i);
      sts.id_image_version (i.version);
      idb.version++;
    }

    if (sts.erase_statement ().execute () != 1)
      throw object_not_persistent ();

    pointer_cache_traits::erase (db, id);
  }

  access::object_traits_impl< ::db::ProcessConfig, id_mysql >::pointer_type
  access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  find (database& db, const id_type& id)
  {
    using namespace mysql;

    {
      pointer_type p (pointer_cache_traits::find (db, id));

      if (!pointer_traits::null_ptr (p))
        return p;
    }

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);

    if (l.locked ())
    {
      if (!find_ (sts, &id))
        return pointer_type ();
    }

    pointer_type p (
      access::object_factory<object_type, pointer_type>::create ());
    pointer_traits::guard pg (p);

    pointer_cache_traits::insert_guard ig (
      pointer_cache_traits::insert (db, id, p));

    object_type& obj (pointer_traits::get_ref (p));

    if (l.locked ())
    {
      select_statement& st (sts.find_statement ());
      ODB_POTENTIALLY_UNUSED (st);

      callback (db, obj, callback_event::pre_load);
      init (obj, sts.image (), &db);
      load_ (sts, obj, false);
      sts.load_delayed (0);
      l.unlock ();
      callback (db, obj, callback_event::post_load);
      pointer_cache_traits::load (ig.position ());
    }
    else
      sts.delay_load (id, obj, ig.position ());

    ig.release ();
    pg.release ();
    return p;
  }

  bool access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  find (database& db, const id_type& id, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);
    assert (l.locked ()) /* Must be a top-level call. */;

    if (!find_ (sts, &id))
      return false;

    select_statement& st (sts.find_statement ());
    ODB_POTENTIALLY_UNUSED (st);

    reference_cache_traits::position_type pos (
      reference_cache_traits::insert (db, id, obj));
    reference_cache_traits::insert_guard ig (pos);

    callback (db, obj, callback_event::pre_load);
    init (obj, sts.image (), &db);
    load_ (sts, obj, false);
    sts.load_delayed (0);
    l.unlock ();
    callback (db, obj, callback_event::post_load);
    reference_cache_traits::load (pos);
    ig.release ();
    return true;
  }

  bool access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  reload (database& db, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);
    assert (l.locked ()) /* Must be a top-level call. */;

    const id_type& id (object_traits_impl::id (obj));
    if (!find_ (sts, &id))
      return false;

    select_statement& st (sts.find_statement ());
    ODB_POTENTIALLY_UNUSED (st);

    callback (db, obj, callback_event::pre_load);
    init (obj, sts.image (), &db);
    load_ (sts, obj, true);
    sts.load_delayed (0);
    l.unlock ();
    callback (db, obj, callback_event::post_load);
    return true;
  }

  bool access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  find_ (statements_type& sts,
         const id_type* id)
  {
    using namespace mysql;

    id_image_type& i (sts.id_image ());
    init (i, *id);

    binding& idb (sts.id_image_binding ());
    if (i.version != sts.id_image_version () || idb.version == 0)
    {
      bind (idb.bind, i);
      sts.id_image_version (i.version);
      idb.version++;
    }

    image_type& im (sts.image ());
    binding& imb (sts.select_image_binding ());

    if (im.version != sts.select_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_select);
      sts.select_image_version (im.version);
      imb.version++;
    }

    select_statement& st (sts.find_statement ());

    st.execute ();
    auto_result ar (st);
    select_statement::result r (st.fetch ());

    if (r == select_statement::truncated)
    {
      if (grow (im, sts.select_image_truncated ()))
        im.version++;

      if (im.version != sts.select_image_version ())
      {
        bind (imb.bind, im, statement_select);
        sts.select_image_version (im.version);
        imb.version++;
        st.refetch ();
      }
    }

    return r != select_statement::no_data;
  }

  result< access::object_traits_impl< ::db::ProcessConfig, id_mysql >::object_type >
  access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));

    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.select_image_binding ());

    if (im.version != sts.select_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_select);
      sts.select_image_version (im.version);
      imb.version++;
    }

    std::string text (query_statement);
    if (!q.empty ())
    {
      text += " ";
      text += q.clause ();
    }

    q.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        text,
        false,
        true,
        q.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::object_result_impl<object_type> > r (
      new (shared) mysql::object_result_impl<object_type> (
        q, st, sts, 0));

    return result<object_type> (r);
  }

  unsigned long long access::object_traits_impl< ::db::ProcessConfig, id_mysql >::
  erase_query (database& db, const query_base_type& q)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));

    std::string text (erase_query_statement);
    if (!q.empty ())
    {
      text += ' ';
      text += q.clause ();
    }

    q.init_parameters ();
    delete_statement st (
      conn,
      text,
      q.parameters_binding ());

    return st.execute ();
  }
}

#include <odb/post.hxx>
