
#include "event_manager.h"
#include "stream/output/video_recorder.h"
#include "stream/stream_manager.h"
#include "util/config/fvm_config.h"

namespace fvm::protocol{
    using namespace fvm::stream;
    using namespace fvm::data;
    using namespace network;

    // iva事件缓存锁
    boost::fibers::mutex eventsLock;
    // iva事件缓存
    std::map<int, std::vector<network::EventOccurInfo>> eventCache;

    // iva事件与偏移发生时的时间间隔
    constexpr auto EVENT_PRESET_CHANGE_DURATION = boost::posix_time::milliseconds (500);

    // 最后一个事件id
    std::string lastEventId;



    /**
     * @brief     根据ptz预置位判断结果来过滤事件
     * @param[in] presetChangeTime: 预置位变化的时间点
     * @param[in] isPresetChange: 预置位是否发生变化
     */
    void filterEvent(boost::posix_time::ptime presetChangeTime, bool isPresetChange, int channelId)
    {
        auto detectChannelPtr = getDetectChannel(channelId);
        if (!detectChannelPtr)
            return;

        auto presetStatus = detectChannelPtr->getPtzPresetStatus();

        auto events = getEvents(channelId);
        for (auto& event : events)
        {
            if (isPresetChange)
            {
                auto eventOccurTime =  boost::posix_time::time_from_string(event.occurTime);
                if (presetStatus == PTZPresetStatus::Offset)
                {
                    //! 发生在预置位偏移时间点之后的事件都过滤掉
                    if (eventOccurTime > presetChangeTime - EVENT_PRESET_CHANGE_DURATION)
                    {
                        removeEvent(event);
                        ai::LogInfo << "filter event ! [offset], channel:" << event.channelId << " eventId:" << event.eventId;
                    }

                    continue;
                }
                else if (presetStatus == PTZPresetStatus::Restore)
                {
                    //! 发生在预置位恢复时间点之前的事件都过滤掉
                    if (eventOccurTime < presetChangeTime - EVENT_PRESET_CHANGE_DURATION)
                    {
                        removeEvent(event);
                        ai::LogInfo << "filter event ! [offset], channel:" << event.channelId << " eventId:" << event.eventId;
                    }

                    continue;
                }
            }
            //! 偏移没有发生变化或者事件不用过滤，则上报事件
            postEvent(event, detectChannelPtr);
            removeEvent(event);
        }

    }

    /**
     * @brief 添加事件到事件缓存中
     */
    void addEvent(const network::EventOccurInfo &eventOccurInfo)
    {
        std::lock_guard<boost::fibers::mutex> lock(eventsLock);
        eventCache[eventOccurInfo.channelId].emplace_back(eventOccurInfo);
    }

    /**
     * @brief 从事件缓存中移除已经过滤的事件
     */
    void removeEvent(const network::EventOccurInfo& eventOccurInfo)
    {
        std::lock_guard<boost::fibers::mutex> lock(eventsLock);

        if (eventCache.find(eventOccurInfo.channelId) == eventCache.end())
            return;

        auto& events = eventCache[eventOccurInfo.channelId];
        auto itr = std::find_if(events.begin(), events.end(), [&](network::EventOccurInfo& eventInfo){
            return eventInfo.eventId == eventOccurInfo.eventId;
        });
        if (itr != events.end())
        {
            events.erase(itr);
        }
    }

    /**
     * @brief 清空对应通道的事件
     */
    void clearChannelEvents(int channelId)
    {
        std::lock_guard<boost::fibers::mutex> lock(eventsLock);
        if (eventCache.find(channelId) != eventCache.end())
            return;

        eventCache.erase(channelId);
    }

    /**
     * @brief 获取缓存的事件
     */
    std::vector<network::EventOccurInfo> getEvents(int channelId)
    {
        std::vector<network::EventOccurInfo> events;
        std::lock_guard<boost::fibers::mutex> lock(eventsLock);
        if (eventCache.find(channelId) != eventCache.end())
            return eventCache.at(channelId);
        else
            return events;
    }

    /**
     * @brief iva上报事件到fvm, fvm根据预置位状态处理事件缓存和过滤
     */
    void eventOccurred(network::EventOccurInfo &eventOccurInfo)
    {
        auto channelId = eventOccurInfo.channelId;
        auto detectChannelPtr = getDetectChannel(channelId);
        if (!detectChannelPtr)
            return;
        auto ptzPresetStatus = detectChannelPtr->getPtzPresetStatus();

        if (!SETTINGS->enableFilterEvent()                  //!< 未开启事件过滤选项,则直接转发IVA事件
        || (ptzPresetStatus == PTZPresetStatus::None)       //!< 当前通道不具有ptz偏移判断能力或者暂时获取不到PTZ坐标，直接上报事件，不进行缓存
        || (ptzPresetStatus == PTZPresetStatus::Abnormal))
        {
            postEvent(eventOccurInfo, detectChannelPtr);
        }
        else   //!< 缓存事件，通知ptz进行偏移判断
        {
            addEvent(eventOccurInfo);
            detectChannelPtr->onNotifyPtzCheck();
        }
    }

    std::vector<int> domeEventTypes;

    /**
     * @brief iva上报 事件移除 消息, 球机预置位归位
     */
    void eventRemoved(network::EventRemoveInfo &eventRemoveInfo)
    {
        if (domeEventTypes.end() == std::find(domeEventTypes.begin(), domeEventTypes.end(), eventRemoveInfo.eventTypeId))
            return;

        if (!lastEventId.empty() &&lastEventId != eventRemoveInfo.eventId)
            return;

        auto channelId = eventRemoveInfo.channelId;
        auto detectChannelPtr = getDetectChannel(channelId);
        if (!detectChannelPtr)
            return;

        /// 预置位归位
        auto videoSourcePtr = detectChannelPtr->getInput()->getVideoSource()->videoSourcePtr;
        auto domeGroupId = videoSourcePtr->getGroupId();
        auto domeGroupUUID = videoSourcePtr->getGroupUUID();
        if (domeGroupId > 0 || !domeGroupUUID.empty())
        {
            auto domeChannel = getDomeChannelBySource(videoSourcePtr);
            auto preset = DATA_MANAGER.getCurrPreset(detectChannelPtr->getInput()->getVideoSource());
            if (!preset)
                return;

            auto actPreset = domeChannel->getInput()->getVideoSource()->videoSourcePtr->getDomePreset();
            domeChannel->asyncCallCameraPreset(preset->getId(), actPreset);
        }
    }

    /**
     * @brief 初始化枪球联动关联事件列表
     */
    void initDomeEventTypes()
    {
        domeEventTypes.clear();
        auto strTypes = DATA_MANAGER.getParamProgData(fvm::data::DOME_EVENT_TYPE, "1");
        std::stringstream ss(strTypes);
        std::string item;
       
        while (std::getline(ss, item, ',')) {
            if (!item.empty()) {
                try {
                    int evtType = std::stoi(item);
                    domeEventTypes.push_back(evtType);
                }
                catch (...) {
                    std::cerr << fvm::data::DOME_EVENT_TYPE << " parse error: " << strTypes << std::endl;
                    break;
                }
            }
        }
    }

    /**
     * @brief fvm上报事件，找到对应通道的VideoRecoder对象，放到事件录像线程池中进行录像。
     */
    void postEvent(EventOccurInfo &eventOccurInfo, DetectChannelPtr& detectChannelPtr)
    {
        if (!detectChannelPtr)
            return;

        ChannelDetectState channelDetectState = detectChannelPtr->getDetectStatus();
        if (!SETTINGS->enableFilterEvent()                       //!< 未开启事件过滤选项，则直接转发IVA事件
        || ChannelDetectState::Detecting == channelDetectState)
        // || ((ChannelDetectState::OffSetPaused == channelDetectState) && eventOccurInfo.eventTypeId == ) //!< 偏移暂停时，可以报部分事件
        {

            std::string videoUrl;
            auto evtRecordType = DATA_MANAGER.getParamProgData(data::EVT_RECORD_TYPE, 0);
            if (evtRecordType == static_cast<int>(EvtRecordType::FVM))
            {
                StreamOutputPtr streamOutput;
                streamOutput = detectChannelPtr->getOutput(StreamOuputType::File);
                if (streamOutput)
                {
                    auto videoRecord = std::dynamic_pointer_cast<VideoRecorder>(streamOutput);
                    videoUrl = videoRecord->eventStart(eventOccurInfo.eventVideo);
                }
            }
            else
            {
                videoUrl = eventOccurInfo.eventVideo;
            }

            auto it = std::find(domeEventTypes.begin(), domeEventTypes.end(), eventOccurInfo.eventTypeId);
            bool isDomeLinkageEvent = it != domeEventTypes.end();

            // 枪球联动 [球机视频]
            std::string domeVideoUrl;
            if (isDomeLinkageEvent)
            {
                lastEventId = eventOccurInfo.eventId;

                stream::StreamPipePtr domeChannel = nullptr;
                auto domeGroupId = detectChannelPtr->getInput()->getVideoSource()->videoSourcePtr->getGroupId();
                auto domeGroupUUID = detectChannelPtr->getInput()->getVideoSource()->videoSourcePtr->getGroupUUID();

                if(!domeGroupUUID.empty())
                {
                    domeChannel = getDomeChannel(domeGroupUUID);
                }
                else if (domeGroupId > 0)
                {
                    domeChannel = getDomeChannel(std::to_string(domeGroupId));
                }

                if (domeChannel)
                {
                    auto streamOutput = domeChannel->getOutput(StreamOuputType::File);
                    if (streamOutput)
                    {
                        auto videoRecord = std::dynamic_pointer_cast<VideoRecorder>(streamOutput);
                        auto domeVideoFile = eventOccurInfo.eventVideo;
                        size_t pos = domeVideoFile.rfind('.');
                        if (pos != std::string::npos) {
                            domeVideoFile.replace(pos, 4, "_dome.mp4");
                        }
                        domeVideoUrl = videoRecord->eventStart(domeVideoFile);
                    }
                }
            }


            //文件创建成功
            if (!videoUrl.empty())
            {
                eventOccurInfo.eventVideo = videoUrl;
                EventInfo eventInfo;
                eventInfo.eventVideo = videoUrl;
                eventInfo.domeVideo = domeVideoUrl;
                eventInfo.eventId = eventOccurInfo.eventId;
                eventInfo.roiId = eventOccurInfo.roiId;
                eventInfo.laneId = eventOccurInfo.laneId;
                eventInfo.checkAreaId = eventOccurInfo.checkAreaId;
                eventInfo.eventTypeId = eventOccurInfo.eventTypeId;
                eventInfo.eventSubType = eventOccurInfo.eventSubType;
                eventInfo.occurTime = eventOccurInfo.occurTime;
                eventInfo.eventVideo = eventOccurInfo.eventVideo;
                eventInfo.eventImg = eventOccurInfo.eventImg;
                eventInfo.isAlarm = eventOccurInfo.isAlarm;
                eventInfo.areaTypeId = eventOccurInfo.areaTypeId;
                eventInfo.objectArea = eventOccurInfo.objectArea;
                bool isPlatform = (DATA_MANAGER.getParamProgData(FVM_EVENT_POST_LOCAL, 1) == 0);
                PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_EVENT_RECORD, eventInfo, isPlatform);

                if (isDomeLinkageEvent && !domeVideoUrl.empty())
                {
                    // 枪球联动
                    focusEvent(detectChannelPtr, eventInfo.objectArea);
                }
            }
        }
        else
        {
            ai::LogInfo << "filter event ! [no detecting],  channel:" << eventOccurInfo.channelId << " eventId:" << eventOccurInfo.eventId;
        }

    }

    /**
     * @brief 事件聚焦
     */
    void focusEvent(DetectChannelPtr& detectChannelPtr, std::vector<Point>& rect, bool ignoreIfRecording)
    {
        try {
            auto streamInput = detectChannelPtr->getInput();
            auto linkedDomePlatform = streamInput->linkedDomePlatform();
            auto linkedDomeSource = streamInput->linkedDomeSource();

            if (linkedDomePlatform && linkedDomeSource)
            {
                int xTop = 15;
                int yTop = 15;
                int xBottom = 240;
                int yBottom = 240;
                if (rect.size() > 1)
                {
                    const int frameWidth = 640;
                    const int frameHeight = 384;
                    const int minWidth = 126;   // 640 / 6 + 10*2 
                    const int minHeight = 76;   // 384 / 6 + 6*2 
                    const int paddingW = 10;
                    const int paddingH = 6;

                    float ltX = rect[0].x - paddingW;
                    float ltY = rect[0].y - paddingH;
                    float brX = rect[1].x + paddingW;
                    float brY = rect[1].y + paddingH;

                    float cX = (brX + ltX) / 2.f;
                    float cY = (brY + ltY) / 2.f;
                    if ((brX - ltX) < minWidth)
                    {
                        ltX = cX - minWidth / 2;
                        brX = cX + minWidth / 2;
                    }
                    if ((brY - ltY) < minHeight)
                    {
                        ltY = cY - minHeight / 2;
                        brY = cY + minHeight / 2;
                    }
                    if (ltX < 0.f) ltX = 0.f;
                    if (ltY < 0.f) ltY = 0.f;
                    if (brX > frameWidth) brX = frameWidth;
                    if (brY > frameHeight) brY = frameHeight;

                    xTop = ltX * 255 / frameWidth;
                    yTop = ltY * 255 / frameHeight;
                    xBottom = brX * 255 / frameWidth;
                    yBottom = brY * 255 / frameHeight;
                }

                int domePreset = streamInput->linkedDomePreset();
                linkedDomePlatform->focusEvent(linkedDomeSource, { domePreset, xTop, yTop, xBottom, yBottom }, ignoreIfRecording);
            }
            else
            {
                ai::LogWarn << "focus event : no dome resource binded ! ";
            }
        }
        catch (...)
        {
            ai::LogWarn << "focus event : no real dome resource binded ! ";
        }
    }

    /**
    * @brief 事件聚焦
    * @param[in] channelID: 通道ID
    * @param[in] rect: 事件检测区域
    */
    void focusEvent(int channelID, std::vector<Point>& rect)
    {
        auto detectChannelPtr = getDetectChannel(channelID);
        if (!detectChannelPtr)
        {
            ai::LogWarn << "focus event : channel" << channelID  << " not found! ";
        }
        else
        {
            focusEvent(detectChannelPtr, rect, true);
        }
    }

    /**
     * @brief 过滤处理iva的videoQuaAlarm消息
     */
    void postVideoQuaAlarm(network::VideoQuaAlarmConf &videoQuaAlarmConf)
    {
        auto videoId = videoQuaAlarmConf.videoId;
        auto channelSrc = DATA_MANAGER.getVideoChannelSource(videoId);
        if (!channelSrc.has_value())
            return;

        auto channelId = channelSrc.value()->channelId;
        auto detectChannelPtr = getDetectChannel(channelId);
        if (!detectChannelPtr)
            return;

        auto ptzPresetStatus = detectChannelPtr->getPtzPresetStatus();

        auto it = std::find_if(videoQuaAlarmConf.vtAlarmType.begin(), videoQuaAlarmConf.vtAlarmType.end(),
                               [](int alarmType) {
                                   return alarmType == static_cast<int> (VideoQuaAlarmType::VIDEO_QUA_ALARM_SHIFTING);
                               });
        if (it != videoQuaAlarmConf.vtAlarmType.end())
        {
            if ((ptzPresetStatus != PTZPresetStatus::None)) //!< 当前管道可通过onvif检测偏移，偏移的上报由onvif判断为准
            {
                videoQuaAlarmConf.vtAlarmType.erase(it);
            }
        }
        if (!videoQuaAlarmConf.vtAlarmType.empty())
        {
            videoQuaAlarmConf.pauseDetect = 0; //!< 偏移不停检测
            detectChannelPtr->pauseDetect(StreamPauseReason::OffsetEvent);
            PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_VIDEO_QUAALARM, videoQuaAlarmConf, true);
            ai::LogWarn << "camera video QuaAlarm ! channel:" << channelId << " videoId:" << videoId;
        }

    }

    /**
     * @brief 过滤处理iva的videoQuaRecovery消息
     */
    void postVideoQuaRecovery(network::VideoQuaRecovery &videoQuaRecovery)
    {
        auto videoId = videoQuaRecovery.videoId;
        auto channelSrc = DATA_MANAGER.getVideoChannelSource(videoId);
        if (!channelSrc.has_value())
            return;

        auto channelId = channelSrc.value()->channelId;
        auto detectChannelPtr = getDetectChannel(channelId);
        if (!detectChannelPtr)
            return;

        auto ptzPresetStatus = detectChannelPtr->getPtzPresetStatus();
        auto it = std::find_if(videoQuaRecovery.vtAlarmType.begin(), videoQuaRecovery.vtAlarmType.end(),[](int alarmType){
            return alarmType == static_cast<int>(VideoQuaAlarmType::VIDEO_QUA_AUTO_RESET)
                   || alarmType == static_cast<int>(VideoQuaAlarmType::VIDEO_QUA_ALARM_SHIFTING);});

        if (it != videoQuaRecovery.vtAlarmType.end())
        {
            if ((ptzPresetStatus != PTZPresetStatus::None)) //!< 当前管道可通过onvif检测偏移，偏移的上报由onvif判断为准
            {
                videoQuaRecovery.vtAlarmType.erase(it);
            }
        }
        if (!videoQuaRecovery.vtAlarmType.empty())
        {
            detectChannelPtr->restoreDetect(StreamRestoreReason::OffsetRestore);
            PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_VIDEO_QUARECOVERY, videoQuaRecovery, true);
            ai::LogWarn << "camera preset restore ! channel:" << channelId << " videoId:" << videoId;
        }
    }


}

