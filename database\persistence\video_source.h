/**
 * @addtogroup odbDatabaseGroup
 * @brief 视频资源
 * @{
 */
#ifndef _VIDEOSOURCE_H
#define _VIDEOSOURCE_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
/**
 * @brief  视频资源 对应数据库aimonitorV3的表wn_video_source
 */
#pragma db object table("wn_video_source")
class VideoSource {

public:

    VideoSource(const std::string& name,
        const std::string& address,
        unsigned long videoServerId,
        const std::string& location,
        bool hasPtz,
        int isDetectable,
        unsigned long detectPointId,
        const std::string& ptzControl,
        int videoStatus,
        unsigned long roadId,
        bool isEnable,
        bool isChange,
        int videoType = 0,
        int domePreset = 0,
        int groupId = 0,
        const std::string& groupUUID=""
        )
        : name(name), address(address), videoServerId(videoServerId),
        location(location), hasPtz(hasPtz), isDetectable(isDetectable),
        detectPointId(detectPointId), ptzControl(ptzControl), videoStatus(videoStatus),
        roadId(roadId),isEnable(isEnable), isChange(isChange)
        // 枪球联动功能      
        , videoType(videoType), domePreset(domePreset), groupId(groupId), groupUUID(groupUUID)
    {
    }


    unsigned long getId() const {
        return id;
    }

    const std::string& getName() const {
        return name;
    }

    void setName(const std::string& name) {
        this->name = name;
    }

    const std::string& getAddress() const {
        return address;
    }

    void setAddress(const std::string& address) {
        this->address = address;
    }

    unsigned long getVideoServerId() const {
        return videoServerId;
    }

    void setVideoServerId(unsigned long videoSrvId) {
        this->videoServerId = videoSrvId;
    }

    const std::string& getLocation() const {
        return location;
    }

    void setLocation(const std::string& local) {
        this->location = local;
    }

    bool getHasPtz() const {
        return hasPtz;
    }

    void setHasPtz(bool ptz) {
        this->hasPtz = ptz;
    }

    int getIsDetectable() const {
        return isDetectable;
    }

    void setIsDetectable(int detect) {
        this->isDetectable = detect;
    }

    unsigned long getDetectPointId() const {
        return detectPointId;
    }

    const std::string& getPtzControl() const {
        return ptzControl;
    }

    void setPtzControl(const std::string& ptz) {
        this->ptzControl = ptz;
    }

    void setVideoSourceId(unsigned long id) {
        this->detectPointId = id;
    }

    int getVideoStatus() const {
        return videoStatus;
    }

    void setVideoStatus(int status) {
        this->videoStatus = status;
    }

    unsigned long getRoadId() const {
        return roadId;
    }

    void setRoadId(unsigned long id) {
        this->roadId = id;
    }

    bool getIsEnable() const {
        return isEnable;
    }

    void setIsEnable(bool enable) {
        this->isEnable = enable;
    }

    bool getIsChange() const {
        return isChange;
    }

    void setIsChange(bool change) {
        this->isChange = change;
    }

    int getVideoType() const {
        return videoType;
    }

    void setVideoType(int type) {
        this->videoType = type;
    }

    int getDomePreset() const {
        return domePreset;
    }

    void setDomePreset(int preset) {
        this->domePreset = preset;
    }

    int getGroupId() const {
        return groupId;
    }

    void setGroupId(int id) {
        this->groupId = id;
    }

    std::string getGroupUUID() const {
        return groupUUID;
    }

    void setGroupUUID(std::string id) {
        this->groupUUID = id;
    }

private:

    friend class odb::access;
    VideoSource() {}

private:

#pragma db id auto
    unsigned long id;              //!< 表ID

#pragma db column("name") type("VARCHAR(255)")
    std::string name;              //!< 视频资源名

#pragma db column("address") type("VARCHAR(255)")
    std::string address;           //!< 视频资源地址或路径

#pragma db column("access_front_end_id")
    unsigned long videoServerId;   //!< 视频对应的视频服务器id

#pragma db column("location") type("VARCHAR(255)")
    std::string location;          //!< 视频资源桩号

#pragma db column("has_ptz") type("INT")
    bool hasPtz;                   //!< 视频是否有云台

#pragma db column("is_detectable") readonly
    int isDetectable;             //!< 视频是否可以检测

#pragma db column("detect_point_id") type("INT")
    unsigned long detectPointId;   //!< 检测通道号 wn_detection_point表Id

#pragma db column("ptz_control") type("VARCHAR(255)")
     std::string ptzControl;      //!< 云台控制

#pragma db column("video_status") type("INT")
    int videoStatus;               //!< 视频资源状态

#pragma db column("road_id") type("INT")
    unsigned long roadId;

#pragma db column("is_enable") type("INT")
    bool isEnable;                 //!< 使能 0:不使能 1:使能

#pragma db column("is_change") type("INT")
    bool isChange;                 //!< 视频资源变化

#pragma db column("video_type") type("INT")
    int videoType;                 //!< 视频资源类型

#pragma db column("dome_preset") type("INT")
    unsigned long domePreset;      //!< 球机预置位

#pragma db column("group_id") type("INT")
    unsigned long groupId;        //!< 枪球一体编号 (球机接入id)

#pragma db column("group_uuid") type("VARCHAR(255)")
    std::string groupUUID;        //!< 枪球一体组号 (二代平台接入)
};
}
#endif //_VIDEOSOURCE_H
/**
 * @}
 */