// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#include <odb/pre.hxx>

#include "preset_lane-odb.hxx"

#include <cassert>
#include <cstring>  // std::memcpy


#include <odb/mysql/traits.hxx>
#include <odb/mysql/database.hxx>
#include <odb/mysql/transaction.hxx>
#include <odb/mysql/connection.hxx>
#include <odb/mysql/statement.hxx>
#include <odb/mysql/statement-cache.hxx>
#include <odb/mysql/simple-object-statements.hxx>
#include <odb/mysql/container-statements.hxx>
#include <odb/mysql/exceptions.hxx>
#include <odb/mysql/simple-object-result.hxx>
#include <odb/mysql/enum.hxx>

namespace odb
{
  // PresetLane
  //

  struct access::object_traits_impl< ::db::PresetLane, id_mysql >::extra_statement_cache_type
  {
    extra_statement_cache_type (
      mysql::connection&,
      image_type&,
      id_image_type&,
      mysql::binding&,
      mysql::binding&)
    {
    }
  };

  access::object_traits_impl< ::db::PresetLane, id_mysql >::id_type
  access::object_traits_impl< ::db::PresetLane, id_mysql >::
  id (const id_image_type& i)
  {
    mysql::database* db (0);
    ODB_POTENTIALLY_UNUSED (db);

    id_type id;
    {
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        id,
        i.id_value,
        i.id_null);
    }

    return id;
  }

  access::object_traits_impl< ::db::PresetLane, id_mysql >::id_type
  access::object_traits_impl< ::db::PresetLane, id_mysql >::
  id (const image_type& i)
  {
    mysql::database* db (0);
    ODB_POTENTIALLY_UNUSED (db);

    id_type id;
    {
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        id,
        i.id_value,
        i.id_null);
    }

    return id;
  }

  bool access::object_traits_impl< ::db::PresetLane, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // id
    //
    t[0UL] = 0;

    // roiId
    //
    t[1UL] = 0;

    // laneArea
    //
    if (t[2UL])
    {
      i.laneArea_value.capacity (i.laneArea_size);
      grew = true;
    }

    // laneType
    //
    t[3UL] = 0;

    // minSpeed
    //
    t[4UL] = 0;

    // maxSpeed
    //
    t[5UL] = 0;

    // eventProperty
    //
    if (t[6UL])
    {
      i.eventProperty_value.capacity (i.eventProperty_size);
      grew = true;
    }

    // labelProperty
    //
    if (t[7UL])
    {
      i.labelProperty_value.capacity (i.labelProperty_size);
      grew = true;
    }

    // paramPlanId
    //
    t[8UL] = 0;

    // param
    //
    if (t[9UL])
    {
      i.param_value.capacity (i.param_size);
      grew = true;
    }

    // direction
    //
    if (t[10UL])
    {
      i.direction_value.capacity (i.direction_size);
      grew = true;
    }

    // isDel
    //
    t[11UL] = 0;

    // isEnable
    //
    t[12UL] = 0;

    return grew;
  }

  void access::object_traits_impl< ::db::PresetLane, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i,
        mysql::statement_kind sk)
  {
    ODB_POTENTIALLY_UNUSED (sk);

    using namespace mysql;

    std::size_t n (0);

    // id
    //
    if (sk != statement_update)
    {
      b[n].buffer_type = MYSQL_TYPE_LONGLONG;
      b[n].is_unsigned = 1;
      b[n].buffer = &i.id_value;
      b[n].is_null = &i.id_null;
      n++;
    }

    // roiId
    //
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.roiId_value;
    b[n].is_null = &i.roiId_null;
    n++;

    // laneArea
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.laneArea_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.laneArea_value.capacity ());
    b[n].length = &i.laneArea_size;
    b[n].is_null = &i.laneArea_null;
    n++;

    // laneType
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.laneType_value;
    b[n].is_null = &i.laneType_null;
    n++;

    // minSpeed
    //
    b[n].buffer_type = MYSQL_TYPE_FLOAT;
    b[n].buffer = &i.minSpeed_value;
    b[n].is_null = &i.minSpeed_null;
    n++;

    // maxSpeed
    //
    b[n].buffer_type = MYSQL_TYPE_FLOAT;
    b[n].buffer = &i.maxSpeed_value;
    b[n].is_null = &i.maxSpeed_null;
    n++;

    // eventProperty
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.eventProperty_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.eventProperty_value.capacity ());
    b[n].length = &i.eventProperty_size;
    b[n].is_null = &i.eventProperty_null;
    n++;

    // labelProperty
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.labelProperty_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.labelProperty_value.capacity ());
    b[n].length = &i.labelProperty_size;
    b[n].is_null = &i.labelProperty_null;
    n++;

    // paramPlanId
    //
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.paramPlanId_value;
    b[n].is_null = &i.paramPlanId_null;
    n++;

    // param
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.param_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.param_value.capacity ());
    b[n].length = &i.param_size;
    b[n].is_null = &i.param_null;
    n++;

    // direction
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.direction_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.direction_value.capacity ());
    b[n].length = &i.direction_size;
    b[n].is_null = &i.direction_null;
    n++;

    // isDel
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.isDel_value;
    b[n].is_null = &i.isDel_null;
    n++;

    // isEnable
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.isEnable_value;
    b[n].is_null = &i.isEnable_null;
    n++;
  }

  void access::object_traits_impl< ::db::PresetLane, id_mysql >::
  bind (MYSQL_BIND* b, id_image_type& i)
  {
    std::size_t n (0);
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.id_value;
    b[n].is_null = &i.id_null;
  }

  bool access::object_traits_impl< ::db::PresetLane, id_mysql >::
  init (image_type& i,
        const object_type& o,
        mysql::statement_kind sk)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (sk);

    using namespace mysql;

    bool grew (false);

    // id
    //
    if (sk == statement_insert)
    {
      long unsigned int const& v =
        o.id;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.id_value, is_null, v);
      i.id_null = is_null;
    }

    // roiId
    //
    {
      long unsigned int const& v =
        o.roiId;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.roiId_value, is_null, v);
      i.roiId_null = is_null;
    }

    // laneArea
    //
    {
      ::std::string const& v =
        o.laneArea;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.laneArea_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.laneArea_value,
        size,
        is_null,
        v);
      i.laneArea_null = is_null;
      i.laneArea_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.laneArea_value.capacity ());
    }

    // laneType
    //
    {
      int const& v =
        o.laneType;

      bool is_null (false);
      mysql::value_traits<
          int,
          mysql::id_long >::set_image (
        i.laneType_value, is_null, v);
      i.laneType_null = is_null;
    }

    // minSpeed
    //
    {
      float const& v =
        o.minSpeed;

      bool is_null (false);
      mysql::value_traits<
          float,
          mysql::id_float >::set_image (
        i.minSpeed_value, is_null, v);
      i.minSpeed_null = is_null;
    }

    // maxSpeed
    //
    {
      float const& v =
        o.maxSpeed;

      bool is_null (false);
      mysql::value_traits<
          float,
          mysql::id_float >::set_image (
        i.maxSpeed_value, is_null, v);
      i.maxSpeed_null = is_null;
    }

    // eventProperty
    //
    {
      ::std::string const& v =
        o.eventProperty;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.eventProperty_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.eventProperty_value,
        size,
        is_null,
        v);
      i.eventProperty_null = is_null;
      i.eventProperty_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.eventProperty_value.capacity ());
    }

    // labelProperty
    //
    {
      ::std::string const& v =
        o.labelProperty;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.labelProperty_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.labelProperty_value,
        size,
        is_null,
        v);
      i.labelProperty_null = is_null;
      i.labelProperty_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.labelProperty_value.capacity ());
    }

    // paramPlanId
    //
    {
      long unsigned int const& v =
        o.paramPlanId;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.paramPlanId_value, is_null, v);
      i.paramPlanId_null = is_null;
    }

    // param
    //
    {
      ::std::string const& v =
        o.param;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.param_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.param_value,
        size,
        is_null,
        v);
      i.param_null = is_null;
      i.param_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.param_value.capacity ());
    }

    // direction
    //
    {
      ::std::string const& v =
        o.direction;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.direction_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.direction_value,
        size,
        is_null,
        v);
      i.direction_null = is_null;
      i.direction_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.direction_value.capacity ());
    }

    // isDel
    //
    {
      bool const& v =
        o.isDel;

      bool is_null (false);
      mysql::value_traits<
          bool,
          mysql::id_long >::set_image (
        i.isDel_value, is_null, v);
      i.isDel_null = is_null;
    }

    // isEnable
    //
    {
      bool const& v =
        o.isEnable;

      bool is_null (false);
      mysql::value_traits<
          bool,
          mysql::id_long >::set_image (
        i.isEnable_value, is_null, v);
      i.isEnable_null = is_null;
    }

    return grew;
  }

  void access::object_traits_impl< ::db::PresetLane, id_mysql >::
  init (object_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    // id
    //
    {
      long unsigned int& v =
        o.id;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.id_value,
        i.id_null);
    }

    // roiId
    //
    {
      long unsigned int& v =
        o.roiId;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.roiId_value,
        i.roiId_null);
    }

    // laneArea
    //
    {
      ::std::string& v =
        o.laneArea;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.laneArea_value,
        i.laneArea_size,
        i.laneArea_null);
    }

    // laneType
    //
    {
      int& v =
        o.laneType;

      mysql::value_traits<
          int,
          mysql::id_long >::set_value (
        v,
        i.laneType_value,
        i.laneType_null);
    }

    // minSpeed
    //
    {
      float& v =
        o.minSpeed;

      mysql::value_traits<
          float,
          mysql::id_float >::set_value (
        v,
        i.minSpeed_value,
        i.minSpeed_null);
    }

    // maxSpeed
    //
    {
      float& v =
        o.maxSpeed;

      mysql::value_traits<
          float,
          mysql::id_float >::set_value (
        v,
        i.maxSpeed_value,
        i.maxSpeed_null);
    }

    // eventProperty
    //
    {
      ::std::string& v =
        o.eventProperty;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.eventProperty_value,
        i.eventProperty_size,
        i.eventProperty_null);
    }

    // labelProperty
    //
    {
      ::std::string& v =
        o.labelProperty;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.labelProperty_value,
        i.labelProperty_size,
        i.labelProperty_null);
    }

    // paramPlanId
    //
    {
      long unsigned int& v =
        o.paramPlanId;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.paramPlanId_value,
        i.paramPlanId_null);
    }

    // param
    //
    {
      ::std::string& v =
        o.param;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.param_value,
        i.param_size,
        i.param_null);
    }

    // direction
    //
    {
      ::std::string& v =
        o.direction;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.direction_value,
        i.direction_size,
        i.direction_null);
    }

    // isDel
    //
    {
      bool& v =
        o.isDel;

      mysql::value_traits<
          bool,
          mysql::id_long >::set_value (
        v,
        i.isDel_value,
        i.isDel_null);
    }

    // isEnable
    //
    {
      bool& v =
        o.isEnable;

      mysql::value_traits<
          bool,
          mysql::id_long >::set_value (
        v,
        i.isEnable_value,
        i.isEnable_null);
    }
  }

  void access::object_traits_impl< ::db::PresetLane, id_mysql >::
  init (id_image_type& i, const id_type& id)
  {
    {
      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.id_value, is_null, id);
      i.id_null = is_null;
    }
  }

  const char access::object_traits_impl< ::db::PresetLane, id_mysql >::persist_statement[] =
  "INSERT INTO `wn_preset_lane` "
  "(`id`, "
  "`roi_id`, "
  "`lane_area`, "
  "`lane_type`, "
  "`min_speed`, "
  "`max_speed`, "
  "`event_property`, "
  "`label_property`, "
  "`param_plan_id`, "
  "`param`, "
  "`direction`, "
  "`is_del`, "
  "`is_enable`) "
  "VALUES "
  "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

  const char access::object_traits_impl< ::db::PresetLane, id_mysql >::find_statement[] =
  "SELECT "
  "`wn_preset_lane`.`id`, "
  "`wn_preset_lane`.`roi_id`, "
  "`wn_preset_lane`.`lane_area`, "
  "`wn_preset_lane`.`lane_type`, "
  "`wn_preset_lane`.`min_speed`, "
  "`wn_preset_lane`.`max_speed`, "
  "`wn_preset_lane`.`event_property`, "
  "`wn_preset_lane`.`label_property`, "
  "`wn_preset_lane`.`param_plan_id`, "
  "`wn_preset_lane`.`param`, "
  "`wn_preset_lane`.`direction`, "
  "`wn_preset_lane`.`is_del`, "
  "`wn_preset_lane`.`is_enable` "
  "FROM `wn_preset_lane` "
  "WHERE `wn_preset_lane`.`id`=?";

  const char access::object_traits_impl< ::db::PresetLane, id_mysql >::update_statement[] =
  "UPDATE `wn_preset_lane` "
  "SET "
  "`roi_id`=?, "
  "`lane_area`=?, "
  "`lane_type`=?, "
  "`min_speed`=?, "
  "`max_speed`=?, "
  "`event_property`=?, "
  "`label_property`=?, "
  "`param_plan_id`=?, "
  "`param`=?, "
  "`direction`=?, "
  "`is_del`=?, "
  "`is_enable`=? "
  "WHERE `id`=?";

  const char access::object_traits_impl< ::db::PresetLane, id_mysql >::erase_statement[] =
  "DELETE FROM `wn_preset_lane` "
  "WHERE `id`=?";

  const char access::object_traits_impl< ::db::PresetLane, id_mysql >::query_statement[] =
  "SELECT "
  "`wn_preset_lane`.`id`, "
  "`wn_preset_lane`.`roi_id`, "
  "`wn_preset_lane`.`lane_area`, "
  "`wn_preset_lane`.`lane_type`, "
  "`wn_preset_lane`.`min_speed`, "
  "`wn_preset_lane`.`max_speed`, "
  "`wn_preset_lane`.`event_property`, "
  "`wn_preset_lane`.`label_property`, "
  "`wn_preset_lane`.`param_plan_id`, "
  "`wn_preset_lane`.`param`, "
  "`wn_preset_lane`.`direction`, "
  "`wn_preset_lane`.`is_del`, "
  "`wn_preset_lane`.`is_enable` "
  "FROM `wn_preset_lane`";

  const char access::object_traits_impl< ::db::PresetLane, id_mysql >::erase_query_statement[] =
  "DELETE FROM `wn_preset_lane`";

  const char access::object_traits_impl< ::db::PresetLane, id_mysql >::table_name[] =
  "`wn_preset_lane`";

  void access::object_traits_impl< ::db::PresetLane, id_mysql >::
  persist (database& db, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    callback (db,
              static_cast<const object_type&> (obj),
              callback_event::pre_persist);

    image_type& im (sts.image ());
    binding& imb (sts.insert_image_binding ());

    if (init (im, obj, statement_insert))
      im.version++;

    im.id_value = 0;

    if (im.version != sts.insert_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_insert);
      sts.insert_image_version (im.version);
      imb.version++;
    }

    {
      id_image_type& i (sts.id_image ());
      binding& b (sts.id_image_binding ());
      if (i.version != sts.id_image_version () || b.version == 0)
      {
        bind (b.bind, i);
        sts.id_image_version (i.version);
        b.version++;
      }
    }

    insert_statement& st (sts.persist_statement ());
    if (!st.execute ())
      throw object_already_persistent ();

    obj.id = id (sts.id_image ());

    callback (db,
              static_cast<const object_type&> (obj),
              callback_event::post_persist);
  }

  void access::object_traits_impl< ::db::PresetLane, id_mysql >::
  update (database& db, const object_type& obj)
  {
    ODB_POTENTIALLY_UNUSED (db);

    using namespace mysql;
    using mysql::update_statement;

    callback (db, obj, callback_event::pre_update);

    mysql::transaction& tr (mysql::transaction::current ());
    mysql::connection& conn (tr.connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    id_image_type& idi (sts.id_image ());
    init (idi, id (obj));

    image_type& im (sts.image ());
    if (init (im, obj, statement_update))
      im.version++;

    bool u (false);
    binding& imb (sts.update_image_binding ());
    if (im.version != sts.update_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_update);
      sts.update_image_version (im.version);
      imb.version++;
      u = true;
    }

    binding& idb (sts.id_image_binding ());
    if (idi.version != sts.update_id_image_version () ||
        idb.version == 0)
    {
      if (idi.version != sts.id_image_version () ||
          idb.version == 0)
      {
        bind (idb.bind, idi);
        sts.id_image_version (idi.version);
        idb.version++;
      }

      sts.update_id_image_version (idi.version);

      if (!u)
        imb.version++;
    }

    update_statement& st (sts.update_statement ());
    if (st.execute () == 0)
      throw object_not_persistent ();

    callback (db, obj, callback_event::post_update);
    pointer_cache_traits::update (db, obj);
  }

  void access::object_traits_impl< ::db::PresetLane, id_mysql >::
  erase (database& db, const id_type& id)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    id_image_type& i (sts.id_image ());
    init (i, id);

    binding& idb (sts.id_image_binding ());
    if (i.version != sts.id_image_version () || idb.version == 0)
    {
      bind (idb.bind, i);
      sts.id_image_version (i.version);
      idb.version++;
    }

    if (sts.erase_statement ().execute () != 1)
      throw object_not_persistent ();

    pointer_cache_traits::erase (db, id);
  }

  access::object_traits_impl< ::db::PresetLane, id_mysql >::pointer_type
  access::object_traits_impl< ::db::PresetLane, id_mysql >::
  find (database& db, const id_type& id)
  {
    using namespace mysql;

    {
      pointer_type p (pointer_cache_traits::find (db, id));

      if (!pointer_traits::null_ptr (p))
        return p;
    }

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);

    if (l.locked ())
    {
      if (!find_ (sts, &id))
        return pointer_type ();
    }

    pointer_type p (
      access::object_factory<object_type, pointer_type>::create ());
    pointer_traits::guard pg (p);

    pointer_cache_traits::insert_guard ig (
      pointer_cache_traits::insert (db, id, p));

    object_type& obj (pointer_traits::get_ref (p));

    if (l.locked ())
    {
      select_statement& st (sts.find_statement ());
      ODB_POTENTIALLY_UNUSED (st);

      callback (db, obj, callback_event::pre_load);
      init (obj, sts.image (), &db);
      load_ (sts, obj, false);
      sts.load_delayed (0);
      l.unlock ();
      callback (db, obj, callback_event::post_load);
      pointer_cache_traits::load (ig.position ());
    }
    else
      sts.delay_load (id, obj, ig.position ());

    ig.release ();
    pg.release ();
    return p;
  }

  bool access::object_traits_impl< ::db::PresetLane, id_mysql >::
  find (database& db, const id_type& id, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);
    assert (l.locked ()) /* Must be a top-level call. */;

    if (!find_ (sts, &id))
      return false;

    select_statement& st (sts.find_statement ());
    ODB_POTENTIALLY_UNUSED (st);

    reference_cache_traits::position_type pos (
      reference_cache_traits::insert (db, id, obj));
    reference_cache_traits::insert_guard ig (pos);

    callback (db, obj, callback_event::pre_load);
    init (obj, sts.image (), &db);
    load_ (sts, obj, false);
    sts.load_delayed (0);
    l.unlock ();
    callback (db, obj, callback_event::post_load);
    reference_cache_traits::load (pos);
    ig.release ();
    return true;
  }

  bool access::object_traits_impl< ::db::PresetLane, id_mysql >::
  reload (database& db, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);
    assert (l.locked ()) /* Must be a top-level call. */;

    const id_type& id (object_traits_impl::id (obj));
    if (!find_ (sts, &id))
      return false;

    select_statement& st (sts.find_statement ());
    ODB_POTENTIALLY_UNUSED (st);

    callback (db, obj, callback_event::pre_load);
    init (obj, sts.image (), &db);
    load_ (sts, obj, true);
    sts.load_delayed (0);
    l.unlock ();
    callback (db, obj, callback_event::post_load);
    return true;
  }

  bool access::object_traits_impl< ::db::PresetLane, id_mysql >::
  find_ (statements_type& sts,
         const id_type* id)
  {
    using namespace mysql;

    id_image_type& i (sts.id_image ());
    init (i, *id);

    binding& idb (sts.id_image_binding ());
    if (i.version != sts.id_image_version () || idb.version == 0)
    {
      bind (idb.bind, i);
      sts.id_image_version (i.version);
      idb.version++;
    }

    image_type& im (sts.image ());
    binding& imb (sts.select_image_binding ());

    if (im.version != sts.select_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_select);
      sts.select_image_version (im.version);
      imb.version++;
    }

    select_statement& st (sts.find_statement ());

    st.execute ();
    auto_result ar (st);
    select_statement::result r (st.fetch ());

    if (r == select_statement::truncated)
    {
      if (grow (im, sts.select_image_truncated ()))
        im.version++;

      if (im.version != sts.select_image_version ())
      {
        bind (imb.bind, im, statement_select);
        sts.select_image_version (im.version);
        imb.version++;
        st.refetch ();
      }
    }

    return r != select_statement::no_data;
  }

  result< access::object_traits_impl< ::db::PresetLane, id_mysql >::object_type >
  access::object_traits_impl< ::db::PresetLane, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));

    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.select_image_binding ());

    if (im.version != sts.select_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_select);
      sts.select_image_version (im.version);
      imb.version++;
    }

    std::string text (query_statement);
    if (!q.empty ())
    {
      text += " ";
      text += q.clause ();
    }

    q.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        text,
        false,
        true,
        q.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::object_result_impl<object_type> > r (
      new (shared) mysql::object_result_impl<object_type> (
        q, st, sts, 0));

    return result<object_type> (r);
  }

  unsigned long long access::object_traits_impl< ::db::PresetLane, id_mysql >::
  erase_query (database& db, const query_base_type& q)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));

    std::string text (erase_query_statement);
    if (!q.empty ())
    {
      text += ' ';
      text += q.clause ();
    }

    q.init_parameters ();
    delete_statement st (
      conn,
      text,
      q.parameters_binding ());

    return st.execute ();
  }
}

#include <odb/post.hxx>
