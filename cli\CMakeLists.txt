cmake_minimum_required(VERSION 3.5.2)

## 输出目录
set(LIBRARY_OUTPUT_PATH   ${PROJECT_SOURCE_DIR}/out/lib)

## 编译选项
add_compile_options(-g -std=c++17 -fPIC -fstack-protector-all -Wno-deprecated-declarations)

## 目标生成
aux_source_directory(src/ MAIN_SRC)
aux_source_directory(src/utils CONSOLE_SRC)
add_library(ai_cli SHARED ${MAIN_SRC} ${CONSOLE_SRC})

## 依赖路径
include_directories(src/utils)
include_directories(include)

## 目标依赖
target_link_libraries(ai_cli pthread)