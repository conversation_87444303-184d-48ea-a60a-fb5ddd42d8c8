#pragma once
#include <map>
#include "wtoe_http_client.h"
#include "wtoe_udp_lib.h"

class WTOESOCKET_API WtoeSocket
{
public:
	static WtoeSocket* getInstance();
	WtoeSocket(const WtoeSocket&) = delete;
	WtoeSocket& operator=(const WtoeSocket&) = delete;

	WtoeAIHttpClient* getHttp(int index, bool log= true);
	WtoeUDPLib* getUdp() { return &udpLib; }

private:
	WtoeSocket();

	WtoeUDPLib udpLib;

	std::map<int, WtoeAIHttpClient> httpClients;
};