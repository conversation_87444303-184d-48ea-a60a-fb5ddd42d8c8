﻿/**
 * Project FVM
 */

#include "protocol_manager.h"
#include "data/data_manager.h"
#include "util/worker_pool.h"
#include "protocol.hpp"
#include "network.h"
#include "ailog.h"

/**
 * @brief: 协议接受接口
 */
namespace fvm::protocol
{
    using namespace network;

    #define MSG_RECEIVED(SIGNAL) \
    worker::post(worker::WorkerType::Message, [this]() { \
        SIGNAL(); \
    });

    #define MSG_DATA_RECEIVED(SIGNAL, PROTOCOL, DATA) \
    { \
        auto msg = deserialize<PROTOCOL>(DATA); \
        worker::post(worker::WorkerType::Message, [this, msg]() {SIGNAL(msg);}); \
    } \

    /*
     * 初始化（启动服务端及相关客户端）
     */
    bool ProtocolManager::init()
    {
        // 初始化FVM 监听服务
        bool rt = network::initServer(DATA_MANAGER.getFvmPort(), [this](auto &data) { handleProtocolReceived(data); });
        if(!rt)
        {
            ai::LogError << "FVM server start failed! port: " << DATA_MANAGER.getFvmPort()<< network::lastErrorMsg() << lastErrorCode();
        }

        //auto gumPort = DATA_MANAGER.getGumPort();
        //auto msmPort = DATA_MANAGER.getIvaPort();
        // 初始化客户端列表
        network::initClients({
             {SessionType::IVA, 5002, DEFAULT_CLIENT_ID, HOST_LOCAL, ConnectionType::UDP, [](int procID){return DATA_MANAGER.getIvaPort(procID);}},
             //{SessionType::GUM, gumPort, DEFAULT_CLIENT_ID, DATA_MANAGER.getGumIP(), ConnectionType::UDP},
             //{SessionType::MSM, msmPort, DEFAULT_CLIENT_ID, DATA_MANAGER.getMsmIP(), ConnectionType::UDP},
             {SessionType::WEB, 8080, WEB_LOCAL, LOCAL_HOST,    ConnectionType::HTTP},
             {SessionType::WEB, 8080, WEB_PLATFORM, DATA_MANAGER.getPlatformIP(), ConnectionType::HTTP}
        });
        return rt;
    }

    /*
     * 消息数据处理
     * @param msgData 消息内容
     */
    void ProtocolManager::handleProtocolReceived(std::string &msgData)
    {
        ai::LogInfo << "RECV " << msgData;
        auto[msgType, msgJson] = network::parseProtocol(msgData);
        switch (msgType)
        {
            case ProtocolType::UDP_REQUEST_INIT: //IVA->FVM 请求初始化
                MSG_DATA_RECEIVED(onIvaRequestInitMessage, RequestInit, msgJson);
                break;
            case ProtocolType::UDP_EVENT_OCCURED://IVA->FVM 上报事件
                MSG_DATA_RECEIVED(onEventOccurInfo, EventOccurInfo, msgJson);
                break;
            case ProtocolType::UDP_REQUEST_DAYNIGHT://IVA->FVM 请求日夜切换
                MSG_DATA_RECEIVED(onRequestDayNightInfo, RequestDayNightInfo, msgJson);
                break;
            case ProtocolType::UDP_VIDEO_QUAALARM:   //IVA->FVM 视频质量告警
                MSG_DATA_RECEIVED(onIvaVideoQuaAlarm, VideoQuaAlarmConf, msgJson);
                break;
            case ProtocolType::UDP_VIDEO_QUARECOVERY: //IVA->FVM 视频质量恢复
                MSG_DATA_RECEIVED(onIvaVideoRecovery, VideoQuaRecovery, msgJson);
                break;
            case ProtocolType::UDP_EVENT_REMOVE: //IVA->FVM 事件解除
                MSG_DATA_RECEIVED(onEventRemoveInfo, EventRemoveInfo, msgJson);
                break;
            case ProtocolType::UDP_REQUEST_VIDEORET://GUM->FVM 回应请求视频流
                MSG_DATA_RECEIVED(onRequestVideoRet, RequestVideoRet, msgJson);
                break;
            case ProtocolType::UDP_STOPVIDEORET://GUM->FVM 回应请求视频流
                MSG_DATA_RECEIVED(onRequestStopVideoRet, RequestStopVideoRet, msgJson);
                break;
            case ProtocolType::UDP_PTZOPERRET://GUM->FVM 回应云台控制
                MSG_DATA_RECEIVED(onRequestPtzOperRet, RequestPtzOperRet, msgJson);
                break;
            case ProtocolType::UDP_FVM_CHANGED://WEB->FVM 新增/修改接入前端(SDK,28181)
                MSG_DATA_RECEIVED(onFvmChangedMessage, FVMChangedId, msgJson);
                break;
            case ProtocolType::UDP_FVM_CHANNEL_CHANGED://WEB->FVM
                MSG_DATA_RECEIVED(onChannelChangedMessage, FVMChannelChanged, msgJson);
                break;
            case ProtocolType::UDP_FVM_RTSP_CHANGED://WEB->FVM 新增/修改接入前端(RTSP)
                MSG_DATA_RECEIVED(onRTSPChangedMessage, RequestRTSPChanged, msgJson);
                break;
            case ProtocolType::UDP_FVM_REQUEST_TEMPVIDEO://WEB->FVM 请求视频查看
                MSG_DATA_RECEIVED(onRequestTempVideoMessage, RequestTempVideo, msgJson);
                break;
            case ProtocolType::UDP_FVM_REQUEST_SETVIDEO://WEB->FVM 请求设置检测区
                MSG_DATA_RECEIVED(onRequestSetVideoMessage, RequestSetVideo, msgJson);
                break;
            case ProtocolType::UDP_FVM_VIDEOPAUSE://WEB->FVM 暂停视频资源
                MSG_DATA_RECEIVED(onVideoPauseMessage, RequestVideoPause, msgJson);
                break;
            case ProtocolType::UDP_FVM_VIDEORESUME://WEB->FVM 恢复视频资源
                MSG_DATA_RECEIVED(onVideoResumeMessage, RequestVideoResume, msgJson);
                break;
            case ProtocolType::UDP_FVM_SETPOSITION://WEB->FVM 设置基准位
                MSG_DATA_RECEIVED(onSetPosition, SetPosition, msgJson);
                break;
            case ProtocolType::UDP_REMOTECHANGED://GUM->FVM 平台视频资源更新
                MSG_DATA_RECEIVED(onRemoteChangedMessage, RequestRemoteChanged, msgJson);
                break;
            case ProtocolType::UDP_REMOTESTATUS: //GUM->FVM 平台状态更新
                MSG_DATA_RECEIVED(onRemoteStatusMessage, PostRemoteStatus, msgJson);
                break;  
            case ProtocolType::UDP_FVM_MONITORCHANGED://WEB->FVM 下发数据
                MSG_RECEIVED(onMonitorChangedMessage);
                break;
            case ProtocolType::UDP_FVM_PTZCONTROL://WEB->FVM ptz控制
                MSG_DATA_RECEIVED(onPtzControl, PtzControl, msgJson);
                break;
            case ProtocolType::UDP_FVM_PRESETCONTROL://WEB->FVM 设置相机预置位
                MSG_DATA_RECEIVED(onPresetControl, PresetControl, msgJson);
                break;
            case ProtocolType::UDP_FVM_FOCUSEVENT://WEB->FVM 聚焦事件
                MSG_DATA_RECEIVED(onFocusEvent, FocusEvent, msgJson);
                break;
            default:
                ai::LogWarn << "no msg handler: " << msgData;//打印没处理的ID
                break;
        }
    }
}


