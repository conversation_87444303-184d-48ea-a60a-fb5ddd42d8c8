#pragma once
#include "inibase.h"

#define FVM_VERSION "4.0.1"

/*
 * 定义INI配置
 *   (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：　int bool string
 */
DEFINE_INI_CONFIG_ALL(fvm, "config/fvm.ini",
    (int, procID, app, 1, "进程号")
    (int, fvmPort, app, 5000, "端口号")
    (int, outrtp, app, 0, "iva收流类型: 0为rtp(默认) 1为udp")
    (int, logSize, log, 256, "保持单日志大小(MB)")
    (int, logNum, log, 50, "保持日志个数")
    (int, fflog, ffmpeg, 0, "ffmpeg日志等级")
    (string, dbName, database, "aimonitorv3", "数据库名称")
    (string, dbHost, database, "127.0.0.1", "数据库地址")
    (double, offsetThreshold, channel, 0.05, "PTZ云台偏移阈值")
    (double, restoreThreshold, channel, 0.001, "PTZ云台恢复阈值")
    (int, eventPztCheckInterval, channel, 1, "事件发生时PTZ云台检测时间间隔秒")
    (bool, fileRepeat, channel, true, "文件流是否循环")
    (string, recordPath, record, "./video", "录像路径")
    (int, cmdSaveLength, record, 1800, "命令行录像最长时间 单位秒")
    (bool, enableFilterEvent, event, true, "是否开启事件过滤")
    (string, localIP, network, "127.0.0.1", "本机默认ip")
)

__attribute__((unused)) static void showVersion() {
    auto tt = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
    struct tm *ptm = localtime(&tt);
    int iYear = ptm->tm_year + 1900;
    std::cout << "FVM version " << FVM_VERSION << " Copyright © 2016 - " << iYear
              << " WELLTRANS O&E CO., All Rights Reserved." << std::endl;
}
#define SETTINGS fvm::Config::instance()
