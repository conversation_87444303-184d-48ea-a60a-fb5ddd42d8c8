#include "Gb28181Sdp.hpp"
#include "Gb28181XmlTag.hpp"

namespace gb28181
{

    CGb28181Sdp::CGb28181Sdp(void)
    {
    }

    CGb28181Sdp::~CGb28181Sdp(void)
    {
    }

    bool CGb28181Sdp::makeString( const SRealMedia &gbtInfo, std::string &outString )
    {
        std::string out;
        char strv[10]={0};
        sprintf(strv,"v=%d\r\n",0);
        out += strv;

        ACE_INET_Addr tmpAddr;
        if( 0 != tmpAddr.set( u_short(0), gbtInfo.sockAddr ) )
            return false;

        char stro[64]={0};
        sprintf(stro,"o=%s %d %d IN IP4 %s\r\n",outString.c_str(),0,0,tmpAddr.get_host_addr());
        out += stro;

        std::string strs = "s=Play\r\n";
        out += strs;

        char strc[32]={0};
        sprintf(strc,"c=IN IP4 %s\r\n",tmpAddr.get_host_addr());
        out += strc;

        char strt[10]={0};
        sprintf(strt,"t=%d %d\r\n",0,0);
        out += strt;

        char strm[50]={0};
        if (gbtInfo.socketType == ESOCKETTYPE_TCP_PASSIVE)
        {
            sprintf(strm, "m=video %d TCP/RTP/AVP 96 97 98\r\n", gbtInfo.sockPort);
            out += strm;

            std::string strb = "a=setup:passive\r\n";
            out += strb;
        }
        else if (gbtInfo.socketType == ESOCKETTYPE_TCP_ACTIVE )
        {
            sprintf(strm, "m=video %d TCP/RTP/AVP 96 97 98\r\n", gbtInfo.sockPort);
            out += strm;

            std::string strb = "a=setup:active\r\n";
            out += strb;
        }
        else if (gbtInfo.socketType == ESOCKETTYPE_UDP)
        {
            sprintf(strm, "m=video %d RTP/AVP 96 97 98\r\n", gbtInfo.sockPort);
            out += strm;
        }

        std::string stra = "a=recvonly\r\n";
        out += stra;

        char straps[32]={0};
        sprintf(straps,"a=rtpmap:96 %s/%d\r\n","PS",90000);
        out += straps;

        char strampeg4[32]={0};
        sprintf(strampeg4,"a=rtpmap:97 %s/%d\r\n",TAG_SDP_VIDEOTYPE_MPEG4.c_str(),90000);
        out += strampeg4;

        char strah264[32]={0};
        sprintf(strah264,"a=rtpmap:98 %s/%d\r\n",TAG_SDP_VIDEOTYPE_H264.c_str(),90000);
        out += strah264;

	    //增加f字段，用于在实时流请求时，描述流信息，如编码格式、分辨率、帧率、码率等
	    char strf[32]={0};
	    //f=v/编码格式/分辨率/帧率/码率类型/码率大小a/编码格式/码率大小/采样率
	    //编码格式：1-MPEG-4 2-H.264 3-SVAC 4-3GP
	    //分辨率：1-QCIF 2-CIF 3-4CIF 4-D1 5-720P 6-1080P/I
	    //帧率： 0~99
	    //码率类型：1-定码率CBR 2-变码率VBR
	    //码率大小：0~1000000 （1表示1kbps）    
	    if (!gbtInfo.supportFormatTypes.empty() && !gbtInfo.supportVideoTypes.empty())
	        sprintf(strf, "f=v/%d/%d/25/1/%da///\r\n",
	            gbtInfo.supportVideoTypes[0],
	            gbtInfo.supportFormatTypes[0], gbtInfo.maxBitrate);
	    else
	        sprintf(strf,"f=v/////a///\r\n");
        out += strf;

        outString = out;

        return true;
    }

    bool CGb28181Sdp::makeString( const SHistoryMedia &gbtInfo,const bool download, std::string &outString)
    {
        std::string out;
        char strv[10]={0};
        sprintf(strv,"v=%d\r\n",0);
        out += strv;

        ACE_INET_Addr tmpAddr;
        if( 0 != tmpAddr.set( u_short(0), gbtInfo.resAddr.c_str() ) )
            return false;

        char stro[64]={0};
        sprintf(stro,"o=%s %d %d IN IP4 %s\r\n",outString.c_str(),0,0,tmpAddr.get_host_addr());
        out += stro;

        std::string strs = download ? "s=Download\r\n" : "s=Playback\r\n";
        out += strs;

        char stru[64]={0};
        sprintf(stru,"u=%s:%d\r\n",outString.c_str(),0);
        out += stru;

        char strc[32]={0};
        sprintf(strc,"c=IN IP4 %s\r\n",tmpAddr.get_host_addr());
        out += strc;

        char strt[32]={0};
        sprintf(strt,"t=%s %s\r\n",gbtInfo.beginTime.c_str(),gbtInfo.endTime.c_str());
        out += strt;

        char strm[64]={0};
        sprintf(strm,"m=video %d RTP/AVP 96 98 97\r\n",gbtInfo.rtpPort);
        out += strm;

        char stra[128]={0};
        sprintf(stra,"a=recvonly\r\na=rtpmap:96 PS/90000\r\na=rtpmap:98 H264/90000\r\na=rtpmap:97 MPEG4/90000\r\n");
        out += stra;

        outString = out;

        return true;
    }

}
