/**
 * @addtogroup odbDatabaseGroup
 * @brief 检测仪
 * @{
 */
#ifndef _MONITOR_H
#define _MONITOR_H


#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
/**
 * @brief: 检测仪 平台信息 对应数据库aimonitorV3的表monitor
 */
#pragma db object table("wn_monitor")
class Monitor {
public:

    Monitor(const std::string& name,
        const std::string& ip,
        unsigned long detectionPointCnt,
        bool isDel,
        bool isEnable,
        std::string ntpServer,
        bool isMain,
        int status/*,
        int fvmStatus,
        std::string fvmUpdateTime,
        int ivaStatus,
        std::string ivaUpdateTime
        */
        )
            : name(name), ip(ip), detectionPointCnt(detectionPointCnt),
            isDel(isDel), isEnable(isEnable), ntpServer(ntpServer),
            isMain(isMain), status(status)/*, fvmStatus(fvmStatus),
            fvmUpdateTime(fvmUpdateTime), ivaStatus(ivaStatus), ivaUpdateTime(ivaUpdateTime)*/
    {
    }

    unsigned long getId() const {
        return id;
    }

    const std::string& getName() const {
        return name;
    }

    void setName(const std::string& name) {
        this->name = name;
    }

    const std::string& getIp() const {
        return ip;
    }

    void setIp(const std::string& ip) {
        this->ip = ip;
    }

    unsigned long getDetectPointCnt()const {
        return detectionPointCnt;
    }

    void setDetectPointCnt(unsigned long cnt) {
        this->detectionPointCnt = cnt;
    }

    bool getIsDel() const {
        return isDel;
    }

    void setIsDel(bool del) {
        this->isDel = del;
    }

    bool getIsEnable() const {
        return isEnable;
    }

    void setIsEnable(bool enable) {
        this->isEnable = enable;
    }

    bool getIsMain() const {
        return isMain;
    }

    void setIsMain(bool ismain) {
        this->isMain = ismain;
    }

    int getStatus()const {
        return status;
    }

    void setStatus(int status) {
        this->status = status;
    }
/*
    int getFvmStatus()const {
        return fvmStatus;
    }

    void setFvmStatus(int status) {
        this->fvmStatus = status;
    }

    const std::string& getFvmUpdateTime() const {
        return fvmUpdateTime;
    }

    void setFvmUpdateTime(const std::string& time) {
        this->fvmUpdateTime = time;
    }

    int getIvaStatus()const {
        return ivaStatus;
    }

    void setIvaStatus(int status) {
        this->ivaStatus = status;
    }

    const std::string& getIvaUpdateTime() const {
        return ivaUpdateTime;
    }

    void setIvaUpdateTime(const std::string& time) {
        this->ivaUpdateTime = time;
    }
*/
private:

    friend class odb::access;
    Monitor() {}

private:
#pragma db id auto
    unsigned long id;                  //!< 表ID

#pragma db column("name")  type("VARCHAR(255)")
    std::string name;                  //!< 检测仪名称

#pragma db column("ip")  type("VARCHAR(255)")
    std::string ip;                    //!< 检测仪ip

#pragma db column("detection_point_count")
    unsigned long detectionPointCnt;   //!< 检测仪通道总数 对应表wn_detection_point(DetectionPoint)

#pragma db column("is_del") type("INT")
    bool isDel;                        //!< 是否删除

#pragma db column("is_enable") type("INT")
    bool isEnable;                     //!< 是否使能

#pragma db column("ntp_server")  type("VARCHAR(255)")
    std::string ntpServer;             //!< ntp服务器地址

#pragma db column("is_main")  type("INT(255)")
    bool isMain;                       //!< 是否是主检测仪

#pragma db column("status")
    int status;                        //!< 检测仪状态
/*
#pragma db column("fvm_status")
    int fvmStatus;                     //!< 当前检测仪上fvm运行状态

#pragma db column("fvm_update_time")  type("VARCHAR(255)")
    std::string fvmUpdateTime;         //!< 当前检测仪上fvm更新时间

#pragma db column("iva_status")
    int ivaStatus;                     //!< 当前检测仪上iva运行状态

#pragma db column("iva_update_time")  type("VARCHAR(255)")
    std::string ivaUpdateTime;         //!< 当前检测仪上iva更新时间
*/
};
}
#endif //_MONITOR_H

/**
 * @}
 */
