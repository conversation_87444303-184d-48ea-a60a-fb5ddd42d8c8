// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#ifndef VIDEO_SERVER_ODB_HXX
#define VIDEO_SERVER_ODB_HXX

#include <odb/version.hxx>


#include <odb/pre.hxx>

#include "video_server.h"

#include <memory>
#include <cstddef>
#include <utility>

#include <odb/core.hxx>
#include <odb/traits.hxx>
#include <odb/callback.hxx>
#include <odb/wrapper-traits.hxx>
#include <odb/pointer-traits.hxx>
#include <odb/container-traits.hxx>
#include <odb/no-op-cache-traits.hxx>
#include <odb/result.hxx>
#include <odb/simple-object-result.hxx>

#include <odb/details/unused.hxx>
#include <odb/details/shared-ptr.hxx>

namespace odb
{
  // VideoServer
  //
  template <>
  struct class_traits< ::db::VideoServer >
  {
    static const class_kind kind = class_object;
  };

  template <>
  class access::object_traits< ::db::VideoServer >
  {
    public:
    typedef ::db::VideoServer object_type;
    typedef ::db::VideoServer* pointer_type;
    typedef odb::pointer_traits<pointer_type> pointer_traits;

    static const bool polymorphic = false;

    typedef long unsigned int id_type;

    static const bool auto_id = true;

    static const bool abstract = false;

    static id_type
    id (const object_type&);

    typedef
    no_op_pointer_cache_traits<pointer_type>
    pointer_cache_traits;

    typedef
    no_op_reference_cache_traits<object_type>
    reference_cache_traits;

    static void
    callback (database&, object_type&, callback_event);

    static void
    callback (database&, const object_type&, callback_event);
  };
}

#include <odb/details/buffer.hxx>

#include <odb/mysql/version.hxx>
#include <odb/mysql/forward.hxx>
#include <odb/mysql/binding.hxx>
#include <odb/mysql/mysql-types.hxx>
#include <odb/mysql/query.hxx>

namespace odb
{
  // VideoServer
  //
  template <typename A>
  struct query_columns< ::db::VideoServer, id_mysql, A >
  {
    // id
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    id_type_;

    static const id_type_ id;

    // videoSourceName
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    videoSourceName_type_;

    static const videoSourceName_type_ videoSourceName;

    // accessType
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    accessType_type_;

    static const accessType_type_ accessType;

    // ip
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    ip_type_;

    static const ip_type_ ip;

    // port
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    port_type_;

    static const port_type_ port;

    // userName
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    userName_type_;

    static const userName_type_ userName;

    // password
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    password_type_;

    static const password_type_ password;

    // factory
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    factory_type_;

    static const factory_type_ factory;

    // sipid
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    sipid_type_;

    static const sipid_type_ sipid;

    // status
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    status_type_;

    static const status_type_ status;

    // subNum
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    subNum_type_;

    static const subNum_type_ subNum;

    // isDel
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        bool,
        mysql::id_long >::query_type,
      mysql::id_long >
    isDel_type_;

    static const isDel_type_ isDel;
  };

  template <typename A>
  const typename query_columns< ::db::VideoServer, id_mysql, A >::id_type_
  query_columns< ::db::VideoServer, id_mysql, A >::
  id (A::table_name, "`id`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoServer, id_mysql, A >::videoSourceName_type_
  query_columns< ::db::VideoServer, id_mysql, A >::
  videoSourceName (A::table_name, "`name`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoServer, id_mysql, A >::accessType_type_
  query_columns< ::db::VideoServer, id_mysql, A >::
  accessType (A::table_name, "`access_type`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoServer, id_mysql, A >::ip_type_
  query_columns< ::db::VideoServer, id_mysql, A >::
  ip (A::table_name, "`ip`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoServer, id_mysql, A >::port_type_
  query_columns< ::db::VideoServer, id_mysql, A >::
  port (A::table_name, "`port`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoServer, id_mysql, A >::userName_type_
  query_columns< ::db::VideoServer, id_mysql, A >::
  userName (A::table_name, "`username`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoServer, id_mysql, A >::password_type_
  query_columns< ::db::VideoServer, id_mysql, A >::
  password (A::table_name, "`password`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoServer, id_mysql, A >::factory_type_
  query_columns< ::db::VideoServer, id_mysql, A >::
  factory (A::table_name, "`factory`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoServer, id_mysql, A >::sipid_type_
  query_columns< ::db::VideoServer, id_mysql, A >::
  sipid (A::table_name, "`sipid`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoServer, id_mysql, A >::status_type_
  query_columns< ::db::VideoServer, id_mysql, A >::
  status (A::table_name, "`status`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoServer, id_mysql, A >::subNum_type_
  query_columns< ::db::VideoServer, id_mysql, A >::
  subNum (A::table_name, "`sub_num`", 0);

  template <typename A>
  const typename query_columns< ::db::VideoServer, id_mysql, A >::isDel_type_
  query_columns< ::db::VideoServer, id_mysql, A >::
  isDel (A::table_name, "`is_del`", 0);

  template <typename A>
  struct pointer_query_columns< ::db::VideoServer, id_mysql, A >:
    query_columns< ::db::VideoServer, id_mysql, A >
  {
  };

  template <>
  class access::object_traits_impl< ::db::VideoServer, id_mysql >:
    public access::object_traits< ::db::VideoServer >
  {
    public:
    struct id_image_type
    {
      unsigned long long id_value;
      my_bool id_null;

      std::size_t version;
    };

    struct image_type
    {
      // id
      //
      unsigned long long id_value;
      my_bool id_null;

      // videoSourceName
      //
      details::buffer videoSourceName_value;
      unsigned long videoSourceName_size;
      my_bool videoSourceName_null;

      // accessType
      //
      int accessType_value;
      my_bool accessType_null;

      // ip
      //
      details::buffer ip_value;
      unsigned long ip_size;
      my_bool ip_null;

      // port
      //
      int port_value;
      my_bool port_null;

      // userName
      //
      details::buffer userName_value;
      unsigned long userName_size;
      my_bool userName_null;

      // password
      //
      details::buffer password_value;
      unsigned long password_size;
      my_bool password_null;

      // factory
      //
      details::buffer factory_value;
      unsigned long factory_size;
      my_bool factory_null;

      // sipid
      //
      details::buffer sipid_value;
      unsigned long sipid_size;
      my_bool sipid_null;

      // status
      //
      int status_value;
      my_bool status_null;

      // subNum
      //
      int subNum_value;
      my_bool subNum_null;

      // isDel
      //
      int isDel_value;
      my_bool isDel_null;

      std::size_t version;
    };

    struct extra_statement_cache_type;

    using object_traits<object_type>::id;

    static id_type
    id (const id_image_type&);

    static id_type
    id (const image_type&);

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&,
          mysql::statement_kind);

    static void
    bind (MYSQL_BIND*, id_image_type&);

    static bool
    init (image_type&,
          const object_type&,
          mysql::statement_kind);

    static void
    init (object_type&,
          const image_type&,
          database*);

    static void
    init (id_image_type&, const id_type&);

    typedef mysql::object_statements<object_type> statements_type;

    typedef mysql::query_base query_base_type;

    static const std::size_t column_count = 12UL;
    static const std::size_t id_column_count = 1UL;
    static const std::size_t inverse_column_count = 0UL;
    static const std::size_t readonly_column_count = 0UL;
    static const std::size_t managed_optimistic_column_count = 0UL;

    static const std::size_t separate_load_column_count = 0UL;
    static const std::size_t separate_update_column_count = 0UL;

    static const bool versioned = false;

    static const char persist_statement[];
    static const char find_statement[];
    static const char update_statement[];
    static const char erase_statement[];
    static const char query_statement[];
    static const char erase_query_statement[];

    static const char table_name[];

    static void
    persist (database&, object_type&);

    static pointer_type
    find (database&, const id_type&);

    static bool
    find (database&, const id_type&, object_type&);

    static bool
    reload (database&, object_type&);

    static void
    update (database&, const object_type&);

    static void
    erase (database&, const id_type&);

    static void
    erase (database&, const object_type&);

    static result<object_type>
    query (database&, const query_base_type&);

    static unsigned long long
    erase_query (database&, const query_base_type&);

    public:
    static bool
    find_ (statements_type&,
           const id_type*);

    static void
    load_ (statements_type&,
           object_type&,
           bool reload);
  };

  template <>
  class access::object_traits_impl< ::db::VideoServer, id_common >:
    public access::object_traits_impl< ::db::VideoServer, id_mysql >
  {
  };

  // VideoServer
  //
}

#include "video_server-odb.ixx"

#include <odb/post.hxx>

#endif // VIDEO_SERVER_ODB_HXX
