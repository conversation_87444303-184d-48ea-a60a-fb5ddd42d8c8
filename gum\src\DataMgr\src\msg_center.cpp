//
// Created by yp on 2021/11/9.
//

#include "msg_center.h"
#include "ailog.h"
#include "network.h"
#include "util/protocol_util.h"
#include "util/json_util.h"
#include "protocol.hpp"
#include "worker_pool.h"

namespace gum {

    using namespace wtoe;
    using namespace network;

    #define MSG_DATA_RECEIVED(SIGNAL, PROTOCOL, DATA) \
    { \
        auto msg = deserialize<PROTOCOL>(DATA); \
        worker::post(worker::WorkerType::Message, [this, msg]() {SIGNAL(msg);}); \
    } \

    bool MsgCenter::init() {
        int listenPort = DATA_CENTER.getListenPort();
        if (listenPort == 0) {
            ai::LogError << "GUM server not set listenport";
            return false;
        }
        // 启动任务池
        worker::init();

        // 初始化GUM 监听服务
        bool rt = network::initServer(listenPort, [this](auto &data) { handleProtocolReceived(data); });
        if(!rt)
        {
            ai::LogError << "GUM server start failed! port: " << listenPort<< network::lastErrorMsg() << lastErrorCode();
            return false;
        }

        ai::LogInfo << "GUM Listen At " << listenPort;

        onRequestVideoMessage.connect([](RequestVideo msg){
            std::string szSrc, szError;
            int ret = DATA_CENTER.onRequestVideo( msg, szSrc, szError );
            if ( ret == 999 )	//表示正在处理上一个请求，本次消息不处理
                return;
            if ( ret != 0 )
                ai::LogInfo << "onRequestVideo " << msg.iVideoId << " ret: " << ret << " "<< szError;
            MSG_CENTER.retRequestVideo( msg, szSrc, ret );
        });
        onRequestStopVideoMessage.connect([](RequestStopVideo msg){
            std::string szError;
            int ret = DATA_CENTER.onRequestStopVideo( msg, szError );
            ai::LogInfo << "onRequestStopVideo " << msg.iVideoId << " ret: " << ret << " " << szError;
            MSG_CENTER.retRequestStopVideo( msg, ret );
        });
        onRequestPtzOperMessage.connect([](RequestPtzOper msg){
            int ret = DATA_CENTER.onRequestPtzOper(msg);
            MSG_CENTER.retPtzOper(msg, ret);
        });
        onRequestRemoteChangedMessage.connect([](RequestRemoteChanged msg){
            DATA_CENTER.onRemoteChanged( msg.iRemoteId );
        });
        onRequestFvmChangedMessage.connect([](FVMChangedId msg){
            DATA_CENTER.onFvmChanged();
        });

        return true;
    }

    bool MsgCenter::fini() {
        worker::close();
        int listenPort = DATA_CENTER.getListenPort();
        network::stopServer( listenPort );
        return true;
    }

    bool MsgCenter::retRequestVideo( const RequestVideo& msg, const std::string& szSrc, int ret )
    {
        /*return info:
        "remoteId":1,
        "videoKey" : "11111111112222222021",
        "destAddr" : "192.168.77.91:9002",
        "transferType" : "UDP",
        "srcAddr" : "192.168.77.115:15260",
        "result" : 0*/
        RequestVideoRet retInfo;
        retInfo.destAddr = msg.destAddr;
        retInfo.iRemoteId = msg.iRemoteId;
        retInfo.iResult = ret;
        retInfo.transferType = msg.transferType;
        retInfo.srcAddr = szSrc;
        retInfo.iVideoId = msg.iVideoId;
        return sendTo(msg.returnAddr, ProtocolType::UDP_REQUEST_VIDEORET, retInfo);
    }

    bool MsgCenter::retRequestStopVideo(const RequestStopVideo& msg, int ret )
    {
        /*return info:
        "remoteId":1,
        "videoKey": "11111111112222222021",
        "result": 0 */
        RequestStopVideoRet retInfo;
        retInfo.iRemoteId = msg.iRemoteId;
        retInfo.iVideoId = msg.iVideoId;
        retInfo.iResult = ret;
        return sendTo(msg.returnAddr, ProtocolType::UDP_STOPVIDEORET, retInfo);
    }

    bool MsgCenter::retPtzOper(const RequestPtzOper& msg, int ret )
    {
        /*return info:
		"remoteId":1,
	    "videoKey": "11111111112222222021",
	    "result": 0 */
        RequestPtzOperRet retInfo;
        retInfo.iRemoteId = msg.iRemoteId;
        retInfo.iVideoId = msg.iVideoId;
        retInfo.iResult = ret;
        return sendTo(msg.returnAddr, ProtocolType::UDP_PTZOPERRET, retInfo);
    }

	bool MsgCenter::postRemoteStatus( const std::string sFvmAddr, int iRemoteId, int iStatus)
	{
        PostRemoteStatus info;
        info.iRemoteId = iRemoteId;
        info.iStatus = iStatus;
        return sendTo(sFvmAddr, ProtocolType::UDP_REMOTESTATUS,  info );
	}

	template<typename T>
    bool MsgCenter::sendTo(const std::string &strAddr, ProtocolType msgType, const T &data) {
        std::string host;
        size_t pos = strAddr.find(":");
        short port = 5000; //默认为5000端口
        if (pos == std::string::npos) {
            host = strAddr;
        }
        else {
            host = strAddr.substr(0, pos);
            port = atoi(strAddr.substr(pos + 1, strAddr.length()).c_str());
        }

        auto msg  = network::generateProtocol<T>(data, msgType, network::SessionType::GUM);
        auto returnVal = network::sendDataTo(host, port, msg);

        if(!returnVal && network::lastErrorCode())
        {
            ai::LogInfo << host <<" msg sent failed! code: " << network::lastErrorCode() <<" err: " << network::lastErrorMsg();
        }
        else
        {
            ai::LogInfo << "To " << host <<" " << msg;
        }
        return returnVal;
    }

    void MsgCenter::handleProtocolReceived(std::string &msgData) {
        ai::LogInfo << "RECV " << msgData;
        auto[msgType, msgJson] = network::parseProtocol(msgData);
        switch (msgType) {
            case ProtocolType::UDP_REQUEST_VIDEO: //FVM->GUM 请求视频
                MSG_DATA_RECEIVED(onRequestVideoMessage, RequestVideo, msgJson);
                break;
            case ProtocolType::UDP_STOPVIDEO: //FVM->GUM 停止视频
                MSG_DATA_RECEIVED(onRequestStopVideoMessage, RequestStopVideo, msgJson );
                break;
            case ProtocolType::UDP_PTZOPER: //FVM->GUM 云台控制
                MSG_DATA_RECEIVED(onRequestPtzOperMessage, RequestPtzOper, msgJson);
                break;
            case ProtocolType::UDP_REMOTECHANGED: //FVM->GUM 新增/修改/删除接入前端 资源变化
                MSG_DATA_RECEIVED(onRequestRemoteChangedMessage, RequestRemoteChanged, msgJson);
                break;
            case ProtocolType::UDP_FVM_CHANGED:
                MSG_DATA_RECEIVED(onRequestFvmChangedMessage, FVMChangedId, msgJson);
                break;
            default:
                ai::LogWarn << "no msg handler: " << msgData;//打印没处理的ID
                break;
        }
    }

} //namespace gum
