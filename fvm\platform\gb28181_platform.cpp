/**
 * Project FVM
 */


#include "gb28181_platform.h"
#include "platform_manager.h"
#include "data/config_param_info.h"

/**
 * GB28181Platform implementation
 */
namespace fvm::platform
{
    using namespace data;
    void GB28181Platform::init(data::VideoServerPtr serverPtr) {
        FrontPlatform::init( serverPtr );
        destIp = DATA_MANAGER.getGumIP();
        destPort = DATA_MANAGER.getGumPort();

        int gbType = DATA_MANAGER.getParamProgData(GB_STREAM_TYPE, 0 );
        if ( gbType == 1 ) //28181暂时只可取主子码流，1表示子码流，其他都为主码流
            streamType = StreamType::Sub;
        else
            streamType = StreamType::Main;
        std::string sipId = serverPtr->getSipid();
        size_t pos = sipId.find( "-");
        int iType = 1;
        if ( pos != std::string::npos )
            iType = atoi( sipId.substr(pos + 1, sipId.length()).c_str() );
        transferType = static_cast<TransferType>(iType);
    }
}