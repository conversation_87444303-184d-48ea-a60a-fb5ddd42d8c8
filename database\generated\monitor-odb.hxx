// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#ifndef MONITOR_ODB_HXX
#define MONITOR_ODB_HXX

#include <odb/version.hxx>


#include <odb/pre.hxx>

#include "monitor.h"

#include <memory>
#include <cstddef>
#include <utility>

#include <odb/core.hxx>
#include <odb/traits.hxx>
#include <odb/callback.hxx>
#include <odb/wrapper-traits.hxx>
#include <odb/pointer-traits.hxx>
#include <odb/container-traits.hxx>
#include <odb/no-op-cache-traits.hxx>
#include <odb/result.hxx>
#include <odb/simple-object-result.hxx>

#include <odb/details/unused.hxx>
#include <odb/details/shared-ptr.hxx>

namespace odb
{
  // Monitor
  //
  template <>
  struct class_traits< ::db::Monitor >
  {
    static const class_kind kind = class_object;
  };

  template <>
  class access::object_traits< ::db::Monitor >
  {
    public:
    typedef ::db::Monitor object_type;
    typedef ::db::Monitor* pointer_type;
    typedef odb::pointer_traits<pointer_type> pointer_traits;

    static const bool polymorphic = false;

    typedef long unsigned int id_type;

    static const bool auto_id = true;

    static const bool abstract = false;

    static id_type
    id (const object_type&);

    typedef
    no_op_pointer_cache_traits<pointer_type>
    pointer_cache_traits;

    typedef
    no_op_reference_cache_traits<object_type>
    reference_cache_traits;

    static void
    callback (database&, object_type&, callback_event);

    static void
    callback (database&, const object_type&, callback_event);
  };
}

#include <odb/details/buffer.hxx>

#include <odb/mysql/version.hxx>
#include <odb/mysql/forward.hxx>
#include <odb/mysql/binding.hxx>
#include <odb/mysql/mysql-types.hxx>
#include <odb/mysql/query.hxx>

namespace odb
{
  // Monitor
  //
  template <typename A>
  struct query_columns< ::db::Monitor, id_mysql, A >
  {
    // id
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    id_type_;

    static const id_type_ id;

    // name
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    name_type_;

    static const name_type_ name;

    // ip
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    ip_type_;

    static const ip_type_ ip;

    // detectionPointCnt
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    detectionPointCnt_type_;

    static const detectionPointCnt_type_ detectionPointCnt;

    // isDel
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        bool,
        mysql::id_long >::query_type,
      mysql::id_long >
    isDel_type_;

    static const isDel_type_ isDel;

    // isEnable
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        bool,
        mysql::id_long >::query_type,
      mysql::id_long >
    isEnable_type_;

    static const isEnable_type_ isEnable;

    // ntpServer
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    ntpServer_type_;

    static const ntpServer_type_ ntpServer;

    // isMain
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        bool,
        mysql::id_long >::query_type,
      mysql::id_long >
    isMain_type_;

    static const isMain_type_ isMain;

    // status
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    status_type_;

    static const status_type_ status;
  };

  template <typename A>
  const typename query_columns< ::db::Monitor, id_mysql, A >::id_type_
  query_columns< ::db::Monitor, id_mysql, A >::
  id (A::table_name, "`id`", 0);

  template <typename A>
  const typename query_columns< ::db::Monitor, id_mysql, A >::name_type_
  query_columns< ::db::Monitor, id_mysql, A >::
  name (A::table_name, "`name`", 0);

  template <typename A>
  const typename query_columns< ::db::Monitor, id_mysql, A >::ip_type_
  query_columns< ::db::Monitor, id_mysql, A >::
  ip (A::table_name, "`ip`", 0);

  template <typename A>
  const typename query_columns< ::db::Monitor, id_mysql, A >::detectionPointCnt_type_
  query_columns< ::db::Monitor, id_mysql, A >::
  detectionPointCnt (A::table_name, "`detection_point_count`", 0);

  template <typename A>
  const typename query_columns< ::db::Monitor, id_mysql, A >::isDel_type_
  query_columns< ::db::Monitor, id_mysql, A >::
  isDel (A::table_name, "`is_del`", 0);

  template <typename A>
  const typename query_columns< ::db::Monitor, id_mysql, A >::isEnable_type_
  query_columns< ::db::Monitor, id_mysql, A >::
  isEnable (A::table_name, "`is_enable`", 0);

  template <typename A>
  const typename query_columns< ::db::Monitor, id_mysql, A >::ntpServer_type_
  query_columns< ::db::Monitor, id_mysql, A >::
  ntpServer (A::table_name, "`ntp_server`", 0);

  template <typename A>
  const typename query_columns< ::db::Monitor, id_mysql, A >::isMain_type_
  query_columns< ::db::Monitor, id_mysql, A >::
  isMain (A::table_name, "`is_main`", 0);

  template <typename A>
  const typename query_columns< ::db::Monitor, id_mysql, A >::status_type_
  query_columns< ::db::Monitor, id_mysql, A >::
  status (A::table_name, "`status`", 0);

  template <typename A>
  struct pointer_query_columns< ::db::Monitor, id_mysql, A >:
    query_columns< ::db::Monitor, id_mysql, A >
  {
  };

  template <>
  class access::object_traits_impl< ::db::Monitor, id_mysql >:
    public access::object_traits< ::db::Monitor >
  {
    public:
    struct id_image_type
    {
      unsigned long long id_value;
      my_bool id_null;

      std::size_t version;
    };

    struct image_type
    {
      // id
      //
      unsigned long long id_value;
      my_bool id_null;

      // name
      //
      details::buffer name_value;
      unsigned long name_size;
      my_bool name_null;

      // ip
      //
      details::buffer ip_value;
      unsigned long ip_size;
      my_bool ip_null;

      // detectionPointCnt
      //
      unsigned long long detectionPointCnt_value;
      my_bool detectionPointCnt_null;

      // isDel
      //
      int isDel_value;
      my_bool isDel_null;

      // isEnable
      //
      int isEnable_value;
      my_bool isEnable_null;

      // ntpServer
      //
      details::buffer ntpServer_value;
      unsigned long ntpServer_size;
      my_bool ntpServer_null;

      // isMain
      //
      int isMain_value;
      my_bool isMain_null;

      // status
      //
      int status_value;
      my_bool status_null;

      std::size_t version;
    };

    struct extra_statement_cache_type;

    using object_traits<object_type>::id;

    static id_type
    id (const id_image_type&);

    static id_type
    id (const image_type&);

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&,
          mysql::statement_kind);

    static void
    bind (MYSQL_BIND*, id_image_type&);

    static bool
    init (image_type&,
          const object_type&,
          mysql::statement_kind);

    static void
    init (object_type&,
          const image_type&,
          database*);

    static void
    init (id_image_type&, const id_type&);

    typedef mysql::object_statements<object_type> statements_type;

    typedef mysql::query_base query_base_type;

    static const std::size_t column_count = 9UL;
    static const std::size_t id_column_count = 1UL;
    static const std::size_t inverse_column_count = 0UL;
    static const std::size_t readonly_column_count = 0UL;
    static const std::size_t managed_optimistic_column_count = 0UL;

    static const std::size_t separate_load_column_count = 0UL;
    static const std::size_t separate_update_column_count = 0UL;

    static const bool versioned = false;

    static const char persist_statement[];
    static const char find_statement[];
    static const char update_statement[];
    static const char erase_statement[];
    static const char query_statement[];
    static const char erase_query_statement[];

    static const char table_name[];

    static void
    persist (database&, object_type&);

    static pointer_type
    find (database&, const id_type&);

    static bool
    find (database&, const id_type&, object_type&);

    static bool
    reload (database&, object_type&);

    static void
    update (database&, const object_type&);

    static void
    erase (database&, const id_type&);

    static void
    erase (database&, const object_type&);

    static result<object_type>
    query (database&, const query_base_type&);

    static unsigned long long
    erase_query (database&, const query_base_type&);

    public:
    static bool
    find_ (statements_type&,
           const id_type*);

    static void
    load_ (statements_type&,
           object_type&,
           bool reload);
  };

  template <>
  class access::object_traits_impl< ::db::Monitor, id_common >:
    public access::object_traits_impl< ::db::Monitor, id_mysql >
  {
  };

  // Monitor
  //
}

#include "monitor-odb.ixx"

#include <odb/post.hxx>

#endif // MONITOR_ODB_HXX
