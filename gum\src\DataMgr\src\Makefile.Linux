###############################################################################
# shared library file compile makefile 文件模板
# 此makefile文件模板用于将一个工程编译成Linux下的共享库文件
###############################################################################
DEBUG   := debug
RELEASE := release

LINUX 	:= Linux
SUNOS   := SunOS
WIN32   := WIN32

ifneq (,$(findstring $(arch),arm ppc))
    CROSSCOMPILE := $(arch)-linux-
endif

###############################################################################
# Section 1. 工程配置段
###############################################################################

#==============================================================================
# PRG_NAME : 程序名称，此处的程序名称是工程编译完成后的可执行的名称的名字，默认请
#            况下PRG_NAME = PROJECT
# ROOT     : 工程所在的根目录，此处是以个相对目录，最好不要使用绝对路径，一般不
#            需要修改
# SRC_DIR  : 工程源文件目录，该目录是存放了工程的源文件的根目录，建议不要修改
# OUT_ROOT : 输出根路径，相对路径用于存放最终的PROGRAM的编译输出路径，注意此处仅仅
#            只是定义到了bin目录
#            目录下的对应的工程目录
# OUT_DIR  : 输出路径，工程PROGRAM输出的路径，一般不要修改此项目
# IMD_DIR  : 工程在编译过程中的中间文件的输出目录，主要用于存放.o和.d文件，建议不要修改
#==============================================================================
PRG_NAME  := lib$(PRJ_NAME).so

ROOT      := ..
OUT_ROOT  := ../../../../bin
SRC_DIR   := .
OUT_DIR   := $(OUT_ROOT)/$(config)
IMD_DIR   := $(SRC_DIR)/$(config)
3RD_DIR   := /home/<USER>/3rdlibs

WTOE_OUT    := $(OUT_ROOT)/$(config)/wtoe
PUB_OUT   := $(OUT_ROOT)/$(config)/pub

INSTALL_ROOT :=

PRJ_MARCO := $(shell echo $(PRJ_NAME) | tr [:lower:] [:upper:])_PRJ

# 定义源文件列表，注意此处并不包含头文件，此次出必须根据项目中的文件对应的添加文件名
CPP_SRCS := \
${addprefix $(SRC_DIR)/, \
DataMgr.cpp \
DataMgrPkg.cpp \
JsDataMgr.cpp \
data_center.cpp \
msg_center.cpp \
}

#==============================================================================
# INCDIR: 包含文件的路径，用于指示第三方包含头文件的路径，如：-I. -I/usr/local/..
# 如果在util工程中使用则需要添加 -I../../../../../util
#==============================================================================
INC_DIRS := -I. -I../../ -I${PROJECT_SOURCE_DIR}/../
                                 -I/opt/boost/include/
                                 -I/opt/ace/include/
                                 -I/opt/pjsip/include/
                                 -I/opt/wtoe/include/
                                 -I../../../database/
                                 -I../../../database/persistence/
                                 -I../../../database/generated/
                                 -I../../../wtoe-socket/include/
                                 -I../../../log/include/

#==============================================================================
# LIBSDIRS: 第三方库路径，此处用于提供连接器在搜索第三方库的路径的列表
# LIBS    : 第三方程序库，此处包含了第三方程序库的名称的列表，如：-ldl -lcppunit
#==============================================================================


LIBS_DIRS := -L. -L$(OUT_DIR) -L/opt/boost/lib/
                                      -L/opt/ace/lib/
                                      -L/opt/pjsip/lib/
                                      -L/opt/wtoe/lib/
                                      -L../../../out/lib/
                                      -L/opt/other3rd/lib/
LIBS	  := -ljs -lPackageManager -lBasicHelper -lboost_log -lACE -lboost_log_setup -lboost_system -lboost_thread

###############################################################################
# Section 2. 编译选项
###############################################################################
CXX      := $(CROSSCOMPILE)g++
CXXFLAGS := -fPIC -Wall -fmessage-length=0 -D$(PRJ_MARCO) -D$(platform) -D$(bytesequence) -DXP_UNIX -D$(arch)
LDFLAGS  := -shared

ifeq ($(config),$(DEBUG))
CXXFLAGS += -O0 -g3 -D_DEBUG -DTEST_SUPPORT
LIBS     += -lTestSupport
else
CXXFLAGS += -O2 -DNDEBUG
LIBS     +=
endif

ifeq ($(platform),$(LINUX))
LIBS +=
endif
ifeq ($(platform),$(SUNOS))
LIBS +=
endif

# 定义用于可移植的文件操作命令串，无须修改
MAKE    := make
RM      := rm -f
MKDIR   := mkdir -p
LN      := ln -s -f
CP      := cp -f
ECHO    := /bin/echo
XLINKER := -Xlinker -rpath=./ -Xlinker -rpath=./lib

###############################################################################
# Section 3. 编译
###############################################################################

# 定义编译tagert
all test: dumy $(OUT_DIR)/$(PRG_NAME)
dumy:
	-$(MKDIR) $(IMD_DIR)

# 编译每一个源文件成为目标文件，并同时生成源文件的依赖关系文件
$(IMD_DIR)/%.o: $(SRC_DIR)/%.cpp Makefile.$(platform)
	@$(ECHO) 'Building file    : $<'
	@$(ECHO) $(CXX) $(CXXFLAGS) -o $@ -c $<
	@$(CXX) $(CXXFLAGS) $(INC_DIRS) -o $@ -c $< && \
	$(ECHO) -n $(@:%.o=%.d) $(dir $@) > $(@:%.o=%.d) && \
	$(CXX) $(CXXFLAGS) $(INC_DIRS) -MM -MG -P -w -c $< >> $(@:%.o=%.d)
	@$(ECHO) 'Finished building: $<'
	@$(ECHO) ' '

# 定义模块编译时的目标文件列表
OBJS := $(CPP_SRCS:$(SRC_DIR)/%.cpp=$(IMD_DIR)/%.o)

# 定义模块所有源文件的依赖关系列表
DEPS := $(CPP_SRCS:$(SRC_DIR)/%.cpp=$(IMD_DIR)/%.d)

# 包含所有的依赖文件*.d，用于更新所有的依赖关系，避免仅仅只是修改了头文件而不能编译的问题
-include $(DEPS)

# 编译最终的程序文件
$(OUT_DIR)/$(PRG_NAME): $(OBJS)
	@$(ECHO) 'Building Target  : $@'
	$(CXX) $(LDFLAGS) $(XLINKER) -o $@ $(OBJS) $(LIBS_DIRS) $(LIBS)
	@$(ECHO) 'Finished building: $@'
	@$(ECHO) ' '

# 清理编译结果
clean:
	-$(RM) $(OBJS) $(DEPS) $(OUT_DIR)/$(PRG_NAME)
	
install:
	$(CP) $(OUT_DIR)/$(PRG_NAME) $(INSTALL_ROOT)/

uninstall:
	$(RM) $(INSTALL_ROOT)/$(PRG_NAME) 

.PHONY: all clean dumy install uninstall
