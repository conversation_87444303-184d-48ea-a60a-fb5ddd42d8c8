/**
 * Project FVM
 */

#include "stream_puller.h"
#include "util/config/fvm_config.h"
#include "ailog.h"
/**
 * StreamPuller implementation
 * @brief: 基础视频输入 (FFMPEG 输入,含RTSP、RTP等)
 */

namespace fvm::stream
{
    using namespace data;
    using std::chrono::steady_clock;
    using std::chrono::duration_cast;
    using std::chrono::milliseconds;
    using namespace network;

    /**
    * 初始化参数
    */
    void StreamPuller::initOptions()
    {
        av_dict_set(&options, "buffer_size", "1024000", 0);
        av_dict_set(&options, "max_delay", "500000", 0);
        av_dict_set(&options, "stimeout", "3000000", 0);  //设置超时断开连接时间 3s
        //av_dict_set(&options, "timeout", "2000", 0);
        av_dict_set(&options, "allowed_media_types", "video", 0);

        if (tcpMode)
        {
            av_dict_set(&options, "rtsp_transport", "tcp", 0);  //默认以udp方式打开，如果以tcp方式打开将udp替换为tcp
        }
    }

    /*
   * 基础视频输入 处理逻辑
   */
    void StreamPuller::process()
    {
        // 解析输入地址
        address = videoSourceInfo->videoSourcePtr->getAddress();
        size_t len = address.length();
        if ( len > 2 && address.substr( len-2, 2 ) == "#u" ) //是否使用tcp收流，若addr后面带上 #u，表示用udp
        {
            tcpMode = false;
            address = address.substr( 0, len-2 );
        }
        printInfo(str(boost::format("Start pull stream %s") % address));

        // 开始连接
        int ret = 0;
        while (jobIsRunning())
        {
            if (!isOpened) // 尝试连接
            {
                auto startTime = steady_clock::now();
                DO_IN_THREAD(this->open();)
                int passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
                if(passed > 1000)
                    std::cerr << this->address<< " openContext "<<  passed << "ms" << std::endl;
            }

            if (!isOpened) // 连接失败 等待5s后尝试
            {
                close();
                WAIT_FOR_SECONDS(5)
                continue;
            }

            // 连接成功 开始收包
            AVPacket* pkt = av_packet_alloc();
            resetContextTimer();

            auto startTime = std::chrono::steady_clock::now();
            DO_IN_THREAD(ret = av_read_frame(formatCtx, pkt);)
            int passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
            if(passed > 1000)
                std::cerr << this->address<< " av_read_frame "<<  passed << "ms" << std::endl;

            // 收包错误处理
            if (ret < 0)
            {
                av_packet_unref(pkt);
                av_packet_free(&pkt);
                if (ret == AVERROR_EOF || !formatCtx->pb || avio_feof(formatCtx->pb) || formatCtx->pb->error)
                {
                    close();
                    onStreamInputLost(); //通知web流状态
                }
                setLastError(str(boost::format("av_read_frame %s FAILED: %s") % address % getErrorString(ret)), ret);
                boost::this_fiber::sleep_for(std::chrono::milliseconds(10));
                continue;
            }
            if (pkt->stream_index != videoIndex) 
            {
                av_packet_unref(pkt);
                av_packet_free(&pkt);
                continue;
            }
            auto packet = std::make_shared<PacketData>(pkt);
            this->onStreamDataReceived(packet);
            boost::this_fiber::sleep_for(std::chrono::milliseconds(2));
        }
        close();
        printInfo(str(boost::format("EXIT %s") % address));
    }

    bool StreamPuller::callCameraPreset(int presetId)
    {
        return onvifPtzInfo->callPreset( presetId );
    }

    bool StreamPuller::isPtzCapable()
    {
        return onvifPtzInfo->isPtzCapable();
    }

    /**
     * @brief                   获取云台坐标
     * @param[out] x,y,z        云台ptz坐标
     * @param[in] actPresetId:  云台真实预置位，nullopt时获取实时坐标，否则获取actPresetId对应的预置位坐标
     */
    bool StreamPuller::getPtzPosition(double& x, double& y, double& z, const std::optional<int>& actPresetId)
    {
        if (!onvifPtzInfo || !videoSourceInfo)
            return false;

        if (actPresetId.has_value())
        {
            //! 优先从数据库中获取预置位的基准坐标，如果数据库中没有，尝试从onvif接口获取
            if (!DATA_MANAGER.getPresetPosition(videoSourceInfo->channelId, actPresetId.value(), x,y,z))
            {
                return onvifPtzInfo->getPresetPosition(actPresetId.value(), x, y, z);
            }
            return true;
        }
        else
        {
            return onvifPtzInfo->getPosition( x, y, z);
        }
    }

    /**
     * @brief      云台控制
     * @param[in] videoId：视频id;
     * @param[in]  action: 云台动作  1：向左 2：向右 3：向上 4：向下 5：变倍短 6：变倍长 7：聚焦近 8：聚焦远 9：光圈小 10：光圈大 11：灯光关 12：灯光开
     * @param[in]  step:  true: 步长  主要用于控制云台转动方向的操作，1-8为步长值，1为最小步长，8为最大步长
     */
    bool StreamPuller::controlPtz(int action, int step)
    {
        if (!onvifPtzInfo)
            return false;

        network::EPtzCommand cmd = convertPtzCmd(action);
        onvifPtzInfo->ptzControl(cmd, step, 0);

        return true;
    }

}