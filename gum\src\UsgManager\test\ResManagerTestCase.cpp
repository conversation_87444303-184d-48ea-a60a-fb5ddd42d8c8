#include <boost/uuid/uuid.hpp>
#include <boost/uuid/uuid_io.hpp>
#include <boost/uuid/uuid_generators.hpp>
#include "ResManagerTestCase.hpp"
#include "UsgManager/src/ResManager.hpp"

namespace usg
{
static const std::string RESFILE("../../testdata/app/UsgManager/config/app/sipRes.cfg");
static const std::string RESFILELOAD("../../testdata/app/UsgManager/config/app/sipRes_load.cfg");

CPPUNIT_TEST_SUITE_NAMED_REGISTRATION( CResManagerTestCase, "CResManagerTestCase" );


CResManagerTestCase::CResManagerTestCase()
{

}

CResManagerTestCase::~CResManagerTestCase()
{

}

void CResManagerTestCase::setUp()
{

}

void CResManagerTestCase::tearDown()
{

}

void CResManagerTestCase::test_onReceiveCatalog()
{
//     CResManager resMgr( RESFILE, EPACKERTYPE_PS );
//     usg::ESgResNotifyAction action = usg::ESGRESNOTIFYACTION_ADD;
//     usg::SResInfo info;
//     info.resName = "test_onReceiveCatalog_add";
//     info.sipResCode = "88888374303";
//     info.resStatus = true;
//     resMgr.onReceiveCatalog( /*action,*/info );
// 
//     std::map<boost::uuids::uuid, SResInfo > all;
//     uint32_t t;
//     CPPUNIT_ASSERT( resMgr.getAllResInfo( all, t ) );
//     CPPUNIT_ASSERT( all.size() == 1 );
// 
//     action = ESGRESNOTIFYACTION_MODIFY;
//     info.resStatus = false;
//     info.resName = "test_onReceiveCatalog_mod";
//     resMgr.onReceiveCatalog( action,info );
//     CPPUNIT_ASSERT( resMgr.getAllResInfo( all, t ) );
//     CPPUNIT_ASSERT( all.size() == 1 );
// 
//     action = ESGRESNOTIFYACTION_DEL;
//     info.sipResCode = "88888374303";
//     resMgr.onReceiveCatalog( /*action,*/info );
//     CPPUNIT_ASSERT( resMgr.getAllResInfo( all, t ) );
//     CPPUNIT_ASSERT( all.size() == 0 );

}

void CResManagerTestCase::test_getResInfo()
{
    CResManager resMgr( RESFILELOAD, EPACKERTYPE_PS );
    CPPUNIT_ASSERT( resMgr.init() );
    uint16_t channel = 1;
    usg::SResInfo info;
    resMgr.getResInfoByChannel( channel, info );
    CPPUNIT_ASSERT( info.sipResCode == "010300000192000002");
    std::string sipcode = "010300000192000003";
    resMgr.getResInfoBySipCode( sipcode,info );
    CPPUNIT_ASSERT( info.channelIndex == 2 );
    std::string strUuid = "3904b6a4-1ac4-42c4-aad6-17488eedc363";
    boost::uuids::uuid id = boost::uuids::string_generator()( strUuid.c_str() );
    resMgr.getResInfoByUuid( id,info );
    CPPUNIT_ASSERT( info.channelIndex == 4 );
    resMgr.fini();
}

}