#include <wtoe/BasicHelper/BasicHelperExp.hpp>
#include "UsgManager.hpp"
#include "SgCtxHandler.hpp"

namespace usg
{

    CSgCtxHandler::CSgCtxHandler()
            : m_protocolWtoe( 0 ), m_sg( 0 )
    {

    }
    bool CSgCtxHandler::init( IProtocolWtoe *protocolWtoe, CUsgManager *sg, IWtoeSipFunc* sipFunc  )
    {
        if( protocolWtoe == 0 )
            return false;
        if( sg == 0 )
            return false;
        if ( sipFunc == 0 )
            return false;
        m_protocolWtoe = protocolWtoe;
        m_sg  = sg;
        m_sipFunc = sipFunc;
        return true;
    }

    void CSgCtxHandler::fini()
    {
        m_protocolWtoe = 0;
        m_sg = 0;
    }

    bool CSgCtxHandler::commitRegist( const std::string &sid )
    {
        // 这是指收到他人向自己注册
        //if ( m_sg )
        //{
        //    return m_sg->onReceiveRegist( sid );
        //}
        //return false;
        return true;
    }

///< sid : 设备id（28181），oid ： 为设备域地址，expries ： 保活的单位时间（60秒）
    bool CSgCtxHandler::commitRegist( const std::string &sid,const std::string &oid,int expries )
    {
        // 这是指收到他人向自己注册
        if ( m_sg )
        {
            return m_sg->onReceiveRegist( sid, oid, expries );
        }
        return false;
    }
    bool CSgCtxHandler::commitSubscribe( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result )
    {
        // 这是指收到他人向自己订阅
        //
        if( 0 == m_protocolWtoe )
            return false;
        if( !m_protocolWtoe->receivePkg( sid, oid, std::string( xml, len ), result ) )
            return false;

        return true;
    }
    bool CSgCtxHandler::commitNotify( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result )
    {
        // 这是指收到他人向自己通知
        // 有:别人向自己推送目录.
        //    别人向自己告警上报.
        if( 0 == m_protocolWtoe )
            return false;
        m_protocolWtoe->setSesstionType(usg::SESSTION_TYPE_NOTIFY);
        if( !m_protocolWtoe->receivePkg( sid, oid, std::string( xml, len ), result ) )
            return false;

        return true;
    }
    bool CSgCtxHandler::commitDdcpDo( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result )
    {
        // 别人发的心跳请求
        // 别人发的历史列表请求
        // 别人发的历史视频请求
        // 别人发的云台控制请求
        // 别人发的预置位查询请求
        if( 0 == m_protocolWtoe || 0 == m_sg )
            return false;
        if ( !m_sg->isRegist( oid ) ) //未注册，不用管
        {
            return false;
        }
        m_protocolWtoe->setSesstionType(usg::SESSTION_TYPE_DDCPDO);
        if( !m_protocolWtoe->receivePkg( sid, oid, std::string( xml, len ), result ) )
            return false;

        return true;
    }

    bool CSgCtxHandler::commitDdcpDo( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::vector<std::string> &resultVec )
    {
        // 别人发的心跳请求
        // 别人发的历史列表请求
        // 别人发的历史视频请求
        // 别人发的云台控制请求
        // 别人发的预置位查询请求
        if( 0 == m_protocolWtoe || 0 == m_sg )
            return false;
//     if ( !m_sg->isRegist( oid ) ) //未注册，不用管
//     {
//         return false;
//     }
        m_protocolWtoe->setSesstionType(usg::SESSTION_TYPE_DDCPDO);
        if( !m_protocolWtoe->receivePkg( sid, oid, std::string( xml, len ), resultVec ) )
            return false;

        return true;
    }

    bool CSgCtxHandler::commitInvite( const std::string &sid, const std::string &oid, const char *xml, size_t len, std::string &result )
    {
        // 别人发的实时视频请求,下级功能，不实现
        //if( 0 == m_protocolWtoe )
        //    return false;
        //if( !m_protocolWtoe->receivePkg( sid, oid, std::string( xml, len ), result ) )
        //    return false;

        return true;
    }
    bool CSgCtxHandler::commitCancel( const std::string &sid )
    {
        // 别人发的实时视频取消，,下级功能，不实现
        //if( 0 == m_protocolWtoe )
        //    return false;
        //if( !m_protocolWtoe->receiveCancel( sid ) )
        //    return false;

        return true;
    }
    bool CSgCtxHandler::commitAck( const std::string &sid )
    {
        // 别人发的实时视频确认，,下级功能，不实现
        //if( 0 == m_protocolWtoe )
        //    return false;
        //if( !m_protocolWtoe->receiveAck( sid ) )
        //    return false;

        return true;
    }
    bool CSgCtxHandler::commitBye( const std::string &sid )
    {
        // 别人发的实时视频结束,下级功能，不实现
        //if( 0 == m_protocolWtoe )
        //    return false;
        //if( !m_protocolWtoe->receiveBye( sid ) )
        //    return false;

        return true;
    }
    bool CSgCtxHandler::commitAnswer( const std::string &sid, const char *xml, size_t len, bool status, int type )
    {
        // 别人发的回应,全部走此函数来处理.
        // 有:
        // 我们发的注册,别人给的回应.
        // 我们发的目录推送,别人给的回应.
        // 我们发的心跳,别人给的回应.
        // 我们发的告警订阅的状态通知,别人给的回应.
        // 我们发的告警上报,别人给的回应.

        if( 0 == m_protocolWtoe || 0 == m_sg || 0 == m_sipFunc )
            return false;

//     if ( !m_sg->isRegist( sid ) ) //未注册，不用管
//     {
//         return false;
//     }
//
        if( !status ) //失败的回应则不需要解析
        {
            return m_sipFunc->onReceiveError( sid );
        }
        else
        {
            if ( type == usg::IInviteSession::SESSION_TYPE )
            {
                std::string codeAddr; // 收answer时,没有用到这个参数.
                std::string result; // 收answer时,没有用到这个参数.
                m_protocolWtoe->setSesstionType(ESESSTION_TYPE(type));
                if( !m_protocolWtoe->receivePkg( sid, codeAddr, std::string( xml, len ), result ,true) )
                    return false;

                std::string format = "";
                if( m_protocolWtoe->getFormatInfo(format) )
                    setFormatValues(sid, format);
                std::string srcAddr = "";
                if ( m_protocolWtoe->getSrcAddr( srcAddr ) )
                    setSrcAddr( sid, srcAddr );
            }
            else
                m_sipFunc->onReceive200Ok( sid );
        }

        return true;
    }

    bool CSgCtxHandler::setCseqValues( const std::string& sid ,int cseq )
    {
        if ( m_sg )
        {
            return m_sg->setCseqValues( sid, cseq );
        }
        return false;
    }

    bool CSgCtxHandler::getCsqlValues( const std::string& sid, int& cseq )
    {
        if ( m_sg )
        {
            return m_sg->getCsqlValues( sid, cseq );
        }
        return false;
    }

    bool CSgCtxHandler::setFormatValues( const std::string& sid, std::string format )
    {
        if ( m_sg )
        {
            return m_sg->setFormatValues( sid, format );
        }
        return false;
    }

    bool CSgCtxHandler::getFormatValues( const std::string& sid, std::string &format )
    {
        if ( m_sg )
        {
            return m_sg->getFormatValues( sid, format );
        }

        return false;
    }

    bool CSgCtxHandler::setSrcAddr( const std::string& sid, std::string srcAddr )
    {
        if ( m_sg )
        {
            return m_sg->setSrcAddr( sid, srcAddr );
        }
        return false;
    }

    bool CSgCtxHandler::getSrcAddr( const std::string& sid, std::string &srcAddr )
    {
        if ( m_sg )
        {
            return m_sg->getSrcAddr( sid, srcAddr );
        }

        return false;
    }

}
