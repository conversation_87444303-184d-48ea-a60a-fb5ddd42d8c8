# AI LOG
&emsp;&emsp;该模块使用boost::log库输出日志信息到窗口或文件中

## 目录
* [目录结构](#目录结构)
* [接口概览](#接口概览)
* [快速示例](#快速示例)  

## 目录结构
<pre>
.
├── include        对外头文件
├── src            主要功能实现
└── test           测试代码
</pre>

## 接口概览

&emsp;&emsp;appName为应用程序名称，生成的日志文件名以该名称开头, InitLog和FiniLog在应用程序的开始和结束仅调用一次

```c++
/*
* 初始化日志系统
* @param appName 日志文件名
* @param "logs" 日志目录
* @param "logSize" 日志单个大小(M)
* @param "logNum" 日志最大数量
*/
ai::InitLog(appName, "logs", SETTINGS->logSize(), SETTINGS->logNum())

// 结束日志系统
ai::FiniLog();

```

按级别输出日志
```c++
ai::LogTrace 
ai::LogDebug 
ai::LogInfo 
ai::LogWarn 
ai::LogError 
ai::LogFatal 
```


## 快速示例
  
```CMakeLists.txt
add_compile_options(-std=c++11 -fPIC -fstack-protector-all -Wall -DBOOST_LOG_DYN_LINK)
# boost
set(BOOST_HOME "/opt/boost")
set(BOOST_LIB
        boost_log
        boost_log_setup )
#头文件
include_directories(
        ${PROJECT_SOURCE_DIR}/../include/
        ${BOOST_HOME}/include/
)

link_directories(
        ${PROJECT_SOURCE_DIR}/../../out/lib
        ${BOOST_HOME}/lib/
)

```
```c++
void testFunc1()
{
    ai::LogError << "this is Error 1";
}

void testFunc2()
{
    ai::LogFatal << "this is Fatal 2";
}

int main( int argc, char* argv[] )
{
    ai::InitLog("test", "logs", 200, 10);

    std::thread td = std::thread([&]()
                            {
                                testFunc2();
                            });
    td.detach();

    testFunc1();
    ai::FiniLog();
    return 0;
};
```