#ifndef _SELECTTHREAD_H
#define _SELECTTHREAD_H

#include "Thread.h"
#include "SocketHandler.h"

class SelectThread :
	public Thread
{
public:
	SelectThread(void);
	~SelectThread(void);
	virtual void Run();
	SocketHandler* GetSocketHandler()
	{
		return &m_h;
	}

private:
	SelectThread(const SelectThread& s) {}
	SelectThread& operator=(const SelectThread& ) { return *this; }
	SocketHandler m_h;
};
#endif

