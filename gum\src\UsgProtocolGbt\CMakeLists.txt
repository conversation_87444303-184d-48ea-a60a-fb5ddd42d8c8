﻿cmake_minimum_required (VERSION 3.5.2)

project(UsgProtocolGbt)
## 目标生成
set(TARGET_LIBRARY "UsgProtocolGbt")
# 输出目录
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/../../out/lib)

add_compile_options(-std=c++17 -fPIC -fstack-protector-all -Wall -Wno-register)
add_compile_options(-DXP_UNIX -DIS_LITTLE_ENDIAN=1 -DPJ_AUTOCONF=1 -DPJ_IS_LITTLE_ENDIAN=1)

# boost
set(BOOST_HOME "/opt/boost")
set(BOOST_LIB
    boost_date_time
    boost_thread
    boost_system
    boost_regex )

#ACE
set(ACE_HOME "/opt/ace")
set(ACE_LIB ACE )

#sip
set(SIP_HOME "/opt/pjsip")
set(SIP_LIB
    pj
    pjlib-util
    pjsip-ua
    pjsip-simple )

#wtoe
set(WTOE_HOME "/opt/wtoe")
set(WTOE_LIB
    BasicHelper)

# 头文件
include_directories(
    ${PROJECT_SOURCE_DIR}/../
    ${BOOST_HOME}/include/
    ${ACE_HOME}/include/
    ${SIP_HOME}/include/
    ${WTOE_HOME}/include/
    /opt/other3rd/include/
)

# 库路径
link_directories(
    ${BOOST_HOME}/lib/
    ${ACE_HOME}/lib/
    ${SIP_HOME}/lib/
    ${WTOE_HOME}/lib/
)

FILE(GLOB src "src/*.cpp")

#file(GLOB_RECURSE sources CONFIGURE_DEPENDS "*.cpp")
SET(ALL_SRC ${include} ${src} )

# 生成动态库
ADD_LIBRARY(${TARGET_LIBRARY} SHARED ${ALL_SRC})
target_link_libraries(${TARGET_LIBRARY} pthread ${BOOST_LIB} ${ACE_LIB} ${SIP_LIB} ${WTOE_LIB})
