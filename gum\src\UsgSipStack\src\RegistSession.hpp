#ifndef REGISTSESSION_HPP_
#define REGISTSESSION_HPP_

#include <pjlib.h>
#include <pjsip.h>

#include "../include/UsgSipStackItf.hpp"

namespace usg {

class CRegistSession : public IRegistSession
{
public:
    CRegistSession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from );
    CRegistSession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from, const std::string& xmlType );
    virtual ~CRegistSession();

public:
    CRegistSession *setHandler( ICtxHandler *handler );
	
	void setUserNameAndPasswd( std::string &userName, std::string &passwd );
    void setExpries( uint32_t iExpries );

public:
//    virtual bool regist( std::string &sid, const std::string &sipUri );
//	virtual bool unregist( std::string &sid, const std::string &sipUri );
    virtual bool answer( pjsip_rx_data *rdata, int status );
    virtual uint32_t getExpries();

public:
    void removeAddFromToHeader(  pjsip_hdr_e type, pjsip_rx_data * rdata, pjsip_tx_data * tdata );
    bool onRegist( pjsip_rx_data *rdata );
    bool onAnswer( pjsip_rx_data *rdata );

private:
    std::string createSid( pjsip_cid_hdr *cid, pjsip_cseq_hdr *cseq );
	std::string cretatAuthorizationValue( pjsip_rx_data *rdata );
	bool regist_i( std::string &sid, const std::string &sipUri, const uint32_t iExpries );

private:
    pj_pool_t      *m_pool;
    pjsip_endpoint *m_endPoint;

private:
    ICtxHandler *m_handler;
    const std::string m_from;
    const std::string m_xmlType;

    uint32_t m_iExpries;

private:
	std::string m_auth;
	std::string m_sipUri;
	std::string m_userName;
	std::string m_password;
};

}

#endif // REGISTSESSION_HPP_
