<?xml version="1.0"?>
<access>
 <local>
  <ip>127.0.0.1</ip>
  <port>5060</port>
  <code>34010000002000000001</code>
  <userName>34010000002000000001</userName>
  <password>12345678</password>
 </local>
</access>

<!-- 可以启动两个sg,让它们互为remote和local,则可以查看它们的 注册,推送目录,心跳的 行为与结果. -->
<!-- remote: 为远端sg配置节点，包括本地sg连接远端sg的ip、port以及远端sg的标识符code. -->
<!-- remote-ip: 为远端sg配置节点之本地sg连接远端sg的ip地址。-->
<!-- remote-port: 为远端sg配置节点之本地sg连接远端sg的端口号，默认6060。-->
<!-- remote-code: 为远端sg配置节点之远端sg的标识符，code。-->
<!-- local: 为本地sg配置节点，包括远端sg连接本地sg的ip、port、本地sg的标识符code以及验证远端登录到本地的用户名和密码 -->
<!-- local-ip: 为本地sg配置节点之远端sg连接本地sg的ip地址。-->
<!-- local-port: 为本地sg配置节点之远端sg连接本地sg的端口号，默认5060。-->
<!-- local-code: 为本地sg配置节点之本地sg的标识符，code。-->
<!-- local-userName: 为本地sg配置节点之本地sg的用户名。-->
<!-- local-password: 为本地sg配置节点之本地sg的密码。-->
