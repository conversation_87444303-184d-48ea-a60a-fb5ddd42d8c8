#include <boost/thread.hpp>
#include "UsgManager.hpp"
#include "UsgManager/src/SgWtoeSipFunc.hpp"

namespace usg
{


    CSgWtoeSipFunc::CSgWtoeSipFunc()
            :m_sg(0)
    {

    }

    CSgWtoeSipFunc::~CSgWtoeSipFunc()
    {

    }

    bool CSgWtoeSipFunc::init( CUsgManager* sg )
    {
        if ( sg == NULL )
        {
            return false;
        }
        m_sg = sg;
        return true;
    }

    bool CSgWtoeSipFunc::fini()
    {
        readLock rLock( m_lock );
        std::map< std::string, onNotifyCallBack_t >::iterator it = m_onNotifyCallBack.begin(), ite = m_onNotifyCallBack.end();
        for ( ; it != ite; ++it )
        {
            if ( (*it).second )
            {
                (*it).second( std::string(), (void*)0 );
            }
        }

        m_onNotifyCallBack.clear();
        return true;
    }


    bool CSgWtoeSipFunc::onReceiveRealMedia( const std::string &sid, const boost::uuids::uuid &resId, const uint8_t subImageSize, const uint8_t masterImageSize, const SRealMedia &r, SRealMediaResponse &out )
    {
        //下级SG接口，上级不需要实现
        return true;
    }

    bool CSgWtoeSipFunc::onReceiveRealMediaCancel( const std::string &sid )
    {
        //下级SG接口，上级不需要实现
        return true;
    }

    bool CSgWtoeSipFunc::onReceiveRealMediaAck( const std::string &sid )
    {
        //下级SG接口，上级不需要实现
        return true;
    }

    bool CSgWtoeSipFunc::onReceiveRealMediaBye( const std::string &sid )
    {
        //下级SG接口，上级不需要实现
        return true;
    }

    bool CSgWtoeSipFunc::onReceiveHistoryList( const std::string &sid, const boost::uuids::uuid &resId, const SHistoryList &r, SHistoryListResponse &out )
    {
        //下级SG接口，上级不需要实现
        return true;
    }

    bool CSgWtoeSipFunc::onReceiveHistoryMedia( const std::string &sid, const boost::uuids::uuid &resId, const SHistoryMedia &r, SHistoryMediaResponse &out )
    {
        //下级SG接口，上级不需要实现
        return true;
    }

    bool CSgWtoeSipFunc::onReceivePresetList( const std::string &sid, const boost::uuids::uuid &resId, const SPresetList &r, SPresetListResponse &out )
    {
        //下级SG接口，上级不需要实现
        return true;
    }

    bool CSgWtoeSipFunc::onReceivePtzCommand( const std::string &sid, const boost::uuids::uuid &resId, const SPtzCommand &r, SPtzCommandResponse &out )
    {
        //下级SG接口，上级不需要实现
        return true;
    }

    bool CSgWtoeSipFunc::onReceiveCatalogResponse( const std::string &sid, const bool isOk )
    {
        //上级收到Catalog的Response OK，是订阅的应答
        readLock rLock( m_lock );
        if (m_onNotifyCallBack.empty())
            return false;

        SubscribeCommandResponse response;
        response.isOk = isOk;
        std::map< std::string, onNotifyCallBack_t >::iterator it = m_onNotifyCallBack.find( sid );
        if ( it != m_onNotifyCallBack.end() )
        {
            if ( (*it).second )
            {
                (*it).second( sid, (void*)&response );
            }
        }

        return true;
    }

    bool CSgWtoeSipFunc::onReceiveSubscribeAlarmResponse( const std::string& sid, bool result )
    {
        //上级收到Alarm的Response OK，是订阅的应答
        readLock rLock( m_lock );
        if( m_responseOpers.empty() )
            return true;

        if( m_responseOpers.find( sid ) == m_responseOpers.end() ) return true;

        m_responseOpers.erase( sid );

        if( result ) return true;

        return true;
    }

    bool CSgWtoeSipFunc::onReceiveBroadcastResponse(const std::string& sid ,const bool result)
    {
        readLock rLock( m_lock );
        if (m_onNotifyCallBack.empty())
            return false;

        SBroadcastResponse response;
        response.isOk = result;
        std::map< std::string, onNotifyCallBack_t >::iterator it = m_onNotifyCallBack.find( sid );
        if ( it != m_onNotifyCallBack.end() )
        {
            if ( (*it).second )
            {
                (*it).second( sid, (void*)&response );
            }
        }

        return true;
    }

    bool CSgWtoeSipFunc::onReceiveCatalog( const std::string &sid, const SCatalog& info, SCatalogResponse& out )
    {
        out.isOk = false;
        if ( m_sg )
        {
            return m_sg->onReceiveCatalog( sid, info, out );
        }
        return false;
    }

    bool CSgWtoeSipFunc::onReceiveKeepaliveResponse( const std::string &sid, const bool isOk )
    {
        if ( m_sg )
        {
            return m_sg->onReceiveKeepalive( sid );
        }
        return false;
    }

    bool CSgWtoeSipFunc::onReceiveKeeplive( const std::string &sid )
    {
        if ( m_sg )
        {
            return m_sg->onReceiveKeepalive( sid );
        }
        return false;
    }

    bool CSgWtoeSipFunc::onReceiveRealMediaResponse( const std::string &sid, SRealMediaResponse& out  )
    {
        readLock rLock( m_lock );
        std::map< std::string, onNotifyCallBack_t >::iterator it = m_onNotifyCallBack.find( sid );
        if ( it != m_onNotifyCallBack.end() )
        {
            if ( (*it).second )
            {
                (*it).second( sid, (void*)&out );
            }
        }

        return true;
    }

    bool CSgWtoeSipFunc::onReceiveHistoryListResponse( const std::string& sid, SHistoryListResponse& out )
    {
        readLock rLock( m_lock );
        std::map< std::string, onNotifyCallBack_t >::iterator it = m_onNotifyCallBack.find( sid );

        if ( it != m_onNotifyCallBack.end() )
        {
            if ( (*it).second )
            {
                (*it).second( sid, (void*)&out );
            }
        }

//     writeLock wLock( m_lock );
//     if ( m_onNotifyCallBack.empty() )
//         return false;
// 
//     (m_onNotifyCallBack.begin())->second( (void*)&out );

        return true;
    }

    bool CSgWtoeSipFunc::onReceiveHistoryMediaResponse( const std::string& sid, SHistoryMediaResponse& out )
    {
        readLock rLock( m_lock );
        std::map< std::string, onNotifyCallBack_t >::iterator it = m_onNotifyCallBack.find( sid );
        if ( it != m_onNotifyCallBack.end() )
        {
            if ( (*it).second )
            {
                (*it).second( sid, (void*)&out );
            }
        }

        return true;
    }

    bool CSgWtoeSipFunc::onReceivePresetListResponse( const std::string& sid,SPresetListResponse& out )
    {
        readLock rLock( m_lock );
        std::map< std::string, onNotifyCallBack_t >::iterator it = m_onNotifyCallBack.find( sid );
        if ( it != m_onNotifyCallBack.end() )
        {
            if ( (*it).second )
            {
                (*it).second( sid, (void*)&out );
            }
        }

        return true;
    }

    void CSgWtoeSipFunc::regOnNotifyCallBack( const std::string& sid, onNotifyCallBack_t& cb )
    {
        writeLock wLock( m_lock );
        m_onNotifyCallBack[sid] = cb;
    }

    void CSgWtoeSipFunc::regOnNotifyCallBack( const std::string& sid )
    {
        writeLock wLock( m_lock );
        m_responseOpers.insert( sid );
    }

    void CSgWtoeSipFunc::unRegOnNotifyCallBack( const std::string& sid )
    {
        writeLock wLock( m_lock );
        if( m_onNotifyCallBack.find( sid ) == m_onNotifyCallBack.end() ) return;

        m_onNotifyCallBack.erase( sid );
    }

    bool CSgWtoeSipFunc::onReceiveError( const std::string& sid )
    {
        readLock rLock( m_lock );
        std::map< std::string, onNotifyCallBack_t >::iterator it = m_onNotifyCallBack.find( sid );
        if ( it != m_onNotifyCallBack.end() )
        {
//		std::string tmpsid = sid;
            (*it).second( sid, (void*)0 );
            m_onNotifyCallBack.erase( it );
        }
        return true;
    }

    void CSgWtoeSipFunc::regOnNotifyCBbyPresetList( const std::string & sipResCode, onNotifyCallBack_t& cb )
    {
        writeLock wLock( m_presetLock );
        std::map< std::string, std::vector< onNotifyCallBack_t > >::iterator it = m_presetListCallBack.find( sipResCode );
        if (  it != m_presetListCallBack.end() )
        {
            it->second.push_back( cb );
        }
        else
        {
            std::vector< onNotifyCallBack_t > vect;
            vect.push_back( cb );
            m_presetListCallBack[sipResCode] = vect;
        }
    }

    void CSgWtoeSipFunc::unRegOnNotifyCBbyPresetList( const std::string & sipResCode )
    {
        writeLock wLock( m_presetLock );
        m_presetListCallBack.erase( sipResCode );
    }

    bool CSgWtoeSipFunc::onReceivePresetListNotify( const std::string& sipCode,SPresetListResponse& out )
    {
        writeLock wLock( m_presetLock );
        std::map< std::string, std::vector< onNotifyCallBack_t > >::iterator it = m_presetListCallBack.find( sipCode );
        if ( it != m_presetListCallBack.end() )
        {
            uint16_t size = (*it).second.size();
            for ( uint16_t i = 0; i < size; ++i )
            {
                if ( (*it).second[i] )
                {
                    (*it).second[i]( sipCode, (void*)&out );
                }
            }
        }
        m_presetListCallBack.erase( sipCode );
        return true;
    }

// bool CSgWtoeSipFunc::onReceiveDeviceCatalog(const std::string &sid, SCatalog &out)
// {
// 	//writeLock wLock( m_presetLock );
//  //   std::map< std::string, onNotifyCallBack_t >::iterator it = m_onNotifyCallBack.find( sid );
//  //   if ( it != m_onNotifyCallBack.end() )
//  //   {
//  //       if ( (*it).second )
//  //       {
//  //           (*it).second( (void*)&out );
//  //       }
//  //   }
// 	//if (out.endflg)
//  //   	m_onNotifyCallBack.erase( sid );
// 
//     writeLock wLock( m_lock );
//     if ( m_onNotifyCallBack.empty() )
//         return false;
// 
//     (m_onNotifyCallBack.begin())->second( (void*)&out );
// 
//     if (out.endflg)
//         m_onNotifyCallBack.erase( sid );
// 
// 	return true;
// }

    bool CSgWtoeSipFunc::onReceiveDeviceInfo( const std::string &sid, const boost::uuids::uuid &resId, SDeviceInfoResponse &out )
    {
        writeLock wLock( m_lock );
        std::map< std::string, onNotifyCallBack_t >::iterator it = m_onNotifyCallBack.find( sid );
        if ( it != m_onNotifyCallBack.end() )
        {
            if ( (*it).second )
            {
                (*it).second( sid, (void*)&out );
            }
        }
        m_onNotifyCallBack.erase( sid );

//     writeLock wLock( m_lock );
//     if ( m_onNotifyCallBack.empty() )
//         return false;
// 
//     (m_onNotifyCallBack.begin())->second( (void*)&out );
        return true;
    }

    bool CSgWtoeSipFunc::onReceiveDeviceStatus( const std::string &sid, const boost::uuids::uuid &resId, SDeviceStatusResponse &out )
    {
        writeLock wLock( m_lock );
        std::map< std::string, onNotifyCallBack_t >::iterator it = m_onNotifyCallBack.find( sid );
        if ( it != m_onNotifyCallBack.end() )
        {
            if ( (*it).second )
            {
                (*it).second( sid, (void*)&out );
            }
        }
        m_onNotifyCallBack.erase( sid );

//     writeLock wLock( m_lock );
//     if ( m_onNotifyCallBack.empty() )
//         return false;
// 
//     (m_onNotifyCallBack.begin())->second( (void*)&out );
        return true;
    }

// bool CSgWtoeSipFunc::onReceiveReboot( const std::string &sid, const boost::uuids::uuid &resId, SRebootCommandResponse &out )
// {
//     return true;
// }

    bool CSgWtoeSipFunc::onReceiveFiletoEnd(const std::string &sid)
    {
        if ( m_sg )
        {
            return m_sg->onReceiveFiletoEnd( sid );
        }
        return false;
    }

    bool CSgWtoeSipFunc::onReceiveAlarmNotify(const SAlarmParam &alarm)
    {
        if ( m_sg )
        {
            return m_sg->onReceiveAlarmNotify( alarm );
        }
        return false;
    }

    bool CSgWtoeSipFunc::onReceiveQueryCatalog( const std::string &sid, const SCatalog& info )
    {
        if ( m_sg )
        {
            return m_sg->onReceiveCatalog( sid, info );
        }

        return false;
    }

    bool CSgWtoeSipFunc::onReceive200Ok( const std::string& sid )
    {
        return true;
    }

}
