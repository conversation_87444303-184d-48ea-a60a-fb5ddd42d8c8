#ifndef DB28181PARSERRTSP_HPP_
#define DB28181PARSERRTSP_HPP_

#include <string>
#include "../include/Gb28181Struct.hpp"

namespace gb28181
{

    class CGb28181ParserRtsp
    {
    public:
        CGb28181ParserRtsp( const std::string & str );
        ~CGb28181ParserRtsp();

    public:
        bool getRtspInfo( gb28181::SHistoryRtspInfo &info );

    private:
        char* strDupSize( const char* str );
        void strDel( char*& str );

        bool parserReceivePkg();
        bool parseRtspLine( const char* inputLine, const char*& nextLine );
        bool parseRtspLine_control( const char* inputLine, uint16_t &type );
        bool parseRtspLine_scale( const char* inputLine, double &scale );
        bool parseRtspLine_range( const char* inputLine, uint32_t &rangeS, uint32_t &rangeE );

    private:
        std::string					m_InputStr;
        gb28181::SHistoryRtspInfo	m_historyRtspInfo;

    };

};

#endif