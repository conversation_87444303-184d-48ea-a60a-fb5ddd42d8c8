#ifndef JAVASCRIPTWRAPPER_HPP_
#define JAVASCRIPTWRAPPER_HPP_

#include "wtoe/BasicHelper/VcWarningOff.hpp"
#include "wtoe/JavaScriptEngine/JavaScriptEngineExp.hpp"
#include "DataMgr/include/DataMgrCfg.hpp"

namespace wtoe 
{

class DATAMGR_PRIVATE CJsDataMgr : public CJavaScriptAdapter<CJsDataMgr>, 
                                                 public CAdapterJavaScriptize
{
    friend class CJavaScriptAdapter<CJsDataMgr>;
public:
    CJsDataMgr();
    virtual ~CJsDataMgr();
    static CJsDataMgr *createObject();

public:
    JSBool init( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
	JSBool showStatus( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
	JSBool showVersion( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
	JSBool start( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
	JSBool stop( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
	JSBool fps( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
	JSBool debug( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
	JSBool level( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );

protected:
    WTOE_USING_BASE_CONSTRUCTOR( CJavaScriptAdapter<CJsDataMgr> );
    WTOE_USING_BASE_FINALIZER( CJavaScriptAdapter<CJsDataMgr> );
};

} //namespace wtoe

namespace msm
{
	bool javaScriptRegisterDataMgr( JSContext *jsCtx, JSObject *jsObj );
}

#include "wtoe/BasicHelper/VcWarningOn.hpp"

#endif // JAVASCRIPTWRAPPER_HPP_

