
#pragma once

#include <functional>
#include <utility>
#include "dhnetsdk.h"
#include "front_platform.h"
#include "timer_manager.h"
#include "util/worker_pool.h"

namespace fvm::platform
{

    //! puller注册的收取实时帧数据回调接口
    using RecvRealData = std::function<void(uint8_t*, unsigned int)>;

    /**
     * @brief 大华单通道管理
     */
    struct DHChannel
    {
        long         loginId = 0;                               //!< 登录句柄
        std::string  name;                                      //!< 通道名称
        int          channelId = 0;                             //!< 通道号， 对应url解析的通道号减1
        long         playHandle = 0;                            //!< 播放句柄
        int          enable = 1;                                //!< 是否有效
        RecvRealData receiveRealData = nullptr;                 //!< 对应回调函数

        DHChannel(long id, std::string name,  int channel, int state = 1)
                : loginId(id), name(std::move(name)), channelId(channel), enable(state){};

        //! 播放
        bool play(const RecvRealData& callback);

        //! 停止
        void stop();

        //切换设置到预置点
        bool ptzControl(DWORD cmd, DWORD lParam1, DWORD lParam2, DWORD lParam3, BOOL dwStop = false, void* param4 = NULL);

        //保存预置点
        bool savePreset(int iActPreset);

        //获取当前ptz坐标
        bool getPtzPosition(double& x, double& y, double& z);

        bool getPresetPosition(int presetId, double& x, double& y, double& z);
    };


    /**
     * @brief 大华平台管理
     */
    class SDKDHPlatform : public FrontPlatform
    {

    public:
        SDKDHPlatform() = default;
        ~SDKDHPlatform();

        /**
         * @brief 初始化, 登录、注册登录定时器、资源扫描定时器
         */
        void init(data::VideoServerPtr svrPtr) override;

        /**
         * @brief               对应的大华通道，并播放
         * @param[in] addr：    视频address,根据address播放对应大华通道（eg: **************:3 对应大华3通道）
         * @param[in] callback: 实时流收取回调
         */
        bool startPlay(const std::string& addr, const RecvRealData& callback);

        /**
         * @brief 停止对应的大华通道播放
         */
        void stopPlay(const std::string& addr);

        /**
        * @brief  获取错误信息
        */
        std::string getLastError(void) override;

        /**
        * @brief  设备登录
        */
        bool login(void) override;

        bool ptzControl(const std::string& addr, int iPreset);

        //获取当前ptz坐标
        bool getPtzPosition(const std::string& addr, double& x, double& y, double& z);

        // 云台保存预置位
        bool savePreset(const std::string& addr, int presetId);

        // 云台控制ptz
        bool controlPtz(const std::string& addr, int action, int step);

        //获取当前ptz坐标
        bool getPresetPosition(const std::string& addr, int presetId, double& x, double& y, double& z);

    public:
        /**
         * @brief  初始化SDK，只调用一次
         */
        static void initSDK();
        /**
         * @brief  清理SDK
         */
        static void cleanupSDK();

    private:

        /**
         * @brief  更新通道资源，并写入数据库
         */
        void updateResource();

        /**
         * @brief  销毁，取消定时器，停止播放，退出登录
         */
        void dispose();

        /**
         * @brief  根据address找到对应大华通道（eg: **************:3 对应大华3通道）
         */
        bool getChanelId(const std::string& addr, int& channelId);

    private:
        //! login 相关
        long             loginId = 0;              //!< 登录句柄
        std::atomic_bool online = false;           //!< 设备是否成功登录
        std::once_flag   initOnce;                 //!< 初始化相关登录、资源更新 任务
        int              lastError = 0;            //!< 上一次登录错误

        timer::TimerPtr  loginTimer = nullptr;     //!< 登录定时器
        timer::TimerPtr  updateResTimer = nullptr; //!< 扫描资源计时器

        //! channel 相关
        uint16_t               channelNum = 0;     //!< 模拟通道数
        boost::fibers::mutex   channelsLock;       //!< 通道channels锁
        std::vector<DHChannel> channels;           //!< 所有模拟通道

        std::atomic_bool exit = false;             //!< 平台退出标志
        std::string      lastErrorString;          //!< 错误信息
        int loginIgnoreCount = 0;                  //!< 登录间隔时间
        std::atomic_bool bNeedGetResource = true;             // 是否需要获取资源
        std::mutex loginMtx;                       // 设备登录锁
        int lastPTZcmd = 0;

    };
    typedef std::shared_ptr<platform::SDKDHPlatform> SDKDHPlatformPtr;
}