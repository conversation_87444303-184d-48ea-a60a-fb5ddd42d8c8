/**
 * Project FVM
 */
#ifdef SDK_HK_SUPPORTED
#include "sdk_hk_puller.h"
#include "ailog.h"
#include "platform/platform_manager.h"
#include "platform/sdk_hk_platform.h"
#include "boost/asio/time_traits.hpp"  //TODO

/**
 * hksdkpuller implementation
 * @brief: 海康SDK接入
 */

namespace fvm::stream
{
    using namespace data;
    using namespace platform;
    using std::chrono::steady_clock;
    using std::chrono::duration_cast;
    using std::chrono::milliseconds;

    const int BUF_SIZE = 1024 * 32;
    const int STREAM_QUEUE_SIZE = 1024 * 1024 * 2;

    SDKHKPuller::SDKHKPuller(FrontPlatformPtr platform) :PlatformPuller(platform)
    {
    }

    void SDKHKPuller::initOptions()
    {
        av_dict_set(&options, "fflags", "nobuffer", 0);
        av_dict_set(&options, "max_analyze_duration", "10", 0);
        av_dict_set(&options, "max_delay", "1000", 0);
        av_dict_set(&options, "stimeout", "1000000", 0);
        av_dict_set(&options, "timeout", "10000000", 0);
        av_dict_set(&options, "probesize", "4096", 0);
    }

    /**
     * 重载StreamInput的open，需要读取缓冲区，打开输入流，获取输入流信息
     */
    bool SDKHKPuller::open()
    {
        int videoId = videoSourceInfo->videoSourcePtr->getId();
        if (!streamQueue.init(videoId, STREAM_QUEUE_SIZE))
        {
            ai::LogError << address << " init stream queue is failed";
            return false;
        }
        printInfo(str(boost::format("Start request hksdk %d %s") % videoId % videoSourceInfo->videoSourcePtr->getAddress()));

        //海康调用
        sourceAddr = videoSourceInfo->videoSourcePtr->getAddress();
        SDKHKPlatformPtr pHKPlatform = static_pointer_cast<SDKHKPlatform>(frontPlatform);
        bool bRet = pHKPlatform->startPlay(sourceAddr, [&](uint8_t* buf, int buf_size)
            {
                dealData(buf, buf_size);
            });
        if (!bRet)
        {
            ai::LogError << "HKSDK open " << sourceAddr << " failed ";
            return false;
        }

        //ffmpeg分析流
        ioContextBuffer = (uint8_t*)av_malloc(sizeof(uint8_t) * BUF_SIZE);
        if ( !ioContextBuffer )
        {
            ai::LogError << this->address << " av_malloc ctx buffer failed";
            return false;
        }
        auto onReadData = [](void* pUser, uint8_t* buf, int bufSize)->int
        {
            SDKHKPuller* pHkPuller = (SDKHKPuller*)pUser;
            if (!pHkPuller)
                return -1;
            auto startTime = steady_clock::now();
            do
            {
                int readLen = pHkPuller->streamQueue.popdata(buf, bufSize);
                if (readLen > 0)
                    return readLen;
                else if (readLen < 0)       //未工作
                    return -1;
                else                        //空
                {
                    int passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
                    if (passed > 5000)
                    {
                        std::cerr << pHkPuller->sourceAddr << " Read No Data for " << passed << "ms" << std::endl;
                        return -1;
                    }
                }
            } while (pHkPuller->jobIsRunning());
            return 0;
        };
        
        pb = avio_alloc_context(ioContextBuffer, BUF_SIZE, 0, this, onReadData, NULL, NULL);
        if (pb == nullptr)  //分配空间失败
        {
            if (ioContextBuffer)
                av_freep(&ioContextBuffer);
            ai::LogError << this->address <<"avio_alloc_context failed" ;
            return false;
        }
        //等待数据包
        auto startTime = steady_clock::now();
        while ( jobIsRunning() )
        {
            if ( streamQueue.getsize() > 100000 )
                break;
            int passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
            if(passed > 15000) {
                ai::LogError << this->address << " wait source stream failed " << passed << "ms";
                return false;
            }
            boost::this_fiber::sleep_for(std::chrono::milliseconds(10));
        }

        if ( !jobIsRunning() ) {
            ai::LogError << "in SDKHKPuller::open wait data failed videoID : " << videoSourceInfo->videoSourcePtr->getId() << ", data size : " << streamQueue.getsize() ;  // FETODO
            return false;
        }

        int ret = 0;

        const AVInputFormat* piFmt = NULL;
        if ((ret = av_probe_input_buffer(pb, &piFmt, "", NULL, 0, 0)) < 0)
        {
            setLastError(str(boost::format("av_probe_input_buffer %s FAILED: %s") % streamUrl() % getErrorString(ret)), ret);
            return false;
        }

        initOptions();
        FFMPEGElement::createContext();
        if(!formatCtx)
        {
            return false;
        }
        formatCtx->pb = pb;

        if ((ret = avformat_open_input(&formatCtx, "", piFmt, &options)) < 0)
        {
            setLastError(str(boost::format("avformat_open_input %s FAILED: %s") % streamUrl() % getErrorString(ret)), ret);
            return false;
        }

        if ((ret = avformat_find_stream_info(formatCtx, nullptr)) < 0)
        {
            setLastError(str(boost::format("avformat_find_stream_info %s FAILED: %s") % streamUrl() % getErrorString(ret)), ret);
            return false;
        }
        //查找视频
        const AVCodec* pi_code = nullptr;
        int tryCount = 1;
        auto index_stream = av_find_best_stream(formatCtx, AVMEDIA_TYPE_VIDEO, -1, -1, &(pi_code), 0);
        if (index_stream < 0)
        {
            ai::LogError << streamUrl() << " av_find_best_stream is err, formatCtx->nb_streams: " << formatCtx->nb_streams;

            if(formatCtx->nb_streams < 1)
                return false;
            
            for ( uint32_t i = 0; i < formatCtx->nb_streams; ++i )
            {
                AVStream *in_stream = formatCtx->streams[i];
                if ( !in_stream )
                {
                    continue;
                }

                if ( in_stream->codecpar->codec_type == AVMEDIA_TYPE_VIDEO )  
                {  
                    index_stream = i;
                    break;
                }
            }

            if (index_stream < 0)
            {
                ai::LogError << streamUrl() << " unable find video stream, formatCtx->nb_streams: " << formatCtx->nb_streams;
                index_stream = 0;
            }
        }
        AVStream* in_stream = formatCtx->streams[index_stream];
        videoIndex = index_stream;
        auto timebase = in_stream->time_base;
        auto codecpar = in_stream->codecpar;
        auto frameRate = av_guess_frame_rate(formatCtx, in_stream, nullptr);
        duration = (int64_t)((double)AV_TIME_BASE / av_q2d(frameRate)) / 1000;

        if (duration == 0 || duration > 100)
        {
            duration = 40;
            frameRate.den = 1;
            frameRate.num = 25;
        }
        auto codecInfo = std::make_shared<CodecInfo>(timebase, frameRate, codecpar);
        this->onStreamCodecInfoRetrieved(codecInfo);
        //av_dump_format(formatCtx, 0, formatCtx->url, 0);

        isOpened = true;
        return true;
    }

    //处理海康收到的数据
    void SDKHKPuller::dealData(uint8_t* buf, int buf_size)
    {
        int32_t len = buf_size;         //写入size
        uint8_t* dbuf = buf;            //环
        int32_t total_read = 0;
        int32_t ret = 0;
        int emptyCount = 0;
        do
        {
            ret = streamQueue.pushdata(dbuf, buf_size);
            if (ret == 0)               //没有数据
            {
                emptyCount++;
                if (emptyCount >= 10)
                    return;
            }
            else if (ret < 0)           //跳过
                return;
            else
                emptyCount = 0;
            total_read += ret;
            dbuf += ret;
        } while (total_read < buf_size && jobIsRunning());
    }

    void SDKHKPuller::close()
    {
        //关闭海康
        SDKHKPlatformPtr pHKPlatform = static_pointer_cast<SDKHKPlatform>(frontPlatform);
        pHKPlatform->stopPlay(sourceAddr);
        if (pb)
        {
            if (pb->buffer)
                av_freep(&pb->buffer);
            avio_context_free(&pb);
        }
        streamQueue.reset(true);
        StreamInput::close();
        streamQueue.fini();
    }

    void SDKHKPuller::process()
    {
        // 初始化参数
        int ret;
        boost::posix_time::ptime tmBegin, tmNow;
        frameIndex = 0;
        while (jobIsRunning())
        {
            if (!isOpened) // 尝试连接
            {
                auto startTime = steady_clock::now();
                DO_IN_THREAD(this->open();)
                frameIndex = 0;
                tmBegin = boost::posix_time::microsec_clock::local_time();
                int passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
                if(passed > 1000 && isOpened)
                    std::cerr << this->address<< " openContext "<<  passed << "ms" << std::endl;
            }

            if (!isOpened) // 连接失败 等待5s后尝试
            {
                close();
                WAIT_FOR_SECONDS(5)
                continue;
            }

            // 连接成功 开始收包
            AVPacket* pkt = av_packet_alloc();
            resetContextTimer();

            DO_IN_THREAD(ret = av_read_frame(formatCtx, pkt);)
            int passed = duration_cast<milliseconds>(steady_clock::now() - startTime).count();
            if(passed > 1000)
                ai::LogWarn << this->address<< " av_read_frame "<<  passed << "ms";

            // 收包错误处理
            if (ret < 0)
            {
                av_packet_unref(pkt);
                av_packet_free(&pkt);
                if ( !jobIsRunning() )
                    break;

                if (ret == AVERROR_EOF || !formatCtx->pb || avio_feof(formatCtx->pb) || formatCtx->pb->error)
                {
                    close();
                    onStreamInputLost(); //通知web流状态
                }
                setLastError(str(boost::format("av_read_frame %s FAILED: %s") % address % getErrorString(ret)), ret);
                boost::this_fiber::sleep_for(std::chrono::milliseconds(10));
                continue;
            }
            if (pkt->stream_index != videoIndex)
            {
                av_packet_unref(pkt);
                av_packet_free(&pkt);
                continue;
            }
            auto packet = std::make_shared<PacketData>(pkt);
            this->onStreamDataReceived(packet);

            frameIndex++;
            tmNow = boost::posix_time::microsec_clock::local_time();
            int actTime = (tmNow - tmBegin).total_milliseconds();
            int needTime = frameIndex * duration;
            int waitTime = max(needTime - actTime - 1, 0);
            if (waitTime > 0)
                boost::this_fiber::sleep_for(std::chrono::milliseconds(waitTime));
            else
                boost::this_fiber::yield();
        }
        close();
        printInfo(str(boost::format("EXIT %s") % address));
    }

    bool SDKHKPuller::getPtzPosition(double& x, double& y, double& z, const std::optional<int>& actPresetId)
    {
        if (actPresetId.has_value())
        {
            //! 优先从数据库中获取预置位的基准坐标，如果数据库中没有，尝试从onvif接口获取
            if (!DATA_MANAGER.getPresetPosition(videoSourceInfo->channelId, actPresetId.value(), x, y, z))
            {
                return onvifPtzInfo->getPresetPosition(actPresetId.value(), x, y, z);
            }
            return true;
        }
        else
        {
            SDKHKPlatformPtr pHKPlatform = static_pointer_cast<SDKHKPlatform>(frontPlatform);
            return pHKPlatform->getPtzPosition(sourceAddr, x, y, z);
        }
    }

    bool SDKHKPuller::callCameraPreset(int presetId)
    {
        SDKHKPlatformPtr pHKPlatform = static_pointer_cast<SDKHKPlatform>(frontPlatform);
        auto addr = videoSourceInfo->videoSourcePtr->getAddress();
        return pHKPlatform->ptzControl(addr, presetId);
    };

    bool SDKHKPuller::controlPtz(int action, int step)
    {
        SDKHKPlatformPtr pHKPlatform = static_pointer_cast<SDKHKPlatform>(frontPlatform);
        return pHKPlatform->controlPtz(action, step);

        //if (!onvifPtzInfo)
        //    return false;

        //network::EPtzCommand cmd = convertPtzCmd(action);
        //onvifPtzInfo->ptzControl(cmd, step, 0);
        //return true;
    }

    std::string SDKHKPuller::getLastError(void)
    {
        SDKHKPlatformPtr pHKPlatform = static_pointer_cast<SDKHKPlatform>(frontPlatform);
        auto errorStr = pHKPlatform->getLastError();
        if(!errorStr.empty())
            return errorStr;

        return StreamElement::getLastError();
    }

    // （新增）3D定位
    bool SDKHKPuller::focusRect(int xTop, int yTop, int xBottom, int yBottom)
    {
        int width = codecpar ? codecpar->width : 1920;
        int height = codecpar ? codecpar->height : 1080;

        SDKHKPlatformPtr pHKPlatform = static_pointer_cast<SDKHKPlatform>(frontPlatform);
        return pHKPlatform->focusRect(videoSourceInfo, xTop * 255/ width, yTop * 255 / height, xBottom * 255 / width, yBottom * 255 / height);
    }

    // 保存预置位
    bool SDKHKPuller::saveCameraPreset(int presetId)
    {
        SDKHKPlatformPtr pHKPlatform = static_pointer_cast<SDKHKPlatform>(frontPlatform);
        return pHKPlatform->savePreset(presetId);
    }
}

#endif