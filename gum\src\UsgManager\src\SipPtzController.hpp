#ifndef SIPPTZCONTROLLER_HPP_
#define SIPPTZCONTROLLER_HPP_

#include <wtoe/PackageManager/PackageManagerExp.hpp>

#include "UsgManager/include/UsgManagerExp.hpp"

namespace usg
{

    class CUsgManager;

    class USGMANAGER_PRIVATE CSipPtzController : public IPtzController
    {
    public:
        CSipPtzController();
        virtual ~CSipPtzController();

    public:
        /** 查询预置位
        *
        * @param[out] infos      预置位列表。
        * @param[out] result     操作执行失败，通过该参数返回失败原因。
        *
        * @return      bool      操作执行成功，返回true，操作执行失败，返回false。
        */
        virtual bool queryPreposition( const boost::uuids::uuid &resId, std::map< uint8_t, std::string > &infos );

        /** 云台动作：上、下、左、右、停止、预置位的切换
        *
        * @param[out]  result    操作执行失败，通过该参数返回失败原因。
        *
        * @return      bool       操作执行成功，返回true，操作执行失败，返回false。
        */
        virtual bool up( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId );
        virtual bool down( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId );
        virtual bool left( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId );
        virtual bool right( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId );
        virtual bool stop( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId );
        virtual bool switchPreposition( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId, const uint8_t index );

        /** 镜头动作：焦距的变化
        *
        * @param[out]  result   操作执行失败，通过该参数返回失败原因。
        *
        * @return      bool     操作执行成功，返回true，操作执行失败，返回false。
        */
        virtual bool focusNear( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId );
        virtual bool focusFar ( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId );
        virtual bool focusStop ( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId );

        /** 镜头动作：缩放倍数
        *
        * @param[out]  result   操作执行失败，通过该参数返回失败原因。
        *
        * @return      bool     操作执行成功，返回true，操作执行失败，返回false。
        */
        virtual bool zoomOut( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId );
        virtual bool zoomIn( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId );

        /** 镜头动作：光圈的变化
        *
        * @param[out]  result   操作执行失败，通过该参数返回失败原因。
        *
        * @return      bool     操作执行成功，返回true，操作执行失败，返回false。
        */
        virtual bool apertureWide( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId );
        virtual bool apertureTele( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId );

        /** 镜头动作：雨刷开启、雨刷关闭
        *
        * @param[out]  result   操作执行失败，通过该参数返回失败原因。
        *
        * @return      bool     操作执行成功，返回true，操作执行失败，返回false。
        */
        virtual bool wiperOn ( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId );
        virtual bool wiperOff( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId );


        /** 镜头动作：灯光开启、灯光关闭
        *
        * @param[out]  result   操作执行失败，通过该参数返回失败原因。
        *
        * @return      bool     操作执行成功，返回true，操作执行失败，返回false。
        */
        virtual bool ledOn ( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId );
        virtual bool ledOff( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId );

        /*
        *  该媒体源是否可以进行云台控制
        * @return  bool  操作执行成功，返回true,能控制。操作执行失败，返回false,不能控制
        */
        virtual bool canControlPtz( const boost::uuids::uuid &resId, bool &retbool );

    public:
        bool init();
        bool fini();
    };

}
#endif