/**
 * Project FVM
 */
#pragma once

#include <vector>
#include <map>
#include <string>
#include <memory>
#include "preset.h"
#include "preset_roi.h"
#include "preset_check_area.h"
#include "preset_lane.h"
#include "preset_offset.h"
#include "program.h"
#include "program_info.h"
#include "video_source.h"
#include "boost/asio/time_traits.hpp"
#include "boost/date_time/posix_time/posix_time.hpp"
#include "preset_lane_line.h"
#include "preset_roi_feature.h"

/**
* @brief: 视频源信息
*/
namespace fvm {
    namespace data {
        using std::nullopt;

        typedef std::shared_ptr<db::VideoSource> VideoSourcePtr;
        typedef std::shared_ptr<db::Preset> PresetPtr;
        typedef std::shared_ptr<db::PresetOffset> PresetOffsetPtr;
        typedef std::shared_ptr<db::PresetCheckArea> PresetCheckAreaPtr;
        typedef std::shared_ptr<db::PresetLane> PresetLanePtr;
        typedef std::shared_ptr<db::PresetLaneLine> PresetLaneLinePtr;
        typedef std::shared_ptr<db::PresetRoiFeature> PresetRoiFeaturePtr;
        /**
         * @brief 通道当前可检测性(wn_video_source 中 is_detectable字段)
         */
        enum class ChannelDetectable
        {
            None = -1,
            ManualPaused,          //!<  已手工暂停
            RestoreDetectable,     //!<  可恢复检测
            SetDetectAreaPaused,   //!<  已设置检测区暂停
            OffsetPaused,          //!<  已偏移暂停IVA检测状态
            OffsetDetectable       //!<  已偏移不暂停IVA检测状态，但过滤IVA事件
        };

        /**
         * 视频接入类型
         */
        enum class StreamInputType {
            GB28181 = 1,
            SDK,
            RTSP,
            File,
            AGENT,
            REAL,
        };

        enum class DetectType
        {
            OutDay = 1, //!< 外场白天
            Tunnel,		//!< 隧道
            OutLight,   //!< 外场亮度
            outNight,   //!< 外场夜间
        };

        /**
         * @brief 预置位程序运行信息
         */
        struct PresetProgramInfo
        {
            int id = 0;
            std::string startDate;                           //!< 定义夏天、冬天起始日期
            std::string switchTime;                          //!< 切换时间
        };

        /**
         * @brief 预置位感兴趣区信息，包括感兴区节点下子区域、车道
         */
        struct PresetRoiInfo{
            std::shared_ptr<db::PresetRoi> roiPtr = nullptr;
            std::map<int, PresetCheckAreaPtr> checkAreasMap = {};  //!< roi下的子区域集 key:checkAreasId
            std::map<int, PresetLanePtr> lanesMap = {};            //!< roi下的车道集 key:LaneId
            PresetRoiFeaturePtr featureArea = nullptr;              //!< roi下的特征提取区域
        };

        /**
         * @brief 预置位信息，包括预置位节点下子程序运行信息、偏移检测区、感兴趣区
         */
        struct PresetInfo{
            PresetPtr presetPtr = nullptr;
            std::map<int, PresetProgramInfo>  programInfosMap = {};  //!< Preset下程序运行信息集 key:programInfoId
            std::map<int, PresetOffsetPtr>  offsetAreasMap = {};     //!< Preset下偏移检测区集 key:offsetId
            std::map<int, PresetRoiInfo>  roiInfosMap = {};          //!< Preset下偏感兴趣区集 key:roiId
            PresetLaneLinePtr presetLaneLinePtr = nullptr;           //!< Preset下的车道线配置
        };

        typedef std::map<uint32_t, PresetPtr> PresetTimeProgramInfo;
        typedef std::map<boost::gregorian::date, PresetTimeProgramInfo> PresetDateProgramInfo;
        /**
         * @brief 视频资源信息 包含wn_video_source表，以及相关配置关联信息
         */
        struct VideoSourceInfo {
            StreamInputType streamInputType;                         //!< 前端类型
            int channelId = 0;                                       //!< 视频资源分配的通道号，对应streamId (大于0 则已经分配通道，否则就是未分配)
            VideoSourcePtr videoSourcePtr = nullptr;                 //!< 视频资源
            std::map<int, PresetInfo>  presetInfosMap = {};          //!< 视频资源下的所有预置位信息集 key:presetId
            PresetDateProgramInfo presetProgramInfos;                //!< 视频资源下的时间切换方案 map <日期 <时间，preset>> eg: <1-16, <07:00, presetPtr>>
        };
    }
    typedef std::shared_ptr<data::VideoSourceInfo> VideoSourceInfoPtr;

}