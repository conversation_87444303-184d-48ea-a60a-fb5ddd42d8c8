#ifndef SIPSERVICETESTCASE_HPP_
#define SIPSERVICETESTCASE_HPP_

#include <cppunit/extensions/HelperMacros.h>

namespace usg {

class CSipServiceTestCase : public CPPUNIT_NS::TestFixture
{
    CPPUNIT_TEST_SUITE( CSipServiceTestCase );
    CPPUNIT_TEST( testSipService );
    CPPUNIT_TEST_SUITE_END();

public:
    CSipServiceTestCase(){}
    ~CSipServiceTestCase(){}

public:
    void testSipService();
};

}

#endif // SIPSERVICETESTCASE_HPP_
