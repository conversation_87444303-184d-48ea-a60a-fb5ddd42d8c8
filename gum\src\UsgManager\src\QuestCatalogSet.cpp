#include <iostream>
#include "QuestCatalogSet.hpp"
#include "ailog.h"

namespace usg {


    CQuestCatalogSet::CQuestCatalogSet()
    {

    }

    CQuestCatalogSet::~CQuestCatalogSet()
    {

    }

    CQuestCatalogSet* CQuestCatalogSet::m_catalogSet = 0;

    bool CQuestCatalogSet::init()
    {
        if( CQuestCatalogSet::m_catalogSet == 0 )
            m_catalogSet = new CQuestCatalogSet;
        return true;
    }

    bool CQuestCatalogSet::fini()
    {
        if( CQuestCatalogSet::m_catalogSet != 0 )
        {
            delete CQuestCatalogSet::m_catalogSet;
            CQuestCatalogSet::m_catalogSet = 0;
        }

        return true;
    }

    CQuestCatalogSet* CQuestCatalogSet::get()
    {

        return m_catalogSet;
    }

    bool CQuestCatalogSet::add( const std::string &sid, const std::string &devid, int num, const SCatalog &catalog )
    {
        boost::mutex::scoped_lock lock( m_mutex );

        if( m_devIds.find( devid ) == m_devIds.end() )
            return false;

        std::pair< std::string, std::string > id( sid, devid );

        if( m_catalogInputs.find( id ) == m_catalogInputs.end() )
        {
            SCatalogInfo ci;

            ci.sid = sid;
            ci.devid = devid;
            ci.num = num;

            for( std::vector< SCatalog::SItem >::const_iterator iter = catalog.subItems.begin(); iter != catalog.subItems.end(); ++iter )
            {
                ci.itms[( *iter ).sipResCode] = *iter;
            }

            m_catalogInputs[id] = ci;
            m_oldCounts[id] = 0;

            if( m_catalogInputs[id].num == m_catalogInputs[id].itms.size() )
            {
                ///< 直接上报，依然走操作对象机制
                m_catalogOuts[id] = m_catalogInputs[id];
                m_catalogInputs.erase( id );
                m_devIds.erase( devid );
            }

            return true;
        }

        SCatalogInfo temp = m_catalogInputs[id];

        for( std::vector< SCatalog::SItem >::const_iterator iter = catalog.subItems.begin(); iter != catalog.subItems.end(); ++iter )
        {
            if( temp.itms.find( ( *iter ).sipResCode ) != temp.itms.end() )///< 资源存在
            {
                ai::LogError << "Has Same Sisipcode " << ( *iter ).sipResCode;
                return false;
            }

            m_catalogInputs[id].itms[( *iter ).sipResCode] = *iter;
        }

        if( m_catalogInputs[id].num == m_catalogInputs[id].itms.size() )
        {
            ///< 直接上报，依然走操作对象机制
            m_catalogOuts[id] = m_catalogInputs[id];
            m_catalogInputs.erase( id );
            m_devIds.erase( devid );
        }

        return true;
    }

    bool CQuestCatalogSet::takeCatalog( const std::string &sid, const std::string &devid, SCatalog &infos )
    {
        boost::mutex::scoped_lock lock( m_mutex );

        std::pair< std::string, std::string > id( sid, devid );
        if( m_catalogOuts.find( id ) == m_catalogOuts.end() ) return false;

        SCatalogInfo temp = m_catalogOuts[id];

        infos.deviceId = temp.devid;
        infos.num = temp.num;
        infos.endflg = true;

        for( std::map< std::string, SCatalog::SItem >::iterator iter = temp.itms.begin(); iter != temp.itms.end(); ++iter )
        {
            infos.subItems.push_back( ( *iter ).second );
        }

        m_catalogOuts.erase( id );
        m_oldCounts[id] = 0;

        return true;
    }

    bool CQuestCatalogSet::createSet( const std::string &devid )
    {
        boost::mutex::scoped_lock lock( m_mutex );

        if( m_devIds.find( devid ) != m_devIds.end() ) return false;

        m_devIds.insert( devid );
        return true;
    }

    bool CQuestCatalogSet::removeCatalog( const std::string &sid, const std::string &devid, SCatalog &infos )
    {
        boost::mutex::scoped_lock lock( m_mutex );

        std::pair< std::string, std::string > id;//( sid, devid );
//	if( m_catalogInputs.find( id ) == m_catalogInputs.end() ) return false;
        for( std::map< std::pair< std::string, std::string >, SCatalogInfo >::iterator iter = m_catalogOuts.begin(); iter != m_catalogOuts.end(); ++iter )
        {
            if( ( *iter ).first.second == devid )
            {
                id = ( *iter ).first;
            }
        }

        SCatalogInfo temp = m_catalogInputs[id];

        infos.deviceId = temp.devid;
        infos.num = temp.num;
        infos.endflg = true;

        for( std::map< std::string, SCatalog::SItem >::iterator iter = temp.itms.begin(); iter != temp.itms.end(); ++iter )
        {
            infos.subItems.push_back( ( *iter ).second );
        }

        m_catalogInputs.erase( id );
        m_oldCounts[id] = 0;

        return true;
    }

    bool CQuestCatalogSet::check( std::string &sid, const std::string &devid )
    {
        boost::mutex::scoped_lock lock( m_mutex );

        bool result = false;
        for( std::map< std::pair< std::string, std::string >, SCatalogInfo >::iterator iter = m_catalogOuts.begin(); iter != m_catalogOuts.end(); ++iter )
        {
            if( ( *iter ).first.second == devid )
            {
                sid = ( *iter ).first.first;
                result = true;
            }
        }

        return result;
    }

    bool CQuestCatalogSet::hasChange( const std::string &devid )
    {
        boost::mutex::scoped_lock lock( m_mutex );

        std::string sid;
        bool hasDevid = false;

        for( std::map< std::pair< std::string, std::string >, SCatalogInfo >::iterator iter = m_catalogInputs.begin(); iter != m_catalogInputs.end(); ++iter )
        {
            if( ( *iter ).first.second == devid )
            {
                sid = ( *iter ).first.first;
                hasDevid = true;
                break;
            }
        }

        if( !hasDevid )
        {
            //std::cerr << "!hasDevid " << devid.c_str() << std::endl;
// 		std::stringstream ss;
// 		ss << "!hasDevid " << devid.c_str() << std::endl;
// 		std::cout << ss.str();
            return false;
        }

        std::pair< std::string, std::string > id( sid, devid );

        int count = m_catalogInputs[id].itms.size();

        if( count == m_oldCounts[id] )
        {
            ai::LogError << "count == m_oldCounts[id] : count : " << count << " : m_oldCounts[id] : " << m_oldCounts[id];
            return false;
        }

        m_oldCounts[id] = count;
        return true;
    }

}
