# 基于ODB 数据库模块
&emsp;&emsp;该模块主要将aimonitor数据库基于ODB映射成C++对象，并通过视图关联关系数据库，并提供抽象的查询、更新、插入、删除等接口。

## 目录
* [ODB介绍](#ODB介绍)
* [注意事项](#注意事项)
* [环境安装](#环境安装)
* [目录结构](#目录结构)
* [接口介绍](#接口介绍)
* [快速示例](#快速示例)


## ODB介绍
&emsp;&emsp;[ODB](https://www.codesynthesis.com/products/odb/doc.xhtml)是一个开源的，支持多平台，支持多数据库的 C++ 的 ORM 框架，可将 C++ 对象数据库表映射，进行轻松的数据库查询和操作。

## 注意事项
&emsp;&emsp; 由于数据库默认配置wait_timeout的时长28800s,即8小时。连续8小时不操作数据库，会被mysql服务断开连接。odb连接以及连接池中没有做断开重连或重建的动作，
需要在odb的源码libodb-mysql-***/odb/mysql/connection.cxx中37行添加自动重连选项：
```c++
int reconnect = 1;
mysql_options (handle_, MYSQL_OPT_RECONNECT, &reconnect);
```
添加完后编译、安装库：
```shell
$ cd gcc-X
$ bpkg build libodb-mysql #x86_64平台使用该命令编译，armp平台如果使用该命令编译不过，则使用下面的命令
#$ bpkg build libodb-mysql  ?sys:libmysqlclient  #使用系统安装的mysqlclient版本进行编译，解决arm环境下安装失败问题
$ bpkg install --all --recursive
```

## 环境安装

### 1.使用脚本自动化构建: <br/>
&emsp;该方法默认安装最新版的依赖和odb库。具体参考：[详细说明](https://build2.org/install.xhtml#unix)

1. 下载、安装自动化工具链(build2): 
    
```shell
$ cd ~/build2-build

$ curl -sSfO https://download.build2.org/0.13.0/build2-install-0.13.0.sh

$ shasum -a 256 -b build2-install-0.13.0.sh

$ aeec2ac0e7080341159bbbc95d8264521ddf82bda01c5c253d63cae5b4558b15

$ sh build2-install-0.13.0.sh
 ```
2. 编译安装odb编译器：具体参考：[详细说明](https://www.codesynthesis.com/products/odb/doc/install-build2.xhtml)

&emsp;（1）安装gcc plugin依赖<br/>
&emsp;&emsp;说明：X是指gcc的主版本号
```shell
$ gcc --version
$ gcc X.Y.Z

$ sudo apt-get install gcc-X-plugin-dev
```

&emsp;（2）配置编译选项（其中X是指gcc的主版本号）<br/>
&emsp;&emsp;说明：X是指gcc的主版本号
```shell
$ mkdir odb-build
$ cd odb-build

$ bpkg create -d odb-gcc-X cc     \
  config.cxx=g++                  \
  config.cc.coptions=-O3          \
  config.bin.rpath=/usr/local/lib \
  config.install.root=/usr/local  \
  config.install.sudo=sudo

$ cd odb-gcc-X
```

&emsp;（3）编译
```shell
$ bpkg build odb@https://pkg.cppget.org/1/beta
```

&emsp;（4）测试
```shell
$ bpkg test odb
```

&emsp;（5）安装odb编译器<br/>
&emsp;&emsp;说明：默认安装路径/usr/local/bin
```shell
$ bpkg install odb

$ which odb
$ odb --version
```
3. 编译安装odb运行库:

&emsp;&emsp;说明：X是指gcc的主版本号
```shell
$ cd odb-build

$ bpkg create -d gcc-X cc         \
config.cxx=g++                  \
config.cc.coptions=-O3          \
config.install.root=/usr/local  \
config.install.sudo=sudo
```
```shell
$ cd gcc-X
$ bpkg add https://pkg.cppget.org/1/beta
$ bpkg fetch
$ bpkg build libodb
$ bpkg build libodb-mysql #x86_64平台使用改命令编译，armp平台如果使用该命令编译不过，则使用下面的命令
#$ bpkg build libodb-mysql  ?sys:libmysqlclient  #使用系统安装的mysqlclient版本进行编译，解决arm环境下安装失败问题
$ bpkg install --all --recursive
```

### 4. 使用安装包编译安装 <br/>
【略】可参考：[安装教程](https://www.codesynthesis.com/products/odb/doc/install-unix.xhtml)、[安装包](https://www.codesynthesis.com/download/odb/)


## 目录结构
- 【generated】 使用odb生成的与数据库映射后支持代码
- 【persistence】数据库表对应的持久类
- 【test】测试代码 

## 接口介绍
屏蔽了不同数据表对象类型操作接口的差异，提供了统一简单的数据库操作接口
### 接口概览
```c++

// 数据库初始化、连接
bool init(const std::string& user,const std::string& passwd, const std::string& dbName, const std::string& host = "", unsigned int port = 0);

// 数据库反初始化
bool deinit();

// 数据库查询
template<typename T>
bool queryData(std::vector<T>& results, odb::query<T>& query)

// 数据库插入
template <typename T>
bool insertData(T& tableObject)

// 数据库更新
template <typename T>
bool updateData(T& tableObject)

// 数据库擦除
template <typename T>
bool eraseData(T& tableObject)

template <typename T>
bool eraseData(odb::query<T>& query)

```
 
## 快速示例
### 数据库表与数据类进行映射
```c++
#pragma db object table("wn_video_source") //将VideoSource类与数据库表wn_video_source映射
class VideoSource {
...

private:

#pragma db id auto                 //成员变量id 映射到wn_video_source表id字段
    unsigned long id;               
    
#pragma db column("has_ptz") type("INT") //成员变量hasPtz映射到wn_video_source表has_ptz字段
    bool hasPtz;                   

};
```
### 生成支持代码
编写数据表对应的持久类后，还需通过odb命令生成支持代码（用于将类和具体数据库（mysql等）的表做映射）
```shell
#使用c++ 11标准(支持智能指针)生成video_source.h对应的支持代码，并保存到../generated/目录中
odb --std c++11 -d mysql --generate-query --output-dir ../generated/ video_source.h
```
### 数据库表查询
```c++

init("root", "welltrans8746", "aimonitorv3", "**************", 3306);

// 查寻数据库表wn_video_source中所有记录，并打印所有记录的address字段
odb::query<VideoSource> q(odb::query<VideoSource>::id > 0);
std::vector<VideoSource> vecResult = {};

queryData(vecResult, q);

for (auto& i : vecResult)
{
    std::cout << i.getAddress() << std::endl;
}
```
### 创建关系表视图 查询关系表数据

```c++

//检测仪(wn_monitor)关联的检测通道(wn_detection_point)
#pragma db view object(Monitor) object(DetectionPoint inner: DetectionPoint::monitorId == Monitor::id)
struct MonitorDetectPointData
{
	std::shared_ptr<Monitor> monitorPtr;
	std::shared_ptr<DetectionPoint> detectPointPtr;
};

std::vector<MonitorDetectPointData> results = {};
odb::query<MonitorDetectPointData> query(odb::query<MonitorDetectPointData>::DetectionPoint::id == 1);

queryData(results, query);

for (auto& data : results)
{
    cout << "qurey DetectionPoint::id == 1 : monitor ip " << data.monitorPtr->getIp() << " DetecPointName " << data.detectPointPtr->getDetectPointName() << endl;
}

```

    


