
#include "InviteSession.hpp"
#include "SipPoolGuard.hpp"
namespace usg {

// CInviteSession::CInviteSession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from )
// : m_pool( pool ), m_endPoint( endPoint ), m_handler( 0 ), m_from( from ), m_logger( pool ),m_xmlType( "DDCP" )
// {
//     m_sipContactUri = "";
// }

    CInviteSession::CInviteSession( pj_pool_t *pool, pjsip_endpoint *endPoint, const std::string &from,const std::string& xmlType  )
            : m_pool( pool ), m_endPoint( endPoint ), m_handler( 0 ), m_from( from ), m_logger( pool ),m_xmlType( xmlType )
    {
        m_sipContactUri = "";
    }
    CInviteSession::~CInviteSession()
    {
    }

    CInviteSession *CInviteSession::setHandler( ICtxHandler *handler )
    {
        m_handler = handler;
        return this;
    }

    bool CInviteSession::invite( std::string &sid, const std::string &sipUri, const std::string &result )
    {
        return invite(sid,sipUri,"",result);
    }

    bool CInviteSession::invite( std::string &sid, const std::string &sipUri, const std::string &subject, const std::string &result )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        if ( sipUri.empty() || m_from.empty() )
        {
            return false;
        }
        pj_str_t uri = { (char *)sipUri.c_str(), (pj_ssize_t)sipUri.length() };
        pj_str_t from = { (char *)m_from.c_str(), (pj_ssize_t)m_from.length() };
        pj_str_t callid = { (char*)sid.c_str(), (pj_ssize_t)sid.length() };
        pjsip_tx_data *tdata = 0;

        if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, pjsip_get_invite_method(), &uri, &from, &uri, &from, &callid, 1, 0, &tdata ) )
            return false;

        pj_str_t type = { (char*)"application", 11 };
        std::string xmlType = m_xmlType;
        if ( m_xmlType == "MANSCDP" )
            xmlType = "sdp";

        pj_str_t subtype = { (char *)xmlType.c_str(), 3 };
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, &type, &subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;

        if (!subject.empty())
        {
            pj_str_t s = { (char*)"Subject", 7 };
            pj_str_t v = { (char *)subject.c_str(), (pj_ssize_t)subject.length() };

            pjsip_generic_string_hdr* hd = pjsip_generic_string_hdr_create(tdata->pool, &s, &v);
            if (hd != NULL)
            {
                pjsip_msg_add_hdr(tdata->msg,(pjsip_hdr *)hd);
            }
        }

        pjsip_tx_data_add_ref( tdata );

        if ( PJ_SUCCESS != pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 ) )
        {
            pjsip_tx_data_dec_ref( tdata );
            return false;
        }

        /*
         * 插入dialog记录.
         */
        pjsip_cseq_hdr *cseq = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CSEQ, 0 );
        pjsip_cid_hdr  *cid  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CALL_ID, 0 );

        m_logger.insertTx( sid = createSid( cid, cseq ), tdata );

        return true;
    }

    bool CInviteSession::invite( std::string &sid, const std::string &cid, const std::string &sipUri, const std::string &subject, const std::string &result )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        if ( sipUri.empty() || m_from.empty() )
        {
            return false;
        }
        pj_str_t uri = { (char *)sipUri.c_str(), (pj_ssize_t)sipUri.length() };
        pj_str_t from = { (char *)m_from.c_str(), (pj_ssize_t)m_from.length() };
        pj_str_t pjcallid = { (char *)cid.c_str(), (pj_ssize_t)cid.length() };

        pjsip_tx_data *tdata = 0;

        if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, pjsip_get_invite_method(), &uri, &from, &uri, &from, &pjcallid, 1, 0, &tdata ) )
            return false;

        pj_str_t type = { (char*)"application", 11 };
        std::string xmlType = m_xmlType;
        if ( m_xmlType == "MANSCDP" )
            xmlType = "sdp";

        pj_str_t subtype = { (char *)xmlType.c_str(), 3 };
        pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
        tdata->msg->body = pjsip_msg_body_create( tdata->pool, &type, &subtype, &text );
        if ( tdata->msg->body == NULL )
            return false;

        if (!subject.empty())
        {
            pj_str_t s = { (char*)"Subject", 7 };
            pj_str_t v = { (char *)subject.c_str(), (pj_ssize_t)subject.length() };

            pjsip_generic_string_hdr* hd = pjsip_generic_string_hdr_create(tdata->pool, &s, &v);
            if (hd != NULL)
            {
                pjsip_msg_add_hdr(tdata->msg,(pjsip_hdr *)hd);
            }
        }

        pjsip_tx_data_add_ref( tdata );

        if ( PJ_SUCCESS != pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 ) )
        {
            pjsip_tx_data_dec_ref( tdata );
            return false;
        }

        /*
         * 插入dialog记录.
         */
        pjsip_cseq_hdr *cseq = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CSEQ, 0 );
        pjsip_cid_hdr  *cid1  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tdata->msg, PJSIP_H_CALL_ID, 0 );

        m_logger.insertTx( sid = createSid( cid1, cseq ), tdata );

        return true;
    }

    bool CInviteSession::answer( pjsip_rx_data *rdata, int status, const std::string &result )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;
        pjsip_tx_data *tdata = 0;
        if ( PJ_SUCCESS != pjsip_endpt_create_response( m_endPoint, rdata, status, 0, &tdata ) )
            return false;

        char *contactUri = (char *)m_sipContactUri.c_str();
        pjsip_uri *uri = pjsip_parse_uri(tdata->pool, contactUri, strlen(contactUri), 0);
        pjsip_contact_hdr* contactHdr = pjsip_contact_hdr_create( tdata->pool );
        if ( contactHdr == NULL )
            return false;
        contactHdr->uri = uri;

        pjsip_msg_add_hdr( tdata->msg, (pjsip_hdr*)contactHdr );

        if ( result.length() != 0 )
        {
            pj_str_t *type = &rdata->msg_info.ctype->media.type;
            pj_str_t *subtype = &rdata->msg_info.ctype->media.subtype;
            pj_str_t text = { (char *)result.c_str(), (pj_ssize_t)result.length() };
            //tdata->msg->body = pjsip_msg_body_create( pool, type, subtype, &text );
            tdata->msg->body = pjsip_msg_body_create( tdata->pool, type, subtype, &text );
            if ( tdata->msg->body == NULL )
                return false;
        }

        return PJ_SUCCESS == pjsip_endpt_send_response2( m_endPoint, rdata, tdata, 0, 0 );
    }

    bool CInviteSession::cancel( const std::string &sid )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        pjsip_tx_data *tPrev = 0;
        if ( !m_logger.removeTx( sid, &tPrev ) ) return false;

        pjsip_tx_data *tdata = 0;
        if ( PJ_SUCCESS != pjsip_endpt_create_cancel( m_endPoint, tPrev, &tdata ) )
        {
            pjsip_tx_data_dec_ref( tPrev );
            return false;
        }

        pjsip_tx_data_dec_ref( tPrev );

        return PJ_SUCCESS == pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 );
    }

    bool CInviteSession::ackImp( pjsip_rx_data *rdata )
    {
//    TRY_REGISTER_THIS_THREAD( false );

        if ( !rdata ) return false;

        rdata->msg_info.msg->line.status.code = 300;

        pjsip_tx_data *tPrev = 0;
        if ( !m_logger.pickupTx( createSid( rdata->msg_info.cid, rdata->msg_info.cseq ), tPrev ) ) return false;

        pjsip_tx_data *tdata = 0;
        if ( PJ_SUCCESS != pjsip_endpt_create_ack( m_endPoint, tPrev, rdata, &tdata ) )
            return false;

        pjsip_rx_data* rxData = 0;
        pjsip_rx_data_clone( rdata,0,&rxData );
        if ( PJ_SUCCESS != pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 ) )
        {
            pjsip_rx_data_free_cloned( rxData );
            return false;
        }

        m_logger.insertRx( createSid( rdata->msg_info.cid, rdata->msg_info.cseq ), rxData );
        return true;
    }

// bool CInviteSession::ack( const std::string &cid, const std::string &sipUri )
// {
// 	TRY_REGISTER_THIS_THREAD( false );
//
// 	if ( sipUri.empty() || m_from.empty() )
// 	{
// 		return false;
// 	}
// 	pj_str_t uri = { (char *)sipUri.c_str(), sipUri.length() };
// 	pj_str_t from = { (char *)m_from.c_str(), m_from.length() };
// 	pj_str_t pjcallid = { (char *)cid.c_str(), cid.length() };
//
// 	pjsip_tx_data *tdata = 0;
//
// 	if ( PJ_SUCCESS != pjsip_endpt_create_request( m_endPoint, pjsip_get_ack_method(), &uri, &from, &uri, &from, &pjcallid, 1, 0, &tdata ) )
// 		return false;
//
// 	if ( PJ_SUCCESS != pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 ) )
// 	{
// 		pjsip_tx_data_dec_ref( tdata );
// 		return false;
// 	}
//
// 	return true;
// }

    bool CInviteSession::bye( const std::string &sid )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        pjsip_tx_data *tPrev = 0;
        pjsip_rx_data *rPrev = 0;

        m_logger.pickupTx( sid, tPrev );
        m_logger.pickupRx( sid, rPrev );

        if ( !tPrev && !rPrev ) return false;

        pjsip_uri      *uri  = 0;
        pjsip_from_hdr *from = 0;
        pjsip_to_hdr   *to   = 0;
        pjsip_cid_hdr  *cid  = 0;
        pjsip_cseq_hdr *seq  = 0;

        if ( tPrev )
        {
            uri  = tPrev->msg->line.req.uri;
            from = (pjsip_from_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_FROM, 0 );
            to   = (pjsip_to_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_TO, 0 );
            cid  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_CALL_ID, 0 );
            seq  = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_CSEQ, 0 );
        }
        if ( rPrev )
        {
            uri  = rPrev->msg_info.msg->line.req.uri;
            from = rPrev->msg_info.from;
            to   = rPrev->msg_info.to;
            cid  = rPrev->msg_info.cid;
            seq  = rPrev->msg_info.cseq;
        }


        pjsip_tx_data *tdata = 0;
        if ( PJ_SUCCESS != pjsip_endpt_create_request_from_hdr( m_endPoint, pjsip_get_bye_method(), uri, from, to, 0, cid, seq->cseq, 0, &tdata ) )
        {
            m_logger.removeRx( sid );
            m_logger.removeTx( sid );
            return false;
        }

        m_logger.removeRx( sid );
        m_logger.removeTx( sid );

        if ( PJ_SUCCESS != pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 ) )
        {
            return false;
        }

        return true;
    }

    bool CInviteSession::bye( const std::string&sid, int cseq )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        pjsip_tx_data *tPrev = 0;
        pjsip_rx_data *rPrev = 0;

        m_logger.pickupTx( sid, tPrev );
        m_logger.pickupRx( sid, rPrev );

        if ( !tPrev && !rPrev ) return false;

        pjsip_uri      *uri  = 0;
        pjsip_from_hdr *from = 0;
        pjsip_to_hdr   *to   = 0;
        pjsip_cid_hdr  *cid  = 0;
        //pjsip_cseq_hdr *seq  = 0;

        if ( tPrev )
        {
            uri  = tPrev->msg->line.req.uri;
            from = (pjsip_from_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_FROM, 0 );
            to   = (pjsip_to_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_TO, 0 );
            cid  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_CALL_ID, 0 );
            //seq  = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_CSEQ, 0 );
        }
        if ( rPrev )
        {
            //uri  = rPrev->msg_info.msg->line.req.uri;
            from = rPrev->msg_info.from;
            to   = rPrev->msg_info.to;
            cid  = rPrev->msg_info.cid;
            //seq  = rPrev->msg_info.cseq;
        }

        pjsip_tx_data *tdata = 0;
        if ( PJ_SUCCESS != pjsip_endpt_create_request_from_hdr( m_endPoint, pjsip_get_bye_method(), uri, from, to, 0, cid, cseq, 0, &tdata ) )
        {
            m_logger.removeRx( sid );
            m_logger.removeTx( sid );
            return false;
        }

        m_logger.removeRx( sid );
        m_logger.removeTx( sid );

        if ( PJ_SUCCESS != pjsip_endpt_send_request_stateless( m_endPoint, tdata, 0, 0 ) )
        {
            return false;
        }

        return true;
    }
    bool CInviteSession::onInvite( pjsip_rx_data *rdata )
    {
        if ( !rdata->msg_info.msg || !rdata->msg_info.msg->body || !rdata->msg_info.cseq )
            return false;
        /*
         * 获取uri.
         */
        if ( !PJSIP_URI_SCHEME_IS_SIP( rdata->msg_info.msg->line.req.uri ) ) return false;

        pjsip_sip_uri *uri = (pjsip_sip_uri *)rdata->msg_info.msg->line.req.uri;
        std::string oid( uri->user.ptr, uri->user.slen );
        if ( oid.empty() ) return false;
        /*
         * 获取其他参数.
         */
        const char *sub = rdata->msg_info.msg->body->content_type.subtype.ptr;
        const char *ptr = (const char *)rdata->msg_info.msg->body->data;
        size_t      len = rdata->msg_info.msg->body->len;
        std::string sid = createSid( rdata->msg_info.cid, rdata->msg_info.cseq );
        /*
         * 判断正文是否是所需要的格式.
         */
        if ( 0 == ::strnicmp( sub, "DDCP", 4 ) ||
             0 == ::strnicmp( sub, "XML", 3 ) ||
             0 == ::strnicmp( sub, "IVS_XML", 7 ) ||
             0 == ::strnicmp( sub, "sdp", 3 ) )
        {
            if ( m_handler )
            {
                std::string result;
                if ( m_handler->commitInvite( sid, oid, ptr, len, result ) )
                {
                    if ( !answer( rdata, 200, result ) ) return false;

                    pjsip_rx_data *clone = 0;
                    pjsip_rx_data_clone( rdata, 0, &clone );
                    m_logger.insertRx( sid, clone );

                    return true;
                }
                else
                {
                    return answer( rdata, 400, result );
                }
            }
            return false;
        }

        return true;
    }

    bool CInviteSession::onAnswer( pjsip_rx_data *rdata )
    {
        if ( !m_handler || !rdata->msg_info.msg || !rdata->msg_info.cseq )
            return false;
        /*
         * 获取其他参数.
         */
        std::string sid = createSid( rdata->msg_info.cid, rdata->msg_info.cseq );
        //std::string fromTag( rdata->msg_info.from->tag.ptr, rdata->msg_info.from->tag.slen );
        //std::string toTag( rdata->msg_info.to->tag.ptr, rdata->msg_info.to->tag.slen );

        int status = rdata->msg_info.msg->line.status.code / 100;
        switch ( status )
        {
            case 2:
            {
                char *ptr = 0;
                size_t len = 0;

                if ( rdata->msg_info.msg->body )
                {
                    ptr = (char *)rdata->msg_info.msg->body->data;
                    len = rdata->msg_info.msg->body->len;
                }

                if ( !m_handler->commitAnswer( sid, ptr, len, true, IInviteSession::SESSION_TYPE ) || !m_handler->setCseqValues( sid, rdata->msg_info.cseq->cseq ) || !ackImp( rdata ) )
                {
                    m_logger.removeTx( sid );
                    m_logger.removeRx( sid );
                    return false;
                }

                return true;
            }
            case 4:
                m_handler->commitAnswer(sid, 0, 0, false, IInviteSession::SESSION_TYPE);
                m_handler->setCseqValues(sid, 0 );
				
				m_logger.removeTx(sid);
				m_logger.removeRx(sid);
				return true;
//                m_logger.removeTx( sid );
//                return m_handler->commitAnswer( sid, 0, 0, false, IInviteSession::SESSION_TYPE );

            default:
                break;
        }

        return true;
    }

    bool CInviteSession::onCancel( pjsip_rx_data *rdata )
    {
        if ( !rdata->msg_info.msg || !rdata->msg_info.cseq )
            return false;
        /*
         * 获取其他参数.
         */
        std::string sid = createSid( rdata->msg_info.cid, rdata->msg_info.cseq );
        m_logger.removeRx( sid );

        if ( m_handler )
        {
            //return m_handler->commitCancel( sid );
            std::string result = "";

            if ( m_handler->commitBye( sid ) )
            {
                if ( !answer( rdata, 200, result ) ) return false;

                return true;
            }
            else
            {
                return answer( rdata, 400, result );
            }
        }

        return false;
    }

    bool CInviteSession::onAck( pjsip_rx_data *rdata )
    {
        if ( !rdata->msg_info.msg || !rdata->msg_info.cseq )
            return false;

        if ( m_handler )
            return m_handler->commitAck( createSid( rdata->msg_info.cid, rdata->msg_info.cseq ) );

        return false;
    }

    bool CInviteSession::onBye( pjsip_rx_data *rdata )
    {
        if ( !rdata->msg_info.msg || !rdata->msg_info.cseq )
            return false;
        /*
         * 获取其他参数.
         */
        std::string sid = createSid( rdata->msg_info.cid, rdata->msg_info.cseq );

        m_logger.removeRx( sid );
        m_logger.removeTx( sid );

        if ( m_handler )
        {
            //return m_handler->commitBye( sid );
            std::string result = "";

            if ( m_handler->commitBye( sid ) )
            {
                //yp171115 这里会异常，暂时取消处理
                //if ( !answer( rdata, 200, result ) ) return false;

                return true;
            }
            else
            {
                return answer( rdata, 400, result );
            }
        }

        return false;
    }

    std::string CInviteSession::createSid( pjsip_cid_hdr *cid, pjsip_cseq_hdr *cseq )
    {
        if ( !cid || !cseq ) return std::string();

        std::string sid( cid->id.ptr, cid->id.slen );

        //char buf[16] = { 0 };
        //sprintf( buf, "%d", cseq->cseq );
        return sid;// += buf;
    }

    bool CInviteSession::getReadSipInfo( const std::string& sid, pjsip_uri *&target, pjsip_from_hdr *&from, pjsip_to_hdr *&to, pjsip_cid_hdr *&call_id, pjsip_contact_hdr *&contact )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        pjsip_tx_data *tPrev = 0;
        pjsip_rx_data *rPrev = 0;

        m_logger.pickupTx( sid, tPrev );
        m_logger.pickupRx( sid, rPrev );

        if ( !tPrev && !rPrev ) return false;

        if ( tPrev )
        {
            target  = tPrev->msg->line.req.uri;
            from = (pjsip_from_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_FROM, 0 );
            to   = (pjsip_to_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_TO, 0 );
            call_id  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_CALL_ID, 0 );
            contact = ( pjsip_contact_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_CONTACT, 0 );
            //seq  = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_CSEQ, 0 );
        }
        if ( rPrev )
        {
            //target  = rPrev->msg_info.msg->line.status.uri;
            from = rPrev->msg_info.from;
            to   = rPrev->msg_info.to;
            call_id  = rPrev->msg_info.cid;
        }

        return true;
    }

    bool CInviteSession::getReadSipInfo( const std::string& sid, pjsip_uri *&target, pjsip_from_hdr *&from, pjsip_to_hdr *&to, pjsip_cid_hdr *&call_id )
    {
        //TRY_REGISTER_THIS_THREAD( false );
        if( !registPjThread() ) return false;

        pjsip_tx_data *tPrev = 0;
        pjsip_rx_data *rPrev = 0;

        m_logger.pickupTx( sid, tPrev );
        m_logger.pickupRx( sid, rPrev );

        if ( !tPrev && !rPrev ) return false;

        if ( tPrev )
        {
            target  = tPrev->msg->line.req.uri;
            from = (pjsip_from_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_FROM, 0 );
            to   = (pjsip_to_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_TO, 0 );
            call_id  = (pjsip_cid_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_CALL_ID, 0 );
            //seq  = (pjsip_cseq_hdr *)pjsip_msg_find_hdr( tPrev->msg, PJSIP_H_CSEQ, 0 );
        }
        if ( rPrev )
        {
            //target  = rPrev->msg_info.msg->line.status.uri;
            from = rPrev->msg_info.from;
            to   = rPrev->msg_info.to;
            call_id  = rPrev->msg_info.cid;
        }

        return true;
    }

    bool CInviteSession::setSipContactUri( const std::string sipContactUri )
    {
        m_sipContactUri = sipContactUri;
        return true;
    }

}
