#ifndef USGSERVICE_HPP_
#define USGSERVICE_HPP_

#include "UsgManager/include/UsgManagerCfg.hpp"
#include <boost/uuid/uuid.hpp>
#include "UsgManager/include/UsgServiceItf.hpp"

namespace usg {

    class USGMANAGER_PRIVATE CUsgService  : public IUsgService
    {
    public:
        CUsgService();
        virtual ~CUsgService();

    public:
        /**
         * @brief 准备实时流播放
         * @param[in]  mediaId         资源ID.
         * @param[in]  mOrs            媒体源的主子码流.[1代表主码流，2代表子码流]
         * @param[in]  clientAddr      客户端接收流地址.
         * @param[in]  remoteAddr      远端地址.
         * @param[out] streamRate      码率.
         * @param[out] packerType      封包类型.
         * @param[out] sid             标识一次呼叫的标识号.
         * @return bool                返回值说明.
         * @retval true                成功.
         * @retval false               失败.
         * @note 现有接口中有一个是通过通道号去标志，rg与mds整合后不支持划归只能通过资源标志符去去处理
         */
        virtual bool preparePlayRealStream(  const boost::uuids::uuid &mediaId,
                                             const uint8_t mOrs,
                                             const ACE_INET_Addr& clientAddr,
                                             ACE_INET_Addr &remoteAddr,
                                             uint16_t& streamRate,
                                             uint8_t  &packerType,
                                             std::string &sid
        );

        /**
         * @brief 确认播放实时流.
         *        在此过程中,向clientAddr发送实时流.
         *        如果两次调用playAck接口函数传递进来的sid号相同,则认为是同一个ACK的重复请求.
         *        一个媒体源可能对应有多个sid,要求准备的有可能保是其中的某一个.
         * @param[in]  sid        标识一次呼叫的标识号.
         * @return bool           返回值说明.
         * @retval true           成功.
         * @retval false          失败.
         */
        virtual bool playRealStreamAck(  const std::string &sid );


        /**
         * @brief 实时流保活.
         * @param[in]  sid        标识一次呼叫的标识号.
         * @return bool           返回值说明.
         * @retval true           成功.
         * @retval false          失败.
         */
        virtual bool realKeepalive(  const std::string &sid );
        /**
         * @brief 停止实时流,即将标识为sid的流请求停止.
         *        一个媒体源可能对应有多个sid,要求停止的有可能不是最后一个sid.
         * @param[in]  sid    标识一次呼叫的标识号,同playXxx接口函数中传递进来的sid号.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */

        virtual bool stopRealStream(  const std::string &sid
        );

        /**
         * @brief 向下级SIP请求URL地址
         * @param[in] mediaId       媒体源的标识符
         * @param[in] timePeriod    时间段，开始时间和结束时间
         * @param[in]  clientAddr      客户端接收流地址.
         * @param[in]  remoteAddr      远端地址.
         * @return bool             返回值说明
         * @retval true             成功
         * @retval false            失败
         */
        virtual bool preparePlayHistoryStream(  const boost::uuids::uuid &mediaId,
                                                const TimePeriodUnit &timePeriod,
                                                const ACE_INET_Addr clientAddr,
                                                ACE_INET_Addr &remoteAddr,
                                                std::string &sid );

        /**
         * @brief 向下级SIP请求URL地址
         * @param[in] mediaId       媒体源的标识符
         * @param[in] timePeriod    时间段，开始时间和结束时间
         * @param[in]  clientAddr      客户端接收流地址.
         * @param[in]  remoteAddr      远端地址.
         * @return bool             返回值说明
         * @retval true             成功
         * @retval false            失败
         */
        virtual bool prepareDownloadHistoryStream(  const boost::uuids::uuid &mediaId,
                                                    const TimePeriodUnit &timePeriod,
                                                    const ACE_INET_Addr clientAddr,
                                                    ACE_INET_Addr &remoteAddr,
                                                    std::string &sid );

        /**
         * @brief 向下级SIP请求播放历史流
         * @param[in] sid       标识一次呼叫的标识号
         * @retval true             成功
         * @retval false            失败
         */
//	virtual bool playHistoryStreamAck(  const std::string &sid);

        /**
     * @brief 向下级SIP请求播放历史流
     * @param[in] sid       标识一次呼叫的标识号
     * @param[in] n         速度/分子
     * @param[in] d         速度/分母
     * @param[in] modifyTime    时间是否有偏移
     * @param[in] beginTime     开始时间偏移，有偏移则填充为偏移值，没有填充为-1,由客户端填充(umg)
     * @param[in] endTime       结束时间偏移，有偏移则填充为偏移值，没有填充为-1,由客户端填充(umg)
     * @retval true             成功
     * @retval false            失败
     */
        virtual bool playHistoryStreamAck(  const std::string &sid,  uint8_t n,  uint8_t d,  bool modifyTime,  uint32_t beginTimeOff,  uint32_t endTimeOff );


        /**
         * @brief 向下级SIP请求暂停历史流
         * @param[in] sid       标识一次呼叫的标识号
         * @retval true             成功
         * @retval false            失败
         */
        virtual bool pause(  const std::string &sid );

        /**
         * @brief 向下级SIP请求设置历史流播放速度
         * @param[in] sid       标识一次呼叫的标识号
         * @param[in] n         速度/分子
         * @param[in] d         速度/分母
         * @retval true         成功
         * @retval false        失败
         */
//	virtual bool setSpeed(  const std::string &sid,  uint8_t n,  uint8_t d);

        /**
         * @brief 向下级SIP请求改变历史流播放时间
         * @param[in] sid           标识一次呼叫的标识号
         * @param[in] beginTime     开始时间偏移
         * @param[in] endTime       结束时间偏移
         * @retval true             成功
         * @retval false            失败
         */
//	virtual bool modifyTimeSpan( const std::string &sid,  uint32_t beginTimeOff,  uint32_t endTimeOff);

        /**
         * @brief 向下级SIP请求停止历史流
         * @param[in] sid           标识一次呼叫的标识号
         * @retval true             成功
         * @retval false            失败
         */
        virtual bool stopHistoryStream(  const std::string &sid );


        virtual bool getAllResInfo(  std::map<boost::uuids::uuid, SResInfo >& mapRes,  uint32_t& timeStamp );
        /**
         * @brief 	设备远程启动，for GB28181
         * @param[in]  resId    		资源id.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */
//	virtual bool deviceReboot(  const boost::uuids::uuid &resId );

        /**
         * @brief 	录像控制，for GB28181
         * @param[in]  resId    		资源id.
         * @param[in]  flag    		true 开始，flase 停止.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */
//	virtual bool recordContronl(  const boost::uuids::uuid &resId,  const bool flag );

        /**
         * @brief 	布撤防，for GB28181
         * @param[in]  resId    		资源id.
         * @param[in]  flag    		true 布防，flase 撤防.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */
//	virtual bool guardContronl(  const boost::uuids::uuid &resId,  const bool flag );

        /**
         * @brief 	告警复位，for GB28181
         * @param[in]  resId    		资源id.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */
//	virtual bool alarmReset(  const boost::uuids::uuid &resId );

        /**
         * @brief 	目录查询，for GB28181
         * @param[in]  devId    		下级id.
         * @param[out]  info    		返回查询结果.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */
//	virtual bool deviceCatalog(  SQueryCatalog &info );

        /**
         * @brief 	  信息查询，for GB28181
         * @param[in]  devId    		资源id.
         * @param[out]  info    		返回查询结果.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */
//	virtual bool deviceInfo(  const boost::uuids::uuid &resId,  SQueryInfo &info );

        /**
         * @brief 	  状态查询，for GB28181
         * @param[in]  devId    		资源id.
         * @param[out]  info    		返回查询结果.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */
//	virtual bool deviceStatus(  const boost::uuids::uuid &resId,  SQueryStatus &info );

        /**
         * @brief SIP上级向下级发送语音广播请求
         * @return bool             返回值说明
         * @retval true             成功
         * @retval false            失败
         */
//	virtual bool audioBroadcast( const std::string &sourceId,  const std::string &targetId);

        /**
        * @brief SIP上级向下级发送云台控制请求
        * @param[in]  ptzCommand    		云台指令
        * @param[in]  resId    			资源id
        * @param[in]  arg1    			指令参数
        * @param[in]  arg2    			指令参数
        * @return bool             返回值说明
        * @retval true             成功
        * @retval false            失败
        */
// 	virtual bool ptzCtrl(  const uint8_t ptzCommand,
// 							 const boost::uuids::uuid &resId, 
// 							 const uint16_t arg1,
// 							 const uint16_t arg2,
// 							RIMI_INVOKE_PARAM);
    };

}

#endif
