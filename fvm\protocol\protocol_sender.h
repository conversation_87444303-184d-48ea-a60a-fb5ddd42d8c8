/**
 * Project FVM
 */

#pragma once
#include <optional>

namespace fvm::protocol
{
    /*
     * 发送全局个性化参数
     * @param processId 进程号
     */
    void sendAlgorithmParam(const std::optional<int> &processId);

    /*
     * 发送SystemConfig
     * @param processId 进程号
     */
    void sendSystemConfig(const std::optional<int> &processId);

    /*
     * 发送BaseConfig
     * TODO  名字待统一
     * @param processId 进程号
     */
    bool sendProcessBasicConfigs(const std::optional<int> &processId);

    /*
     * 发送通道检测参数
     * @param processId 进程号
     */
    void sendChannelDetectParam(int channelId);
}