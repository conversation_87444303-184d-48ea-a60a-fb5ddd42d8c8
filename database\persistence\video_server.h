/**
 * @addtogroup odbDatabaseGroup
 * @brief 视频接入服务器
 * @{
 */
#ifndef _VIDEOSERVER_H
#define _VIDEOSERVER_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {

/**
 * @brief  视频接入服务器 对应数据库aimonitorV3的表wn_access_front_end
 */
#pragma db object table("wn_access_front_end")
class VideoServer {
public:

    VideoServer(const std::string& name,
        int accessType,
        const std::string& ip,
        int port,
        const std::string& user,
        const std::string& pwd,
        const std::string& factory,
        const std::string& sipid,
        int status,
        int subNum,
        bool isDel
    )
        : videoSourceName(name), accessType(accessType), ip(ip),
        port(port), userName(user),
        password(pwd), factory(factory),
        sipid(sipid), status(status),
        subNum(subNum), isDel(isDel)
    {
    }


    int getId() const {
        return id;
    }

    const std::string& getVideoSrcName() const {
        return videoSourceName;
    }

    void setVideoSrcName(const std::string& name) {
        this->videoSourceName = name;
    };

    int getAccessType() const {
        return accessType;
    }

    void setAccessType(int accesType) {
        this->accessType = accesType;
    }

    const std::string& getIp() const {
        return ip;
    }

    void setIp(const std::string& ip) {
        this->ip = ip;
    }

   int getPort() const {
        return port;
    }

    void setPort(int port) {
        this->port = port;
    }

    const std::string& getUserName() const {
        return userName;
    }

    void setUserName(const std::string& name) {
        this->userName = name;
    }

    const std::string& getPassword() const {
        return password;
    }

    void setPassword(const std::string& pwd) {
        this->password = pwd;
    }

    const std::string& getFactory() const {
        return factory;
    }

    void setFactory(const std::string& fac) {
        this->factory = fac;
    }

    const std::string& getSipid() const {
        return sipid;
    }

    void setSipid(const std::string& sipid) {
        this->sipid = sipid;
    }

    int getStatus() const {
        return status;
    }

    void setStatus(int status) {
        this->status = status;
    };

    int getSubNum() const {
        return subNum;
    }

    void setSubNum(int num) {
        this->subNum = num;
    };

    bool getIsDel() const {
        return isDel;
    }

    void setIsDel(bool del) {
        this->isDel = del;
    }

 private:

     friend class odb::access;
     VideoServer() {}

private:

#pragma db id auto
    unsigned long id;                  //!< 表ID

#pragma db column("name") type("VARCHAR(255)")
    std::string videoSourceName;      //!< 视频服务器名

#pragma db column("access_type")
    int accessType;                   //!< 视频服务接入类型 1:28181 2:sdk 3:rtsp 4:file

#pragma db column("ip") type("VARCHAR(255)")
    std::string ip;                   //!< 服务器IP

#pragma db column("port")
    int port;                         //!< 服务器port

#pragma db column("username") type("VARCHAR(255)")
    std::string userName;             //!< 服务器userName

#pragma db column("password") type("VARCHAR(255)")
    std::string password;             //!< 服务器password

#pragma db column("factory") type("VARCHAR(255)")
    std::string factory;              //!< 服务器厂商

#pragma db column("sipid") type("VARCHAR(255)")
    std::string sipid;                //!< sip信令id

#pragma db column("status")
    int status;                       //!< 服务器状态

#pragma db column("sub_num")
    int subNum;                       //!<

#pragma db column("is_del") type("INT")
    bool isDel;
};
}
#endif //_VIDEOSERVER_H
/**
 * @}
 */