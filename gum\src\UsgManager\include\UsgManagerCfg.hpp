#ifndef USGMANAGERCFG_HPP_
#define USGMANAGERCFG_HPP_

#if defined( WIN32 ) && defined( _MSC_VER )
#    ifdef USGMANAGER_PRJ
#        define USGMANAGER_PUBLIC __declspec(dllexport)
#    else
#        define USGMANAGER_PUBLIC __declspec(dllimport)
#    endif
#elif defined( __GNUC__ )
#    define USGMANAGER_PUBLIC //__attribute__((visibility("default")))
#endif

#if defined( TEST_SUPPORT )	// 单元测试支持
#    define USGMANAGER_PRIVATE USGMANAGER_PUBLIC
#else
#    if defined( WIN32 ) && defined( _MSC_VER )
#        define USGMANAGER_PRIVATE
#    elif defined( __GNUC__ )
#        define USGMANAGER_PRIVATE __attribute__((visibility("hidden")))
#    endif
#endif

#if defined( COMPILE_BY_SOURCE ) && defined( WIN32 ) && defined( _MSC_VER )
#	undef  USGMANAGER_PUBLIC
#	undef  USGMANAGER_PRIVATE
#	define USGMANAGER_PUBLIC
#	define USGMANAGER_PRIVATE
#endif

#ifndef interface
#   define interface struct
#endif

#endif
