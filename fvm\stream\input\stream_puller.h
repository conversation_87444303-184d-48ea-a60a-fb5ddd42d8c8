/**
 * Project FVM
 */

#pragma once
#include "stream_input.h"

/**
 * @brief: 基础视频输入 (FFMPEG 输入, 含RTSP、RTP等)
 */
namespace fvm::stream
{
    class StreamPuller : public StreamInput {
    private:
        void process() override;

        /**
        * 初始化参数
        */
        void initOptions() override;

        /**
        * 调用相机预置位
        */
        bool callCameraPreset(int presetId) override;

        /**
         * 输入流是否能调用预置位
         */
        bool isPtzCapable() override;

        /**
         * 控制云台
         */
        bool controlPtz(int action, int step) override;

    public:
        bool getPtzPosition(double& x, double& y, double& z, const std::optional<int>& actPresetId) override;

    private:

        /**
         * TCP 收流模式
         */
        bool tcpMode = true;

    };
}