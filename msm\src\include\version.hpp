#ifndef VERSION_HPP_
#define VERSION_HPP_

static const std::string PROG_VERSION = "1.0.0";
static const std::string PROG_NAME = "MSM";
#ifdef _WIN32
#include <Windows.h>
#define UNUSED
#else
#define UNUSED __attribute__((unused)) 
#endif

UNUSED static void showVer()
{
	int iYear = getCurTime().date().year();
	std::cout << PROG_NAME << " version " << PROG_VERSION << " Copyright (c) 2021-" << iYear << " WELLTRANS O&E CO.,LTD.\n";
}

#endif // VERSION_HPP_

