cmake_minimum_required(VERSION 3.5.2)


## 项目信息
project("test_odb_databse")

set(EXECUTABLE_OUTPUT_PATH   ../out/bin)

set(CMAKE_VERBOSE_MAKEFILE ON)  
add_compile_options(-g -std=c++17 -fPIC -fstack-protector-all -Wno-deprecated-declarations)

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}  MAIN_SRC)

include_directories(../generated)
include_directories(../persistence)
include_directories(/user/local/include)
link_directories(/user/include/c++)

link_directories(/user/local/lib)
link_directories(${PROJECT_SOURCE_DIR}/../out/lib/)

add_subdirectory(../ odb_databse_binary_dir)

add_executable(test_odb_databse ${MAIN_SRC})

message(STATUS "lib odb_database =  ${PROJECT_SOURCE_DIR}/../out/lib/")

target_link_libraries(test_odb_databse odb-mysql odb ai_database)

