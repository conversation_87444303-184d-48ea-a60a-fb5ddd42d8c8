/**
 * @file    command.h
 * @brief   命令行交互接口
 * <AUTHOR>
 * @version 0.0.1
 * @date    01 Nov 2021
 */


#ifndef TEST_CLI_COMMAND_PROCESS_H
#define TEST_CLI_COMMAND_PROCESS_H

#include <iostream>
#include <array>
#include <functional>
#include <vector>
#include <thread>
#include <map>
#include <mutex>
#include <termio.h>
#include <atomic>

namespace cli {


    using  consoleParamList = std::vector<std::string>;
    using  consoleFunction = std::function<void(const consoleParamList& paramList)>;
    //! 检查参数个数
    #define CHECK_PARAM_NUM(paramList,num)    if((paramList).size() < (num)) {std::cout << "error: Incorrect parameter number, " \
    << "need "<< (num) << " parameters !" << std::endl; return;}
    //! 字符串(char*)转为int
    #define GET_PARAM_INT(param)              (std::stoi(param))
    //! 字符串(char*)转为long
    #define GET_PARAM_LONG(param)             (std::stol(param))
    //! 字符串(char*)转为double
    #define GET_PARAM_DOUBLE(param)           (std::stod(param))
    //! 字符串(char*)转为float
    #define GET_PARAM_FLOAT(param)            (std::stof(param))

    /**
     * @brief      注册的命令结构
     */
    struct Command{
        std::string       name;          //!< 命令名
        consoleFunction   func;          //!< 命令函数对象
        std::string       description;   //!< 命令功能描述，参数描述
    };

    /**
     * @brief      处理终端命令输入，命令解析，执行命令
     */
    class CommandProcess
    {
    public:
        CommandProcess() = default ;
        ~CommandProcess();

        /**
         * @brief      开启终端处理线程
         */
        void start();

        /**
         * @brief      停止终端处理线程
         */
        void stop();
        /**
         * @brief      添加一个用户命令，将用户函数与命令名绑定
         * @param[in]  funcName: 命令名，在命令行输入该名后调用相关的函数
         * @param[in] func: 命令名对应的函数
         */
        void addCommand(const std::string& funcName, const consoleFunction& func, const std::string& description);
        /**
         * @brief      删除命令
         * @param[in]  funcName: 需要删除的命令名
         */
        void removeCommand(const std::string& funcName);

        /**
         * @brief      获取已经注册的所有命令
         */
        std::vector<Command> getCommands();

    private:
        /**
         * @brief      执行用户输入的命令
         * @param[in]  funcName: 命令名 params: 命令参数
         */
        bool execCommand(const std::string& funcName, const consoleParamList& params);
        /**
         * @brief      处理终端输入命令字符串，分割处命令名和参数，并执行命令绑定的函数
         * @param[in]  funcName: 终端输入命令字符串
         */
        bool handleInputCommand(const std::string& inputStr);
        /**
         * @brief      终端处理程序
         */
        void consoleRun();

    private:
        std::vector<Command> commands = {};                        //!< 命令集
        std::mutex commandsLock;                                   //!< 命令集锁
        std::thread consoleThread;                                 //!< 终端处理线程
        std::atomic<bool> running = false;
        struct termios rawTermios = {};                            //!< 原生终端参数，stop调用kill线程后恢复现场
    };
}

#endif //TEST_CLI_COMMAND_PROCESS_H
