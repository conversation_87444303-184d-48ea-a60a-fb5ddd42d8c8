/**
 * Project FVM
 */

#include <algorithm>
#include "stream_pipe.h"
#include "ailog.h"

/**
 * StreamPipe implementation
 *
 * @brief: 视频流管道 （可作为临时通道、或派生检测通道）
 */
namespace fvm::stream
{
    /**
     * 初始化输入器
     * @param StreamInput
     */
    void StreamPipe::initInput(StreamInputPtr input)
    {
        if(input == nullptr) return;

        std::lock_guard<boost::fibers::mutex> lock(mutexInput);
        if(this->inputStream == nullptr || !this->inputStream->isPlaying())
        {
            this->switchInput(input);
        }
        else
        {
            //是同一个视频输入，并且在播放，查看是否正常取流，若是，则直接返回，不处理，否则停止当前播放，然后播放新的
            unsigned long videoId = input->getVideoSource()->videoSourcePtr->getId();
            if ( this->inputStream->getVideoSource()->videoSourcePtr->getId() == videoId )
            {
                //已经得到了视频索引，不需要重新请求
                if ( this->inputStream->getVideoIndex() >= 0 )
                    return;
            }
            if(!scheduledInputStreams.empty())// 当前 输入正在使用
            {
                scheduledInputStreams.clear();
            }
            scheduledInputStreams.push_back(input);
            this->inputStream->stopPlay();
        }
    }

    /**
     * 初始化输出列表
     * @param StreamOutputList
     */
    void StreamPipe::initOutputs(StreamOutputList outputs)
    {
        std::lock_guard<boost::fibers::mutex> lock(mutexOutput);
        for (auto& output: this->outputStreams)
        {
            if(output)
            {
                this->removeOutput(output);
            }
        }

        this->outputStreams = outputs;
        if(this->codecInfo != nullptr)
        {
            for (auto &output : this->outputStreams)
            {
                output->setCodecInfo(this->codecInfo);
            }
        }
    }

    /**
     * 增加视频输出
     * @param StreamOutput
     */
    bool StreamPipe::addOutput(StreamOutputPtr output)
    {
        std::lock_guard<boost::fibers::mutex> lock(mutexOutput);
        bool alreadyOutput = false;
        for (auto& out : this->outputStreams)
        {
            if (out->getPort() == output->getPort() &&
                out->getStreamOuputType() == output->getStreamOuputType())
            {
                alreadyOutput = true;
                ai::LogInfo << "Output already pushing:  " << out->getPort();
                if (!out->isPlaying())
                {
                    out->startPlay();
                }
                return false;
            }
        }

        auto outputTarget = output->getOutputTarget();
        if(outputTarget != OutputTargetType::Common)
        {
           auto rt = std::find_if(this->outputStreams.begin(), this->outputStreams.end(),[&](const auto& s)-> bool{
                return s->getOutputTarget() == outputTarget;
            });
           if(rt != this->outputStreams.end())
           {
               (*rt)->stopPlay(true);
               this->removeOutput(*rt);
               this->outputStreams.erase(rt);
           }
        }
        auto rtFind = std::find(this->outputStreams.begin(), this->outputStreams.end(), output);
        if (rtFind ==this->outputStreams.end())
        {
            this->outputStreams.push_back(output);
            if(this->codecInfo != nullptr)
            {
                output->setCodecInfo(this->codecInfo);
            }
            output->startPlay();

            return true;
        }
        return false;
    }

    /**
     * 获取视频输入
     * @param StreamOutput
     */
    StreamInputPtr StreamPipe::getInput()
    {
        return inputStream;
    }

    /**
     * 获取视频输出
     * @param StreamOutput
     */
    StreamOutputPtr StreamPipe::getOutput(StreamOuputType outputType, int port)
    {
        StreamOutputPtr output = nullptr;
        std::lock_guard<boost::fibers::mutex> lock(mutexOutput);
        for (auto& it : outputStreams)
        {
            if (it->getStreamOuputType() == outputType)
            {
                if (port == 0 || it->getPort() == port)
                {
                    output = it;
                    break;
                }
            }
        }
        return output;
    }

    /**
     * 切换输入
     * @param input
     */
    void StreamPipe::switchInput(StreamInputPtr input)
    {
        if(this->inputStream != nullptr)
        {
            this->inputStream->onStreamDataReceived.disconnect_all_slots();
            this->inputStream->onStreamStatusChanged.disconnect_all_slots();
            this->inputStream->onStreamCodecInfoRetrieved.disconnect_all_slots();
            if(this->inputStream->isPlaying())
                this->inputStream->stopPlay();
        }

        this->inputStream = input;
        input->onStreamStatusChanged.connect([this](auto status){this->inputStreamStatusChanged(status);});
        input->onStreamDataReceived.connect([this](auto data){this->streamDataReceived(data);});
        input->onStreamCodecInfoRetrieved.connect([this](auto codec){this->streamCodecInfoRetrieved(codec);});
    }

    void StreamPipe::removeOutput(StreamOutputPtr output)
    {
        if(output->isPlaying())
        {
            output->stopPlay(true);
        }
    }

    /**
     * 开始管道
     */
    void StreamPipe::startPipe()
    {
        mutexInput.lock();
        if(this->inputStream && !this->inputStream->isPlaying())
        {
            this->inputStream->startPlay();
        }
        mutexInput.unlock();

        mutexOutput.lock();
        for (auto& outputStream: outputStreams)
        {
            if(outputStream && !outputStream->isPlaying())
                outputStream->startPlay();
        }
        mutexOutput.unlock();
    }

    /**
     * 停止管道
     */
    void StreamPipe::stopPipe(bool waitFinish, bool dispose)
    {
        auto input = this->inputStream;
        auto outputs = this->outputStreams;
        onTimeProgramsTimerExpired.disconnect_all_slots();
        std::vector<std::shared_ptr<StreamElement>> elements;
        if(input)
        {
            elements.push_back(input);
            input->stopPlay(dispose);
            input->notifyCheckPTZPresetThread();
        }
        for (auto& output: outputs)
        {
            if(output)
            {
                elements.push_back(output);
                output->stopPlay(dispose);
            }
        }

        if(waitFinish)
        {
            for(auto& e: elements)
            {
                e->waitForFinished();
            }
        }
    }

    /**
     * 收到输入流状态变化回调
     * @param status
     */
    void StreamPipe::inputStreamStatusChanged(StreamElementStatus status)
    {
        if(status == StreamElementStatus::Paused)
        {
            mutexInput.lock();
            if(!scheduledInputStreams.empty())
            {
                auto inputStream = scheduledInputStreams.front();
                scheduledInputStreams.pop_front();

                this->switchInput(inputStream);
                this->inputStream->startPlay();
            }
            mutexInput.unlock();
        }
    }

    /**
     * 收到视频流数据包
     * @param packet
     */
    void StreamPipe::streamDataReceived(PacketDataPtr packet)
    {
        std::lock_guard<boost::fibers::mutex> lock(mutexOutput);
        for (auto &output : this->outputStreams)
        {
            if(output)
                output->pushData(packet);
        }
    }

    /**
     * 收到视频流信息
     * @param codecInfo
     */
    void StreamPipe::streamCodecInfoRetrieved(CodecInfoPtr codec)
    {
        std::lock_guard<boost::fibers::mutex> lock(mutexOutput);
        this->codecInfo = codec;
        for (auto &output : this->outputStreams)
        {
            output->setCodecInfo(codec);
        }
    }

    /**
     * 获取通道流状态
     * @param detailed 是否显示详细
     */
    std::string StreamPipe::getStreamStatus(bool detailed)
    {
        std::string retStr;
        int frameRate(0), width(0), height(0);
        std::string elementStatus, inputLog, outputLog, videoStatus;
        int allErrorCount(0);
        //input
        {
            std::lock_guard<boost::fibers::mutex> lock(mutexInput);
            if (nullptr != inputStream)
            {
                videoStatus = "[" + std::to_string(inputStream->getVideoSource()->videoSourcePtr->getId()) + "] ";
                elementStatus = "Element: " + inputStream->getStatusString();
                if (!inputStream->getLastError().empty())
                    inputLog = "inputLog: " + inputStream->getLastError() + "\n";
                allErrorCount += inputStream->getErrorCount();
            }
        }
        //Output
        {
            std::lock_guard<boost::fibers::mutex> lock(mutexOutput);
            if (codecInfo)
            {
                auto [num, den] = codecInfo->getFrameRate();
                if (0 != den)
                    frameRate = (float)num / (float)den;
                width = codecInfo->getWidth();
                height = codecInfo->getHeight();
            }
            for (auto& it : outputStreams)
            {
                if (!it->getLastError().empty())
                {
                    outputLog += it->getAddress() + "|" + it->getLastError() + "\n";
                    allErrorCount += it->getErrorCount();
                }
            }
        }

        //是否显示详细
        if (detailed)
            retStr = str(boost::format("%sFPS: %d Size: %dx%d %s\n  %s  %s") % videoStatus % frameRate % width % height % elementStatus % inputLog % outputLog);
        else 
            retStr = str(boost::format("%sFPS: %d Size: %dx%d %s ErrorNum: %d") % videoStatus % frameRate % width % height % elementStatus % allErrorCount);
        return retStr;
    }
    /**
     * 调用当前视频的预置位
     */
    void StreamPipe::asyncCallCameraPreset(int presetId, int actPresetId) {
        if ( !inputStream || !inputStream->isPtzCapable() )
            return;
        inputStream->asyncCallCameraPreset( presetId, actPresetId, false );
    }
    /**
     * 获取当前视频的id
     */
    int StreamPipe::getInputVideoId() {
        if ( !inputStream || !inputStream->getVideoSource() || !inputStream->getVideoSource()->videoSourcePtr )
            return 0;
        return inputStream->getVideoSource()->videoSourcePtr->getId();
    }
    /**
     * 获取当前视频的前端id
     */
    int StreamPipe::getInputVideoServerId() {
        if (!inputStream || !inputStream->getVideoSource() || !inputStream->getVideoSource()->videoSourcePtr)
            return 0;
        return inputStream->getVideoSource()->videoSourcePtr->getVideoServerId();
    }
    /**
     * 根据输出类型删除视频输出
     * @param StreamOutput
     */
    void StreamPipe::removeOutputTarget(OutputTargetType outputTarget) {
        std::lock_guard<boost::fibers::mutex> lock(mutexOutput);
        auto rt = std::find_if(this->outputStreams.begin(), this->outputStreams.end(),[&](const auto& s)-> bool{
            return s->getOutputTarget() == outputTarget;
        });
        if(rt != this->outputStreams.end())
        {
            this->removeOutput(*rt);
            this->outputStreams.erase(rt);
        }
    }

    /**
     * 往数据库中写预置位对应的基准坐标值
     */
    bool StreamPipe::writePresetPosition(int presetId, PresetPosition& position)
    {
        if ( !inputStream || !inputStream->getVideoSource() || !inputStream->getVideoSource()->videoSourcePtr )
            return false;
        auto videoId = (int)inputStream->getVideoSource()->videoSourcePtr->getId();
        double x = 0,y = 0,z = 0;
        if (!inputStream->getPtzPosition(x,y,z, std::nullopt))
        {
            ai::LogError << "get position failed, videoId " << videoId;
            return false;
        }
        position = {x,y,z};
        return DATA_MANAGER.writePresetPosition(videoId, presetId, x, y, z);
    }
}