/**
 * Project FVM
 */

#include "file_puller.h"
#include "boost/asio/time_traits.hpp"  //TODO
#include "util/config/fvm_config.h"

#ifdef __cplusplus
extern "C"
{
#endif
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/time.h>
#ifdef __cplusplus
}
#endif

/**
 * FilePuller implementation
 * 文件流输入
 *
 * TODO 1、支持MP4等其他容器格式
 */
namespace fvm::stream
{
    using namespace data;

    // 最大p帧大小限制
    auto constexpr MAX_P_FRAME_SIZE = 512 * 1024;

    /*
     * 文件流 处理逻辑
     */
    void FilePuller::process()
    {
        address = videoSourceInfo->videoSourcePtr->getAddress();
        //暂时先去掉按块读文件流发送的功能
        size_t pos = address.find("#");
        if ( pos != std::string::npos )
        {
            address = address.substr( 0, pos );
        }
        auto filePath = address.c_str();
        printInfo(str(boost::format("Start reading file %s") % filePath));
        // 初始化参数
        int ret;
        boost::posix_time::ptime tmBegin, tmNow;
        frameIndex = 0;

        while (jobIsRunning())
        {
            if (!isOpened)
            {
                if (access(filePath, 0) != 0)
                {
                    printInfo(str(boost::format("File not exist %s") % filePath));
                    setLastError(str(boost::format("File does not exist: %s") % filePath));
                    WAIT_FOR_SECONDS(10)
                    continue;
                }
                if (open())
                {
                    frameIndex = 0;
                    tmBegin = boost::posix_time::microsec_clock::local_time();
                }
                else
                {
                    printInfo(str(boost::format("open file failed %s") % filePath));
                    close();
                    WAIT_FOR_SECONDS(3)
                    continue;
                }
            }
            AVPacket* pkt = av_packet_alloc();
            resetContextTimer();
            ret = av_read_frame(formatCtx, pkt);
            //处理结束时 回到文件头
            if (ret == AVERROR_EOF)
            {
                av_packet_unref(pkt);
                av_packet_free(&pkt);

                //文件结束判断不循环跳出
                if (!SETTINGS->fileRepeat())
                {
                    onStreamInputLost();  //反馈状态 停止
                    break;
                }

                ret = av_seek_frame(formatCtx, this->videoIndex, 0, AVSEEK_FLAG_BYTE | AVSEEK_FLAG_BACKWARD);
                if (ret < 0)
                {
                    close();
                }
                continue;
            }

            uint32_t szPacket = pkt->size;
            if (!(pkt->flags & AV_PKT_FLAG_KEY) && szPacket > MAX_P_FRAME_SIZE)
            {
                setLastError(str(boost::format("Packet exceed max p frame size: %s") % filePath));
                av_packet_unref(pkt);
                av_packet_free(&pkt);
                continue;
            }

            if (pkt->stream_index != this->videoIndex) 
            {
                av_packet_unref(pkt);
                av_packet_free(&pkt);
                continue;
            } else
            {
                auto packet = std::make_shared<PacketData>(pkt);
                this->onStreamDataReceived(packet);
            }
            //下面处理wait
            frameIndex++;
            tmNow = boost::posix_time::microsec_clock::local_time();
            int actTime = (tmNow - tmBegin).total_milliseconds();
            int needTime = frameIndex * duration;
            if (needTime > actTime)
                boost::this_fiber::sleep_for(std::chrono::milliseconds(needTime - actTime));
            else
                boost::this_fiber::yield();
        }
        close();
        printInfo(str(boost::format("EXIT %s") % filePath));
    }
}