/**
 * Project FVM
 */
#include "network.h"
#include "protocol.hpp"
#include "ailog.h"
#include "data/data_manager.h"

namespace fvm::protocol
{
    /*
     * 发送消息至IVA
     * @param msgType 消息类型
     * @param data 消息内容
     * @param processID 进程ID
     */
    template<typename T>
    bool ProtocolManager::sendToIVA(ProtocolType msgType, const T &data, int processID)
    {
        auto msg  = network::generateProtocol<T>(data, msgType, network::SessionType::FVM);
        auto returnVal = network::sendDataTo(network::SessionType::IVA, msg, processID);

        if(!returnVal && network::lastErrorCode())
        {
             ai::LogInfo << "IVA msg sent failed! protocol: " << getProtocolName(msgType) << " process: " << processID
                        << " code: " << network::lastErrorCode() <<" err: " << network::lastErrorMsg();
        }
        else if ( returnVal )
        {
            ai::LogInfo << "To IVA " << processID << " " << msg;
        }
        return returnVal;
    };

    /*
     * 发送消息至WEB
     * @param msgType 消息类型
     * @param data 消息内容
     * @param isPlatform 是否平台
     */
    template<typename T>
    bool ProtocolManager::sendToWEB(ProtocolType msgType, const T &data, bool isPlatform)
    {
        auto msg  = network::serialize<T>(data);
        int targetID = isPlatform ? WEB_PLATFORM: WEB_LOCAL;
        assert(network::webAPIs.find(msgType) != network::webAPIs.end());

        auto api = network::webAPIs[msgType];
        auto returnVal = network::sendDataTo(network::SessionType::WEB, msg, targetID, api);

        if(!returnVal && network::lastErrorCode())
        {
            ai::LogInfo << "WEB msg sent failed! protocol: " << getProtocolName(msgType) << " isPlatform: " << isPlatform
                         << " code: " << network::lastErrorCode() <<" err: " << network::lastErrorMsg();
        }
        else if ( returnVal && msgType != ProtocolType::HTTP_HEART_INFO && msgType != ProtocolType::HTTP_STREAM_STATUS )
        {
            ai::LogInfo << "To WEB " << (isPlatform?"Plat ":"Local ") << api << " " << msg;
        }
        return returnVal;
    };

    /*
     * 发送消息至GUM
     * @param msgType 消息类型
     * @param data 消息内容
     */
    template<typename T>
    bool ProtocolManager::sendToGUM(ProtocolType msgType, const T &data)
    {
        auto msg  = network::generateProtocol<T>(data, msgType, network::SessionType::FVM);
        auto returnVal = network::sendDataTo(network::SessionType::GUM, msg);

        if(!returnVal && network::lastErrorCode())
        {
            ai::LogInfo << "GUM msg sent failed! protocol: " << getProtocolName(msgType)
                         << " code: " << network::lastErrorCode() <<" err: " << network::lastErrorMsg();
        }
        return returnVal;
    };

    /*
     * 发送消息至MSM
     * @param msgType 消息类型
     * @param data 消息内容
     */
    template<typename T>
    bool ProtocolManager::sendToMSM(ProtocolType msgType, const T &data)
    {
        auto msg  = network::generateProtocol<T>(data, msgType, network::SessionType::FVM);
        auto returnVal = network::sendDataTo(network::SessionType::MSM, msg);

        if(!returnVal && network::lastErrorCode())
        {
            ai::LogInfo << "MSM msg sent failed! protocol: " << getProtocolName(msgType)
                         << " code: " << network::lastErrorCode() <<" err: " << network::lastErrorMsg();
        }
        return returnVal;
    };

    /*
    * 发送数据 （默认UDP）
    * @param host 目标地址
    * @param port 目标端口
    * @param msgType 消息类型
    * @param data 消息内容
    */
    template<typename T>
    bool ProtocolManager::sendTo(const std::string& host, unsigned short port, ProtocolType msgType, const T& data)
    {
        auto msg  = network::generateProtocol<T>(data, msgType, network::SessionType::FVM);
        auto returnVal = network::sendDataTo(host, port, msg);

        if(!returnVal && network::lastErrorCode())
        {
            ai::LogInfo << host << ":" << port <<" msg sent failed! protocol: " << getProtocolName(msgType)
                        << " code: " << network::lastErrorCode() <<" err: " << network::lastErrorMsg();
        }
        else
        {
            if ( port == DATA_MANAGER.getMsmPort() )
                ai::LogInfo << "To MSM " << msg;
            else if ( port == DATA_MANAGER.getGumPort() )
                ai::LogInfo << "To GUM " << msg;
            else
                ai::LogInfo << "To " << host << ":" << port <<" " << msg;
        }
        return returnVal;
    }
}
