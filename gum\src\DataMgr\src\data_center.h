//
// Created by yp on 2021/11/9.
//

#ifndef GUM_DATA_CENTER_H
#define GUM_DATA_CENTER_H

#include <boost/serialization/singleton.hpp>
#include "UsgManager/include/UsgManagerExp.hpp"
#include "include/basic_define.hpp"
#include "include/file_info.hpp"
#include "network.h"
#include "protocol/gum.h"
#include "gb_plat.h"
#include <mutex>

namespace gum {

    using namespace network;

    class DataCenter : public boost::noncopyable {
    public:
        DataCenter();

    public:
        bool init( const std::string& ip, const std::string& db );
        bool fini();
        void showStatus( int remoteId, const std::string& szKey );
        int getListenPort();

        int onRequestVideo(const RequestVideo& msg, std::string& szSrc, std::string& szError );
        int onRequestStopVideo(const RequestStopVideo& msg, std::string& szError );
        int onRequestPtzOper(const RequestPtzOper& msg);
        bool onRemoteChanged(int remoteId);
        bool onFvmChanged();

    private:
        //初始化sip管理器
        bool initUsgMgr();
        //初始化数据库
        bool initDb(const std::string &ip, const std::string &db);
        //获得28181的access类型
        bool initAccessType();
        //初始化参数
        bool initParam();
        //初始化前端
        bool initRemotes();
        //初始化所有视频
        bool initVideos();
        //初始化所有监测仪
        bool initMonitors();

        //插入新视频
        bool insertVideo(uint32_t remoteId, const std::string& szAddr, const std::string& szName, uint32_t& newId);
        //修改视频名称
        bool updateVideoName(uint32_t remoteId, const std::string& szKey, const std::string& szNewName );
        //修改远端状态
        bool updateRemoteStatus(uint32_t remoteId, bool status);

        //从视频名称中得到K开头的桩号
        std::string findLocation(const std::string& szName);
        
        GbPlatPtr getPlat(int id);

        GbPlatPtr createPlat(int id, const std::string& sipid, const std::string& ip, int port);

        bool checkMonitor(const std::string& szAddr);
    private:
        int m_iListenPort;
        int m_iAccessType;              //前端类型ID
        bool m_bUseFrontName;
        std::string m_sFvmAddr;         //平台FVM地址端口

        std::mutex m_mutex;
        std::map<int, GbPlatPtr > m_mapPlat;
        usg::CSpIGb28181 m_usgManager;

        std::mutex m_monitorMutex;
        std::vector<std::string> m_vecMonitor;  //保留一个当前系统中的监测仪信息，不是这个监测仪发来的就不处理
    };

    typedef boost::serialization::singleton<DataCenter> SingletonDataCenter;
    #define DATA_CENTER SingletonDataCenter::get_mutable_instance()
}  //namespace gum

#endif //GUM_DATA_CENTER_H
