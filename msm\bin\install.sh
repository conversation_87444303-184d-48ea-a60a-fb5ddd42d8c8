#!/bin/bash
BINDIR="/usr/sbin"
STARTUP="/etc/init.d"
CONFIGDIR="/usr/etc"
LOCALDIR=`pwd`
curPath=${PWD##*/}
svName=${curPath:0:3}
RUNFILE=$svName"Run.sh"
echo $svName

# create RUN<PERSON>LE and copy to BINDIR
echo "#!/bin/sh" > $RUNFILE
echo "cd $LOCALDIR/bin/" >> $RUNFILE
echo "" >> $RUNFILE
echo "ulimit -s 1024" >> $RUNFILE
echo "$LOCALDIR/bin/"$svName"D --load=$LOCALDIR/config/load.js --init=$LOCALDIR/config/init.js --port=5200" >> $RUNFILE

chmod +x $svName"Run.sh"
echo "wtoe&8746" | sudo -S /bin/mv $svName"Run.sh" $BINDIR

#start service
chmod +x ./bin/$svName
echo "wtoe&8746" | sudo -S /bin/cp -a ./bin/$svName $STARTUP
echo "wtoe&8746" | sudo -S chmod 755 $STARTUP/$svName
echo "wtoe&8746" | sudo -S systemctl enable $svName.service

SYSNAME=`cat /proc/version | grep Ubuntu`
if [ -z "$SYSNAME" ]
then
    echo "CentOS"
    echo "wtoe&8746" | sudo -S chkconfig --del $svName
    echo "wtoe&8746" | sudo -S chkconfig --add $svName
    echo "wtoe&8746" | sudo -S chkconfig $svName on
else
    echo "Ubuntu"
    echo "wtoe&8746" | sudo -S update-rc.d -f $svName remove
    echo "wtoe&8746" | sudo -S update-rc.d $svName defaults  90 10
fi

