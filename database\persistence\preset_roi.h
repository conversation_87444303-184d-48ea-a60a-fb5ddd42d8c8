/**
 * @addtogroup odbDatabaseGroup
 * @brief 预置位对应的感兴趣区信息
 * @{
 */
#ifndef _PRESETROI_H
#define _PRESETROI_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>


namespace db {

/**
 * @brief  预置位对应的感兴趣区信息 对应数据库aimonitorV3的表wn_preset_roi
 */
#pragma db object table("wn_preset_roi")
class PresetRoi {
public: 

    PresetRoi(unsigned long presetId,
        const std::string& checkArea,
        const std::string& direction,
        unsigned long logicId,
        const std::string& speedArea,
        const std::string& speedPoint1,
        const std::string& speedPoint2,
        int speedLineLen,
        const std::string&  countLine,
        const std::string& eventProperty,
        const std::string& labelProperty,
        unsigned long paramPlanId,
        const std::string& param,
        bool isDel,
        bool isEnable

    )
        : presetId(presetId), checkArea(checkArea), direction(direction),
        logicId(logicId), speedArea(speedArea), speedPoint1(speedPoint1),
        speedPoint2(speedPoint2), speedLineLen(speedLineLen), countLine(countLine),eventProperty(eventProperty),
        labelProperty(labelProperty), paramPlanId(paramPlanId), param(param),
        isDel(isDel), isEnable(isEnable)
    {
    }


    unsigned long getId() const {
        return id;
    }

    unsigned long getPresetId()const {
        return presetId;
    }

    void setPresetId(unsigned long id) {
        this->presetId = id;
    }

    const std::string& getCheckArea() const {
        return checkArea;
    }

    void setCheckArea(const std::string& are) {
        this->checkArea = are;
    }

    const std::string& getDirection() const {
        return direction;
    }

    void setDirection(const std::string& dir) {
        this->direction = dir;
    }

    unsigned long getLogicId()const {
        return logicId;
    }

    void setLogicId(unsigned long id) {
        this->logicId = id;
    }

    const std::string& getSpeedArea() const {
        return speedArea;
    }

    void setSpeedArea(const std::string& are) {
        this->speedArea = are;
    }

    const std::string& getSpeedPoint1() const {
        return speedPoint1;
    }

    void setSpeedPoint1(const std::string& point) {
        this->speedPoint1 = point;
    }
    
    const std::string& getSpeedPoint2() const {
        return speedPoint2;
    }

    void setSpeedPoint2(const std::string& point) {
        this->speedPoint2 = point;
    }

    int getSpeedLineLen()const {
        return speedLineLen;
    }

    void setSpeedLineLen(int len) {
        this->speedLineLen = len;
    }


    const std::string& getEventProperty() const {
        return eventProperty;
    }

    void setEventProperty(const std::string& property) {
        this->eventProperty = property;
    }

    const std::string& getCountLine() const {
        return countLine;
    }

    void setCountLine(const std::string& line) {
        this->countLine = line;
    }
    
    const std::string& getLabelProperty() const {
        return labelProperty;
    }

    void setLabelProperty(const std::string& property) {
        this->labelProperty = property;
    }

    unsigned long getParamPlanId()const {
        return paramPlanId;
    }

    void setParamPlanId(unsigned long id) {
        this->paramPlanId = id;
    }

    const std::string& getParam() const {
        return param;
    }

    void setParam(const std::string& param) {
        this->param = param;
    }
    

    bool getIsDel() const {
        return isDel;
    }

    void setIsDel(bool del) {
        this->isDel = del;
    }

    bool getIsEnable() const {
        return isEnable;
    }

    void setIsEnable(bool enable) {
        this->isEnable = enable;
    }


private:

    friend class odb::access;
    PresetRoi() {}

private: 
#pragma db id auto
    unsigned long id;                    //!< 表ID

#pragma db column("preset_id")  
    unsigned long presetId;              //!< 预置位id 对应表wn_preset(preset) id TODO:


#pragma db column("check_area") type("VARCHAR(255)")
    std::string checkArea;              //!< roi检测区

#pragma db column("direction") type("VARCHAR(255)")
    std::string direction;              //!< roi行驶方向

#pragma db column("logic_id")
    unsigned long logicId;              //!< 感兴趣区道路方向ID eg:武汉方向 十堰方向

#pragma db column("p0") type("VARCHAR(255)")
    std::string speedArea;              //!< 速度平面变化区域

#pragma db column("p1") type("VARCHAR(255)")
    std::string speedPoint1;            //!< 计算速度的点1 真实距离与Point1-Point2之间像素距离的比例映射 

#pragma db column("p2") type("VARCHAR(255)")
    std::string speedPoint2;            //!< 计算速度的点2 真实距离与Point1-Point2之间像素距离的比例映射 

#pragma db column("l12")                //!< Point1-Point2对应的真实距离
    int speedLineLen;

#pragma db column("count_line") type("VARCHAR(255)")
    std::string countLine;

#pragma db column("event_property") type("VARCHAR(255)")
    std::string eventProperty;          //!< 事件属性值

#pragma db column("label_property") type("VARCHAR(255)")
    std::string labelProperty;          //!< 目标标签属性值

#pragma db column("param_plan_id") 
    unsigned long paramPlanId;          //!< 灵敏度预案 对应表wn_alg_param_plan(AlgParamPlan) id 

#pragma db column("param") 
    std::string param;                  //!< 敏度参数参数 用户web配置后下发到数据库,不配置则使用iva默认灵敏度参数

#pragma db column("is_del") type("INT")                  
    bool isDel;                         //!< 是否删除

#pragma db column("is_enable") type("INT")   
    bool isEnable;                      //!< 是否使能
};
}
#endif //_PRESETROI_H

/**
 * @}
 */