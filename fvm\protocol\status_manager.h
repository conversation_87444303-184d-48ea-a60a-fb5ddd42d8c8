/**
 * Project FVM
 */
#pragma once

/*
 * 状态管理模块 （流状态，心跳保活等）
 * TODO 待完善
 */
namespace fvm::protocol
{
    /**
     * @brief     初始化状态管理
     */
    void initStatusManager();

    /**
     * @brief      清除所有已分配通道的流状态
     * @note       程序在异常退出时，且通道分配发生了变化，可能导致之前的通道的状态还存在，但该通道已经没有分配了，所以需要清楚掉进程。
     */
    void clearAllStreamStatus();

    /**
     * @brief    通知web更新流状态暂停
     * @param[in]  channelId:通道号
     * @param[in] videoId:视频号
     */
    void pauseStreamStatus(int channelId, int videoId=0);

    /**
     * @brief      通知web更新流状态恢复
     * @param[in]  channelId:通道号
     * @param[in]  videoId:视频ID
     */
    void restoreStreamStatus(int channelId,  int videoId);


    /**
     * @brief  停止状态管理器，销毁资源
     */
    void disposeStatusManager();
}
