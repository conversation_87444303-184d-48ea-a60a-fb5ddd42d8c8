#ifndef FILE_INFO_HPP_
#define FILE_INFO_HPP_

__attribute__((unused)) static std::string int2string( int iValue )
{
    char tmp[64];
#if defined( WIN32 )
    sprintf_s( tmp, "%u", iValue );
#else
    snprintf( tmp, sizeof( tmp ), "%u", iValue );
#endif
    return std::string( tmp );
}

__attribute__((unused)) static std::string trim( char* buf )
{
    std::string tmp( buf );
    if ( tmp.find_last_of( '\r' ) != std::string::npos )
        tmp.erase( tmp.find_last_of( '\r' ) );
    if ( tmp.find_last_of( '\n' ) != std::string::npos )
        tmp.erase( tmp.find_last_of( '\n' ) );
    return tmp;
}

__attribute__((unused)) static int readFile( char* chFile, std::string szData[], int maxCount = 1 )
{
    FILE* fp = 0;
#if defined (WIN32)
    fopen_s(&fp, chFile, "r" );
#else
    fp = fopen( chFile, "r" );
#endif
    if ( fp == NULL )
        return 0;
    char buf[100];
    int count = 0;
    while ( fgets( buf, 100, fp ) )
    {
        std::string tmp = trim( buf  );
        szData[count] = tmp;
        count ++;
        if ( count >= maxCount )
            break;
    }
    fclose( fp );
    fp = NULL;
    return count;
}

__attribute__((unused)) static bool writeLineFile( char* chFile, std::string szData[], int lines )
{
    std::string szTxt;
    for ( int i = 0; i < lines; i++ )
    {
        szTxt += szData[i] + "\n";
    }
    FILE* fp = 0;
#if defined (WIN32)
    fopen_s(&fp, chFile, "w" );
#else
    fp = fopen( chFile, "w" );
#endif
    if ( fp != NULL )
    {
        fputs( szTxt.c_str(), fp );
        fclose( fp );
        return true;
    }
    return false;
}

__attribute__((unused)) static bool getIniInfo( char* chFile, std::map<std::string, std::string>&mapParam )
{
    std::string szFileData[100];
    int count = readFile( chFile, szFileData, 100 );
    if ( count == 0 )
        return false;
    for ( int i = 0; i < count; i++ )
    {
        std::string ss = szFileData[i];
        int pos = ss.find( '/' );
        if ( pos > 0 )
            ss = ss.substr( 0, pos );
        pos = ss.find( ' ' );  //去除末尾的空格
        if ( pos > 0 )
            ss = ss.substr( 0, pos );
        pos = ss.find( '=' );
        if ( pos < 0 )
            continue;
        std::string sLeft = ss.substr( 0, pos );
        std::string sRight = ss.substr( pos+1, ss.length() );
        mapParam[sLeft] = sRight;
    }
    return ( mapParam.size() > 0 );
}

__attribute__((unused)) static int stringSplit( const std::string& szSrc, std::vector<std::string>& vecDest, char chSplit =',' )
{
    if ( szSrc.empty() )
        return 0;
    std::string tmp = trim( (char*)( szSrc.c_str() ) );
    int count = 0;
    size_t pos = tmp.find( chSplit );
    while ( pos != std::string::npos )
    {
        if ( pos == std::string::npos )
            break;
        vecDest.push_back( tmp.substr( 0, pos ) );
        tmp = tmp.substr( pos+1, tmp.length() );
        count ++;
        pos = tmp.find( chSplit );
    }
    if ( !tmp.empty() )
    {
        vecDest.push_back( tmp );
        count ++;
    }
    return count;
}

__attribute__((unused)) static std::string getParamValue( std::map<std::string, std::string> mapParam, const std::string& szKey, const std::string& szDef = "" )
{
    if ( mapParam.find( szKey ) == mapParam.end() )
        return szDef;
    return mapParam[szKey];
}

#if defined ( WIN32 )
static int scsIconv( const std::string &strSou, std::string &strDest)
{
	strDest = strSou;
	return 0;
}
#elif defined ( Linux )
#include <iconv.h>
#define TRANS_OUT_BUF_DEF_SIZE 255
__attribute__((unused)) static int scsIconv( const std::string &strSou, std::string &strDest)
{
	strDest = strSou;
#if 0
	// 初始化转换函数
	iconv_t hIconv =
		iconv_open("utf8", "gb2312" );//g_strDestCharSet.c_str(), g_strSouCharSet.c_str());
	if (hIconv == (iconv_t)-1)
	{
		return -1;
	}

	// 获取源数据
	size_t nSouSize = strSou.size();
	char *pcInBuf = (char*)malloc(strSou.size() + 1);
	memset(pcInBuf, 0, strSou.size() + 1);
	memcpy(pcInBuf, strSou.c_str(), strSou.size());
	char *pcIn = pcInBuf;

	// 开辟目的存储空间
	size_t nDestSize = TRANS_OUT_BUF_DEF_SIZE > 2 * nSouSize ?  TRANS_OUT_BUF_DEF_SIZE : 2 * nSouSize;
	char *pcOutBuf = (char*)malloc(nDestSize);
	if (!pcOutBuf)
	{
		free(pcInBuf);
		return -1;
	}
	char *pcOut = pcOutBuf;

	// 开始转换
	size_t nRet;
	do
	{
		memset(pcOutBuf, 0, nDestSize);
		nRet = iconv(hIconv, &pcIn, &nSouSize, &pcOut, &nDestSize);
		if (nRet == (size_t)-1)
		{
			if (errno == E2BIG)
			{
				pcOutBuf = (char*)realloc(pcOutBuf, 2 * nDestSize);
				if (!pcOutBuf)
				{
					free(pcInBuf);
					return -1;
				}
				pcIn = pcInBuf;
				pcOut = pcOutBuf;

				nDestSize = 2 * nDestSize;
			}
			else
			{
				free(pcInBuf);
				return -1;
			}
		}
		else
		{
			strDest = pcOutBuf;
			break;
		}
	}while (1);

	free(pcInBuf);
	free(pcOutBuf);
#endif
	return 0;
}
#endif

#endif // FILE_INFO_HPP_