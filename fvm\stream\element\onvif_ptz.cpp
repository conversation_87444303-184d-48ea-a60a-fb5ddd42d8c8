//
// Created by yp on 2022/1/11.
//
#include "onvif_ptz.h"
#include "data/data_manager.h"
#include "data/config_param_info.h"
#include "ailog.h"
#include <thread>

namespace fvm {
    using namespace std::literals::chrono_literals;

    OnvifPtz::OnvifPtz(const VideoSourceInfoPtr source )
    {
        //首先分析特定的云台配置参数，若没有，则用地址分析
        if ( !analysis( source->videoSourcePtr->getPtzControl() ) )
            analysis( source->videoSourcePtr->getAddress() );
        getOnvifInfo( source->videoSourcePtr->getHasPtz() );
    }

    //sInfo格式：从video_source的ptz_control字段来：用户名:密码@IP:0， :0表示云台不可转动，不填或:1表示可通过onvif转动
    //           从video_source的addr（主要是rtsp地址来）：rtsp://用户名:密码@IP:端口/XXX，则肯定可通过onvif转动，去掉//.../之外的字符再分析
    bool OnvifPtz::analysis(const std::string &sInfo) {
        if (sInfo.empty())
            return false;

        std::string sTmp = sInfo;
        //去掉末尾的#u
        size_t len = sTmp.length();
        if ( len > 2 && sTmp.substr( len - 2, 2 ) == "#u")
            sTmp = sTmp.substr( 0, len - 2 );
        //先去掉最前面的rts(m)p://
        size_t pos = sTmp.find("//");
        if (pos != std::string::npos) {
            sTmp = sTmp.substr(pos + 2, sTmp.length());
        }
        pos = sTmp.find('/'); //再找下一个/
        if (pos != std::string::npos) {
            sTmp = sTmp.substr(0, pos);
        }
        if (sTmp.empty())
            return false;

        //admin:pass@ip:port
        pos = sTmp.find_last_of("@");
        if ( pos == std::string::npos )  //没有用户名密码的，不处理
            return false;
        std::string s1 = sTmp.substr( 0, pos );
        std::string s2 = sTmp.substr( pos+1, sTmp.length() );
        //先处理用户名
        if ( ( pos = s1.find(":") ) == std::string::npos )
            return false;
        onvifUser = s1.substr( 0, pos );
        onvifPass = s1.substr( pos+1, s1.length() );
        if ( ( pos = s2.find(":") ) == std::string::npos ){
            onvifIp = s2;
        }
        else {
            onvifIp = s2.substr( 0, pos );
        }
        return true;
    }

    bool OnvifPtz::isPtzCapable() {
        return canPtz;
    }

    bool OnvifPtz::callPreset(int presetId) {
        if ( !canPtz )
            return true;
        ai::COnvifLib ov;
        bool bRet = ov.callPreset(ptzUrl, ptzProfile, onvifUser, onvifPass, presetId);
        if ( bRet )
            ai::LogInfo << onvifIp << " PTZ " << presetId << " is ok";
        return true;
    }

    bool OnvifPtz::ptzControl(network::EPtzCommand command, int param1, int param2) {
        if ( !canPtz )
            return true;
        ai::COnvifLib ov;
        if (command != network::EPTZCOMMAND_STOP)
            ov.ptzOper(ptzUrl, ptzProfile, onvifUser, onvifPass, (ai::EPtzCommand)command, param1, param2);
        else
            ov.ptzStop(ptzUrl, ptzProfile, onvifUser, onvifPass);
        return true;
    }

    bool OnvifPtz::getPosition(double &x, double &y, double &z) {
        if ( !canGetPosition )
            return false;
        ai::COnvifLib ov;
        return ov.getPosition(ptzUrl, ptzProfile, onvifUser, onvifPass, x, y, z, lastError );
    }

    bool OnvifPtz::getPresetPosition(int presetId, double& x, double& y, double& z) {
        if (!canGetPosition)
            return false;
        ai::COnvifLib ov;
        return ov.getPresetPosition(ptzUrl, ptzProfile, onvifUser, onvifPass, presetId, x, y, z, lastError);
    }

    bool OnvifPtz::getOnvifInfo( bool hasPtz ) {
        if ( onvifUser.empty() || onvifPass.empty() ) 
            return false;
        
        //首先判断该摄像机是否支持onvif操作
        ai::COnvifLib ov;
        if ( !ov.getDeviceInfo( onvifIp, onvifUser, onvifPass, ptzUrl, mediaUrl ) )
            return false;

        if (!ov.getPtzCapability(ptzUrl, onvifUser, onvifPass, ptzRange))
            return false;

        if (!ov.getProfile(mediaUrl, onvifUser, onvifPass, ptzProfile))
            return false;
                
        canOnvif = true;
        int offsetTime = DATA_MANAGER.getParamProgData(data::FVM_OFFSET_WAIT_TIME, 0);
        if (offsetTime > 0)  //FVM判断偏移的间隔时间，大于0，表示fvm可以获取坐标来判断是否偏移
            canGetPosition = true;
        if (hasPtz)
            canPtz = true;

        return true;
    }

}