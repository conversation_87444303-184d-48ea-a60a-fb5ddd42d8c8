/**
 * @addtogroup odbDatabaseGroup
 * @brief 灵敏度预案
 * @{
 */
#ifndef _ALGPARAMPLAN_H
#define _ALGPARAMPLAN_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
/**
 * @brief  灵敏度预案: 对应数据库aimonitorV3的表wn_alg_param_plan
 */
#pragma db object table("wn_alg_param_plan")

class AlgParamPlan {
public:

    AlgParamPlan(unsigned long planId,
                 unsigned long paramId,
                 const std::string &paramValue
    )
            : planId(planId), paramId(paramId), paramValue(paramValue) {
    }

    unsigned long getId() const {
        return id;
    }

    unsigned long getPlanId() const {
        return planId;
    }

    void setPlanId(unsigned long id) {
        this->planId = id;
    }

    unsigned long getParamIdId() const {
        return paramId;
    }

    void setParamIdId(unsigned long id) {
        this->paramId = id;
    }

    const std::string &getProgramVal() const {
        return paramValue;
    }

    void setProgramVal(const std::string &val) {
        this->paramValue = val;
    }

private:

    friend class odb::access;

    AlgParamPlan() {}


private:
#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("plan_Id")
    unsigned long planId;               //!< 灵敏度预案ID

#pragma db column("param_id")
    unsigned long paramId;              //!< 灵敏度参数ID   对应表wn_algorithm_param(AlgorithmParam) id

#pragma db column("param_value")  type("VARCHAR(255)")
    std::string paramValue;             //!< 灵敏度参数值
};
}
#endif //_ALGPARAMPLAN_H

/**
 * @}
 */