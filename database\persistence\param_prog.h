/**
 * @addtogroup odbDatabaseGroup
 * @brief 程序运行参数
 * @{
 */
#ifndef _PARAMPROG_H
#define _PARAMPROG_H


#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {

/**
 * @brief: 程序运行参数 对应数据库aimonitorV3的表monitor
 */
#pragma db object table("wn_param_prog")
class ParamProg {
public:

    ParamProg(const std::string& paramName,
        const std::string& paramKey,
        const std::string& paramValue,
        const std::string& program,
        const std::string& description,
        bool isShow,
        bool isDel
    )
        : paramName(paramName), paramKey(paramKey), paramValue(paramValue),
        program(program), description(description),
        isShow(isShow), isDel(isDel)
    {
    }


    unsigned long getId() const {
        return id;
    }

    const std::string& getParamName() const {
        return paramName;
    }

    void setParamName(const std::string& name) {
        this->paramName = name;
    }

    const std::string& getParamKey() const {
        return paramKey;
    }

    void setParamKey(const std::string& key) {
        this->paramKey = key;
    }

    const std::string& getParamValue() const {
        return paramValue;
    }

    void setParamValue(const std::string& val) {
        this->paramValue = val;
    }

    const std::string& getProgram() const {
        return program;
    }

    void setProgram(const std::string& name) {
        this->program = name;
    }

    const std::string& getDescription() const {
        return description;
    }

    void setDescription(const std::string& desc) {
        this->description = desc;
    }

    bool setIsShow() const {
        return isShow;
    }

    void getIsShow(bool show) {
        this->isShow = show;
    }

    bool setIsDel() const {
        return isDel;
    }

    void getIsDel(bool del) {
        this->isDel = del;
    }


private:

    friend class odb::access;
    ParamProg() {}

private:
#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("alias")  type("VARCHAR(255)")
    std::string paramName;              //!< 参数别名

#pragma db column("param_key")  type("VARCHAR(255)")
    std::string paramKey;               //!< 参数字段key

#pragma db column("param_value")  type("VARCHAR(255)")
    std::string paramValue;             //!< 参数值

#pragma db column("program")  type("VARCHAR(255)")
    std::string program;                //!< 参数对应的程序 ：FVM IVA WEB

#pragma db column("description")  type("VARCHAR(255)")
    std::string description;            //!< 参数描述

#pragma db column("is_show") type("INT")
    bool isShow;                        //!< web端是否显示该参数的配置

#pragma db column("is_del") type("INT")
    bool isDel;                         //!< 是否删除
};

/**
 * @brief      ParamProg数据表视图：只获取param_Key，param_Value数据
 */
#pragma db view object(ParamProg)
struct ParamProgData
{
    #pragma db column("param_key")  type("VARCHAR(255)")
    std::string paramKey;               //!< 参数字段key
    #pragma db column("param_value")  type("VARCHAR(255)")
    std::string paramValue;             //!< 参数值
};

}
#endif //_PARAMPROG_H
/**
 * @}
 */
