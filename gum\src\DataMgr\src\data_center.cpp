//
// Created by yp on 2021/11/9.
//

#include "data_center.h"
#include "database_process.h"
#include "video_server.h"
#include "video_server-odb.hxx"
#include "param_prog.h"
#include "param_prog-odb.hxx"
#include "video_source.h"
#include "video_source-odb.hxx"
#include "video_logic.h"
#include "video_logic-odb.hxx"
#include "sys_dict.h"
#include "sys_dict-odb.hxx"
#include "monitor.h"
#include "monitor-odb.hxx"
#include "ailog.h"
#include "gb_plat.h"
#include "gb_resource.h"
#include "msg_center.h"

namespace gum {
    DataCenter::DataCenter() : m_iListenPort(5100), m_bUseFrontName(false) {

    }

    bool DataCenter::init(const std::string &ip, const std::string &db) {
        ai::LogInfo << "GB/T 28181 Upper Manager begin!";

        if (!initUsgMgr())
            return false;
        if (!initDb(ip, db) )
            return false;
        if ( !initAccessType())
            return false;
        initParam();
        initRemotes();
        initVideos();
        initMonitors();
        return true;
    }

    bool DataCenter::fini() {
        //向fvm报告所有28181远端状态为0
        {
            std::lock_guard<std::mutex> lock(m_mutex);
            for (auto& r : m_mapPlat)
            {
                MSG_CENTER.postRemoteStatus(m_sFvmAddr, r.first, false);
                r.second.reset();
            }
            m_mapPlat.clear();
        }
        db::deinit();
        if (m_usgManager) {
            m_usgManager->fini();
            m_usgManager.reset();
        }
        ai::LogInfo << "GB/T 28181 Upper Manager end!";
        return true;
    }

    bool DataCenter::initUsgMgr() {
        if (!wtoe::getMainApp()->getServiceInstance(WTOE_SNAME_UsgManager_UsgManager, m_usgManager))
            return false;

        m_usgManager->init(boost::bind(&DataCenter::insertVideo, this, _1, _2, _3, _4),
                           boost::bind(&DataCenter::updateRemoteStatus, this, _1, _2),
                           boost::bind(&DataCenter::updateVideoName, this, _1, _2, _3));

        return true;
    }

    bool DataCenter::initDb(const std::string &ip, const std::string &db) {
        //相关初始化工作
        bool ret = false;
        int errCount = 0;
        while (errCount < 10) {
            ret = db::init("root", "welltrans8746", db, ip, 3306);
            if (ret)
                break;
            errCount++;
            if (errCount >= 10) {
                ai::LogInfo << "Open Db: " << ip << ":" << db << " is failed";
                return false;
            }
            sleepMillion(1000);
        }
        ai::LogInfo << "Open Db: " << ip << ":" << db << " is ok";
        return true;
    }

    bool DataCenter::initAccessType() {
        //得到本设备的类型
        odb::query<db::SysDict> q(odb::query<db::SysDict>::dictType == "access_type" && odb::query<db::SysDict>::dictCodeCnValue=="GB/T 28181");
        std::vector<db::SysDict> vecResult = {};
        db::queryData(vecResult, q);
        if (vecResult.empty()){
            ai::LogError << "No Type with GB/T 28181";
            return false;
        }
        m_iAccessType = vecResult[0].getDictCode();
        return true;
    }

    bool DataCenter::initParam() {
        // 查寻数据库表wn_video_source中所有记录，并打印所有记录的address字段
        odb::query<db::ParamProg> q;
        std::vector<db::ParamProg> vecResult = {};

        db::queryData(vecResult, q);       
        std::string fvmIp = "127.0.0.1";
        std::string fvmPort = "5000";
        for (const auto &i : vecResult) {
            auto &key = i.getParamKey();
            auto &value = i.getParamValue();
            if (key == "GUM_port")
            {
                std::vector<std::string> vecDest;
                int num = stringSplit(value, vecDest, ':');
                if (num > 0)
                    m_iListenPort = atoi(vecDest[num - 1].c_str());
            }
            else if (key == "useFrontName")
                m_bUseFrontName = (value == "1");
            else if (key == "mainIp")
                fvmIp = value;
            else if (key == "FVM_port")
                fvmPort = value;
        }
        m_sFvmAddr = fvmIp+":"+fvmPort;
        return true;
    }

    bool DataCenter::initRemotes() {
        odb::query<db::VideoServer> q(odb::query<db::VideoServer>::accessType == m_iAccessType);
        std::vector<db::VideoServer> vecResult = {};
        db::queryData(vecResult, q);

        for ( auto &i : vecResult) {
            if ( i.getSipid().empty() )
                continue;
            createPlat(i.getId(), i.getSipid(), i.getIp(), i.getPort());
        }

        return true;
    }
    //fvm启动时发送此消息，避免在fvm没运行期间增加或删除了远端，没有通知过来
    bool DataCenter::onFvmChanged()
    {
        initMonitors();
        odb::query<db::VideoServer> q(odb::query<db::VideoServer>::accessType == m_iAccessType);
        std::vector<db::VideoServer> vecResult = {};
        db::queryData(vecResult, q);
        //先检查远端是否有被删除的               
        {
            std::lock_guard<std::mutex> lock(m_mutex);
            std::map<int, GbPlatPtr>::iterator it = m_mapPlat.begin(), itEnd = m_mapPlat.end();
            while (it != itEnd)
            {
                auto id = it->first;
                auto itV = std::find_if(vecResult.begin(), vecResult.end(), [id](db::VideoServer& videoServer) {
                    if (videoServer.getId() == id)
                        return true;
                    return false;
                    });
                if (itV == vecResult.end())
                {
                    it->second.reset();
                    it = m_mapPlat.erase(it);
                }
                else
                    it++;
            }
        }
        for (auto& i : vecResult) {
            if (i.getSipid().empty())
                continue;
            auto plat = getPlat(i.getId() );
			if (plat && !plat->match(i.getSipid(), i.getIp(), i.getPort()))
            {
                plat.reset();
                m_mapPlat.erase(i.getId());
            }
            if ( !plat )
                createPlat(i.getId(), i.getSipid(), i.getIp(), i.getPort());
        }
        {
            std::lock_guard<std::mutex> lock(m_mutex);
			for (auto& r : m_mapPlat)
			{
				MSG_CENTER.postRemoteStatus(m_sFvmAddr, r.first, r.second->isRegist() );
			}
        }
        return true;
    }

    bool DataCenter::initVideos() {
        std::lock_guard<std::mutex> lock(m_mutex);
        for (auto p : m_mapPlat)
        {
            odb::query<db::VideoSource> q(odb::query<db::VideoSource>::videoServerId == p.first);
            std::vector<db::VideoSource> vecResult = {};
            db::queryData(vecResult, q);
			for (const auto& i : vecResult) {
				p.second->addRes(i.getId(), i.getAddress(), i.getName());
			}
        }
        return true;
    }

	bool DataCenter::initMonitors()
	{
		// 查寻数据库表wn_video_source中所有记录，并打印所有记录的address字段
		odb::query<db::Monitor> q;
		std::vector<db::Monitor> vecResult = {};

		db::queryData(vecResult, q);

		std::lock_guard<std::mutex> lock(m_monitorMutex);
        m_vecMonitor.clear();
        
		for (const auto& i : vecResult) {
			auto& ip = i.getIp();
            m_vecMonitor.push_back(ip);
        }
        return true;
    }

    int DataCenter::getListenPort() {
        return m_iListenPort;
    }

    std::string DataCenter::findLocation(const std::string &szName) {
        //如果name中包含K开头，则自动获取桩号
        std::string szLocation = "";
        {
            std::vector<std::string> vecTmp;
            std::string szTmp;
            std::string szSplit = "";
            for (size_t i = 0; i < szName.length(); i++) {
                char ch = szName[i];
                std::string sch = szName.substr(i, 1);
                if (ch >= '0' && ch <= '9')
                    szTmp += sch;
                else if (ch == '+' || ch == '-') {
                    if (szSplit != "" || szTmp == "")  //出错，不处理
                    {
                        vecTmp.clear();
                        break;
                    }
                    szSplit = sch;
                    vecTmp.push_back(szTmp);
                    szTmp = "";
                } else if (ch == 'K') {
                    szTmp = "";
                    vecTmp.clear();
                }
            }
            if (vecTmp.size() == 1) {
                szLocation = "K" + vecTmp[0];
                if (szSplit != "" && szTmp != "")
                    szLocation += szSplit + szTmp.substr(0, 3);
            }
        }
        return szLocation;
    }

    bool
    DataCenter::insertVideo(uint32_t remoteId, const std::string &szAddr, const std::string &szName, uint32_t &newId) {
		GbPlatPtr plat = getPlat(remoteId);
		if (!plat) {
			ai::LogError << "Video " << szAddr << " remote " << remoteId << " is not exist";
			return false;
		}
        //先查数据库中远端下是否存在这个addr，
        odb::query<db::VideoSource> q(odb::query<db::VideoSource>::address == szAddr &&
        							  odb::query<db::VideoSource>::videoServerId == remoteId);
        std::vector<db::VideoSource> vecResult = {};
        db::queryData(vecResult, q);
        if (vecResult.size() > 0) { //若数据库中存在这个addr，更新内存中的res信息
            auto& vs = vecResult[0];
            newId = vs.getId();
            //添加资源到内存中
            plat->addRes(newId, vs.getAddress(), szName);
            return true;
        }
        //数据库中没有这个视频addr
        //先判断一下远端sipid中是否包含132，且远端下只有一个资源，若是，可能是修改了视频的sipid，不用新增
        {
			odb::query<db::VideoSource> q(odb::query<db::VideoSource>::videoServerId == remoteId);
			std::vector<db::VideoSource> vecResult = {};
			db::queryData(vecResult, q);
            if (vecResult.size() == 1) {
                auto& vs = vecResult[0];
                if (vs.getAddress().find("132") != std::string::npos)
                {
                    vs.setAddress(szAddr);
					db::updateData(vs);

                    newId = vs.getId();
					//更新内存数据
                    plat->addRes(newId, szAddr, szName);                    
                    return true;
                }
            }
        }
        //否则插入新视频
		std::string szLocation = findLocation(szName);
		db::VideoSource vs(szName, szAddr, remoteId, szLocation, true, true, 0, "", 1, 0, false, false);
		if (db::insertData(vs)==-1)
		{
			return false;
		}
		newId = vs.getId();
		ai::LogInfo << "remote: " << remoteId << " video: " << szAddr << " name: " << szName << " id: " << newId;
		//插入两个逻辑点
		std::string szVideoId = std::to_string(newId);
		db::VideoLogic logic1(szVideoId + "-Area1->2", newId, 1);
		db::insertData(logic1);
		db::VideoLogic logic2(szVideoId + "-Area2->1", newId, 2);
		db::insertData(logic2);
        plat->addRes( newId, szAddr, szName);
        return true;
    }

    bool DataCenter::updateVideoName(uint32_t remoteId, const std::string &szKey, const std::string &szNewName) {
        if (!m_bUseFrontName)
            return false;

        odb::query<db::VideoSource> q(odb::query<db::VideoSource>::address == szKey &&
        odb::query<db::VideoSource>::videoServerId == remoteId);
        std::vector<db::VideoSource> vecResult = {};
        db::queryData(vecResult, q);
        if (vecResult.empty())
            return false;

        auto& dbVideo = vecResult.at(0);
        dbVideo.setName(szNewName);
        db::updateData(dbVideo);
        ai::LogInfo << "remote: " << remoteId << " video: " << szKey << " newName: " << szNewName;
        auto plat = getPlat(remoteId);
        if (plat)
        {
            auto res = plat->getRes(dbVideo.getId());
            if (res)
                res->updateName(szNewName);
        }
        return true;
    }

    bool DataCenter::updateRemoteStatus(uint32_t remoteId, bool status) {
        GbPlatPtr plat = getPlat(remoteId);
        if (!plat)
            return false;
        plat->setStatus(status);

        MSG_CENTER.postRemoteStatus(m_sFvmAddr, remoteId, status);
        return true;
    }

    int DataCenter::onRequestVideo(const RequestVideo &msg, std::string &szSrc, std::string& szError ) {
        if (!checkMonitor(msg.returnAddr))
            return 999;
        auto plat = getPlat(msg.iRemoteId);
		if (!plat) {
			szError = "No such Remote " + std::to_string(msg.iRemoteId);
			return -1;
		}
        return plat->startPlay(msg.iVideoId, msg.transferType, msg.streamType, msg.destAddr, szSrc, szError);
    }

    int DataCenter::onRequestStopVideo(const RequestStopVideo& msg, std::string& szError )
    {
		if (!checkMonitor(msg.returnAddr))
			return 999;
		auto plat = getPlat(msg.iRemoteId);
		if (!plat) {
			szError = "No such Remote " + std::to_string(msg.iRemoteId);
			return -1;
		}
        return plat->stopPlay(msg.iVideoId, msg.streamType);
    }

    int DataCenter::onRequestPtzOper(const RequestPtzOper& msg )
    {
		if (!checkMonitor(msg.returnAddr))
			return 999;
		auto plat = getPlat(msg.iRemoteId);
		if (!plat) {
            ai::LogDebug << "No such Remote " << msg.iRemoteId;
			return -1;
		}
        return plat->ptzOper(msg.iVideoId,(usg::EPtzCommand)msg.ptzCommand, msg.arg1, msg.arg2);
    }

    bool DataCenter::onRemoteChanged( int remoteId ) {
        //先在数据库里找这个远端
		odb::query<db::VideoServer> q(odb::query<db::VideoServer>::id == remoteId && odb::query<db::VideoServer>::accessType == m_iAccessType);
		std::vector<db::VideoServer> vecResult = {};
		db::queryData(vecResult, q);
		GbPlatPtr plat = nullptr;
        if (vecResult.empty()) //远端被删除了
        {
            {
				std::lock_guard<std::mutex> lock(m_mutex);
                if (m_mapPlat.find(remoteId) == m_mapPlat.end())
                    return true;
                plat = m_mapPlat[remoteId];
                m_mapPlat.erase(remoteId);
            }
            plat.reset();
            return true;
        }
        //远端信息发生了改变

        plat = getPlat(remoteId);
        auto& vs = vecResult[0];
        if ( !plat )  //新增远端，此时视频肯定没加进来
        { 
            //还要判断一次当前远端信息是否已存在，若是，前面的要删除
            {
                std::lock_guard<std::mutex> lock(m_mutex);
                for ( auto& it: m_mapPlat )
                {
                    int tmpId = it.first;
                    auto p = it.second;
                    if (p->match( vs.getSipid(), vs.getIp(), vs.getPort() ) )
                    {
                        //看在数据库中是否还存在
						odb::query<db::VideoServer> q(odb::query<db::VideoServer>::id == p->getId() && odb::query<db::VideoServer>::accessType == m_iAccessType);
						std::vector<db::VideoServer> tmpResult = {};
						db::queryData(tmpResult, q);

                        if (tmpResult.empty())
                        {
                            m_mapPlat.erase(tmpId);
                            p.reset();
                            ai::LogInfo << "Plat " << tmpId << " IS NOT EXIST NOW, REMOVE IT";
                        }
                        break;
                    }
                }
            }
            plat = createPlat(remoteId, vs.getSipid(), vs.getIp(), vs.getPort());
            if (!plat)
                return false;
        }
        else  //修改远端
        {
            if ( !plat->match( vs.getSipid(), vs.getIp(), vs.getPort() ) )            
            {
                plat.reset();  //删除这个远端，重新创建
                plat = createPlat(remoteId, vs.getSipid(), vs.getIp(), vs.getPort());
                if (!plat)
                    return false;
            }
            //更新视频信息
			odb::query<db::VideoSource> q(odb::query<db::VideoSource>::videoServerId == remoteId);
			std::vector<db::VideoSource> videoResult = {};
			db::queryData(videoResult, q);
			for (const auto& i : videoResult) {
                plat->addRes(i.getId(), i.getAddress(), i.getName());
			}
        }
        return true;
    }

    void DataCenter::showStatus(int remoteId, const std::string& szKey) {
        std::stringstream ss;
        for (auto& m : m_mapPlat)
        {
            if ( remoteId > 0 && m.second->getId() != remoteId )
                continue;
            ss << m.second->showStatus( szKey );
        }
        std::cout << ss.str();
    }

    GbPlatPtr DataCenter::getPlat(int id)
	{
        std::lock_guard<std::mutex> lock(m_mutex);
        if (m_mapPlat.find(id) != m_mapPlat.end())
            return m_mapPlat[id];
        return nullptr;
	}

	GbPlatPtr DataCenter::createPlat(int id, const std::string& sipid, const std::string& ip, int port)
	{
		GbPlat* p = new GbPlat(id, sipid, ip, port );
		if (!p->init())
		{
			delete p;
			return nullptr;
		}
        GbPlatPtr plat;
		plat.reset(p);
        std::lock_guard<std::mutex> lock(m_mutex);
		m_mapPlat[id] = plat;
        return plat;
	}

	bool DataCenter::checkMonitor(const std::string& szAddr)
	{
        std::string szIp = szAddr;
        size_t pos = szIp.find(':');
        if (pos != std::string::npos)
            szIp = szIp.substr(0, pos);
        std::lock_guard<std::mutex> lock(m_monitorMutex);;
		auto it = std::find_if(m_vecMonitor.begin(), m_vecMonitor.end(),
			[szIp]( const std::string& ip) {
				return ip == szIp;
			});
		if (it == m_vecMonitor.end())
		{
			return false;
		}
		return true;
	}
}  //namespace gum
