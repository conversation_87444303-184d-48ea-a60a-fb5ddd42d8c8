/**
 * @file    database_process.tcc
 * @brief   database_process.h文件的模板实现文件
 * <AUTHOR>
 * @version 0.0.1
 * @date    18 Oct 2021
 */

/**
 * @addtogroup odbDatabaseGroup odbDatabase
 * @brief database_process 模板实现文件
 * @{
 */
namespace db{

    inline constexpr unsigned short MAX_RETRIES = 5;

    /**
     * @brief      获取数据库连接，如果连接断开，则重新创建连接
     */
    std::shared_ptr<odb::database> getDatabase();

    /**
     * @brief      根据条件查询数据库表或者视图
     * @param[in]  query 查询条件，T: 数据库表对应的持久化类或视图结构
     * @param[out] results: 返回的对应条件的数据库对象或者视图对象
     */
    template<typename T>
    bool queryData(std::vector<T>& results, odb::query<T>& query)
    {
        for (unsigned short retryCount (0); ; retryCount++)
        {
            auto databasePtr = getDatabase();
            if (nullptr == databasePtr)
            {
                std::cout << "The database has not been connected or failed to connect" << std::endl;
                return false;
            }
            //auto &database = databasePtr->connection()->database();  //!< 从连接池中取
            try
            {
                odb::transaction t(databasePtr->begin());
                //odb::result<T> queResult(databasePtr->query<T>(query));
                //for (const auto& data : queResult)
                for (const T &data : databasePtr->query<T>(query))
                {
                    results.emplace_back(data);
                }

                t.commit();

                return true;
            }
            catch(const odb::recoverable& e)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr << "retry limit exceeded : " << e.what() << std::endl;
                    return false;
                }
                continue;
            }
            catch (const odb::exception &e)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr << e.what() << std::endl;
                    return false;
                }
                continue;
            }
            catch (...)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr <<" caught an anonymous exception " << std::endl;
                    return false;
                }
                continue;
            }
        }
    }


    /**
     * @brief      往数据库表中新增一条记录（持久化对象)
     * @param[in]  tableObject 需要新增表对应的对象
     */
    template <typename T>
    int insertData(T& tableObject)
    {
        int retId(0);
        for (unsigned short retryCount (0); ; retryCount++)
        {
            auto databasePtr = getDatabase();
            if (nullptr == databasePtr)
            {
                std::cout << "The database has not been connected or failed to connect" << std::endl;
                return -1;
            }

            try
            {
                odb::transaction t(databasePtr->begin());

                retId = databasePtr->persist(tableObject);

                t.commit();

                return retId;
            }
            catch (const odb::recoverable& e)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr << "retry limit exceeded : " << e.what() << std::endl;
                    return -1;
                }
                continue;
            }
            catch (const odb::exception &e)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr << e.what() << std::endl;
                    return -1;
                }
                continue;
            }
            catch (...)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr <<" caught an anonymous exception " << std::endl;
                    return -1;
                }
                continue;
            }
        }
    }

    /**
     * @brief      更新数据库表中记录（持久化对象)
     * @param[in]  tableObject 需要更新的表对应的对象 T: 数据库表对应的持久化类
     * @note       使用者需捕捉odb::object_changed异常，如果数据发生变化，需重新读取该对象，然后更新对象成员
     */
    template <typename T>
    bool updateData(T& tableObject)
    {
        for (unsigned short retryCount (0); ; retryCount++)
        {
            auto databasePtr = getDatabase();
            if (nullptr == databasePtr)
            {
                std::cout << "The database has not been connected or failed to connect" << std::endl;
                return false;
            }

            try
            {
                odb::transaction t(databasePtr->begin());
                databasePtr->update(tableObject);
                t.commit();
                return true;
            }
            catch (const odb::object_changed & e)       //!< Throws if object state has changed
            {
                std::cerr << "object changed : " << e.what() << std::endl;
                throw odb::object_changed();
            }
            catch (const odb::recoverable& e)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr << "retry limit exceeded : " << e.what() << std::endl;
                    return false;
                }
                continue;
            }
            catch (const odb::exception &e)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr << e.what() << std::endl;
                    return false;
                }
                continue;
            }
            catch (...)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr <<" caught an anonymous exception " << std::endl;
                    return false;
                }
                continue;
            }
        }
    }

    /**
     * @brief      擦除数据库表中记录（持久化对象)
     * @param[in]  tableObject 需要更新的表对应的对象 T: 数据库表对应的持久化类
     */
    template <typename T>
    bool eraseData(T& tableObject)
    {
        for (unsigned short retryCount (0); ; retryCount++)
        {
            auto databasePtr = getDatabase();
            if (nullptr == databasePtr)
            {
                std::cout << "The database has not been connected or failed to connect" << std::endl;
                return false;
            }

            try
            {
                odb::transaction t(databasePtr->begin());
                databasePtr->erase(tableObject);
                t.commit();
                return true;
            }
            catch(const odb::recoverable& e)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr << "retry limit exceeded : " << e.what() << std::endl;
                    return false;
                }
                continue;
            }
            catch (const odb::exception &e)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr << e.what() << std::endl;
                    return false;
                }
                continue;
            }
            catch (...)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr <<" caught an anonymous exception " << std::endl;
                    return false;
                }
                continue;
            }
        }
    }

    /**
     * @brief      擦除给定条件的数据库表记录（持久化对象)
     * @param[in]  query 删除条件
     */
    template <typename T>
    bool eraseData(odb::query<T>& query)
    {
        for (unsigned short retryCount (0); ; retryCount++)
        {
            auto databasePtr = getDatabase();
            if (nullptr == databasePtr)
            {
                std::cout << "The database has not been connected or failed to connect" << std::endl;
                return false;
            }

            try
            {
                odb::transaction t(databasePtr->begin());
                databasePtr->erase_query<T>(query);
                t.commit();
                return true;
            }
            catch(const odb::recoverable& e)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr << "retry limit exceeded : " << e.what() << std::endl;
                    return false;
                }
                continue;
            }
            catch (const odb::exception &e)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr << e.what() << std::endl;
                    return false;
                }
                continue;
            }
            catch (...)
            {
                if (retryCount > MAX_RETRIES)
                {
                    std::cerr <<" caught an anonymous exception " << std::endl;
                    return false;
                }
                continue;
            }
        }
    }
}
/**
 * @}
 */