#ifndef USGSERVICETYPEDEFINE_HPP_
#define USGSERVICETYPEDEFINE_HPP_

#include <string>
#include <vector>
#include <boost/uuid/nil_generator.hpp>
#include "UsgManager/include/CommonTypeDefine.hpp"

namespace usg
{

    enum ESgResNotifyAction
    {
        ESGRESNOTIFYACTION_ADD,
        ESGRESNOTIFYACTION_DEL,
        ESGRESNOTIFYACTION_MODIFY,
    };

    typedef struct TagSResInfo
    {
        std::string         sipResCode;     // SIP资源标号，只有SG使用它
        uint32_t            channelIndex;   // 通道号
        boost::uuids::uuid  resId;          // 资源ID
        std::string         resName;        // 资源名
        std::string         resIP;          // 资源IP
        bool                hasPtz;          // 是否有云台
        bool                resStatus;      // 资源状态,true 在线 false离线
        EMediaImageSize     masterImageSize; //主码流视频尺寸，平台需要
        EMediaImageSize     subImageSize;    //子码流视频尺寸，平台需要
        EPackerType         packerType;      //码流打包格式，平台需要

        TagSResInfo()
        {
            sipResCode = "";
            channelIndex = -1;
            resId = boost::uuids::nil_generator()();//将Uuid设置为空,为空即全0.
            resName = "";
            resIP = "";
            hasPtz = false;
            resStatus = true;
            masterImageSize = EMEDIAIMAGESIZE_4CIF;
            subImageSize = EMEDIAIMAGESIZE_CIF;
            packerType = EPACKERTYPE_TS;
        }
        TagSResInfo( const TagSResInfo& rhs )
        {
            sipResCode = rhs.sipResCode;
            channelIndex = rhs.channelIndex;
            resId = rhs.resId;
            resName = rhs.resName;
            resIP = rhs.resIP;
            hasPtz = rhs.hasPtz;
            resStatus = rhs.resStatus;
            masterImageSize = rhs.masterImageSize;
            subImageSize = rhs.subImageSize;
            packerType = rhs.packerType;
        }

        TagSResInfo& operator = ( const TagSResInfo &rhs )
        {
            if ( &rhs != this )
            {
                sipResCode = rhs.sipResCode;
                channelIndex = rhs.channelIndex;
                resId = rhs.resId;
                resName = rhs.resName;
                resIP = rhs.resIP;
                hasPtz = rhs.hasPtz;
                resStatus = rhs.resStatus;
                masterImageSize = rhs.masterImageSize;
                subImageSize = rhs.subImageSize;
                packerType = rhs.packerType;
            }
            return *this;
        }

        bool operator < ( const TagSResInfo &rhs ) const
        {
            if ( sipResCode < rhs.sipResCode )
                return true;
            else if ( resId < rhs.resId )
                return true;
            else if( channelIndex < rhs.channelIndex )
                return true;
            return false;
        }

    } SResInfo;

    typedef std::pair< uint32_t, uint32_t > TimePeriodUnit;

    typedef struct TagAlarmInfo
    {
        std::string         sipResCode;     // SIP资源标号，只有SG使用它
        boost::uuids::uuid  resId;          // 资源ID
        int                 type;           // 报警类型
        uint32_t            time;           // 报警时间
        std::string         description;    // 报警描述
        float               longitude;      // 经度
        float               latitude;       // 纬度
    }SAlarmInfo;

    struct SItem
    {
        std::string name;
        std::string sipResCode;
        std::string operatorType;
        std::string ip;
        bool resStatus;
        bool isHD;
        bool  hasPtz;
    };
    typedef struct
    {

        std::vector< SItem > subItems;

    }SQueryCatalog;


    typedef struct
    {
        bool online;
        bool status;
        std::string resAddr;
        std::string deviceType;
        std::string manufacturer;
        std::string model;
        std::string firmware;
        uint32_t	maxCamera;
        uint32_t	maxAlarm;

    }SQueryInfo;

    typedef struct
    {
        std::string resAddr;				   // 设备ID
        bool isEncoder; 					   // 是否编码
        bool isRecord;						   // 是否录像
        bool isOnline;						   // 是否在线
        bool isNormal;						   // 是否正常工作
        std::string strFaultReason; 		   // 故障原因
    }SQueryStatus;

} // usg

#endif // SSGTYPEDEFINE_HPP_
