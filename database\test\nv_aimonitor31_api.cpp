/**
 * @file    nv_aimonitor31_join.h
 * @brief   基于ODB的nvidia检测仪aimonitor v3.1的数据库联合操作api
 *          用户根据表的关联性自行添加接口
 * <AUTHOR>
 * @version 0.0.1
 * @date    18 Oct 2021
 */

#include <iostream>
#include <string>
#include <odb/core.hxx>
#include <memory>     // std::auto_ptr

#include <odb/database.hxx>
#include <odb/transaction.hxx>
#include <odb/mysql/database.hxx>
#include "monitor.h"
#include "detection_point.h"
#include "program_info.h"
#include "preset.h"
#include "program.h"
#include "algorithm_param.h"
#include "alg_param_plan.h"
#include "nv_aimonitor31_view.h"
#include "nv_aimonitor31_api.h"
#include "../database_process.h"

/**
 * @brief: 基于ODB的nvidia检测仪aimonitor v3.1的数据库联合操作api
 */
namespace db {

	using namespace std;
	using namespace odb::core;


	bool queryMonitorDetectPoint(std::vector<MonitorDetectPointData>& results, odb::query<MonitorDetectPointData>& query)
	{
        auto databasePtr = getDatabase();
        if (nullptr == databasePtr)
        {
            std::cout << "The database has not been connected or failed to connect" << std::endl;
            return false;
        }
		transaction t(databasePtr->begin());
		for (const MonitorDetectPointData& data : databasePtr->query<MonitorDetectPointData>(query))
		{
			results.emplace_back(data);
		}
		t.commit();
	}

	bool queryProgramPreset(std::vector<PresetProgramData>& results, odb::query<PresetProgramData>& query)
	{
        auto databasePtr = getDatabase();
        if (nullptr == databasePtr)
        {
            std::cout << "The database has not been connected or failed to connect" << std::endl;
            return false;
        }
		transaction t(databasePtr->begin());
		for (const PresetProgramData& data : databasePtr->query<PresetProgramData>(query))
		{
			results.emplace_back(data);
		}
		t.commit();
	
	}

	bool queryAlgoPlanParams(std::vector<AlgoParamsData>& results, odb::query<AlgoParamsData>& query)
	{
        auto databasePtr = getDatabase();
        if (nullptr == databasePtr)
        {
            std::cout << "The database has not been connected or failed to connect" << std::endl;
            return false;
        }
		transaction t(databasePtr->begin());
		for (const AlgoParamsData& data : databasePtr->query<AlgoParamsData>(query))
		{
			results.emplace_back(data);
		}
		t.commit();
	}


	bool queryMonitorChannelId(unsigned long& channelInd, unsigned long detectPointId, const std::string& monitorIp)
	{
        auto databasePtr = getDatabase();
        if (nullptr == databasePtr)
        {
            std::cout << "The database has not been connected or failed to connect" << std::endl;
            return false;
        }
		transaction t(databasePtr->begin());
		typedef odb::query<MonitorDetectPointData> query;
		for (const MonitorDetectPointData& data : databasePtr->query<MonitorDetectPointData>(query(query::DetectionPoint::id == detectPointId && query::Monitor::ip == monitorIp)))
		{
			channelInd = data.detectPointPtr->getStreamId();
		}

		t.commit();

	}


}
