/**
 * Project FVM
 */

#pragma once
#include "stream_input.h"
#include "platform_puller.h"
#include "stream/element/circular_queue.h"

/**
 * @brief: 海康SDK接入
 */
namespace fvm::stream
{
    class SDKHKPuller : public PlatformPuller
    {
    public:

        SDKHKPuller(FrontPlatformPtr platform);
        /**
         * @brief 获取错误信息
         */
        
        std::string getLastError(void) override;

        // （新增）3D定位
        bool focusRect(int xTop, int yTop, int xBottom, int yBottom) override;

        /**
         * 云台保存预置位
         */
        bool saveCameraPreset(int presetId) override;

    private:
        void initOptions();

        //打开输入流
        bool open() override;

        //关闭输入流
        void close() override;

        //线程函数
        void process() override;

        //获取当前ptz坐标
        bool getPtzPosition(double& x, double& y, double& z, const std::optional<int>& actPresetId) override;

        //调用相机切换预置位
        bool callCameraPreset(int presetId) override;

        //处理海康的实时数据
        void dealData(uint8_t* buf, int buf_size);

         /**
         * @brief 控制云台 
         * @param[in] action:  云台动作 1：向左 2：向右 3：向上 4：向下 5：变倍短 6：变倍长 7：聚焦近 8：聚焦远 9：光圈小 10：光圈大 11：灯光关 12：灯光开
         * @param[in] step:  步长  主要用于控制云台转动方向的操作，1-8为步长值，1为最小步长，8为最大步长
         */
        bool controlPtz(int action, int step);
    private:
        uint8_t* ioContextBuffer = nullptr;
        AVIOContext* pb = nullptr;
        CCircularQueue streamQueue;
    };
}