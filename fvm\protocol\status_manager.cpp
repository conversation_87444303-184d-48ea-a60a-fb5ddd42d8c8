/**
 * Project FVM
 */
#include "status_manager.h"
#include "protocol_manager.h"
#include "data/data_manager.h"
#include "util/worker_pool.h"
#include "ailog.h"

/*
 * 状态管理模块 （流状态，心跳保活等）
 */
namespace fvm::protocol
{
    using namespace network;
    using namespace std::chrono;
    using SteadyClock = std::chrono::steady_clock;

    /*
     * 状态发送阶段
     */
    enum class StatusSendFlag
    {
        ToSend,  // 准备发送
        Failure, // 发送失败
        Success  // 发送成功
    };

    /*
     * 流状态
     */
    struct ChannelStreamStatus
    {
        int channelId;
        int videoId;
        int status;
        StatusSendFlag flag = StatusSendFlag::ToSend;
        SteadyClock::time_point lastPostTime;
    };

    // 重新尝试间隔（秒）
    constexpr int RETRY_INTERVAL_SECONDS = 60;

    std::atomic_int channelStart = 1;
    std::atomic_int channelEnd = 1;
    std::atomic_bool streamStatusTaskExit = false;
    std::atomic_bool KeepAliveTaskExit = false;
    boost::fibers::mutex mutexStreamStatus;
    std::map<int, ChannelStreamStatus> streamStatusMap;

    /**
     * @brief      流状态上报任务线程
     */
    void postStreamStatusThread();

    /**
     * @brief      fvm-->web心跳包
     */
    void keepAliveThread();

    /**
     * @brief     初始化状态管理
     * @note
     */
    void initStatusManager()
    {
        protocol::clearAllStreamStatus();
        worker::post(worker::WorkerType::Message, []() {postStreamStatusThread();});
        worker::post(worker::WorkerType::Message, []() {keepAliveThread();});
    }

    /**
     * @brief  停止状态管理器，销毁资源
     */
    void disposeStatusManager()
    {
        streamStatusTaskExit = true;
        KeepAliveTaskExit = true;
    }

    /**
     * @brief      push流状态消息到消息buff中
     * @param[in]  channelId:通道号
     * @param[in]  videoId:视频ID
     * @param[in]  status:流状态 false 离线 true 在线
     */
    void pushStreamStatusMessage(int channelId, int videoId, bool status)
    {
        if (videoId < 0 || channelId <= 0) return;

        std::lock_guard<boost::fibers::mutex> lck(mutexStreamStatus);
        streamStatusMap[channelId].channelId = channelId;
        streamStatusMap[channelId].videoId = videoId;
        streamStatusMap[channelId].status = status;
        streamStatusMap[channelId].flag = StatusSendFlag::ToSend;

        if(channelId < channelStart) channelStart = channelId;
        if(channelId > channelEnd) channelEnd = channelId;
    }

    /**
    * @brief      清除所有已分配通道的流状态
    * @note       程序在异常退出时，且通道分配发生了变化，可能导致之前的通道的状态还存在，但该通道已经没有分配了，所以需要清楚掉进程。
    */
    void clearAllStreamStatus()
    {
        {
            std::lock_guard<boost::fibers::mutex> lck(mutexStreamStatus);
            streamStatusMap.clear();
        }
        const auto &processChannels = DATA_MANAGER.queryMonitorChannelIds();  //!< 获取所有通道
        for (const auto&[processId, channels] : processChannels)
        {
            for (const auto &channelId : channels)
                pushStreamStatusMessage(channelId, 0, false);
        }
    }

    /**
     * @brief    通知web更新流状态暂停
     * @param[in]  channelId:通道号
     */
    void pauseStreamStatus(int channelId, int videoId)
    {
        pushStreamStatusMessage(channelId, videoId, false);
    }

    /**
     * @brief      通知web更新流状态恢复
     * @param[in]  channelId:通道号
     * @param[in]  videoId:视频ID
     */
    void restoreStreamStatus(int channelId,  int videoId)
    {
        pushStreamStatusMessage(channelId, videoId, true);
    }

    /**
     * @brief      流状态上报任务线程
     */
    void postStreamStatusThread()
    {
        boost::this_fiber::sleep_for(100ms);
        while (!streamStatusTaskExit)
        {
            boost::this_fiber::sleep_for(10ms);
            for(int channel = channelStart; channel <= channelEnd; channel++)
            {
                if (streamStatusTaskExit)
                    break;

                boost::this_fiber::sleep_for(10ms);
                mutexStreamStatus.lock();
                if (streamStatusMap.find(channel) == streamStatusMap.end())
                {
                    mutexStreamStatus.unlock();
                    continue;
                }
                auto streamInfo = streamStatusMap[channel];
                mutexStreamStatus.unlock();

                // 隔一段时间重发
                if(streamInfo.flag != StatusSendFlag::ToSend)
                {
                    auto passTime = std::chrono::duration_cast<std::chrono::seconds>(SteadyClock::now() - streamStatusMap[channel].lastPostTime).count();
                    if (passTime < RETRY_INTERVAL_SECONDS || streamInfo.videoId == 0 ) continue;
                }

                bool ret = PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_STREAM_STATUS, StreamStatus{streamInfo.channelId, streamInfo.videoId, streamInfo.status});

                // ai::LogInfo << "Send heart info! channel:" << streamInfo.channelId << " video:" << streamInfo.videoId << " status:" << streamInfo.status;
                streamStatusMap[channel].lastPostTime = SteadyClock::now();
                std::lock_guard<boost::fibers::mutex> lck(mutexStreamStatus);
                {
                    if (streamStatusMap.find(channel) != streamStatusMap.end())
                    {
                        streamStatusMap[channel].flag = ret ? StatusSendFlag::Success : StatusSendFlag::Failure;
                    }
                }
            }
        }
    }

    /**
     * @brief      fvm-->web心跳包
     */
    void keepAliveThread()
    {
        boost::this_fiber::sleep_for(1s);
        while ( !KeepAliveTaskExit )
        {
            PROTOCOL_MANAGER.sendToWEB<HeartInfo>(ProtocolType::HTTP_HEART_INFO, {DATA_MANAGER.getFvmPort(), 1});
            for ( int i = 0; i < 50; i++ )
            {
                if ( KeepAliveTaskExit )
                    break;
                boost::this_fiber::sleep_for(100ms);
            }
        }
    }
}