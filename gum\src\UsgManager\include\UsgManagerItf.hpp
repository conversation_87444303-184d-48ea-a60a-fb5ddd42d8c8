#ifndef USGMANAGERITF_HPP_
#define USGMANAGERITF_HPP_

#include <boost/thread.hpp>
#include <functional>
#include <stdint.h>
#include "UsgManager/include/UsgServiceItf.hpp"
#include "UsgManager/include/UsgManagerCfg.hpp"
#include "UsgManager/include/WtoeStruct.hpp"
#include "include/basic_define.hpp"
using namespace boost::placeholders;
namespace usg
{
    typedef boost::function< bool( const std::string &sid, void* ) > onNotifyCallBack_t;

    typedef boost::shared_mutex rwmutex;
    typedef boost::shared_lock<rwmutex> readLock;
    typedef boost::unique_lock<rwmutex> writeLock;

    interface IGb28181;
    interface IPtzController;

    typedef boost::shared_ptr< IGb28181 > CSpIGb28181;
    typedef boost::shared_ptr< IPtzController > CSpIPtzController;

    interface IUsgManager
    {

    public:
        IUsgManager(){}
        virtual ~IUsgManager(){}

    public:
        //接受IWtoeSipFunc发送的回应消息
        virtual bool onReceiveKeepalive( const std::string &sid ) = 0;
        virtual bool onReceiveCatalog( const std::string &sid, const SCatalog& info, SCatalogResponse& out ) = 0;
    public:
        //接受ISipPtzController发送的云台控制消息
        virtual bool onReceivePresetListResponse( const std::string& sid,SPresetListResponse& out ) = 0;
        virtual bool onReceivePtzCommand( const EPtzCommand ptzCommand,const boost::uuids::uuid &resId, const uint8_t arg ) = 0;

        //add for GB28181
        virtual bool onReceiveDeviceReboot(const boost::uuids::uuid &resId) = 0;
        virtual bool onReceiveRecordContronl(const boost::uuids::uuid &resId, const bool flag) = 0;
        virtual bool onReceiveGuardContronl(const boost::uuids::uuid &resId, const bool flag) = 0;
        virtual bool onReceiveAlarmReset(const boost::uuids::uuid &resId) = 0;
        virtual bool onReceiveQueryDeviceInfo(const boost::uuids::uuid &resId, SDeviceInfoResponse &info) = 0;
        virtual bool onReceiveQueryDeviceStatus(const boost::uuids::uuid &resId, SDeviceStatusResponse &info) = 0;
        virtual bool onReceiveFiletoEnd( const std::string& sid ) = 0;
        virtual bool onReceiveAlarmNotify(const SAlarmParam &alarm) = 0;

        /**
         * @brief 	设备远程启动，for GB28181
         * @param[in]  resId    		资源id.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */
        virtual bool deviceReboot( const boost::uuids::uuid &resId ) = 0;

        /**
         * @brief 	录像控制，for GB28181
         * @param[in]  resId    		资源id.
         * @param[in]  flag    		true 开始，flase 停止.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */
        virtual bool recordContronl( const boost::uuids::uuid &resId, const bool flag ) = 0;

        /**
         * @brief 	布撤防，for GB28181
         * @param[in]  resId    		资源id.
         * @param[in]  flag    		true 布防，flase 撤防.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */
        virtual bool guardContronl( const boost::uuids::uuid &resId, const bool flag ) = 0;

        /**
         * @brief 	告警复位，for GB28181
         * @param[in]  resId    		资源id.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */
        virtual bool alarmReset( const boost::uuids::uuid &resId ) = 0;


        /**
         * @brief 	  信息查询，for GB28181
         * @param[in]  devId    		资源id.
         * @param[out]  info    		返回查询结果.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */
        virtual bool deviceInfo( const boost::uuids::uuid &resId, SQueryInfo &info ) = 0;

        /**
         * @brief 	  状态查询，for GB28181
         * @param[in]  devId    		资源id.
         * @param[out]  info    		返回查询结果.
         * @return bool       返回值说明.
         * @retval true       成功.
         * @retval false      失败.
         */
        virtual bool deviceStatus( const boost::uuids::uuid &resId, SQueryStatus &info ) = 0;

    public:
        //接受注册信息
        virtual bool onReceiveRegist( const std::string &sid,const std::string &oid,int expries ) = 0;
        virtual bool isRegist( const std::string& oid ) = 0;

        virtual bool setCseqValues( const std::string& sid ,int cseq ) = 0;
        virtual bool getCsqlValues( const std::string& sid, int& cseq ) = 0;

    public:
        /**
         * 用来管理PJ库的pj_thread_desc的对象
         * pj_thread_desc对象用来提供非PJ线程的管理。会被注册到线程的专属内存中
         * 本函数只保存，但本库不使用。在库销毁时，销毁所有的对象。
         */
        virtual bool addPJThreadDes( long *des ) = 0;
    };

    interface IPtzController
    {
        /** 查询预置位
         *
         * @param[out] infos      预置位列表。
         * @param[out] result     操作执行失败，通过该参数返回失败原因。
         *
         * @return      bool      操作执行成功，返回true，操作执行失败，返回false。
         */
        virtual bool queryPreposition( const boost::uuids::uuid &resId, std::map< uint8_t, std::string > &infos ) = 0;

        /** 云台动作：上、下、左、右、停止、预置位的切换
         *
         * @param[out]  result    操作执行失败，通过该参数返回失败原因。
         *
         * @return      bool       操作执行成功，返回true，操作执行失败，返回false。
         */
        virtual bool up( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId ) = 0;
        virtual bool down( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId ) = 0;
        virtual bool left( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId ) = 0;
        virtual bool right( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId ) = 0;
        virtual bool stop( const boost::uuids::uuid &resId, const uint8_t speed, const boost::uuids::uuid &userId ) = 0;
        virtual bool switchPreposition( const boost::uuids::uuid &resId,  const boost::uuids::uuid &userId, const uint8_t index ) = 0;

        /** 镜头动作：焦距的变化
         *
         * @param[out]  result   操作执行失败，通过该参数返回失败原因。
         *
         * @return      bool     操作执行成功，返回true，操作执行失败，返回false。
         */
        virtual bool focusNear( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId ) = 0;
        virtual bool focusFar ( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId ) = 0;
        virtual bool focusStop ( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId ) = 0;

        /** 镜头动作：缩放倍数
         *
         * @param[out]  result   操作执行失败，通过该参数返回失败原因。
         *
         * @return      bool     操作执行成功，返回true，操作执行失败，返回false。
         */
        virtual bool zoomOut( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId ) = 0;
        virtual bool zoomIn( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId ) = 0;

        /** 镜头动作：光圈的变化
         *
         * @param[out]  result   操作执行失败，通过该参数返回失败原因。
         *
         * @return      bool     操作执行成功，返回true，操作执行失败，返回false。
         */
        virtual bool apertureWide( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId ) = 0;
        virtual bool apertureTele( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId ) = 0;

        /** 镜头动作：雨刷开启、雨刷关闭
         *
         * @param[out]  result   操作执行失败，通过该参数返回失败原因。
         *
         * @return      bool     操作执行成功，返回true，操作执行失败，返回false。
         */
        virtual bool wiperOn ( const boost::uuids::uuid &resId,  const boost::uuids::uuid &userId ) = 0;
        virtual bool wiperOff( const boost::uuids::uuid &resId,  const boost::uuids::uuid &userId ) = 0;


        /** 镜头动作：灯光开启、灯光关闭
         *
         * @param[out]  result   操作执行失败，通过该参数返回失败原因。
         *
         * @return      bool     操作执行成功，返回true，操作执行失败，返回false。
         */
        virtual bool ledOn ( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId ) = 0;
        virtual bool ledOff( const boost::uuids::uuid &resId, const boost::uuids::uuid &userId ) = 0;

        /*
        *  该媒体源是否可以进行云台控制
        * @return  bool  操作执行成功，返回true,能控制。操作执行失败，返回false,不能控制
        */
        virtual bool canControlPtz( const boost::uuids::uuid &resId, bool &retbool ) = 0;

        virtual ~IPtzController() {}
    };

    interface ISgResNotify
    {
        /**
         * @brief 通知上线、下线、资源改变.
         * @parm[in] act            通知类型
         * @parm[in] mapRes         资源信息
         * @parm[in] timeStamp      时戳。
         */
        virtual bool sgResNotify( const std::map<boost::uuids::uuid, SResInfo >& mapRes, const uint32_t timeStamp ) = 0;

        virtual ~ISgResNotify() {}
    };


    interface ISgAlarmNotify
    {
        /**
         * @brief                   报警通知
         * @parm[in] act            通知信息
         */
        virtual bool sgAlarmNotify( const SAlarmInfo& alarmInfo ) = 0;

        ~ISgAlarmNotify() {}
    };

    interface IGb28181
    {
        virtual CSpIPtzController getPtzControllerMgr() = 0;
        virtual CSpIUsgService	getUsgService() = 0;
        virtual void registResNotify( ISgResNotify *notify ) = 0;
        virtual void unregistResNofity( ISgResNotify *notify ) = 0;
        virtual void registAlarmNotify( ISgAlarmNotify *notify ) = 0;
        virtual void unregistAlarmNotify( ISgAlarmNotify *notify ) = 0;
        virtual bool getMediaInfoByMedia( const boost::uuids::uuid &mediaId, const TimePeriodUnit &timePeriod, std::vector< std::pair< std::string, TimePeriodUnit > > &info ) = 0;
        virtual bool getAllResInfos( std::map<boost::uuids::uuid, SResInfo >& mapRes, uint32_t& timeStamp ) = 0;
        virtual bool addRemote( uint32_t id, const std::string& szSipId, const std::string& szAddr ) = 0;
        virtual bool delRemote( const std::string& szSipId, const std::string& szAddr ) = 0;
        virtual bool addVideo( const std::string& szSipId, const std::string& szVideoAddr, const std::string& szName, uint32_t id ) = 0;
        /**
         *
         * @param szSipId
         * @param szResCode
         * @param szDest
         * @param szSrc
         * @param eType
         * @return 0 - ok, -1 - no such res, -2 - not regist, -3 - play failed
         */
        virtual int startPlay( const std::string& szSipId, const std::string& szResCode, const std::string& szDest, std::string& szSrc, ESocketType eType, bool bUseSub, std::string& szError ) = 0;
        virtual bool stopPlay( const std::string& szSipId, const std::string& szResCode, const std::string& szDest, bool bUseSub ) = 0;
        virtual bool getVideoStatus( const std::string& szSipId, const std::string& szVideoAddr ) = 0;

        virtual bool ptzControl( const std::string& szSipId, const std::string& szResCode, uint8_t iPreset ) = 0;
        virtual bool doPtzControl(const std::string& szSipId, const std::string& szResCode, EPtzCommand command, int iParam1 = 0, int iParam2 = 0) = 0;
        virtual bool getPtzs( const std::string& szSipId, const std::string& szResCode, std::map< uint8_t, std::string>& mapPtzs ) = 0;
        virtual void setLevel( uint16_t level ) = 0;
        virtual void fini() = 0;
        virtual bool init(FUNC_INSERTVIDEO_CALLBACK func1,
                          FUNC_UPDATEREMOTESTATUS_CALLBACK func2,
                          FUNC_UPDATEVIDEONAME_CALLBACK func3 ) = 0;
        virtual bool isPlaying() = 0;
        virtual ~IGb28181() {}
    };

}


#endif