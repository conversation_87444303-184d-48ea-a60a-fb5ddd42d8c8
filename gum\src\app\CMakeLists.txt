﻿
cmake_minimum_required (VERSION 3.5.2)
project(app)
set(APP_NAME "gum")
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY  "${PROJECT_SOURCE_DIR}/../../out")

add_compile_options(-std=c++17 -fPIC -fstack-protector-all -Wall -Wno-register)
add_compile_options(-DXP_UNIX -DIS_LITTLE_ENDIAN=1 -DPJ_AUTOCONF=1 -DPJ_IS_LITTLE_ENDIAN=1 -DBOOST_LOG_DYN_LINK)


set(FVM_HOME "${PROJECT_SOURCE_DIR}/../../..")
set(FVM_LIB 
    ai_log )

# boost
set(BOOST_HOME "/opt/boost")
set(BOOST_LIB
    boost_log
    boost_log_setup
    boost_program_options
    boost_system
    boost_date_time
    boost_filesystem
)

# ACE
set(ACE_HOME "/opt/ace")
set(ACE_LIB ACE )

# ffmpeg
set(FFMPEG_HOME "/opt/ffmpeg")
set(FFMPEG_LIB
    avutil
    avcodec
    avformat
    swresample)

# wtoe
set(WTOE_HOME "/opt/wtoe")
set(WTOE_LIB
    BasicHelper
    PackageManager )

# 头文件
include_directories(
    ${PROJECT_SOURCE_DIR}/../
    ${BOOST_HOME}/include/
    ${ACE_HOME}/include/
    ${WTOE_HOME}/include/
    ${FVM_HOME}/log/include/
    /opt/other3rd/include/
)

# 库路径
link_directories(
    ${LIB_FVM}
    ${BOOST_HOME}/lib/
    ${ACE_HOME}/lib/
    ${WTOE_HOME}/lib/
    /opt/other3rd/lib/
    ${FVM_HOME}/out/lib/
)

FILE(GLOB src "src/*.cpp")
FILE(GLOB platform "platform/*.cpp")
FILE(GLOB stream "stream/*.cpp")
FILE(GLOB channel "stream/channel/*.cpp")
FILE(GLOB input "stream/input/*.cpp")
FILE(GLOB output "stream/output/*.cpp")
FILE(GLOB util "util/*.cpp")
FILE(GLOB protocol "protocol/*.cpp")
FILE(GLOB thirdparty "thirdparty/*.cpp")

#file(GLOB_RECURSE sources CONFIGURE_DEPENDS "*.cpp")
SET(ALL_SRC ${include} ${src} )
add_executable(${APP_NAME} ${src} )

SET(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)
target_link_libraries(${APP_NAME} pthread dl js ${BOOST_LIB} ${ACE_LIB} ${WTOE_LIB} ${FVM_LIB})
set_target_properties(${APP_NAME} PROPERTIES INSTALL_RPATH "./lib;${BOOST_HOME}/lib;${FVM_HOME}/out/lib")
