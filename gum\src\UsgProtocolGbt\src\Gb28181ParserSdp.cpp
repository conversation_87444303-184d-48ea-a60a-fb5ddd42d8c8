#include <boost/uuid/uuid_io.hpp>
#include <wtoe/TestSupport/TestSupportExp.hpp>

#include "Gb28181ParserSdp.hpp"
#include "ConfigFile.hpp"
#include "../../UsgManager/include/WtoeSipFuncItf.hpp"
#include "Gb28181XmlTag.hpp"

namespace gb28181
{

    CGb28181ParserSdp::CGb28181ParserSdp(
            usg::IWtoeSipFunc* wsf,
            CConfigFile *configFile,
            const std::string &sid,
            const std::string &resAddr,
            const std::string &gbtStr ,
            const bool answer)
            : m_wsf( wsf )
            , m_sid( sid )
            , m_resAddr( resAddr )
            , m_gbtStr( gbtStr )
            , m_answer(answer)
            , m_configFile( configFile )
            , m_isOk( false )
    {
        if( m_wsf == 0 )
            return;
        if( m_configFile == 0 )
            return;
        if( m_gbtStr.empty() )
            return;

        m_historyMedia.beginTime	= 0;
        m_historyMedia.endTime		= 0;
        m_historyMedia.playUrl		= "";

        m_result = "";
        m_format = "";
        m_srcAddr = "";
        m_inviteType = -1;

        m_isOk = true;
    }

    CGb28181ParserSdp::~CGb28181ParserSdp()
    {

    }

    std::string CGb28181ParserSdp::getResultStr()
    {
        return m_result;
    }

    std::string CGb28181ParserSdp::getFormat()
    {
        return m_format;
    }

    std::string CGb28181ParserSdp::getSrcAddr()
    {
        return m_srcAddr;
    }

    bool CGb28181ParserSdp::isOk()
    {
        return m_isOk;
    }

    int CGb28181ParserSdp::getInviteType()
    {
        return m_inviteType;
    }

    bool CGb28181ParserSdp::parserReceivePkg()
    {
        std::string optStr = "";  //用于记录操作类型：实时点播、回放、下载
        std::string addrStr = ""; //用于记录地址
        m_format = "";  //用于记录支持的格式（分辨率）
        m_srcAddr = "";
        std::vector<std::string> videoTypeStrs; //用于记录支持的视频类型
        uint32_t beginTime = 0;   //用于记录回放的起始时间
        uint32_t endTime = 0;     //用于记录回放的结束时间
        bool isVideo = false;
        uint32_t port = 0;
        std::string resAddr = "";

        //解析指令
        const char* sdpLine = m_gbtStr.c_str();
        const char* nextSDPLine = sdpLine;
        while(1)
        {
            sdpLine = nextSDPLine;
            if ( sdpLine == NULL ) break;

            if( !parseSDPLine(sdpLine, nextSDPLine) )
                return false;

            std::string ver = "";
            if ( parseSDPLine_v(sdpLine, ver ) )
            {
                continue;
            }
            std::string resCode = "";
            if ( parseSDPLine_o(sdpLine,resCode))
            {
                resAddr = resCode;
                continue;
            }

            std::string optStrTmp = "";
            if( parseSDPLine_s(sdpLine, optStrTmp) )
            {
                optStr = optStrTmp;
                continue;
            }

            uint32_t beginTimeTmp = 0;
            uint32_t endTimeTmp = 0;
            if( parseSDPLine_t(sdpLine, beginTimeTmp, endTimeTmp) )
            {
                beginTime = beginTimeTmp;
                endTime = endTimeTmp;
                continue;
            }

            std::string addrStrTmp = "";
            if( parseSDPLine_c(sdpLine, addrStrTmp) )
            {
                addrStr = addrStrTmp;
                continue;
            }

            std::string resCodeTmp = "";
            if ( parseSDPLine_u(sdpLine,resCodeTmp))
            {
                resAddr = resCodeTmp;
                continue;
            }

            std::string formatTmp = "";
            if( parseSDPLine_f(sdpLine, formatTmp) )
            {
                m_format = formatTmp;
                continue;
            }

            bool isVideoTmp = false;
            uint16_t portTmp = 0;
            if( parseSDPLine_m(sdpLine, isVideoTmp, portTmp) )
            {
                isVideo = isVideoTmp;
                port = portTmp;
                continue;
            }

            std::string videoTypeStr = "";
            if( parseSDPAttribute_rtpmap(sdpLine, videoTypeStr) )
            {
                videoTypeStrs.push_back( videoTypeStr );
                continue;
            }
        }

        ACE_INET_Addr addr(port, addrStr.c_str());
        char chTmp[40] = { 0 };
        sprintf( chTmp, "%s:%d", addrStr.c_str(), port );
        m_srcAddr = std::string( chTmp );
        //beginTime = 1410758105;
        //endTime = 1410758405;

        bool ret = true;
        if( optStr == TAG_RTP_OPT_REALPLAY )
        {
            if (!m_answer)
                ret = request_func_play( addr, resAddr,m_format, isVideo, videoTypeStrs );
            else
                ret = response_func_play();

            m_inviteType = 0;
        }
        else if( optStr == TAG_RTP_OPT_PLAYBACK )
        {
            if (!m_answer)
                ret = request_func_playback( addr, resAddr,beginTime, endTime );
            else
                ret = response_func_playback(addr, resAddr,beginTime);

            m_inviteType = 1;
        }
        else if( optStr == TAG_RTP_OPT_DOWNLOAD )
        {
            if (!m_answer)
                ret = request_func_download( addr, resAddr, beginTime, endTime );
            else
                ret = response_func_download(addr, resAddr,beginTime);

            m_inviteType = 2;
        }

        return ret;
    }

    char* CGb28181ParserSdp::strDupSize( const char* str )
    {
        if ( !str )
            return NULL;

        size_t len = strlen( str ) + 1;

        char* strCopy = new char[len];

        return strCopy;
    }

    void CGb28181ParserSdp::strDel( char*& str )
    {
        if ( str )
        {
            delete[] str;
            str = 0;;
        }
    }

    bool CGb28181ParserSdp::parseSDPLine( const char* inputLine, const char*& nextLine )
    {
        // Begin by finding the start of the next line (if any):
        nextLine = NULL;
        for ( const char* ptr = inputLine; *ptr != '\0'; ++ptr )
        {
            if ( *ptr == '\r' || *ptr == '\n' )
            {
                // We found the end of the line
                ++ptr;
                while ( *ptr == '\r' || *ptr == '\n' )
                    ++ptr;
                nextLine = ptr;
                if ( nextLine[0] == '\0' )
                    nextLine = NULL; // special case for end
                break;
            }
        }

        // Then, check that this line is a SDP line of the form <char>=<etc>
        // (However, we also accept blank lines in the input.)
        if ( inputLine[0] == '\r' || inputLine[0] == '\n' )
            return true;

        if ( strlen(inputLine) < 2 || inputLine[1] != '='
             || inputLine[0] < 'a' || inputLine[0] > 'z' )
        {
            return false;
        }

        return true;
    }

    bool CGb28181ParserSdp::parseSDPLine_v( const char* sdpLine, std::string &format )
    {
        bool parseSuccess = false;
        char* buffer = strDupSize( sdpLine );

        if ( sscanf( sdpLine, "v=%s", buffer ) == 1 )
        {
            format = buffer;
            parseSuccess = true;
        }

        strDel( buffer );
        return parseSuccess;
    }

    bool CGb28181ParserSdp::parseSDPLine_s( const char* sdpLine, std::string &optStr )
    {
        // Check for "s=<session name>" line
        bool parseSuccess = false;
        char* buffer = strDupSize( sdpLine );

        if ( sscanf( sdpLine, "s=%[^\r\n]", buffer ) == 1 )
        {
            optStr = buffer;
            parseSuccess = true;
        }

        strDel( buffer );
        return parseSuccess;
    }

    bool CGb28181ParserSdp::parseSDPLine_t( const char* sdpLine, uint32_t &beginTime, uint32_t &endTime )
    {
        bool parseSuccess = false;
        char* buffer = strDupSize( sdpLine );

        if ( sscanf( sdpLine, "t=%u %u", &beginTime, &endTime ) == 2 )
        {
            parseSuccess = true;
        }

        strDel( buffer );
        return parseSuccess;
    }

    bool CGb28181ParserSdp::parseSDPLine_c( const char* sdpLine, std::string &addr )
    {
        // Check for "c=IN IP4 <connection-endpoint>"
        // or "c=IN IP4 <connection-endpoint>/<ttl+numAddresses>"
        // (Later, do something with <ttl+numAddresses> also #####)
        bool parseSuccess = false;
        char* buffer = strDupSize( sdpLine ); // ensures we have enough space
        //c=IN IP4 ************
        if ( sscanf( sdpLine, "c=IN IP4 %[^/\r\n]", buffer ) == 1 )
        {
            // Later, handle the optional /<ttl> and /<numAddresses> #####
            addr = buffer;
            parseSuccess = true;
        }

        strDel( buffer );

        return parseSuccess;
    }

    bool CGb28181ParserSdp::parseSDPLine_o( const char* sdpLine, std::string &addr )
    {
        bool parseSuccess = false;
        char* buffer1 = strDupSize( sdpLine );
        char* buffer2 = strDupSize( sdpLine );

        //o=34020000001320001101 604 604 IN IP4 ************
        if ( sscanf( sdpLine, "o=%s %[^/\r\n]", buffer1,buffer2 ) == 2 )
        {
            addr = buffer1;
            parseSuccess = true;
        }

        strDel( buffer1 );
        strDel( buffer2 );
        return parseSuccess;
    }

    bool CGb28181ParserSdp::parseSDPLine_u( const char* sdpLine, std::string &rescode)
    {
        bool parseSuccess = false;
        char* buffer = strDupSize( sdpLine );

        if ( sscanf( sdpLine, "u=%[^/:]", buffer ) == 1 )
        {
            rescode = buffer;
            parseSuccess = true;
        }

        strDel( buffer );
        return parseSuccess;
    }

    bool CGb28181ParserSdp::parseSDPLine_f( const char* sdpLine, std::string &format )
    {
        bool parseSuccess = false;
        char* buffer = strDupSize( sdpLine );

        if ( sscanf( sdpLine, "f=%s", buffer ) == 1 )
        {
            format = buffer;
            parseSuccess = true;
        }

        strDel( buffer );
        return parseSuccess;
    }

    bool CGb28181ParserSdp::parseSDPLine_m( const char* sdpLine, bool &isVideo, uint16_t &port )
    {
        bool parseSuccess = false;
        char* buffer = strDupSize( sdpLine ); // ensures we have enough space
        char* buffer2 = strDupSize( sdpLine );

        std::string beginStr = "";
        if ( sscanf( sdpLine, "m=%s %hu RTP/AVP %s", buffer, &port, buffer2 ) == 3 )
        {
            beginStr = buffer;
            parseSuccess = true;
        }
        else if (sscanf(sdpLine, "m=%s %hu TCP/RTP/AVP %s", buffer, &port, buffer2) == 3)
        {
            beginStr = buffer;
            parseSuccess = true;
        }

        if( beginStr == "video" )
            isVideo = true;
        else
            isVideo = false;

        strDel( buffer );
        strDel( buffer2 );//20150715 添加，销毁buffer2分配的内存，原来没有销毁，有内存泄漏。

        return parseSuccess;
    }

    bool CGb28181ParserSdp::parseSDPAttribute_rtpmap( const char* sdpLine, std::string &videoType )
    {
        bool parseSuccess = false;
        char* buffer = strDupSize( sdpLine ); // ensures we have enough space
        uint32_t rtpTimestampFrequency = 0;
        uint32_t rtpmapPayloadFormat;
        if ( sscanf( sdpLine, "a=rtpmap: %u %[^/]/%u", &rtpmapPayloadFormat, buffer, &rtpTimestampFrequency ) == 3 )
        {
            parseSuccess = true;
            videoType = buffer;
        }
        strDel( buffer );

        if( videoType.empty() )
            return false;

        return parseSuccess;
    }

    bool CGb28181ParserSdp::request_func_play(ACE_INET_Addr &addr, std::string &resAddr,std::string &format, bool &isVideo, std::vector<std::string> &videoTypes)
    {
        gb28181::SRealMedia gbt_request;
        gbt_request.resAddr = resAddr;

        if( isVideo )
        {
            gbt_request.supportVideoTypes.clear();

            for( size_t i=0;i<videoTypes.size();i++ )
            {
                gb28181::EVideoType videoType;
                if( trans(videoTypes[i], videoType) )
                    gbt_request.supportVideoTypes.push_back(videoType);
            }
        }
        else
        {
            gbt_request.supportAudioTypes.clear();
            for( size_t i=0;i<videoTypes.size();i++ )
            {
                gb28181::EAudioType audioType;
                if( trans(videoTypes[i], audioType) )
                    gbt_request.supportAudioTypes.push_back(audioType);
            }
        }

        //gbt_request.maxBitrate;
        gbt_request.socketType = gb28181::ESOCKETTYPE_UDP;
        gbt_request.sockAddr = addr.get_ip_address();
        gbt_request.sockPort = addr.get_port_number();

        // 得到UUID
        boost::uuids::uuid resUuid;
        if( !m_configFile->getResUuid( gbt_request.resAddr, resUuid ) )
            return false;

        uint8_t subImageSize = 0;
        if( !m_configFile->getSubImageSize( gbt_request.resAddr, subImageSize ) )
            return false;
        uint8_t masterImageSize = 0;
        if( !m_configFile->getMasterImageSize( gbt_request.resAddr, masterImageSize ) )
            return false;

        //if( format.empty() )
        //{
        //    EFormatType subType;
        //    EFormatType masterType;
        //    //如果format为空，则用主/子码流视频尺寸填充
        //    trans(subImageSize, subType);
        //    trans(masterImageSize, masterType);

        //    gbt_request.supportFormatTypes.push_back(masterType);
        //    gbt_request.supportFormatTypes.push_back(subType);
        //}
        //else
        //{
        //    gbt_request.supportFormatTypes;
        //}

        // 转换成msg的
        usg::SRealMedia msg_request;
        usg::EFormatType subType;
        usg::EFormatType masterType;
        trans(subImageSize, subType);
        trans(masterImageSize, masterType);
        msg_request.supportFormatTypes.push_back(subType);
        msg_request.supportFormatTypes.push_back(masterType);

        if( !m_structExchange.realMediaGbtToWtoe( gbt_request, msg_request ) )
            return false;

        // 执行命令
        usg::SRealMediaResponse msg_response;
        if( !m_wsf->onReceiveRealMedia( m_sid, resUuid, subImageSize, masterImageSize, msg_request, msg_response ) )
            return false;

        // 用结果获取发送字符串.
        if( !mkResultStr( msg_response, m_result ) )
            return false;

        return true;
    }

    bool CGb28181ParserSdp::response_func_play()
    {
        usg::SRealMediaResponse msg_response;
        msg_response.bitRate = 8192;
        if (!m_wsf->onReceiveRealMediaResponse(m_sid,msg_response))
            return false;

        return true;
    }

    bool CGb28181ParserSdp::request_func_playback( ACE_INET_Addr &addr, std::string &resAddr,uint32_t beginTime, uint32_t endTime )
    {
        return request_func_gengealImple( addr,resAddr, beginTime, endTime );
    }

    bool CGb28181ParserSdp::request_func_download( ACE_INET_Addr &addr, std::string &resAddr,uint32_t beginTime, uint32_t endTime )
    {
        return request_func_gengealImple( addr,resAddr, beginTime, endTime );
    }

    bool CGb28181ParserSdp::request_func_gengealImple( ACE_INET_Addr &addr, std::string &resAddr,uint32_t beginTime, uint32_t endTime )
    {
        if( !m_isOk ) return false;

        usg::SHistoryMedia msg_request;
        msg_request.startTime = beginTime;
        msg_request.endTime = endTime;

        // 得到UUID
        boost::uuids::uuid resUuid;
        if( !m_configFile->getResUuid( resAddr, resUuid ) )
            return false;
        // 执行命令
        usg::SHistoryMediaResponse msg_response;
        if( !m_wsf->onReceiveHistoryMedia( m_sid, resUuid, msg_request, msg_response ) )
            return false;

        char addbuff[128] = {0};

        addr.addr_to_string( addbuff, 128 );

        m_historyMedia.beginTime	= beginTime;
        m_historyMedia.endTime		= endTime;
        m_historyMedia.playUrl		= msg_response.playUrl;
        m_historyMedia.receiveAddr	= addbuff;

        if( !mkResultStr( msg_response, m_result ) )
            return false;

        return true;
    }

    bool CGb28181ParserSdp::response_func_playback(ACE_INET_Addr &addr, std::string &resAddr, uint32_t beginTime)
    {
        if( !m_isOk ) return false;

        // 执行命令
        usg::SHistoryMediaResponse msg_response;

        // 请求时的定位信息：rtsp://***********:554/uuid.mp4
        // 根据MG人员的提示:历史开始时间会在RTSP中再次沟通,所以不必在url上体现.
        std::string url( "rtsp://" );
        url = url + addr.get_host_addr() + ":55555/";
        try
        {
            url = url + boost::lexical_cast<std::string>(beginTime);
        }
        catch( boost::bad_lexical_cast & /*a*/ )
        {
        }
        catch( ... )
        {
        }
        url = url + ".mp4";

        msg_response.isOk = true;
        msg_response.playUrl = url;

//     if( !m_wsf->onReceiveHistoryMediaResponse( m_sid, msg_response ) )
//         return false;

        return true;
    }

    bool CGb28181ParserSdp::response_func_download(ACE_INET_Addr &addr, std::string &resAddr, uint32_t beginTime)
    {
        if( !m_isOk ) return false;

        // 执行命令
        usg::SHistoryMediaResponse msg_response;

        // 请求时的定位信息：rtsp://***********:554/uuid.mp4
        // 根据MG人员的提示:历史开始时间会在RTSP中再次沟通,所以不必在url上体现.
        std::string url( "rtsp://" );
        url = url + addr.get_host_addr() + ":55555/";
        try
        {
            url = url + boost::lexical_cast<std::string>(beginTime);
        }
        catch( boost::bad_lexical_cast & /*a*/ )
        {
        }
        catch( ... )
        {
        }
        url = url + ".mp4";

        msg_response.isOk = true;
        msg_response.playUrl = url;

        if( !m_wsf->onReceiveHistoryMediaResponse( m_sid, msg_response ) )
            return false;

        return true;
    }

    bool CGb28181ParserSdp::trans( uint8_t imageSize, gb28181::EFormatType &type )
    {
        switch ( imageSize )
        {
            case 0:
                type = gb28181::EFORMATTYPE_QCIF;
                break;
            case 1:
                type = gb28181::EFORMATTYPE_CIF;
                break;
            case 2:
                type = gb28181::EFORMATTYPE_2CIF;
                break;
            case 3:
                type = gb28181::EFORMATTYPE_4CIF;
                break;
            case 4:
                type = gb28181::EFORMATTYPE_D1;
                break;
            case 5:
                type = gb28181::EFORMATTYPE_4CIF;
                break;
            case 6:
                type = gb28181::EFORMATTYPE_16CIF;
                break;
            case 7:
                type = gb28181::EFORMATTYPE_16CIF;
                break;
            case 8:
                type = gb28181::EFORMATTYPE_16CIF;
                break;
            default:
                return false;
                ;
        }

        return true;
    }

    bool CGb28181ParserSdp::trans( const std::string &videoTypeStr, gb28181::EVideoType &type )
    {
        if( videoTypeStr.empty() )
            return false;
        else if( videoTypeStr == TAG_SDP_VIDEOTYPE_MPEG4 )
            type = gb28181::EVIDEOTYPE_MPEG_4 ;
        else if( videoTypeStr == TAG_SDP_VIDEOTYPE_H264 )
            type = gb28181::EVIDEOTYPE_H_264 ;
        else
            type = gb28181::EVIDEOTYPE_H_264 ;

        return true;
    }

    bool CGb28181ParserSdp::trans( const std::string &videoTypeStr, gb28181::EAudioType &type )
    {
        //暂时未填充
        if( videoTypeStr.empty() )
            return false;
        //else if( videoTypeStr == TAG_SDP_VIDEOTYPE_MPEG4 )
        //    type = gb28181::EVIDEOTYPE_MPEG_4 ;
        //else if( videoTypeStr == TAG_SDP_VIDEOTYPE_H264 )
        //    type = gb28181::EVIDEOTYPE_H_264 ;
        //else
        //    return false;

        return true;
    }

    bool CGb28181ParserSdp::trans( uint8_t imageSize, usg::EFormatType &type )
    {
        type = usg::EFORMATTYPE_CIF;
        switch ( imageSize )
        {
            case 0:
                type = usg::EFORMATTYPE_QVGA;
                break;
            case 1:
                type = usg::EFORMATTYPE_CIF;
                break;
            case 2:
                type = usg::EFORMATTYPE_VGA;
                break;
            case 3:
                type = usg::EFORMATTYPE_4CIF;
                break;
            case 4:
                type = usg::EFORMATTYPE_SVGA;
                break;
            case 5:
                type = usg::EFORMATTYPE_XGA;
                break;
            case 6:
                type = usg::EFORMATTYPE_720P;
                break;
            case 7:
                type = usg::EFORMATTYPE_UVGA;
                break;
            case 8:
                type = usg::EFORMATTYPE_1080P;
                break;
            default:
                return false;
        }

        return true;
    }

    bool CGb28181ParserSdp::mkResultStr( usg::SRealMediaResponse &msg_response, std::string &resultStr )
    {
        resultStr = "";
/*    resultStr = "\
v=0\r\n\
o=34020000001320000001 0 0 IN IP4 ************\r\n\
s=Play\r\n\
t=0 0\r\n\
a=sendonly\r\n\
m=video 6000 RTP/AVP 96\r\n\
c=IN IP4 ************\r\n\
a=rtpmap:96 PS/90000\r\n\
a=username:34020000001320000001\r\n\
a=password:12345678\r\n\
y=0100000001\r\n\
f=\r\n";*/

        ACE_INET_Addr addr(msg_response.sockPort, msg_response.sockAddr);
        std::string serverAddr = addr.get_host_addr();
        std::string sdpStr = m_gbtStr;
        std::string lineTmp = "";

        while(1)
        {
            int pos = sdpStr.find("\r\n");
            if( pos <= 0 )
                break;

            lineTmp = sdpStr.substr( 0, pos );
            sdpStr = sdpStr.substr(pos+2, sdpStr.length()-pos-1);

            //解析转换转换一下ip地址和a=sendonly
            if( replace_o(lineTmp, serverAddr) )
            {
                resultStr += lineTmp + "\r\n";
                continue;
            }

            if (replace_m(lineTmp,msg_response.sockPort) )
            {
                resultStr += lineTmp + "\r\n";
                continue;
            }

            if (replace_s(lineTmp) )
            {
                resultStr += lineTmp + "\r\n";
                continue;
            }

            if( replace_c(lineTmp, serverAddr) )
            {
                resultStr += lineTmp + "\r\n";
                continue;
            }

            if( replace_a(lineTmp) )
            {
                if (!lineTmp.empty())
                    resultStr += lineTmp + "\r\n";
                continue;
            }

            resultStr += lineTmp + "\r\n";
        }

        return true;
    }

    bool CGb28181ParserSdp::mkResultStr( usg::SHistoryMediaResponse &msg_response, std::string &resultStr )
    {
        resultStr = "";

        if( msg_response.playUrl.empty() )
            return false;

        std::string serverAddr = "";
        std::string sdpStr = m_gbtStr;
        std::string lineTmp = "";

        parseAddrStr(msg_response.playUrl, serverAddr);
        while(1)
        {
            int pos = sdpStr.find("\r\n");
            if( pos <= 0 )
                break;

            lineTmp = sdpStr.substr( 0, pos );
            sdpStr = sdpStr.substr(pos+2, sdpStr.length()-pos-1);

            //解析转换转换一下ip地址和a=sendonly
            if( replace_o(lineTmp, serverAddr) )
            {
                resultStr += lineTmp + "\r\n";
                continue;
            }

            if( replace_c(lineTmp, serverAddr) )
            {
                resultStr += lineTmp + "\r\n";
                continue;
            }

            if( replace_m(lineTmp, 55555) )
            {
                resultStr += lineTmp + "\r\n";
                continue;
            }

            if( replace_a(lineTmp,false) )
            {
                resultStr += lineTmp + "\r\n";
                continue;
            }

            resultStr += lineTmp + "\r\n";
        }

        return true;
    }

    bool CGb28181ParserSdp::replace_o( std::string &line, std::string addr )
    {
        bool parseSuccess = false;
        const char* sdpLine = line.c_str();
        char* buffer = strDupSize( line.c_str() );
        char* buffer2 = strDupSize( line.c_str() );

        std::string newLine = "o=";
        int aInt = 0;
        int bInt = 0;

        if ( sscanf( sdpLine, "o=%s %u %u IN IP4 %s", buffer, &aInt, &bInt, buffer2 ) == 4 )
        {
            newLine += buffer;
            newLine += " 3159 3159 IN IP4 ";
            newLine += addr;
            parseSuccess = true;

            line = newLine;
        }

        strDel( buffer );
        strDel( buffer2 );

        return parseSuccess;
    }

    bool CGb28181ParserSdp::replace_c( std::string &line, std::string addr )
    {
        bool parseSuccess = false;
        const char* sdpLine = line.c_str();
        char* buffer = strDupSize( sdpLine );

        std::string newLine = "";

        if ( sscanf( sdpLine, "c=IN IP4 %s", buffer ) == 1 )
        {
            newLine = "c=IN IP4 " + addr;
            parseSuccess = true;

            line = newLine;
        }

        strDel( buffer );

        return parseSuccess;
    }

    bool CGb28181ParserSdp::replace_s( std::string &line )
    {
        bool parseSuccess = false;
        if ( "s=Play" == line )
        {
            line = "s=play";
            parseSuccess = true;
        }

        return parseSuccess;
    }

    bool CGb28181ParserSdp::replace_m( std::string &line , uint16_t port)
    {
        bool parseSuccess = false;
        const char* sdpLine = line.c_str();
        uint32_t sendport = 0;
        uint32_t type1 = 0;
        uint32_t type2 = 0;
        uint32_t type3 = 0;

        std::string newLine = "";
        char* buffer = strDupSize( sdpLine );
        if ( sscanf( sdpLine, "m=video %u RTP/AVP %u %u %u", &sendport,&type1,&type2,&type3 ) == 4 )
        {
            sprintf( buffer,"m=video %u RTP/AVP %u",port,type1);
            parseSuccess = true;

            line = buffer;
        }

        strDel( buffer );

        return parseSuccess;
    }

    bool CGb28181ParserSdp::replace_a( std::string &line  ,bool real)
    {
        bool parseSuccess = false;
        if ( "a=recvonly" == line )
        {
            line = "a=sendonly";
            parseSuccess = true;
        }
        else if ("a=rtpmap:96 PS/90000" == line)
        {
            if (real)
                line += "\r\ny=0200000001";
            else
                line += "\r\ny=1200000001";

            parseSuccess = true;
        }
        else if ("a=rtpmap:98 H264/90000" == line)
        {
            line = "";
            parseSuccess = true;
        }
        else if ("a=rtpmap:97 MPEG4/90000" == line)
        {
            line = "";
            parseSuccess = true;
        }

        return parseSuccess;
    }

    bool CGb28181ParserSdp::parseAddrStr( std::string url, std::string &addrStr )
    {
        int iPos = url.find("rtsp://");
        if( iPos < 0 )
            return false;
        url = url.substr(iPos+7, url.length()-iPos-1);
        iPos = url.find(":");

        if( iPos < 0 )
            return false;

        addrStr = url.substr(0, iPos);
        return true;
    }

    SHistoryMediaResponse CGb28181ParserSdp::getHistoryMediaInfo()
    {
        return m_historyMedia;
    }

}
