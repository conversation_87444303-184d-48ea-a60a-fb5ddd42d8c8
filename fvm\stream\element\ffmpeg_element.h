/**
 * Project FVM
 */
#pragma once
#include "stream_element.h"

#ifdef __cplusplus
extern "C"
{
#endif
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/time.h>
#ifdef __cplusplus
}
#endif

/**
 * @brief: 视频流 ffmpeg上下文封装
 *
 */
namespace fvm::stream
{
    class FFMPEGElement : public StreamElement
    {
    protected:
        /**
         * 创建流数据的相关context,设置中断回调函数
         */
        bool createContext();

        /**
         * 销毁流数据的相关context
         */
        void disposeContext();

        /**
         * 初始化参数
         */
        virtual void initOptions(){}

        /**
         * 重置AVFormatContext的超时判断定时器
         */
        inline void resetContextTimer(){startTime = std::chrono::steady_clock::now();};

        /**
         * AVFormatContext相关操作超时起始时间
         */
        std::chrono::steady_clock::time_point startTime;

        //释放AVPacket
        void freeAVPacket(AVPacket* pkt);

    protected:

        AVFormatContext* formatCtx = nullptr;
        AVInputFormat* fmt = nullptr;
        AVStream* outStream = nullptr;
        AVCodecParameters* codecpar = nullptr;
        AVDictionary* options = nullptr;
        AVRational timebase, frameRate;
        int64_t frameIndex = 0;
        int64_t lastPts = 0;
        int64_t lastDts = 0;
    };
}

