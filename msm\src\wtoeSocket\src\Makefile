#
# makefileģ��
#
#
BIN_DIR=../../../bin/debug/
INC_DIR=/usr/local/include/curl
LIB_DIR=/usr/local/lib
SRC_DIR=.
BIN=libwtoeSocket.so
CC=g++

#==========������·������===========
LIBS += -lcurl -L$(LIB_DIR) -L/home/<USER>/3rdlibs/lib
#������
LIBS += -shared -lm  -lnsl -lrt -lpthread -ldl -lz

#==============����ѡ��=============
CPPFLAGS = -fPIC -Wall -g -O2
#CPPFLAGS += -DHAVE_TIMER_MODULE -DSYS_LINUX -DNDEBUG -DWITH_OPENSSL -DWITH_DOM -DWITH_PURE_VIRTUAL

#============ͷ�ļ�����·��===========
IFLAGS = -I$(INC_DIR) -Iinclude

COBJS = $(addsuffix .o, $(basename $(wildcard $(SRC_DIR)/*.c)))

CPPOBJS = $(addsuffix .o, $(basename $(wildcard $(SRC_DIR)/common/*.cpp))) \
					$(addsuffix .o, $(basename $(wildcard $(SRC_DIR)/*.cpp))) 

$(COBJS) : %.o: %.c
	$(CC) -c $< -o $@ $(CPPFLAGS) $(IFLAGS)
$(CPPOBJS) : %.o: %.cpp
	$(CC) -c  $< -o $@ $(CPPFLAGS) $(IFLAGS)

ALLOBJS = $(COBJS) $(CPPOBJS)

none:
	$(MAKE) all

all:$(BIN)

$(BIN): $(ALLOBJS)
	$(CC) -o $(BIN) $(ALLOBJS) $(LIBS)
	#ar cr $(BIN) $(ALLOBJS)
	 cp -rv $(BIN) $(BIN_DIR)

#libsdk:
#	$(MAKE) sdk_lib SUBDIRS="sdkmain unicom" PFLAG="linux-lib"

install: $(BIN)
	sudo cp -rv $(BIN) $(BIN_DIR)

clean:
	-rm -rf $(ALLOBJS)
	-rm -rf $(BIN)