// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#include <odb/pre.hxx>

#include "detection_point-odb.hxx"

#include <cassert>
#include <cstring>  // std::memcpy


#include <odb/mysql/traits.hxx>
#include <odb/mysql/database.hxx>
#include <odb/mysql/transaction.hxx>
#include <odb/mysql/connection.hxx>
#include <odb/mysql/statement.hxx>
#include <odb/mysql/statement-cache.hxx>
#include <odb/mysql/simple-object-statements.hxx>
#include <odb/mysql/container-statements.hxx>
#include <odb/mysql/exceptions.hxx>
#include <odb/mysql/simple-object-result.hxx>
#include <odb/mysql/enum.hxx>

namespace odb
{
  // DetectionPoint
  //

  struct access::object_traits_impl< ::db::DetectionPoint, id_mysql >::extra_statement_cache_type
  {
    extra_statement_cache_type (
      mysql::connection&,
      image_type&,
      id_image_type&,
      mysql::binding&,
      mysql::binding&)
    {
    }
  };

  access::object_traits_impl< ::db::DetectionPoint, id_mysql >::id_type
  access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  id (const id_image_type& i)
  {
    mysql::database* db (0);
    ODB_POTENTIALLY_UNUSED (db);

    id_type id;
    {
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        id,
        i.id_value,
        i.id_null);
    }

    return id;
  }

  access::object_traits_impl< ::db::DetectionPoint, id_mysql >::id_type
  access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  id (const image_type& i)
  {
    mysql::database* db (0);
    ODB_POTENTIALLY_UNUSED (db);

    id_type id;
    {
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        id,
        i.id_value,
        i.id_null);
    }

    return id;
  }

  bool access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  grow (image_type& i,
        my_bool* t)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (t);

    bool grew (false);

    // id
    //
    t[0UL] = 0;

    // monitorId
    //
    t[1UL] = 0;

    // detectionPointName
    //
    if (t[2UL])
    {
      i.detectionPointName_value.capacity (i.detectionPointName_size);
      grew = true;
    }

    // videoId
    //
    t[3UL] = 0;

    // projectId
    //
    t[4UL] = 0;

    // streamId
    //
    t[5UL] = 0;

    // isRoll
    //
    t[6UL] = 0;

    // isDel
    //
    t[7UL] = 0;

    // isEnable
    //
    t[8UL] = 0;

    // bindVideoId
    //
    t[9UL] = 0;

    return grew;
  }

  void access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  bind (MYSQL_BIND* b,
        image_type& i,
        mysql::statement_kind sk)
  {
    ODB_POTENTIALLY_UNUSED (sk);

    using namespace mysql;

    std::size_t n (0);

    // id
    //
    if (sk != statement_update)
    {
      b[n].buffer_type = MYSQL_TYPE_LONGLONG;
      b[n].is_unsigned = 1;
      b[n].buffer = &i.id_value;
      b[n].is_null = &i.id_null;
      n++;
    }

    // monitorId
    //
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.monitorId_value;
    b[n].is_null = &i.monitorId_null;
    n++;

    // detectionPointName
    //
    b[n].buffer_type = MYSQL_TYPE_STRING;
    b[n].buffer = i.detectionPointName_value.data ();
    b[n].buffer_length = static_cast<unsigned long> (
      i.detectionPointName_value.capacity ());
    b[n].length = &i.detectionPointName_size;
    b[n].is_null = &i.detectionPointName_null;
    n++;

    // videoId
    //
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.videoId_value;
    b[n].is_null = &i.videoId_null;
    n++;

    // projectId
    //
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.projectId_value;
    b[n].is_null = &i.projectId_null;
    n++;

    // streamId
    //
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.streamId_value;
    b[n].is_null = &i.streamId_null;
    n++;

    // isRoll
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.isRoll_value;
    b[n].is_null = &i.isRoll_null;
    n++;

    // isDel
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.isDel_value;
    b[n].is_null = &i.isDel_null;
    n++;

    // isEnable
    //
    b[n].buffer_type = MYSQL_TYPE_LONG;
    b[n].is_unsigned = 0;
    b[n].buffer = &i.isEnable_value;
    b[n].is_null = &i.isEnable_null;
    n++;

    // bindVideoId
    //
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.bindVideoId_value;
    b[n].is_null = &i.bindVideoId_null;
    n++;
  }

  void access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  bind (MYSQL_BIND* b, id_image_type& i)
  {
    std::size_t n (0);
    b[n].buffer_type = MYSQL_TYPE_LONGLONG;
    b[n].is_unsigned = 1;
    b[n].buffer = &i.id_value;
    b[n].is_null = &i.id_null;
  }

  bool access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  init (image_type& i,
        const object_type& o,
        mysql::statement_kind sk)
  {
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (sk);

    using namespace mysql;

    bool grew (false);

    // id
    //
    if (sk == statement_insert)
    {
      long unsigned int const& v =
        o.id;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.id_value, is_null, v);
      i.id_null = is_null;
    }

    // monitorId
    //
    {
      long unsigned int const& v =
        o.monitorId;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.monitorId_value, is_null, v);
      i.monitorId_null = is_null;
    }

    // detectionPointName
    //
    {
      ::std::string const& v =
        o.detectionPointName;

      bool is_null (false);
      std::size_t size (0);
      std::size_t cap (i.detectionPointName_value.capacity ());
      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_image (
        i.detectionPointName_value,
        size,
        is_null,
        v);
      i.detectionPointName_null = is_null;
      i.detectionPointName_size = static_cast<unsigned long> (size);
      grew = grew || (cap != i.detectionPointName_value.capacity ());
    }

    // videoId
    //
    {
      long unsigned int const& v =
        o.videoId;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.videoId_value, is_null, v);
      i.videoId_null = is_null;
    }

    // projectId
    //
    {
      long unsigned int const& v =
        o.projectId;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.projectId_value, is_null, v);
      i.projectId_null = is_null;
    }

    // streamId
    //
    {
      long unsigned int const& v =
        o.streamId;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.streamId_value, is_null, v);
      i.streamId_null = is_null;
    }

    // isRoll
    //
    {
      bool const& v =
        o.isRoll;

      bool is_null (false);
      mysql::value_traits<
          bool,
          mysql::id_long >::set_image (
        i.isRoll_value, is_null, v);
      i.isRoll_null = is_null;
    }

    // isDel
    //
    {
      bool const& v =
        o.isDel;

      bool is_null (false);
      mysql::value_traits<
          bool,
          mysql::id_long >::set_image (
        i.isDel_value, is_null, v);
      i.isDel_null = is_null;
    }

    // isEnable
    //
    {
      bool const& v =
        o.isEnable;

      bool is_null (false);
      mysql::value_traits<
          bool,
          mysql::id_long >::set_image (
        i.isEnable_value, is_null, v);
      i.isEnable_null = is_null;
    }

    // bindVideoId
    //
    {
      long unsigned int const& v =
        o.bindVideoId;

      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.bindVideoId_value, is_null, v);
      i.bindVideoId_null = is_null;
    }

    return grew;
  }

  void access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  init (object_type& o,
        const image_type& i,
        database* db)
  {
    ODB_POTENTIALLY_UNUSED (o);
    ODB_POTENTIALLY_UNUSED (i);
    ODB_POTENTIALLY_UNUSED (db);

    // id
    //
    {
      long unsigned int& v =
        o.id;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.id_value,
        i.id_null);
    }

    // monitorId
    //
    {
      long unsigned int& v =
        o.monitorId;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.monitorId_value,
        i.monitorId_null);
    }

    // detectionPointName
    //
    {
      ::std::string& v =
        o.detectionPointName;

      mysql::value_traits<
          ::std::string,
          mysql::id_string >::set_value (
        v,
        i.detectionPointName_value,
        i.detectionPointName_size,
        i.detectionPointName_null);
    }

    // videoId
    //
    {
      long unsigned int& v =
        o.videoId;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.videoId_value,
        i.videoId_null);
    }

    // projectId
    //
    {
      long unsigned int& v =
        o.projectId;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.projectId_value,
        i.projectId_null);
    }

    // streamId
    //
    {
      long unsigned int& v =
        o.streamId;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.streamId_value,
        i.streamId_null);
    }

    // isRoll
    //
    {
      bool& v =
        o.isRoll;

      mysql::value_traits<
          bool,
          mysql::id_long >::set_value (
        v,
        i.isRoll_value,
        i.isRoll_null);
    }

    // isDel
    //
    {
      bool& v =
        o.isDel;

      mysql::value_traits<
          bool,
          mysql::id_long >::set_value (
        v,
        i.isDel_value,
        i.isDel_null);
    }

    // isEnable
    //
    {
      bool& v =
        o.isEnable;

      mysql::value_traits<
          bool,
          mysql::id_long >::set_value (
        v,
        i.isEnable_value,
        i.isEnable_null);
    }

    // bindVideoId
    //
    {
      long unsigned int& v =
        o.bindVideoId;

      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_value (
        v,
        i.bindVideoId_value,
        i.bindVideoId_null);
    }
  }

  void access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  init (id_image_type& i, const id_type& id)
  {
    {
      bool is_null (false);
      mysql::value_traits<
          long unsigned int,
          mysql::id_ulonglong >::set_image (
        i.id_value, is_null, id);
      i.id_null = is_null;
    }
  }

  const char access::object_traits_impl< ::db::DetectionPoint, id_mysql >::persist_statement[] =
  "INSERT INTO `wn_detection_point` "
  "(`id`, "
  "`monitor_id`, "
  "`detection_point_name`, "
  "`video_id`, "
  "`project_id`, "
  "`stream_id`, "
  "`is_roll`, "
  "`is_del`, "
  "`is_enable`, "
  "`bind_video_id`) "
  "VALUES "
  "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

  const char access::object_traits_impl< ::db::DetectionPoint, id_mysql >::find_statement[] =
  "SELECT "
  "`wn_detection_point`.`id`, "
  "`wn_detection_point`.`monitor_id`, "
  "`wn_detection_point`.`detection_point_name`, "
  "`wn_detection_point`.`video_id`, "
  "`wn_detection_point`.`project_id`, "
  "`wn_detection_point`.`stream_id`, "
  "`wn_detection_point`.`is_roll`, "
  "`wn_detection_point`.`is_del`, "
  "`wn_detection_point`.`is_enable`, "
  "`wn_detection_point`.`bind_video_id` "
  "FROM `wn_detection_point` "
  "WHERE `wn_detection_point`.`id`=?";

  const char access::object_traits_impl< ::db::DetectionPoint, id_mysql >::update_statement[] =
  "UPDATE `wn_detection_point` "
  "SET "
  "`monitor_id`=?, "
  "`detection_point_name`=?, "
  "`video_id`=?, "
  "`project_id`=?, "
  "`stream_id`=?, "
  "`is_roll`=?, "
  "`is_del`=?, "
  "`is_enable`=?, "
  "`bind_video_id`=? "
  "WHERE `id`=?";

  const char access::object_traits_impl< ::db::DetectionPoint, id_mysql >::erase_statement[] =
  "DELETE FROM `wn_detection_point` "
  "WHERE `id`=?";

  const char access::object_traits_impl< ::db::DetectionPoint, id_mysql >::query_statement[] =
  "SELECT "
  "`wn_detection_point`.`id`, "
  "`wn_detection_point`.`monitor_id`, "
  "`wn_detection_point`.`detection_point_name`, "
  "`wn_detection_point`.`video_id`, "
  "`wn_detection_point`.`project_id`, "
  "`wn_detection_point`.`stream_id`, "
  "`wn_detection_point`.`is_roll`, "
  "`wn_detection_point`.`is_del`, "
  "`wn_detection_point`.`is_enable`, "
  "`wn_detection_point`.`bind_video_id` "
  "FROM `wn_detection_point`";

  const char access::object_traits_impl< ::db::DetectionPoint, id_mysql >::erase_query_statement[] =
  "DELETE FROM `wn_detection_point`";

  const char access::object_traits_impl< ::db::DetectionPoint, id_mysql >::table_name[] =
  "`wn_detection_point`";

  void access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  persist (database& db, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    callback (db,
              static_cast<const object_type&> (obj),
              callback_event::pre_persist);

    image_type& im (sts.image ());
    binding& imb (sts.insert_image_binding ());

    if (init (im, obj, statement_insert))
      im.version++;

    im.id_value = 0;

    if (im.version != sts.insert_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_insert);
      sts.insert_image_version (im.version);
      imb.version++;
    }

    {
      id_image_type& i (sts.id_image ());
      binding& b (sts.id_image_binding ());
      if (i.version != sts.id_image_version () || b.version == 0)
      {
        bind (b.bind, i);
        sts.id_image_version (i.version);
        b.version++;
      }
    }

    insert_statement& st (sts.persist_statement ());
    if (!st.execute ())
      throw object_already_persistent ();

    obj.id = id (sts.id_image ());

    callback (db,
              static_cast<const object_type&> (obj),
              callback_event::post_persist);
  }

  void access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  update (database& db, const object_type& obj)
  {
    ODB_POTENTIALLY_UNUSED (db);

    using namespace mysql;
    using mysql::update_statement;

    callback (db, obj, callback_event::pre_update);

    mysql::transaction& tr (mysql::transaction::current ());
    mysql::connection& conn (tr.connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    id_image_type& idi (sts.id_image ());
    init (idi, id (obj));

    image_type& im (sts.image ());
    if (init (im, obj, statement_update))
      im.version++;

    bool u (false);
    binding& imb (sts.update_image_binding ());
    if (im.version != sts.update_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_update);
      sts.update_image_version (im.version);
      imb.version++;
      u = true;
    }

    binding& idb (sts.id_image_binding ());
    if (idi.version != sts.update_id_image_version () ||
        idb.version == 0)
    {
      if (idi.version != sts.id_image_version () ||
          idb.version == 0)
      {
        bind (idb.bind, idi);
        sts.id_image_version (idi.version);
        idb.version++;
      }

      sts.update_id_image_version (idi.version);

      if (!u)
        imb.version++;
    }

    update_statement& st (sts.update_statement ());
    if (st.execute () == 0)
      throw object_not_persistent ();

    callback (db, obj, callback_event::post_update);
    pointer_cache_traits::update (db, obj);
  }

  void access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  erase (database& db, const id_type& id)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    id_image_type& i (sts.id_image ());
    init (i, id);

    binding& idb (sts.id_image_binding ());
    if (i.version != sts.id_image_version () || idb.version == 0)
    {
      bind (idb.bind, i);
      sts.id_image_version (i.version);
      idb.version++;
    }

    if (sts.erase_statement ().execute () != 1)
      throw object_not_persistent ();

    pointer_cache_traits::erase (db, id);
  }

  access::object_traits_impl< ::db::DetectionPoint, id_mysql >::pointer_type
  access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  find (database& db, const id_type& id)
  {
    using namespace mysql;

    {
      pointer_type p (pointer_cache_traits::find (db, id));

      if (!pointer_traits::null_ptr (p))
        return p;
    }

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);

    if (l.locked ())
    {
      if (!find_ (sts, &id))
        return pointer_type ();
    }

    pointer_type p (
      access::object_factory<object_type, pointer_type>::create ());
    pointer_traits::guard pg (p);

    pointer_cache_traits::insert_guard ig (
      pointer_cache_traits::insert (db, id, p));

    object_type& obj (pointer_traits::get_ref (p));

    if (l.locked ())
    {
      select_statement& st (sts.find_statement ());
      ODB_POTENTIALLY_UNUSED (st);

      callback (db, obj, callback_event::pre_load);
      init (obj, sts.image (), &db);
      load_ (sts, obj, false);
      sts.load_delayed (0);
      l.unlock ();
      callback (db, obj, callback_event::post_load);
      pointer_cache_traits::load (ig.position ());
    }
    else
      sts.delay_load (id, obj, ig.position ());

    ig.release ();
    pg.release ();
    return p;
  }

  bool access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  find (database& db, const id_type& id, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);
    assert (l.locked ()) /* Must be a top-level call. */;

    if (!find_ (sts, &id))
      return false;

    select_statement& st (sts.find_statement ());
    ODB_POTENTIALLY_UNUSED (st);

    reference_cache_traits::position_type pos (
      reference_cache_traits::insert (db, id, obj));
    reference_cache_traits::insert_guard ig (pos);

    callback (db, obj, callback_event::pre_load);
    init (obj, sts.image (), &db);
    load_ (sts, obj, false);
    sts.load_delayed (0);
    l.unlock ();
    callback (db, obj, callback_event::post_load);
    reference_cache_traits::load (pos);
    ig.release ();
    return true;
  }

  bool access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  reload (database& db, object_type& obj)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));
    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    statements_type::auto_lock l (sts);
    assert (l.locked ()) /* Must be a top-level call. */;

    const id_type& id (object_traits_impl::id (obj));
    if (!find_ (sts, &id))
      return false;

    select_statement& st (sts.find_statement ());
    ODB_POTENTIALLY_UNUSED (st);

    callback (db, obj, callback_event::pre_load);
    init (obj, sts.image (), &db);
    load_ (sts, obj, true);
    sts.load_delayed (0);
    l.unlock ();
    callback (db, obj, callback_event::post_load);
    return true;
  }

  bool access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  find_ (statements_type& sts,
         const id_type* id)
  {
    using namespace mysql;

    id_image_type& i (sts.id_image ());
    init (i, *id);

    binding& idb (sts.id_image_binding ());
    if (i.version != sts.id_image_version () || idb.version == 0)
    {
      bind (idb.bind, i);
      sts.id_image_version (i.version);
      idb.version++;
    }

    image_type& im (sts.image ());
    binding& imb (sts.select_image_binding ());

    if (im.version != sts.select_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_select);
      sts.select_image_version (im.version);
      imb.version++;
    }

    select_statement& st (sts.find_statement ());

    st.execute ();
    auto_result ar (st);
    select_statement::result r (st.fetch ());

    if (r == select_statement::truncated)
    {
      if (grow (im, sts.select_image_truncated ()))
        im.version++;

      if (im.version != sts.select_image_version ())
      {
        bind (imb.bind, im, statement_select);
        sts.select_image_version (im.version);
        imb.version++;
        st.refetch ();
      }
    }

    return r != select_statement::no_data;
  }

  result< access::object_traits_impl< ::db::DetectionPoint, id_mysql >::object_type >
  access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  query (database& db, const query_base_type& q)
  {
    using namespace mysql;
    using odb::details::shared;
    using odb::details::shared_ptr;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));

    statements_type& sts (
      conn.statement_cache ().find_object<object_type> ());

    image_type& im (sts.image ());
    binding& imb (sts.select_image_binding ());

    if (im.version != sts.select_image_version () ||
        imb.version == 0)
    {
      bind (imb.bind, im, statement_select);
      sts.select_image_version (im.version);
      imb.version++;
    }

    std::string text (query_statement);
    if (!q.empty ())
    {
      text += " ";
      text += q.clause ();
    }

    q.init_parameters ();
    shared_ptr<select_statement> st (
      new (shared) select_statement (
        conn,
        text,
        false,
        true,
        q.parameters_binding (),
        imb));

    st->execute ();

    shared_ptr< odb::object_result_impl<object_type> > r (
      new (shared) mysql::object_result_impl<object_type> (
        q, st, sts, 0));

    return result<object_type> (r);
  }

  unsigned long long access::object_traits_impl< ::db::DetectionPoint, id_mysql >::
  erase_query (database& db, const query_base_type& q)
  {
    using namespace mysql;

    mysql::connection& conn (
      mysql::transaction::current ().connection (db));

    std::string text (erase_query_statement);
    if (!q.empty ())
    {
      text += ' ';
      text += q.clause ();
    }

    q.init_parameters ();
    delete_statement st (
      conn,
      text,
      q.parameters_binding ());

    return st.execute ();
  }
}

#include <odb/post.hxx>
