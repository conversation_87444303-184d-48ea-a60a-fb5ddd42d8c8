/**
 * Project FVM
 */
#include "front_platform.h"
#include "util/worker_pool.h"
#include "ailog.h"
#include "util/config/fvm_config.h"
#include "data/config_param_info.h"

/**
 * FrontPlatform implementation
 * 
 * 前端平台
 */

namespace fvm::platform
{
    void FrontPlatform::init(data::VideoServerPtr svrPtr) {
        this->serverPtr = svrPtr;
    }

    void FrontPlatform::startPlay(VideoSourceInfoPtr videoSourceInfo, const std::string& destAddr) {
        if (videoSourceInfo == nullptr || destIp == "" || destPort == 0 || serverPtr == nullptr)
            return;
        RequestVideo msg;
        msg.iRemoteId = serverPtr->getId();
        msg.returnAddr = DATA_MANAGER.getLocalIP() + ":" + std::to_string(DATA_MANAGER.getFvmPort());
        msg.iVideoId = videoSourceInfo->videoSourcePtr->getId();
        msg.videoKey = videoSourceInfo->videoSourcePtr->getAddress();
        msg.iforceRequest = 1;
        msg.transferType = getTransferName();
        msg.streamType = (int)streamType;
        msg.destAddr = destAddr;

        PROTOCOL_MANAGER.sendTo(destIp, destPort, network::ProtocolType::UDP_REQUEST_VIDEO, msg);
    }

    void FrontPlatform::stopPlay(VideoSourceInfoPtr videoSourceInfo, const std::string& destAddr) {
        if (videoSourceInfo == nullptr || destIp == "" || destPort == 0 || serverPtr == nullptr)
            return;
        RequestStopVideo msg;
        msg.iRemoteId = serverPtr->getId();
        msg.returnAddr = DATA_MANAGER.getLocalIP() + ":" + std::to_string(DATA_MANAGER.getFvmPort());
        msg.iVideoId = videoSourceInfo->videoSourcePtr->getId();
        msg.videoKey = videoSourceInfo->videoSourcePtr->getAddress();
        msg.transferType = getTransferName();
        msg.streamType = (int)streamType;
        msg.destAddr = destAddr;

        PROTOCOL_MANAGER.sendTo(destIp, destPort, network::ProtocolType::UDP_STOPVIDEO, msg);
    }

    void FrontPlatform::onResourceUpdated() {

    }

    void FrontPlatform::onStateChanged() {

    }

    std::string FrontPlatform::getTransferName() {
        std::string name = "UDP";
        switch (transferType)
        {
        case TransferType::TCPPASSIVE:
            name = "TCP PASSIVE";
            break;
        case TransferType::TCPACTIVE:
            name = "TCP ACTIVE";
            break;
        case TransferType::RTMP:
            name = "RTMP";
            break;
        case TransferType::UDP:
        default:
            name = "UDP";
            break;
        }
        return name;
    }

    void FrontPlatform::onChanged() {
        if ( destIp == "" || destPort == 0 || serverPtr == nullptr )
            return;
        RequestRemoteChanged msg;
        msg.iRemoteId = serverPtr->getId();
        PROTOCOL_MANAGER.sendTo(destIp, destPort, network::ProtocolType::UDP_REMOTECHANGED, msg );
    }

    bool FrontPlatform::callPreset(VideoSourceInfoPtr videoSourceInfo, int ptzId ) {
        if ( videoSourceInfo == nullptr || destIp == "" || destPort == 0|| serverPtr == nullptr )
            return false;
        RequestPtzOper msg;
        msg.iRemoteId = serverPtr->getId();
        msg.returnAddr = DATA_MANAGER.getLocalIP()+":"+std::to_string(DATA_MANAGER.getFvmPort());
        msg.iVideoId = videoSourceInfo->videoSourcePtr->getId();
        msg.videoKey = videoSourceInfo->videoSourcePtr->getAddress();
        msg.ptzCommand = EPTZCOMMAND_SWITCH;
        msg.arg1 = ptzId;
        msg.arg2 = 0;
        PROTOCOL_MANAGER.sendTo(destIp, destPort, network::ProtocolType::UDP_PTZOPER, msg );
        return true;
    }

    bool FrontPlatform::savePreset(VideoSourceInfoPtr videoSourceInfo, int ptzId) {
        if (videoSourceInfo == nullptr || destIp == "" || destPort == 0 || serverPtr == nullptr)
            return false;
        RequestPtzOper msg;
        msg.iRemoteId = serverPtr->getId();
        msg.returnAddr = DATA_MANAGER.getLocalIP() + ":" + std::to_string(DATA_MANAGER.getFvmPort());
        msg.iVideoId = videoSourceInfo->videoSourcePtr->getId();
        msg.videoKey = videoSourceInfo->videoSourcePtr->getAddress();
        msg.ptzCommand = EPTZCOMMAND_DEL;
        msg.arg1 = ptzId;
        msg.arg2 = 0;
        PROTOCOL_MANAGER.sendTo(destIp, destPort, network::ProtocolType::UDP_PTZOPER, msg);

        msg.ptzCommand = EPTZCOMMAND_ADD;
        PROTOCOL_MANAGER.sendTo(destIp, destPort, network::ProtocolType::UDP_PTZOPER, msg);

        return true;
    }

    bool FrontPlatform::controlPtz(VideoSourceInfoPtr videoSourceInfo, network::EPtzCommand cmd, int step) {
        if ( videoSourceInfo == nullptr || destIp == "" || destPort == 0|| serverPtr == nullptr )
            return false;
        ai::LogInfo << "cmd: " << cmd << " step:"<<step;
       
        RequestPtzOper msg;
        msg.iRemoteId = serverPtr->getId();
        msg.returnAddr = DATA_MANAGER.getLocalIP()+":"+ std::to_string(DATA_MANAGER.getFvmPort());
        msg.iVideoId = videoSourceInfo->videoSourcePtr->getId();
        msg.videoKey = videoSourceInfo->videoSourcePtr->getAddress();
        msg.ptzCommand = cmd;
        msg.arg1 = step;
        msg.arg2 = 0;
        PROTOCOL_MANAGER.sendTo(destIp, destPort, network::ProtocolType::UDP_PTZOPER, msg );
        return true;
    }

    network::EPtzCommand FrontPlatform::getPtzStopCmd(network::EPtzCommand cmd)
    {
        if(cmd == network::EPTZCOMMAND_DOWN || cmd == network::EPTZCOMMAND_LEFT || cmd == network::EPTZCOMMAND_RIGHT || cmd == network::EPTZCOMMAND_UP)
            return network::EPTZCOMMAND_STOP;
        if(cmd == network::EPTZCOMMAND_FOCUSNEAR || cmd == network::EPTZCOMMAND_FOCUSFAR)
            return network::EPTZCOMMAND_FOCUSSTOP;
        
        return network::EPTZCOMMAND_STOP;
    }

    bool FrontPlatform::doFocusEvent(VideoSourceInfoPtr videoSourceInfo)
    {
        std::unique_lock<std::mutex> lck(mutexFocus);
        if (eventFocusList.empty())
        {
            this->eventFocusing = false;
            return false;
        }

        auto focusInfo = eventFocusList.front();
        eventFocusList.pop_front();
        lck.unlock();

        ai::LogInfo << "focus event :" << " xTop:" << focusInfo.xTop << " yTop:" 
            << focusInfo.yTop << " xBottom:" << focusInfo.xBottom << " yBottom:" << focusInfo.yBottom;

        // 调用 球机 预置位
        this->callPreset(videoSourceInfo, focusInfo.presetId);
        // 等待一段时间(预置位转到位置)
        auto focusWaitTime = DATA_MANAGER.getParamProgData(fvm::data::DOME_PTZ_TIME, 5);
        WORKER_SLEEP(std::chrono::seconds(focusWaitTime));

        // 调用 球机 focus接口
        this->focusRect(videoSourceInfo, focusInfo.xTop, focusInfo.yTop, focusInfo.xBottom, focusInfo.yBottom);

        lck.lock();
        if(eventFocusList.empty())
            this->eventFocusing = false;

        return !eventFocusList.empty();
    }

    // （新增）聚焦事件
    bool FrontPlatform::focusEvent(VideoSourceInfoPtr videoSourceInfo, EventFocusInfo focusInfo, bool ignoreIfFocusing)
    {
        std::unique_lock<std::mutex> lck(mutexFocus);
        if (ignoreIfFocusing && this->eventFocusing)
        {
            ai::LogInfo << "Camera in focusing, ignored!";
            return false;
        }
        eventFocusList.push_back(focusInfo);
		lck.unlock();

        auto focusInterval = DATA_MANAGER.getParamProgData(fvm::data::DOME_EVENT_INTERVAL, 2);
        if (!this->eventFocusing)
        {
            this->eventFocusing = true;
            auto capturedSelf = weak_from_this();
            worker::post(worker::WorkerType::Device, [capturedSelf, videoSourceInfo, focusInterval]()
                {
                    while (auto self = capturedSelf.lock())
                    {
                        if (self->doFocusEvent(videoSourceInfo))
                            WORKER_SLEEP(std::chrono::seconds(focusInterval));
                        else
                            break;
                    }
                });
        }
        return true;
    }
}