/**
 * Project FVM
 */

#include "detect_channel.h"
#include "protocol/protocol_sender.h"
#include "ailog.h"

/**
 * DetectChannel implementation
 * @brief: 检测通道
 *          主要控制预置位、轮切、时间方案等
 */
namespace fvm::stream
{
    using namespace data;
    using namespace timer;
    /**
     * 初始化输入器
     * @param StreamInput
     */
    void DetectChannel::initInput(StreamInputPtr input)
    {
        if(input == nullptr) return;
        StreamPipe::initInput(input);
        channelID = (int)this->inputStream->getVideoSource()->channelId;
        this->inputStream->onStreamCodecInfoRetrieved.connect([this](auto codec) {
                    this->codecInfo = std::move(codec);
                    this->restoreDetect(StreamRestoreReason::Init);
                    this->onStreamCodecInfoRetrieved(codec);});

        this->inputStream->onCameraPresetCalled.connect([this]() {streamRestored(StreamRestoreReason::Init);});
        this->inputStream->onStreamInputLost.connect([this](){
            this->pauseDetect(StreamPauseReason::Init);
            this->onStreamInputLost();});

        this->onNotifyPtzCheck.connect([this](){ this->inputStream->notifyCheckPTZPresetThread();});
        this->inputStream->onPTZPresetChecked.connect([this](auto time, auto isPresetChange){
            if (isPresetChange){
                if (PTZPresetStatus::Offset == this->getPtzPresetStatus()){   //!< 检测到偏移，将状态改为pause。检测到偏移恢复，web会发送VideoResume，这里不做Restore分支处理
                    this->pauseDetect(StreamPauseReason::OffsetEvent);
                }
            }
            this->onPTZPresetChecked(time, isPresetChange);
        });

        this->onTimeProgramsTimerExpired.connect([this](){this->inputStream->switchTimeProgram();});
        configAutoPtzTimer(true);
    }


    void DetectChannel::streamRestored(StreamRestoreReason reason)
    {
        detectState = ChannelDetectState::Detecting;
        //if (reason == StreamRestoreReason::OffsetRestore)
        //    protocol::sendChannelDetectParam(channelID);  //!< iva通道重建，防止历史缓存事件的影响
        onChannelStreamRestored(reason);

        if (restorePtzTimer)     //!< 归位后，如果存在超时归位的定时器，则移除
        {
            TIMER_MANAGER.removeTimer(restorePtzTimer);
            restorePtzTimer = nullptr;

            std::vector<int> restoreTypes{ (int)VideoQuaAlarmType::VIDEO_QUA_AUTO_RESET };
            auto channelSource = inputStream->getVideoSource();
            if (channelSource)
            {
                auto videoId = channelSource->videoSourcePtr->getId();
                int presetId = DATA_MANAGER.getCurrPresetId(channelSource);
                VideoQuaRecovery videoQuaRecovery{ videoId, presetId, DATA_MANAGER.getLocalIP(), restoreTypes };
                PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_VIDEO_QUARECOVERY, videoQuaRecovery, true);
            }
        }
    }

    /**
     * 管道恢复检测: 转动预置位后恢复检测
     * @param reason
     */
    void DetectChannel::restoreDetect(StreamRestoreReason reason)
    {
        //! 通道是否可以恢复检测 (1、状态为RestoreDetectable 2、在偏移不停检状态下，如果云台改为不可控，或者自动偏移归位时间配置为0,否则使用自动归位逻辑进行归位)
        auto detectable = DATA_MANAGER.queryChannelDetectable(channelID);
        if ((data::ChannelDetectable::RestoreDetectable == detectable)
        || ((data::ChannelDetectable::OffsetDetectable == detectable) && (!inputStream->isPtzCapable() || (autoPtzTime == 0))) )
        {
            //! 文件流不存在或者已离线
            if (!inputStream->isPlaying())
                return;

            //! 当前通道没有预置位
            auto preset = DATA_MANAGER.getCurrPreset(inputStream->getVideoSource());
            if (!preset)
            {
                ai::LogWarn << "channel:"<< channelID << " no preset";
                return;
            }
            //! 当前预置位没有配置检测区或感兴趣区
            if (!DATA_MANAGER.hasCheckAreaRoi(inputStream->getVideoSource(), (int)preset->getId()))
            {
                return;
            }

            //! 发送restore消息，不转动预置位 (1、流类型不可控平台 2、偏移恢复)
            if (!inputStream->isPtzCapable() || (reason == StreamRestoreReason::OffsetRestore))
            {
                inputStream->setCurrentPresetId((int)preset->getId());
                streamRestored(reason);
            }
            else
            {
                pauseDetect(StreamPauseReason::Init);
                inputStream->asyncCallCameraPreset((int)preset->getId(), (int)preset->getActPreset());
            }
        }
    }

    /**
     * 配置偏移自动归位定时器
     * @param init 是否第一次初始化
     */
    void DetectChannel::configAutoPtzTimer(bool init)
    {
        if (inputStream->isPtzCapable())
        {
            auto currAutoPtzTime = DATA_MANAGER.getParamProgData(WAIT_DEVIATION,0);
            if (restorePtzTimer)
            {
                if ((autoPtzTime != currAutoPtzTime) || (currAutoPtzTime == 0)) //!< 时间配置发生变化，则移除定时器
                {
                    TIMER_MANAGER.removeTimer(restorePtzTimer);
                    restorePtzTimer = nullptr;
                }
            }
            autoPtzTime = currAutoPtzTime;

            bool needAutoPtz = true;
            if (init)  //! 第一次初始化 从数据库读取偏移状态，其他情况并未立即写库(默认已处于偏移情况)
            {
                auto detectable = DATA_MANAGER.queryChannelDetectable(channelID);
                if (data::ChannelDetectable::OffsetPaused != detectable && data::ChannelDetectable::OffsetDetectable != detectable)
                {
                    needAutoPtz = false;
                }
            }

            if (needAutoPtz)
            {
                if ((!restorePtzTimer) && (currAutoPtzTime > 0)) //!< 偏移情况下，自动归位时间配置且大于0，则添加定时
                {
                    ai::LogInfo << "add auto ptz timer: " << currAutoPtzTime << " channel: " << channelID;
                    restorePtzTimer = TIMER_MANAGER.addTimer(std::chrono::minutes(autoPtzTime), [this](auto timerPtr){
                        worker::post(worker::WorkerType::Channel, [this](){this->autoRestorePtz();});});
                }
            }
        }
    }
    /**
     * 管道暂停检测
     * @param reason
     */
    void DetectChannel::pauseDetect(StreamPauseReason reason, bool notify)
    {
        switch (reason)
        {
            case StreamPauseReason::Init:
                break;
            case StreamPauseReason::Manual:
                break;
            case StreamPauseReason::SetDetectArea:
                break;
            case StreamPauseReason::OffsetEvent:
                configAutoPtzTimer();
                break;
            default:
                break;
        }
        detectState = ChannelDetectState::Paused;
        if (notify)
            this->onChannelStreamPaused(reason);
    }


    void DetectChannel::stopPipe(bool waitFinish, bool dispose)
    {
        if (restorePtzTimer)
        {
            TIMER_MANAGER.removeTimer(restorePtzTimer);
            restorePtzTimer = nullptr;
        }
        StreamPipe::stopPipe(waitFinish, dispose);
    }

    /**
     * 偏移自动归位
     */
    void DetectChannel::autoRestorePtz()
    {
        ai::LogInfo << "[auto restore ptz] start channel:" << channelID;
        //! 文件流已不存在或已离线
        if (!inputStream || !inputStream->isPlaying())
        {
            ai::LogInfo << "[auto restore ptz] input stream not playing, channel:" << channelID;
            return;
        }

        //! 当前通道已没有预置位
        auto preset = DATA_MANAGER.getCurrPreset(inputStream->getVideoSource());
        if (!preset)
        {
            ai::LogWarn << "[auto restore ptz] channel:"<< channelID << " no preset";
            return;
        }

        //! 当前预置位已没有配置检测区或感兴趣区
        if (!DATA_MANAGER.hasCheckAreaRoi(inputStream->getVideoSource(), (int)preset->getId()))
        {
            ai::LogInfo << "[auto restore ptz] channel:" << channelID << " no such preset: " << (int)preset->getId();
            return;
        }

        //! 云台已经归位了
        if (detectState == ChannelDetectState::Detecting)
        {
            ai::LogInfo << "[auto restore ptz] channel:" << channelID << " already detecting";
            return;
        }

        //! 被改为不可控平台
        if (!inputStream->isPtzCapable())
        {
            ai::LogInfo << "[auto restore ptz] direct restore:" << channelID;
            inputStream->setCurrentPresetId((int)preset->getId());
            streamRestored(StreamRestoreReason::OffsetRestore);
        }
        else
        {
            ai::LogInfo << "[auto restore ptz] async call preset for channel:" << channelID;
            inputStream->asyncCallCameraPreset((int)preset->getId(), (int)preset->getActPreset());
        }
    }


    /**
     * 获取视频编码基本信息
     */
    CodecInfoPtr DetectChannel::getCodecInfo()
    {
        if (this->codecInfo)
            return this->codecInfo;
        else
        {
            std::cerr << "No Codec information, channel:" << channelID << std::endl;
            return nullptr;
        }
    }

    /**
     * 检查管道的分配关系以及资源配置是否发生变化
     */
    bool DetectChannel::isInputChanged()
    {
        auto videoSourceInfoPtr = this->inputStream->getVideoSource();
        //! 当前通道的资源已经不存在
        if ((nullptr == videoSourceInfoPtr) || (nullptr == videoSourceInfoPtr->videoSourcePtr))
        {
            return true;
        }

        //! 当前通道的资源存在，但web上已经取消分配了该通道
        int channelId = videoSourceInfoPtr->channelId;
        auto newChannelSource = DATA_MANAGER.getChannelSource(channelId);
        if (!newChannelSource.has_value() || nullptr == newChannelSource.value()->videoSourcePtr)
        {
            return true;
        }

        //! 历史通道资源还在，且web上也存在该通道的配置，则检查通道信息是否变化
        auto newVideoId = newChannelSource.value()->videoSourcePtr->getId();
        auto oldVideoId = videoSourceInfoPtr->videoSourcePtr->getId();
        if ((newVideoId != oldVideoId) || (newChannelSource.value()->videoSourcePtr->getIsChange()))
        {
            return true;
        }

        if (inputStream->isPtzCapable())
        {
            //! 自动归位定时器时间配置发生变化 （WAIT_DEVIATION值是否大于0，来判断是否需要自动归位）
            auto newAutoPtzTime = DATA_MANAGER.getParamProgData(WAIT_DEVIATION,0);
            if (autoPtzTime != newAutoPtzTime)
                return true;
        }

        //! 偏移判断时间配置发生变化 （FVM_OFFSET_WAIT_TIME值是否大于0，来判断是否需要创建偏移判断线程）
        auto oldOffsetWaitTime = inputStream->getOffsetWaitTime();
        auto newOffsetWaitTime = DATA_MANAGER.getParamProgData(FVM_OFFSET_WAIT_TIME,0);
        if (oldOffsetWaitTime != newOffsetWaitTime)
            return true;

        return false;
    }

    /**
     * 通过onvif获取输入流ptz的偏移状态，如果onvif没有能力判断偏移则返回None
     */
    PTZPresetStatus DetectChannel::getPtzPresetStatus()
    {
        if (inputStream)
            return inputStream->getPTZPresetInfo().ptzPresetStatus;
        else
            return PTZPresetStatus::None;
    }



}