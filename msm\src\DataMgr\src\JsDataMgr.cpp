#if defined( WIN32 )
#   include <WinSock2.h>
#else
#   include <arpa/inet.h>
#endif
#if defined( Linux )
#   include <sys/socket.h>
#endif

#include "wtoe/BasicHelper/VcWarningOff.hpp"
#include "wtoe/BasicHelper/BasicHelperExp.hpp"
#include "wtoe/PackageManager/PackageManagerExp.hpp"
#include "wtoe/JavaScriptEngine/JavaScriptEngineExp.hpp"
#include "DataMgr/include/DataMgrExp.hpp"
#include "DataMgr/src/JsDataMgr.hpp"
#include "include/file_info.hpp"
#include "include/version.hpp"


namespace wtoe {

template<>
JSClass CJavaScriptAdapter<CJsDataMgr>::s_jsClass = 
{
    "dm", 
    JSCLASS_HAS_PRIVATE,
    JS_PropertyStub, // add
    JS_PropertyStub, // del
    JS_PropertyStub, // get
    JS_PropertyStub, // set
    JS_EnumerateStub, 
    JS_ResolveStub, 
    JS_ConvertStub, 
    WTOE_JS_FINALIZER( CJsDataMgr , finalize ),
    JSCLASS_NO_OPTIONAL_MEMBERS
};

template<>
JSPropertySpec CJavaScriptAdapter<CJsDataMgr>::s_jsClassProperties[] = 
{
    { 0, 0, 0, 0, 0 }
};

template<>
JSFunctionSpec CJavaScriptAdapter<CJsDataMgr>::s_jsClassMethords[] = 
{
    { "init", WTOE_JS_METHORD( CJsDataMgr, init ),     0, 0, 0 },
	{ "status", WTOE_JS_METHORD( CJsDataMgr, showStatus ),     0, 0, 0 },
	{ "ver", WTOE_JS_METHORD( CJsDataMgr, showVersion ),     0, 0, 0 },
	{ "start", WTOE_JS_METHORD( CJsDataMgr, start ),     0, 0, 0 },
	{ "stop", WTOE_JS_METHORD( CJsDataMgr, stop ),     0, 0, 0 },
	{ "fps", WTOE_JS_METHORD( CJsDataMgr, fps ),     0, 0, 0 },
	{ "debug", WTOE_JS_METHORD( CJsDataMgr, debug ),     0, 0, 0 },
	{ "level", WTOE_JS_METHORD( CJsDataMgr, level ),     0, 0, 0 },
   { 0, 0, 0, 0, 0 }
};

template<>
JSPropertySpec CJavaScriptAdapter<CJsDataMgr>::s_jsObjectProperties[] =
{
    { 0, 0, 0, 0, 0 }
};

template<>
JSFunctionSpec CJavaScriptAdapter<CJsDataMgr>::s_jsObjectMethords[] =
{
    { 0, 0, 0, 0, 0 }
};

CJsDataMgr::CJsDataMgr()
{
}

CJsDataMgr::~CJsDataMgr()
{
}

CJsDataMgr *CJsDataMgr::createObject()
{
    return 0;
}

JSBool CJsDataMgr::init( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
{
	msm::CSpIDataMgr dm;
	if ( !getMainApp()->getServiceInstance( WTOE_SNAME_DataMgr_DataMgr, dm ) )
	{
		return JS_FALSE;
	}
	std::string szIp;
	std::string szDb;

	if ( argc == 2 && JS_FALSE == JType2CType( szIp, szDb, ctx, argv ) )
		return false;
	if ( dm->init( szIp, szDb ) )
		return JS_TRUE;
	return JS_FALSE;
}

JSBool CJsDataMgr::showStatus( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
{
    msm::CSpIDataMgr dm;
    if ( !getMainApp()->getServiceInstance( WTOE_SNAME_DataMgr_DataMgr, dm ) )
    {
        return JS_FALSE;
    }
	std::string remoteId = "";
	std::string szKey = "";
	if (argc == 2 && JS_FALSE == JType2CType(remoteId, szKey, ctx, argv))
		return JS_FALSE; 
	else if (argc == 1 && JS_FALSE == JType2CType(remoteId, ctx, argv))
		return JS_FALSE;

	dm->showStatus( atoi(remoteId.c_str() ), szKey );
    return JS_TRUE;
}

JSBool CJsDataMgr::showVersion( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
{
	showVer();
	return JS_TRUE;
}

JSBool CJsDataMgr::start( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
{
	msm::CSpIDataMgr dm;
	if ( !getMainApp()->getServiceInstance( WTOE_SNAME_DataMgr_DataMgr, dm ) )
	{
		return JS_FALSE;
	}
	std::string videoId = "";
	uint32_t streamType = 0;
	if (argc == 2 && JS_FALSE == JType2CType(videoId, streamType, ctx, argv))
		return JS_FALSE; 
	else if (argc == 1 && JS_FALSE == JType2CType(videoId, ctx, argv))
		return JS_FALSE;
	std::string szSrc;
	RequestVideo msg;
	msg.iVideoId = atoi( videoId.c_str() );
	msg.iRemoteId = dm->getRemoteId( msg.iVideoId );
	msg.destAddr = "127.0.0.1";
	msg.iforceRequest = 1;
	msg.iStreamType = streamType;
	msg.returnAddr = "192.168.77.56:5110";
	int ret = dm->onRequestVideo( msg, szSrc );
	if ( ret == 0 )
		return JS_TRUE;
	return JS_FALSE;
}

JSBool CJsDataMgr::stop( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
{
	msm::CSpIDataMgr dm;
	if ( !getMainApp()->getServiceInstance( WTOE_SNAME_DataMgr_DataMgr, dm ) )
	{
		return JS_FALSE;
	}
	std::string videoId = "";
	uint32_t streamType = 0;
	if (argc == 2 && JS_FALSE == JType2CType(videoId, streamType, ctx, argv))
		return JS_FALSE; 
	else if (argc == 1 && JS_FALSE == JType2CType(videoId, ctx, argv))
		return JS_FALSE;
	if ( videoId.empty() )
		return JS_FALSE;
	std::string szSrc;
	RequestStopVideo msg;
	msg.iVideoId = atoi( videoId.c_str() );
	msg.iRemoteId = dm->getRemoteId( msg.iVideoId );
	if ( msg.iRemoteId == 0 )
		return JS_FALSE;
	msg.destAddr = "";
	msg.iStreamType = streamType;
	msg.returnAddr = "192.168.77.56:5110";
	int ret = dm->onRequestStopVideo( msg );
	if ( ret == 0 )
		return JS_TRUE;
	return JS_FALSE;
}

JSBool CJsDataMgr::fps( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
{
	msm::CSpIDataMgr dm;
	if ( !getMainApp()->getServiceInstance( WTOE_SNAME_DataMgr_DataMgr, dm ) )
	{
		return JS_FALSE;
	}
	uint32_t streamType = 0;
	uint32_t videoId = 0;
	if (argc == 2 && JS_FALSE == JType2CType(videoId, streamType, ctx, argv))
		return JS_FALSE;
	else if (argc == 1 && JS_FALSE == JType2CType(videoId, ctx, argv))
		return JS_FALSE;
	if ( videoId == 0 )
		return JS_FALSE;
	dm->showFps( videoId, streamType );
	return JS_TRUE;
}

JSBool CJsDataMgr::debug( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
{
	msm::CSpIDataMgr dm;
	if ( !getMainApp()->getServiceInstance( WTOE_SNAME_DataMgr_DataMgr, dm ) )
	{
		return JS_FALSE;
	}
	uint32_t videoId = 0;
	uint32_t dbgFunc = 0;
	uint32_t streamType = 0;
	if (argc == 3 && JS_FALSE == JType2CType(videoId, streamType, dbgFunc, ctx, argv))
		return JS_FALSE;
	else if (argc == 2 && JS_FALSE == JType2CType(videoId, dbgFunc, ctx, argv))
		return JS_FALSE;
	else if (argc == 1 && JS_FALSE == JType2CType(videoId, ctx, argv))
		return JS_FALSE;
	if ( videoId == 0 )
		return JS_FALSE;
	dm->debug( videoId, streamType, dbgFunc );
	return JS_TRUE;
}

JSBool CJsDataMgr::level( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
{
	msm::CSpIDataMgr dm;
	if ( !getMainApp()->getServiceInstance( WTOE_SNAME_DataMgr_DataMgr, dm ) )
	{
		return JS_FALSE;
	}
	uint32_t iLevel = 0;
	if (argc == 1 && JS_FALSE == JType2CType(iLevel, ctx, argv))
		return JS_FALSE;
	dm->setFfmpegLevel( iLevel );
	return JS_TRUE;
}

}

namespace msm {
/******************************************************************************/
/* javaScriptRegister                                                         */
/******************************************************************************/
bool DATAMGR_PRIVATE javaScriptRegisterDataMgr( JSContext *jsCtx, JSObject *jsObj )
{
    return wtoe::CJavaScriptAdapter<wtoe::CJsDataMgr>::registerJavaScriptClass( jsCtx, jsObj );
}

}

