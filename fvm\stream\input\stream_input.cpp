/**
 * Project FVM
 */
#include "stream_input.h"
#include "ailog.h"
#include "util/config/fvm_config.h"
#include <protocol/protocol_manager.h>
#include "protocol/all.h"
/**
 * StreamInput implementation
 * @brief: 视频输入器
 *          派生文件流输入、RTSP输入等
 */
namespace fvm {
    namespace stream {
        using namespace data;
        using namespace network;
        /**
         * 初始化视频资源信息
         * @param VideoSourceInfoPtr 视频配置信息
         */
        void StreamInput::initSource(const VideoSourceInfoPtr source) {
            if (this->isInited()) {
                setLastError("StreamInput already inited !");
                return;
            }

            this->videoSourceInfo = source;
            this->onvifPtzInfo = std::make_shared<OnvifPtz>(source );
            this->inited();

            if (this->videoSourceInfo && this->videoSourceInfo->channelId > 0)
            {
                auto capturedSelf = weak_from_this();
                presetThreadFuture = worker::post(worker::WorkerType::Channel, [capturedSelf]()
                {
                    if (auto self = capturedSelf.lock())
                    {
                        auto input = static_pointer_cast<StreamInput>(self);
                        input->checkPTZPresetThread();
                    }
                });
            }
        }

        /**
         * 打开输入流，获取输入流信息
         */
        bool StreamInput::open()
        {
            printInfo(str(boost::format("Start open context %s") % address));

            int ret = 0;
            initOptions();
            FFMPEGElement::createContext();
            if(!formatCtx)
            {
                return false;
            }

            //const auto format = fmt == nullptr ? nullptr : &fmt;
            if ((ret = avformat_open_input(&formatCtx, streamUrl(), fmt, &options)) < 0)
            {
                printInfo(str(boost::format("avformat_open_input %s FAILED: %s") % streamUrl() % getErrorString(ret)));
                setLastError(str(boost::format("avformat_open_input %s FAILED: %s") % streamUrl() % getErrorString(ret)), ret);
                return false;
            }

            if ((ret = avformat_find_stream_info(formatCtx, nullptr)) < 0)
            {
                printInfo(str(boost::format("avformat_find_stream_info %s FAILED: %s") % streamUrl() % getErrorString(ret)));
                setLastError(str(boost::format("avformat_find_stream_info %s FAILED: %s") % streamUrl() % getErrorString(ret)), ret);
                return false;
            }

            av_dump_format(formatCtx, 0, streamUrl(), 0);
            //查找视频
            for (uint32_t i = 0; i < formatCtx->nb_streams; i++)
            {
                if (formatCtx->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO && formatCtx->streams[i]->codecpar->codec_id == AV_CODEC_ID_H264)
                {
                    videoIndex = i;
                    auto timebase = formatCtx->streams[i]->time_base;
                    auto codecpar = formatCtx->streams[i]->codecpar;
                    auto frameRate = formatCtx->streams[i]->avg_frame_rate;
                    if (frameRate.den == 0 || frameRate.num == 0)
                    {
                        frameRate = formatCtx->streams[i]->r_frame_rate;
                        if (frameRate.den == 0 || frameRate.num == 0)
                        {
                            frameRate.den = 1;
                            frameRate.num = 25;
                        }
                    }
                    duration = (int64_t) ((double) AV_TIME_BASE / av_q2d(frameRate)) / 1000;
                    ai::LogInfo << "open stream:" << address << " frameRate:" << av_q2d(frameRate);

                    if (!codecpar || codecpar->width == 0 || codecpar->height == 0) {
                        printInfo(str(boost::format("avformat_find_stream_info %s, get codecpar failed") % streamUrl()));
                        setLastError(str(boost::format("avformat_find_stream_info %s, get codecpar failed") % streamUrl()), 0);
                        return false;
                    }

                    auto codecInfo = std::make_shared<CodecInfo>(timebase, frameRate, codecpar);
                    this->onStreamCodecInfoRetrieved(codecInfo);
                    break;
                }
            }
            if (videoIndex == -1)
            {
                printInfo(str(boost::format("Could not find video stream %s") % streamUrl()));
                setLastError(str(boost::format("Could not find video stream: %s") % streamUrl()));
                return false;
            }

            printInfo(str(boost::format("open video succ  stream %s  videoIndex %d") % streamUrl() %videoIndex));
            isOpened = true;
            return true;
        }

        /**
         * 关闭输入流
         */
        void StreamInput::close()
        {
            printInfo(str(boost::format("close stream %s") % address));
            FFMPEGElement::disposeContext();
            videoIndex = -1;
            isOpened = false;
        }

        /**
         * 异步调用预置位
         */
        void StreamInput::asyncCallCameraPreset(int presetId, int actPresetId, bool needWait )
        {
            auto presetPauseTime = DATA_MANAGER.getParamProgData(data::FVM_PRESET_PAUSE_TIME, 10);//!< 预置位转动时间 秒
            auto capturedSelf = weak_from_this();
            worker::post(worker::WorkerType::Channel, [capturedSelf, presetPauseTime, actPresetId, needWait, presetId]()
            {
                    if (auto self = capturedSelf.lock())
                    {
                        auto input = static_pointer_cast<StreamInput>(self);
                        auto updated = input->updatePTZPresetInfo(actPresetId);          //!< 在调用预置位之前将预置位状态改为callPtz,防止偏移线程判断偏移
                        if (input->callCameraPreset(actPresetId))
                        {
                            input->setCurrentPresetId(presetId);                          //!< 设置当前正在使用的预置位Id

                            if (!needWait)
                                return;

                            if (updated)
                                input->notifyCheckPTZPresetThread();                      //!< 通知ptz判断现在是否归位

                            for (int i = 0; i < presetPauseTime * 10; i++)                //!< 等待预置位转动归位
                            {
                                if (!input->jobIsRunning())
                                    break;
                                WORKER_SLEEP(std::chrono::milliseconds(100));
                            }
                        }
                        if (input->jobIsRunning())                                       //!< 防止lambda生命周期长于该对象，导致调用信号崩溃
                        {
                            auto ptzPresetStatus = input->getPTZPresetInfo().ptzPresetStatus;
                            if (ptzPresetStatus == PTZPresetStatus::None     //!< 没有判断ptz能力
                            || ptzPresetStatus == PTZPresetStatus::Abnormal  //!< 读取ptz坐标异常
                            || ptzPresetStatus == PTZPresetStatus::CallPtz)   //!< 长时间没有检测到归位
                                input->onCameraPresetCalled();
                        }
                    }
            });
        }

        /**
         * @brief 切换时间方案
         */
        void StreamInput::switchTimeProgram()
        {
            auto presetProgramInfos = this->videoSourceInfo->presetProgramInfos;
            auto channelId = this->videoSourceInfo->channelId;

            //! 非检测通道不进行切换
            if (channelId <= 0)
                return;

            //! 1、没有时间切换方案 2、只有一个时间切换方案 则不进行时间切换线程
            if (presetProgramInfos.empty() || (presetProgramInfos.size() == 1 && presetProgramInfos.begin()->second.size() == 1))
                return;

            //! 当前通道没有配置预置位
            auto preset = DATA_MANAGER.getCurrPreset(getVideoSource());
            if (!preset)
                return;

            //! 不可恢复时，不调用预置位
            auto detectable = DATA_MANAGER.queryChannelDetectable(channelId);
            if (!(data::ChannelDetectable::RestoreDetectable == detectable || data::ChannelDetectable::OffsetDetectable == detectable)) /// TODO 不读数据
                return;

            ai::LogInfo << "start Time Program Switch thread ! channel:" << channelId << " current preset:" << presetId;

            if ((int)preset->getId() != presetId )
            {
                ai::LogInfo << "switching Time Program ! old preset:" << presetId << " new preset:" << preset->getId();
                asyncCallCameraPreset((int)preset->getId(), (int)preset->getActPreset());
            }
        }

        /**
         * @brief  在调用预置位之前更新当前预置位的坐标，以及预置状态
         */
        bool StreamInput::updatePTZPresetInfo(int actPresetId)
        {
            if (getPTZPresetInfo().ptzPresetStatus == PTZPresetStatus::None)
                return false;

            double x = 0, y = 0, z = 0;
            if (getPtzPosition(x, y, z, actPresetId))
            {
                ptzPresetInfo = {x, y, z, PTZPresetStatus::CallPtz};
                return true;
            }
            else
            {
                ptzPresetInfo = {x, y, z, PTZPresetStatus::Abnormal};
                ai::LogError << "get actual preset:" << actPresetId << " ptz position failed! "  << " channelId:" << this->videoSourceInfo->channelId;
                return false;
            }
        }

        /**
         * @brief  通过onvif获取输入流ptz的信息，包括坐标和预置位偏移状态
         */
        PTZPresetInfo StreamInput::getPTZPresetInfo()
        {
            if (ptzPresetInfo.ptzPresetStatus == PTZPresetStatus::NotInited)
            {
                PTZPresetInfo presetInfo = {0,0,0, PTZPresetStatus::None};

                //! 1、数据库中OffsetWaitTime大于0 2、能获取基准坐标
                if ((offsetWaitTime = DATA_MANAGER.getParamProgData(data::FVM_OFFSET_WAIT_TIME, 0)) > 0)
                {
                    double x = 0, y = 0, z = 0;
                    if (DATA_MANAGER.getCurrPresetPosition(this->videoSourceInfo, x, y, z))
                        presetInfo = {x, y, z, PTZPresetStatus::Normal};
                }
                this->ptzPresetInfo = presetInfo;
            }
            return ptzPresetInfo;
        }

        /**
         * @brief  实时检测相机云台ptz的信息，判断相机的偏移状态
         */
        void StreamInput::checkPTZPresetThread()
        {
            if (getPTZPresetInfo().ptzPresetStatus == PTZPresetStatus::None)
                return;

            auto videoId =(int)this->videoSourceInfo->videoSourcePtr->getId();
            auto channelId = (int)this->videoSourceInfo->channelId;
            auto localIp = DATA_MANAGER.getLocalIP();
            auto ptzOffsetThreshold = SETTINGS->offsetThreshold();
            auto ptzRestoreThreshold = SETTINGS->restoreThreshold();
            int checkInterval = DATA_MANAGER.getParamProgData(FVM_OFFSET_WAIT_TIME,10);
            auto eventCheckInterval = SETTINGS->eventPztCheckInterval();
            checkPtzTime = boost::posix_time::second_clock::local_time();

            ai::LogInfo << "start ptz preset check thread ! channel:" << channelId;
            while (jobIsRunning())
            {
                std::unique_lock<boost::fibers::mutex> lock(checkPtzLock);
                checkPtzCondVal.wait_for(lock, std::chrono::seconds(checkInterval), [&]{return waitingForCheckPreset(eventCheckInterval);});
                if (!jobIsRunning())
                    break;

                auto presetInfo = getPTZPresetInfo();
                auto lastPtzPresetStatus = presetInfo.ptzPresetStatus;
                checkPtzTime = boost::posix_time::second_clock::local_time();
                if (lastPtzPresetStatus == PTZPresetStatus::Restore
                    || lastPtzPresetStatus == PTZPresetStatus::Offset
                    || lastPtzPresetStatus == PTZPresetStatus::CallPtz)
                {
                    double x = 0, y = 0, z = 0;
                    if(getPtzPosition(x,y,z, std::nullopt))
                    {
                        auto checkStatus = presetInfo.checkPTZPresetChange(x, y, z, ptzOffsetThreshold, ptzRestoreThreshold);  //!< 通过坐标判断偏移
                        if (checkStatus.has_value())
                        {
                            presetInfo.ptzPresetStatus = checkStatus.value();
                            setPTZPresetInfo(presetInfo);

                            if (presetInfo.ptzPresetStatus == PTZPresetStatus::Offset)
                            {
                                VideoQuaAlarmConf videoQuaAlarmConf = {videoId,0,localIp,{static_cast<int>(VideoQuaAlarmType::VIDEO_QUA_ALARM_SHIFTING)}};
                                PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_VIDEO_QUAALARM, videoQuaAlarmConf, true);
                                ai::LogWarn << "camera offset ! channel:" << channelId << " videoId:" << videoId;
                                ai::LogWarn << "current ptz x:" << x << " y:" << y << " z:"<< z << ", preset:" << presetId << " ptz x: " << presetInfo.x << " y:" << presetInfo.y << " z:"<< presetInfo.z;
                                onPTZPresetChecked(checkPtzTime, true);
                                continue;
                            }
                            else if (presetInfo.ptzPresetStatus == PTZPresetStatus::Restore)
                            {
                                VideoQuaRecovery videoQuaRecovery = {videoId,presetId,localIp,{static_cast<int>(VideoQuaAlarmType::VIDEO_QUA_ALARM_SHIFTING)}};
                                PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_VIDEO_QUARECOVERY, videoQuaRecovery, true);
                                ai::LogWarn << "camera restore ! channel:" << channelId << " videoId:" << videoId;
                                ai::LogWarn << "current ptz x:" << x << " y:" << y << " z:"<< z << ", preset:" << presetId << " ptz x: " << presetInfo.x << " y:" << presetInfo.y << " z:"<< presetInfo.z;
                                onPTZPresetChecked(checkPtzTime, true);
                                continue;
                            }
                        }
                    }
                    else
                    {
                        presetInfo.ptzPresetStatus = PTZPresetStatus::Abnormal;
                        setPTZPresetInfo(presetInfo);
                        ai::LogError << "get ptz position failed, channel:" << channelId << " videoId:" << videoId;
                    }
                    onPTZPresetChecked(checkPtzTime, false);
                }
                boost::this_fiber::sleep_for(std::chrono::milliseconds(100));
            }
            ai::LogInfo << "checkPTZPresetThread EXIT, channel:" << channelId << " videoId:" << videoId;
        }

        /**
         * @brief  判断当前是否需要立即进行ptz云台检测
         */
        bool StreamInput::waitingForCheckPreset(int interval)
        {
            //! 通道退出，立即处理
            if (!jobIsRunning())
                return true;

            if (isNotify)
            {
                isNotify = false;
                //! 用于判断调用预置位是否归位
                if (getPTZPresetInfo().ptzPresetStatus == PTZPresetStatus::CallPtz)
                    return true;

                //! 事件发生时，如果距离上次偏移判断超过5秒钟，则开始再判断一次。
                auto timeNow = boost::posix_time::second_clock::local_time();
                if ((timeNow - checkPtzTime) > boost::posix_time::seconds(interval))
                    return true;

                onPTZPresetChecked(timeNow, false);
            }
            return false;
        }

        /**
         * @brief 等待元件结束，相关线程退出
         */
        void StreamInput::waitForFinished()
        {
            StreamElement::waitForFinished();

            if(presetThreadFuture.has_value())
                presetThreadFuture->wait();
            if(timeProgramThreadFuture.has_value())
                timeProgramThreadFuture->wait();
        }

        network::EPtzCommand StreamInput::convertPtzCmd(int action)
        {
            if(0 == action)
            {
                if(preCmd == network::EPTZCOMMAND_FOCUSNEAR || preCmd == network::EPTZCOMMAND_FOCUSFAR)
                {
                    preCmd = network::EPTZCOMMAND_FOCUSSTOP;
                    return network::EPTZCOMMAND_FOCUSSTOP;
                }
                preCmd = network::EPTZCOMMAND_STOP;
                return network::EPTZCOMMAND_STOP;
            }
                
            if(1 == action)
            {
                preCmd = network::EPTZCOMMAND_LEFT;
                return network::EPTZCOMMAND_LEFT;
            }
            if(2 == action)
            {
                preCmd = network::EPTZCOMMAND_RIGHT;
                return network::EPTZCOMMAND_RIGHT;
            }
            if(3 == action)
             {
                preCmd = network::EPTZCOMMAND_UP;
                return network::EPTZCOMMAND_UP;
            }
            if(4 == action)
             {
                preCmd = network::EPTZCOMMAND_DOWN;
                return network::EPTZCOMMAND_DOWN;
            }
            if(5 == action)
             {
                preCmd = network::EPTZCOMMAND_ZOOMIN;
                return network::EPTZCOMMAND_ZOOMIN;
            }
            if(6 == action)
             {
                preCmd = network::EPTZCOMMAND_ZOOMOUT;
                return network::EPTZCOMMAND_ZOOMOUT;
            }
            if(7 == action)
             {
                preCmd = network::EPTZCOMMAND_FOCUSNEAR;
                return network::EPTZCOMMAND_FOCUSNEAR;
            }
            if(8 == action)
             {
                preCmd = network::EPTZCOMMAND_FOCUSFAR;
                return network::EPTZCOMMAND_FOCUSFAR;
            }
            // if(9 == action)
            //     return network::EPTZCOMMAND_LEFT;
            // if(10 == action)
            //     return network::EPTZCOMMAND_LEFT;
            if(11 == action)
             {
                preCmd = network::EPTZCOMMAND_LEDOFF;
                return network::EPTZCOMMAND_LEDOFF;
            }
            if(12 == action)
             {
                preCmd = network::EPTZCOMMAND_LEDON;
                return network::EPTZCOMMAND_LEDON;
            }

            preCmd = network::EPTZCOMMAND_STOP;
            return network::EPTZCOMMAND_STOP;
        }
    }
}