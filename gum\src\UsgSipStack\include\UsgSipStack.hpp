#ifndef USGSIPSTACK_HPP_
#define USGSIPSTACK_HPP_

#include "UsgSipStackItf.hpp"

namespace usg {

class CUsgSipUdpService;

class CUsgSipStack
{
public:
    static CUsgSipStack* instance();
	CUsgSipStack();
	virtual ~CUsgSipStack();

public:
	boost::shared_ptr< IUsgSipUdpService > createUdpSipService( const std::string &lAddr, uint16_t lPort, const std::string &lCode,const std::string& xmlType = "DDCP" );
	void setLevel( uint16_t level );

	bool startupImpl();
	bool shutdownImpl();

private:
	boost::shared_ptr< CUsgSipUdpService > m_sipService;
};

}

#define USG_SIPSTACK usg::CUsgSipStack::instance()

#endif
