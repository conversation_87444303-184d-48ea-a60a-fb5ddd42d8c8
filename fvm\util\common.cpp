
#include "common.h"
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/types.h>
#include <errno.h>

namespace fvm
{
    //检查 processName 是否已运行实例
    bool isRunning(std::string processName)
    {
        if (processName.empty())
            exit(1);
        // 打开或创建一个文件
        std::string filePath = std::string("/tmp/") + processName + ".pid";
        int fd = open(filePath.c_str(), O_RDWR | O_CREAT, 0666);
        if (fd < 0)
        {
            printf("Open file failed, error : %s", strerror(errno));
            exit(1);
        }
        // 将该文件锁定
        // 锁定后的文件将不能够再次锁定
        struct flock fl;
        fl.l_type = F_WRLCK; // 写文件锁定
        fl.l_start = 0;
        fl.l_whence = SEEK_SET;
        fl.l_len = 0;
        int ret = fcntl(fd, F_SETLK, &fl);
        if (ret < 0)
        {
            if (errno == EACCES || errno == EAGAIN)
            {
                printf("%s already locked, error: %s\n", filePath.c_str(), strerror(errno));
                close(fd);
                return true;
            }
        }
        // 锁定文件后，将该进程的pid写入文件
        char buf[16] = { '\0' };
        sprintf(buf, "%d", getpid());
        ftruncate(fd, 0);
        ret = write(fd, buf, strlen(buf));
        if (ret < 0)
        {
            printf("Write file failed, file: %s, error: %s\n", filePath.c_str(), strerror(errno));
            close(fd);
            exit(1);
        }
        // 函数返回时不需要调用close(fd)不然文件锁将失效
        // 程序退出后kernel会自动close
        return false;
    }

    //时刻字符串转秒[如 01:00 转为3600s]
    int stringToSec(std::string timeStr)
    {
        try
        {
            int ipos = timeStr.find_first_of(":");
            if (ipos < 0)
                return 0;
            int hh = atoi(timeStr.substr(0, ipos).c_str());
            int mm = atoi(timeStr.substr(ipos + 1, timeStr.size()).c_str());
            return hh * 3600 + mm * 60;
        }
        catch (...)
        {
            return 0;
        }
    }

}
