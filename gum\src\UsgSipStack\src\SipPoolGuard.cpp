#include "UsgSipStack/src/SipPoolGuard.hpp"

namespace usg
{

    CSipPoolGuard::CSipPoolGuard()
            :m_pool( 0 )
    {
        /*
        * 创建内存池.
        */
        pj_caching_pool_init( &m_cache, 0, 0 );
        m_pool = pj_pool_create( &m_cache.factory, NULL, 4096, 1024, 0 );
    }

    CSipPoolGuard::~CSipPoolGuard()
    {
        pj_pool_release( m_pool );
        pj_caching_pool_destroy( &m_cache );
    }

    pj_pool_t * CSipPoolGuard::getPool()
    {
        return m_pool;
    }
}
