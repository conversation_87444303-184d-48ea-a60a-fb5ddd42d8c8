#include <pjlib.h>

#include "SipPrinter.hpp"

namespace usg {

namespace {

/* Notification on incoming messages */
static pj_bool_t logging_on_rx_msg( pjsip_rx_data *rdata )
{
    PJ_LOG(3,("SipStack", "RX %d bytes %s from %s %s:%d:\n"
             "--beg msg--\n"
			 "%.*s\n"
			 "--end msg--\n",
			 rdata->msg_info.len,
			 pjsip_rx_data_get_info(rdata),
			 rdata->tp_info.transport->type_name,
			 rdata->pkt_info.src_name,
			 rdata->pkt_info.src_port,
			 (int)rdata->msg_info.len,
			 rdata->msg_info.msg_buf));
    return PJ_SUCCESS;
}

/* Notification on outgoing messages */
static pj_status_t logging_on_tx_msg( pjsip_tx_data *tdata )
{
    PJ_LOG(3,("SipStack", "TX %d bytes %s to %s %s:%d:\n"
             "--beg msg--\n"
			 "%.*s\n"
			 "--end msg--\n",
			 (tdata->buf.cur - tdata->buf.start),
			 pjsip_tx_data_get_info(tdata),
			 tdata->tp_info.transport->type_name,
			 tdata->tp_info.dst_name,
			 tdata->tp_info.dst_port,
			 (int)(tdata->buf.cur - tdata->buf.start),
			 tdata->buf.start));
    return PJ_SUCCESS;
}

/* The module instance. */
static pjsip_module modPrinter =
{
    NULL, NULL,				/* prev, next.		*/
    { (char*)"SipPrinter", 10 },		/* Name.		*/
    -1,					/* Id			*/
    PJSIP_MOD_PRIORITY_TRANSPORT_LAYER-1,/* Priority	        */
    NULL,				/* load()		*/
    NULL,				/* start()		*/
    NULL,				/* stop()		*/
    NULL,				/* unload()		*/
    &logging_on_rx_msg,			/* on_rx_request()	*/
    &logging_on_rx_msg,			/* on_rx_response()	*/
    &logging_on_tx_msg,			/* on_tx_request.	*/
    &logging_on_tx_msg,			/* on_tx_response()	*/
    NULL,				/* on_tsx_state()	*/
};

}

bool CSipPrinter::registerModule( pjsip_endpoint *endPoint )
{
    return PJ_SUCCESS == pjsip_endpt_register_module( endPoint, &modPrinter );
}

}
