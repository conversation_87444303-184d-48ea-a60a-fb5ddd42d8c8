# GUM

* [目录说明](#目录说明)
* [自定义视频输入、输出](#自定义流元件)
* [编译步骤](#编译步骤)
* [安装步骤](#安装步骤)

## 目录说明
.<br>
├── [app](app)    `程序入口`<br>
├── [DataMgr](DataMgr)    `数据管理`<br>
├── [UsgManager](UsgManager)    `28181下级管理模块和信令交互`<br>
├── [UsgSipStack](stream)    `SIP协议处理库`<br>
├── [UsgProtocolGbt](UsgProtocolGbt)    `28181协议解析库`<br>


```
```

## 编译步骤

### 1.依赖包: <br/>
&emsp;[boost 1.77.0 +](https://www.boost.org/)  <br>
&emsp;[ace 6.4.0 +]
&emsp;[pjsip 2.10 +]
&emsp;[js 1.8.0 +]
&emsp; wtoe

1. /opt/boost <br>
2. /opt/ace <br>
3. /opt/pjsip <br>
4. /opt/other3rd 

### 2.编译fvmlog、database、wtoe-socket库
### 3.编译gum下所有库

## 安装步骤
  进入out目录，可见生成了gum，lib下的4个库
  运行 sh genBin.sh，即可生成压缩包  gum-hw-1.0-20211125.tar.bz2
  Nvidia平台下压缩包名称为： gum-1.0-20211125.tar.bz2
  将压缩包解压到 /data/opt下，得到gum目录
  
cd /data/opt/gum
./install.sh
./gum start 即可运行成screen