#include <boost/shared_ptr.hpp>
#include <wtoe/PackageManager/PackageManagerExp.hpp>
#include "UsgManager/include/UsgManagerExp.hpp"
#include "UsgManager.hpp"
#include "UsgService.hpp"
#include "ailog.h"

namespace usg {

CUsgService::CUsgService()
{

}

CUsgService::~CUsgService()
{

}

bool CUsgService::preparePlayRealStream(  const boost::uuids::uuid &mediaId,  const uint8_t mOrs,  const ACE_INET_Addr& clientAddr,  ACE_INET_Addr &remoteAddr,  uint16_t& streamRate,  uint8_t &packerType,  std::string &sid )
{
	boost::shared_ptr< IUsgManager > sg;
	if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( sgc == 0 ) return false;

	if( !sgc->preparePlayRealStream( mediaId, mOrs, clientAddr, remoteAddr, streamRate, packerType, sid ) )
	{
		std::cerr << "CUsgService::preparePlayRealStream false" << std::endl;;
	}

//    sgc->addEpid2Sids( rimi_env.epid, sid );
	
    return true;
}

bool CUsgService::playRealStreamAck(  const std::string &sid )
{
	boost::shared_ptr< IUsgManager > sg;
	if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( sgc == 0 ) return false;

    if ( !sgc->playRealStreamAck( sid ) )
    {
        ai::LogError << "sgc->playRealStreamAck " << sid << " failed! ";
        return false;
    }

    return true;
}

bool CUsgService::realKeepalive(  const std::string &sid )
{
	boost::shared_ptr< IUsgManager > sg;
	if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( sgc == 0 ) return false;

	return sgc->realKeepalive( sid );
}

bool CUsgService::stopRealStream(  const std::string &sid )
{
	boost::shared_ptr< IUsgManager > sg;
	if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( sgc == 0 ) return false;

	if( !sgc->stopRealStream( sid ) )
	{
		std::cerr << "CUsgService::stopRealStream false" << std::endl;
		return false;
	}

	return true;
}

bool CUsgService::preparePlayHistoryStream(  const boost::uuids::uuid &mediaId,  const TimePeriodUnit &timePeriod,  const ACE_INET_Addr clientAddr,  ACE_INET_Addr &remoteAddr,  std::string &sid )
{
	boost::shared_ptr< IUsgManager > sg;
	if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( sgc == 0 ) return false;

	TimePeriodUnit timeUnit = timePeriod;
	if( timeUnit.second > 0x7fffffff )
		timeUnit.second = 0x7fffffff;

	if( ( timeUnit.second - timeUnit.first ) == 1 )
		timeUnit.second = 0x7fffffff;

	if( !sgc->preparePlayHistoryStream( mediaId, timeUnit/*timePeriod*/, clientAddr, remoteAddr, sid ) )
	{
		std::cerr << "CUsgService::preparePlayHistoryStream false" << std::endl;
		return false;
	}

//    sgc->addEpid2Sids( rimi_env.epid, sid );

	return true;
}

bool CUsgService::prepareDownloadHistoryStream(  const boost::uuids::uuid &mediaId,  const TimePeriodUnit &timePeriod,  const ACE_INET_Addr clientAddr,  ACE_INET_Addr &remoteAddr,  std::string &sid )
{
	boost::shared_ptr< IUsgManager > sg;
	if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( sgc == 0 ) return false;

	TimePeriodUnit timeUnit = timePeriod;
	if( timeUnit.second > 0x7fffffff )
		timeUnit.second = 0x7fffffff;

	if( !sgc->prepareDownloadHistoryStream( mediaId, timeUnit/*timePeriod*/, clientAddr, remoteAddr, sid ) )
	{
		std::cerr << "CUsgService::prepareDownloadHistoryStream false" << std::endl;
		return false;
	}

//    sgc->addEpid2Sids( rimi_env.epid, sid );

	return true;
}

bool CUsgService::playHistoryStreamAck(  const std::string &sid,  uint8_t n,  uint8_t d,  bool modifyTime,  uint32_t beginTimeOff,  uint32_t endTimeOff )
{
	boost::shared_ptr< IUsgManager > sg;
	if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( sgc == 0 ) return false;

	uint32_t endTime = endTimeOff;
	if( endTime > 0x7fffffff )
		endTime = 0x7fffffff;

	if( !sgc->playHistoryStreamAck( sid, n, d, modifyTime, beginTimeOff, endTime/*endTimeOff*/ ) )
	{
		std::cerr << "CUsgService::playHistoryStreamAck false" << std::endl;
		return false;
	}

	return true;
}

bool CUsgService::pause(  const std::string &sid )
{
	boost::shared_ptr< IUsgManager > sg;
	if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( sgc == 0 ) return false;

	if( !sgc->pause( sid ) ) 
	{
		std::cerr << "CUsgService::pause false" << std::endl;
		return false;
	}

	return true;
}

bool CUsgService::stopHistoryStream(  const std::string &sid )
{
	boost::shared_ptr< IUsgManager > sg;
	if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( sgc == 0 ) return false;

	if( !sgc->stopHistoryStream( sid ) )
	{
		std::cerr << "CUsgService::stopHistoryStream false" << std::endl;
		return false;
	}
	
    return true;
}

bool CUsgService::getAllResInfo(  std::map<boost::uuids::uuid, SResInfo >& mapRes,  uint32_t& timeStamp )
{
	boost::shared_ptr< IUsgManager > sg;
	if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return false;

	boost::shared_ptr< CUsgManager > sgc = boost::dynamic_pointer_cast< CUsgManager >( sg );
	if( sgc == 0 ) return false;

	return sgc->getAllResInfo( mapRes, timeStamp );
}

}
