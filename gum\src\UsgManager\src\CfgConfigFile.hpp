#ifndef CFGCONFIGFILE_HPP_
#define CFGCONFIGFILE_HPP_

#include <vector>
#include <utility>
#include <string>
#include <boost/utility.hpp>
#include <string>
#include <sstream>
#include <iostream>
#include <fstream>
#include <stdint.h>

#include "UsgManager/include/UsgManagerCfg.hpp"


namespace usg
{
    class USGMANAGER_PRIVATE CCfgConfigFile //: private boost::noncopyable
    {
    public:
        CCfgConfigFile();
        ~CCfgConfigFile();

    public:

        /** 读取配置文件.
        *
        * @param[in]  fileName   文件名.
        * @param[out] lines      把文件解析成多行std::string
        *
        * @return 成功返回true. 去掉了注释行(#),去掉了行前面空格和后面空格
        */
        bool readFile( const std::string &fileName, std::vector<std::string>& lines );

        /** 保存配置文件.
        *
        * @param[in]  fileName   文件名.
        * @param[in]  lines      要保存文件的行
        * @return 成功返回true.
        */
        bool writeFile( const std::string &fileName,const std::vector<std::string>& lines );

        /** 分割字符串.
        *
        * @param[in]  s         要分割的字符串,比如readFile返回的lines中的一行
        * @param[in]  delim     分割符
        * @param[out] vtr       分割后的字符串数组
        *
        * @return 成功返回true
        */
        bool split( const std::string &s, std::vector<std::string>& vtr, char delim = ',' );

    private:
        /** 打开文件.
        *
        * @param[in]  fileName  要打开的文件名.
        * @param[out] ifs       文件流.
        * @return 成功返回true.
        */
        virtual bool openFile( const std::string &fileName, std::ifstream& ifs );

        /** 去字符串前后空格.
        *
        * @param[in] s  字符串.
        *
        * @return 去空格后的字符串.
        */
        std::string& trim( std::string &s );

    };

}
#endif