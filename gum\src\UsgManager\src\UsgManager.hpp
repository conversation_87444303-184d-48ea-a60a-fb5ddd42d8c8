#ifndef SGSERVICE_HPP_
#define SGSERVICE_HPP_

#include <set>

#include <wtoe/PackageManager/PackageManagerExp.hpp>

#include "UsgManager/include/UsgManagerCfg.hpp"
#include "UsgManager/include/UsgManagerItf.hpp"
#include "UsgSipStack/include/UsgSipStackExp.hpp"
#include "UsgManager/include/ProtocolWtoeItf.hpp"
#include "UsgManager/include/WtoeSipFuncItf.hpp"
#include "UsgManager/src/SgCtxHandler.hpp"
#include "UsgManager/src/SgWtoeSipFunc.hpp"
#include "UsgManager/src/ResManager.hpp"
#include "UsgManager/src/SipPtzController.hpp"
#include "UsgManager/src/ReceiveSipData.hpp"


namespace usg
{

    class USGMANAGER_PRIVATE CUsgManager : public wtoe::IServiceEntry,
                                           public IUsgManager,
                                           public IGb28181
    {
    public:
        CUsgManager();
        ~CUsgManager();

    protected:
        virtual bool startupImpl();
        virtual bool shutdownImpl();
        virtual std::vector<std::string> getHelpInfos();

    private:
        static bool javaScriptRegister( JSContext *jsCtx, JSObject *jsObj );

    public:
        virtual bool playRealStreamAck( const std::string &sid );

        virtual bool stopRealStream( const std::string &sid );


        /**
         * @brief 向下级SIP请求URL地址
         * @param[in] mediaId       媒体源的标识符
         * @param[in] timePeriod    时间段，开始时间和结束时间
         * @param[in]  clientAddr      客户端接收流地址.
         * @param[in]  remoteAddr      远端地址.
         * @return bool             返回值说明
         * @retval true             成功
         * @retval false            失败
         */
        virtual bool preparePlayHistoryStream( const boost::uuids::uuid &mediaId,
                                               const TimePeriodUnit &timePeriod,
                                               const ACE_INET_Addr clientAddr,
                                               ACE_INET_Addr &remoteAddr,
                                               std::string &sid );



        /**
     * @brief 向下级SIP请求URL地址
     * @param[in] mediaId       媒体源的标识符
     * @param[in] timePeriod    时间段，开始时间和结束时间
     * @param[in]  clientAddr      客户端接收流地址.
     * @param[in]  remoteAddr      远端地址.
     * @return bool             返回值说明
     * @retval true             成功
     * @retval false            失败
     */
        virtual bool prepareDownloadHistoryStream( const boost::uuids::uuid &mediaId,
                                                   const TimePeriodUnit &timePeriod,
                                                   const ACE_INET_Addr clientAddr,
                                                   ACE_INET_Addr &remoteAddr,
                                                   std::string &sid );

        virtual bool playHistoryStreamAck( const std::string &sid, uint8_t n, uint8_t d, bool modifyTime, uint32_t beginTimeOff, uint32_t endTimeOff );

        virtual bool pause( const std::string &sid);

        virtual bool stopHistoryStream( const std::string &sid);

        virtual bool getAllResInfo( std::map<boost::uuids::uuid, SResInfo >& mapRes, uint32_t& timeStamp );


        virtual bool realKeepalive( const std::string &sid );

        virtual bool deviceReboot( const boost::uuids::uuid &resId );
        virtual bool recordContronl( const boost::uuids::uuid &resId, const bool flag );
        virtual bool guardContronl( const boost::uuids::uuid &resId, const bool flag );
        virtual bool alarmReset( const boost::uuids::uuid &resId );
        virtual bool deviceInfo( const boost::uuids::uuid &resId, SQueryInfo &info );
        virtual bool deviceStatus( const boost::uuids::uuid &resId, SQueryStatus &info );
        virtual bool ptzCtrl( const uint8_t ptzCommand, const boost::uuids::uuid &resId,
                              const uint16_t arg1, const uint16_t arg2 );

        virtual bool preparePlayRealStream( const boost::uuids::uuid &mediaId,//资源uuid
                                            const uint8_t mOrs, //主/子码流
                                            const ACE_INET_Addr& clientAddr, //urg地址
                                            ACE_INET_Addr &remoteAddr,//远端地址
                                            uint16_t& streamRate,//码率
                                            uint8_t  &packerType,//ps/ts
                                            std::string &sid/*设备id*/ );

        virtual int startPlay( const std::string& szSipId, const std::string& szResCode, const std::string& szDest, std::string& szSrc, ESocketType socketType, bool bUseSub, std::string& szError );
	virtual bool stopPlay( const std::string& szSipId, const std::string& szResCode, const std::string& szDest, bool bUseSub );

        virtual bool ptzControl( const std::string& szSipId, const std::string& szResCode, uint8_t iPreset );
        virtual bool doPtzControl(const std::string& szSipId, const std::string& szResCode, EPtzCommand command, int iParam1 = 0, int iParam2 = 0);
        virtual bool getPtzs( const std::string& szSipId, const std::string& szResCode, std::map< uint8_t, std::string>& mapPtzs );

        virtual bool addRemote( uint32_t id, const std::string& szSipId, const std::string& szAddr );
        virtual bool delRemote( const std::string& szSipId, const std::string& szAddr );
        virtual bool addVideo( const std::string& szRemoteAddr, const std::string& szVideoAddr, const std::string& szName, uint32_t id );
        virtual bool getVideoStatus( const std::string& szRemoteAddr, const std::string& szVideoAddr );

        virtual void setLevel( uint16_t level );
        virtual void fini();
        virtual bool init(FUNC_INSERTVIDEO_CALLBACK func1,
                          FUNC_UPDATEREMOTESTATUS_CALLBACK func2,
                          FUNC_UPDATEVIDEONAME_CALLBACK func3);
    public://<gb28181
        virtual CSpIUsgService	getUsgService();
        virtual CSpIPtzController getPtzControllerMgr();
        virtual void registResNotify( ISgResNotify *notify );
        virtual void unregistResNofity( ISgResNotify *notify );
        virtual void registAlarmNotify( ISgAlarmNotify *notify );
        virtual void unregistAlarmNotify( ISgAlarmNotify *notify );
        virtual bool getMediaInfoByMedia( const boost::uuids::uuid &mediaId, const TimePeriodUnit &timePeriod, std::vector< std::pair< std::string, TimePeriodUnit > > &info );
        virtual bool getAllResInfos( std::map<boost::uuids::uuid, SResInfo >& mapRes, uint32_t& timeStamp );

    public:
        virtual bool addPJThreadDes( long *des );

    public://用于语音广播
        virtual bool audioBroadcast( const std::string &sourceId,  const std::string &targetId);
        ISgResNotify* getNotify();
        ISgAlarmNotify* getAlarmNotify();
        void  addEpid2Sids( const uint32_t& epid,      const std::string& sid );
        void  delEpid2Sids( const uint32_t& epid = -1, const std::string& sid = "" );
        void  getSidsByEpid( const uint32_t& epid, std::set<std::string>& setSids );
        void  clearEpid2Sids();

    public:
        void setLoacalAddr( const std::string &addr );
        void setDBParam(const std::string& ip, const std::string& dbName);

        bool setPackerType( uint8_t type );
        bool initRemotes();

        virtual bool onReceiveCatalog( const std::string &sid, const SCatalog& info, SCatalogResponse& out );
        virtual bool onReceiveCatalog( const std::string &sid, const SCatalog& info );
        ///< 推送目录查询的响应数据，由CQuestCatalogActive对象调用
        bool pushReceiveCatalog( const SCatalog &info );
        virtual bool onReceiveKeepalive( const std::string &sid );

        virtual bool onReceivePresetListResponse( const std::string& sid,SPresetListResponse& out );
        virtual bool onReceivePtzCommand( const EPtzCommand ptzCommand,const boost::uuids::uuid &resId, const uint8_t arg );
        //add for GB28181
        virtual bool onReceivePtzCommand( const EPtzCommand ptzCommand,const boost::uuids::uuid &resId, const uint16_t arg1, const uint16_t arg2 );
        virtual bool onReceiveDeviceReboot(const boost::uuids::uuid &resId);
        virtual bool onReceiveRecordContronl(const boost::uuids::uuid &resId, const bool flag);
        virtual bool onReceiveGuardContronl(const boost::uuids::uuid &resId, const bool flag);
        virtual bool onReceiveAlarmReset( const boost::uuids::uuid &resId );
        virtual bool onReceiveQueryDeviceCatalog(const std::string &devId/*, SCatalog &info*/);
        virtual bool onReceiveQueryDeviceInfo(const boost::uuids::uuid &resId, SDeviceInfoResponse &info);
        virtual bool onReceiveQueryDeviceStatus(const boost::uuids::uuid &resId, SDeviceStatusResponse &info);
        virtual bool onReceiveFiletoEnd( const std::string& sid );
        virtual bool onReceiveAlarmNotify(const SAlarmParam &alarm);

        virtual bool onqueryPreposition( const boost::uuids::uuid &resId, std::map< uint8_t, std::string > &infos );
        virtual bool onReceiveRegist( const std::string &sid,const std::string &oid,int expries );
        virtual bool isRegist( const std::string& oid );

//    bool onReceiveCatalog( const ESgResNotifyAction action, const SResInfo& info );
        virtual bool canControlPtz( const boost::uuids::uuid &resId, bool &retbool );

        virtual bool setCseqValues( const std::string& sid ,int cseq );
        virtual bool getCsqlValues( const std::string& sid ,int& cseq );

        //增加format缓存，用于缓存实时显示时的码率大小、视频尺寸等信息
        virtual bool setFormatValues( const std::string& sid ,std::string format );
        virtual bool getFormatValues( const std::string& sid ,std::string &format );

        //增加源地址信息
        virtual bool setSrcAddr( const std::string& sid, std::string srcAddr );
        virtual bool getSrcAddr( const std::string& sid, std::string &srcAddr );
        virtual bool isPlaying();
    private:
        void parseFormat(std::string format, uint16_t &streamRate);
        void convertSizeToRate(int videoSize, uint16_t &streamRate);
        std::vector<int> splitFormat(std::string str,std::string pattern);
        bool onReceiveQueryDeviceCatalog( CResManager* pMgr );
        bool getResInfo( const boost::uuids::uuid &resId, SResInfo& resInfo, std::string& sipUrl );
        CResManager* getResManager( const std::string& rCode );
        bool playRealStream( const SResInfo& resInfo, const std::string& sipUrl,
                             const std::string& szDest, std::string& szSrc, std::string& sid, ESocketType socketType, bool bUseSub, std::string& szError );
        std::string getSocketType( ESocketType socketType );
    public:

    private:
        std::map< std::string, std::string > m_questCatalogCmds;
        bool questCatalogError( const std::string &sid, void *param );
        ///< waitResponse主要是用来处理流请求中，等待下级确认的
        bool waitResponse( const std::string &sid, int sec );

    private:
        bool startAllThread();
        void stopAllThread();

        bool getRelativePath( std::string &rp );
        void rgLinkDown();
        void byeAllRealThread();

        std::string getReceiveSeq(int size);

        bool subscribeCatalog( CResManager* pMgr );
        bool subscribeAlarm( CResManager* pMgr );

        void onReceiveFiletoEndImp(const std::string& sid);

        bool playHistoryPrepare( const boost::uuids::uuid &mediaId,const std::string &fileName,const TimePeriodUnit &timePeriod, const ACE_INET_Addr &rtpAddr, std::string &sid);
        bool downloadHistoryPrepare( const boost::uuids::uuid &mediaId,const std::string& fileName,const TimePeriodUnit &timePeriod,const ACE_INET_Addr &rtpAddr,std::string &sid);
        bool getResId( const std::string& szSipId, const std::string& szResCode, SResInfo& info, std::string& remoteSipUrl, std::string& sipUrl );
        bool getMediaInfo( const boost::uuids::uuid& mediaId, SResInfo& resInfo, std::string& remoteSipUrl, std::string& sipUrl );
        bool getSidInfo( const std::string& sid, SResInfo& resInfo, std::string& remoteSipUrl, std::string& sipUrl );

        //获得目录
        void getCatalog(CResManager* pMgr);
    private:
        CSpIPtzController    m_ptzController;
        CSpIUsgService		m_usgService;
        ISgResNotify *m_notify;
        ISgAlarmNotify *m_alarmNotify;

        std::string             m_loadLibName;
        std::string             m_xmlType;
//     std::string             m_remoteSipUrl;
        std::string             m_remoteCode;
        std::string             m_keepaliveSipUrl;
        std::string             m_localSipUrl;
        std::string m_keepaliveSid; // 发送的请求的标识号.


        CSgWtoeSipFunc m_wtoeSipFunc;
        CSgCtxHandler m_ctxHandler;
//     CResManager*  m_resManager;
        IProtocolWtoe *m_protocolWtoe;
        void *m_h;

        std::string m_rAddr;
        uint16_t    m_rPort;
        std::string m_rCode;
        std::string m_lAddr;
        uint16_t    m_lPort;
        std::string m_lCode;

        boost::shared_ptr< usg::IUsgSipUdpService > m_sipUdpService;
        usg::IInviteSession *m_inviteSession;
        usg::INotifySession *m_notifySession;
        usg::IRegistSession *m_registSession;
        usg::IDdcpDoSession *m_ddcpDoSession;

        boost::thread *m_threadKeepalive;

        volatile bool m_threadForceStop; // 强制停止所有线程.
        volatile bool m_KeepaliveOk;
        //volatile bool m_isSubscribeCatalog;
//     volatile bool m_isSubscribeAlarm;
        rwmutex m_regMutex;
        typedef boost::unique_lock< boost::mutex > lock_type;
        boost::mutex  m_mutexReg,m_mutexKeepalive;
        boost::condition m_keepaliveCond,m_registerCond;
        int m_expries;

        std::map< std::string, std::string > m_streamSid2Format;   //实时使用，且互斥
        std::map< std::string, std::string > m_streamSid2SrcAddr;   //实时使用，且互斥

        std::map< std::string, int > m_streamSid2Cseq;   //历史和实时共用，且互斥
        std::map< std::string, boost::uuids::uuid > m_streamSid2uuid; //历史和实时使用，且互斥，流与视频id的对应
        rwmutex m_uuidLock;
        /*
         *	key为对端epid,value为会话的sid，用以区分哪些sid会话属于哪一个服务器的链，
         *  在USG与对端的服务器断链后，可以将该链epid对应的sid会话主动bye掉。缓存的
         *  Sid集合应与m_streamSid2Cseq表保持一致!
         */
        std::map< uint32_t, std::set<std::string> > m_epid2Sids;
        boost::mutex                                m_mutexEpid2Sids;

        rwmutex m_formateLock;
        rwmutex m_cseqLock;
        rwmutex m_srcAddrLock;

        boost::thread *m_threadByeRealall;
        boost::condition m_realSidsCond;
        EPackerType    m_packerType;
//	boost::posix_time::ptime m_keepAliveTime;///<记录保活上报的时间

        boost::mutex   m_mutexPlaySid;
        std::map< std::string, std::string> m_mapPlaySid;
        rwmutex   m_mutexAllowSip;
        std::map< std::string, CResManager*> m_mapAllowSip;

        bool m_bOkey;
        boost::recursive_mutex m_mutexPlaying;
        bool m_isPlaying;

        bool m_bIsListening;    //是否在监听sip端口，若没有，不需要做后续的操作

        FUNC_INSERTVIDEO_CALLBACK m_funcInsertVideo;
        FUNC_UPDATEREMOTESTATUS_CALLBACK m_funcUpdateRemoteStatus;
        FUNC_UPDATEVIDEONAME_CALLBACK m_funcUpdateVideoName;

    };

}

#endif
