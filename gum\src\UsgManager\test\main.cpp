
#include <cppunit/BriefTestProgressListener.h>
#include <cppunit/CompilerOutputter.h>
#include <cppunit/extensions/TestFactoryRegistry.h>
#include <cppunit/TestResult.h>
#include <cppunit/TestResultCollector.h>
#include <cppunit/TestRunner.h>

#include "MocApp.hpp"


int main( int argc, char* argv[] )
{
    //if ( argc == 1 )
    //{
    //    argc = 2;
    //    argv[1] = "--load=../../testdata/app/SgSevice/config/app/load_empty.js";
    //}

    msg::CMocApp app( argc, argv );
    // Create the event manager and test controller
    CPPUNIT_NS::TestResult controller;

    // Add a listener that colllects test result
    CPPUNIT_NS::TestResultCollector result;
    controller.addListener( &result );

    // Add a listener that print dots as test run.
    CPPUNIT_NS::BriefTestProgressListener progress;
    controller.addListener( &progress );

    
    if( !app.startup() )
    {
        app.shutdown();
        return -1;
    }
    if( app.run() != 0 )
    {
        app.shutdown();
        return -1;
    }

    CPPUNIT_NS::TestRunner runner2;
    runner2.addTest( CPPUNIT_NS::TestFactoryRegistry::getRegistry( "CCfgConfigFileTestCase" ).makeTest() );
    runner2.addTest( CPPUNIT_NS::TestFactoryRegistry::getRegistry( "CResManagerTestCase" ).makeTest() );
    runner2.run( controller );

    // Print test in a compiler compatible format.
    CPPUNIT_NS::CompilerOutputter outputter( &result, std::cout );
    outputter.write(); 

    std::ofstream outputFile("testResult.txt",std::ios_base::app);
    int total = result.runTests();
    int failure = result.testFailuresTotal();
    int success = total-failure;
    outputFile << "UsgManager" << "\t" << total << "\t" << success << "\t" << failure << std::endl;

    if( !app.shutdown() )
    {
        return -1;
    }

    return result.wasSuccessful() ? 0 : 1;
}
