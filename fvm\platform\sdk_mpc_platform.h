/**
 * Project FVM
 */
#pragma once
#include <atomic>
#include <chrono>
#include <condition_variable>
#include "front_platform.h"
#include "MpcNetSdkItf.hpp"

 /**
  * @brief:MPC 平台
  */
namespace  fvm::platform {

    using std::chrono::steady_clock;

    class SDKMPCPlatform;
    class MPCSessionNotify : public mpc::nsdk::IManageSessionNotify
    {
    public:
        MPCSessionNotify(SDKMPCPlatform* parent) :plat(parent) {}
        //平台连接异常通知
        virtual void terminate(mpc::nsdk::IManageSession* session);
    private:
        SDKMPCPlatform* plat;
    };

    class MPCMediaStatusNotify : public mpc::nsdk::IMediaStatusNotify
    {
    public:
        MPCMediaStatusNotify(SDKMPCPlatform* parent) :plat(parent) {}
        //资源状态变化通知
        virtual void mediaStatusNotify(mpc::nsdk::IMediaStatusIterator* it) {}
    private:
        SDKMPCPlatform* plat;
    };

    using RecvCallback = std::function<void(uint8_t*, int)>;
    class SDKMPCPlatform : public  FrontPlatform {
        friend class MPCSessionNotify;
        friend class MPCMediaStatusNotify;
    public:
        SDKMPCPlatform():platNotify(this), resourceNotify(this){}

        void init(data::VideoServerPtr serverPtr) override;
        bool login(void) override;

        bool startPlay(const std::string& addr, RecvCallback pCallback);
        void stopPlay(const std::string& addr);

        bool callPreset(VideoSourceInfoPtr videoSourceInfo, int ptzId) override;
        bool savePreset(VideoSourceInfoPtr videoSourceInfo, int ptzId) override;
        bool focusRect(VideoSourceInfoPtr videoSourceInfo, int xTop, int yTop, int xBottom, int yBottom);

        bool controlPtz(VideoSourceInfoPtr videoSourceInfo, network::EPtzCommand cmd, int step) override;

    private:
        void setOnline(bool status);
        void updateResource();
        bool getResource(mpc::nsdk::IMediaIterator* it);

        std::once_flag   initflag;
        std::mutex mtxLogin;
        std::mutex mtxStartPlay;
        std::mutex mtxResource;
        std::condition_variable cvLogin;
        steady_clock::time_point lastLoginTime;
        steady_clock::time_point lastResUpdateTime;
        std::string lastPlatInfo;

        std::atomic_bool logining = false;
        MPCSessionNotify platNotify;
        MPCMediaStatusNotify resourceNotify;
        mpc::nsdk::IManageSession* mgrSession = nullptr;
        std::map<std::string, mpc::nsdk::ILivingStream*> livingStreams;
    };

    typedef std::shared_ptr<platform::SDKMPCPlatform> SDKMPCPlatformPtr;

    class MPCStreamHandler : public mpc::nsdk::IStreamHandler
    {
    public:
        MPCStreamHandler(RecvCallback pCallback) :cb(pCallback) {}
        void stream(const uint8_t* buf, const size_t len, const uint32_t realTime)
        {
            // 检查H.264 NAL单元类型，过滤掉未定义的类型0
            if (len > 4)
            {
                // 检查NAL单元起始码
                if ((buf[0] == 0x00 && buf[1] == 0x00 && buf[2] == 0x00 && buf[3] == 0x01) || 
                    (buf[0] == 0x00 && buf[1] == 0x00 && buf[2] == 0x01))
                {
                    int offset = (buf[2] == 0x01) ? 3 : 4;
                    if (offset < len)
                    {
                        uint8_t nal_header = buf[offset];
                        uint8_t nal_type = nal_header & 0x1F;
                        if (nal_type == 0) // 未定义的NAL类型
                        {
                            return; // 直接丢弃这个包
                        }
                    }
                }
            }
            
            cb((uint8_t*)buf, (int)len);
        }

        void startup() {}
        void establish(const EStreamType md) {}
        void terminate() {}
        void shutdown() {}
        void release() { ref--; if (ref <= 0) { delete this; } }
        void addRef() { ref++; }

    private:
        RecvCallback cb;
        std::atomic_int ref = 0;
    };
};