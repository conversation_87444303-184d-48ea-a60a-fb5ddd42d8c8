/**
 * @addtogroup odbDatabaseGroup
 * @brief 轮切方案
 * @{
 */
#ifndef _ROLLPROJECT_H
#define _ROLLPROJECT_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
/**
 * @brief  轮切方案: 对应数据库aimonitorV3的表wn_roll_project
 */
#pragma db object table("wn_roll_project")
class RollProject {
public:

    RollProject(bool isDel,bool isEnable)
     : isDel(isDel), isEnable(isEnable)
    {
    }

    unsigned long getId() const {
        return id;
    }

    bool getIsDel() const {
        return isDel;
    }

    void setIsDel(bool del) {
        this->isDel = del;
    }

    bool getIsEnable() const {
        return isEnable;
    }

    void setIsEnable(bool enable) {
        this->isEnable = enable;
    }
private:

    friend class odb::access;
    RollProject() {}

private:
#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("is_del") type("INT")
    bool isDel;                         //!< 是否删除

#pragma db column("is_enable") type("INT")
    bool isEnable;                      //!< 是否使能
};
}
#endif //_ROLLPROJECT_H
/**
 * @}
 */