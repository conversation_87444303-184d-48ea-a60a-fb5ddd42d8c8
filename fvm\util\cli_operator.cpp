#include "cli_operator.h"
#include "command_process.h"
#include "stream/stream_manager.h"
#include "stream/output/video_recorder.h"
#include "config/fvm_config.h"

#ifdef __cplusplus
extern "C"
{
#endif
#include "libavutil/pixdesc.h"
#ifdef __cplusplus
}
#endif
#include "stream/input/sdk_hk_puller.h"
#include "platform/sdk_hk_platform.h"

extern bool appRunning;

namespace fvm
{
    using namespace cli;
    using namespace std;

    std::shared_ptr<CommandProcess> cmdProcPtr = nullptr;

    /**
     * @brief      设置ffmpeg内部log
     */
    void setFFmpegLog(int level)
    {
        switch (level)
        {
            case 0:default:
                av_log_set_level(AV_LOG_QUIET);
                std::cout << "av_log_set_level - " << "QUIET" << std::endl;
                break;
            case 1:
                av_log_set_level(AV_LOG_TRACE);
                std::cout << "av_log_set_level - " << "TRACE" << std::endl;
                break;
            case 2:
                av_log_set_level(AV_LOG_DEBUG);
                std::cout << "av_log_set_level - " << "DEBUG" << std::endl;
                break;
            case 3:
                av_log_set_level(AV_LOG_INFO);
                std::cout << "av_log_set_level - " << "INFO" << std::endl;
                break;
            case 4:
                av_log_set_level(AV_LOG_WARNING);
                std::cout << "av_log_set_level - " << "WARNING" << std::endl;
                break;
            case 5:
                av_log_set_level(AV_LOG_ERROR);
                std::cout << "av_log_set_level - " << "ERROR" << std::endl;
                break;
        }
    }

    void startCli()
    {
        //默认设置ffmmpeg内部日志等级
        setFFmpegLog(SETTINGS->fflog());

        cmdProcPtr = make_shared<CommandProcess>();
        //! 添加测试命令
        cmdProcPtr->addCommand("test", [](const cli::consoleParamList& paramList) {
            CHECK_PARAM_NUM(paramList, 2);
            int a = GET_PARAM_INT(paramList[0]);
            int b = GET_PARAM_INT(paramList[1]);
            cout << "test a+b = " << a + b << endl;
            std::cout << "command execute successfully !" << std::endl;
        }, "test a+b, Parameters: int int");

        //! 添加流状态查看命令
        cmdProcPtr->addCommand("status", [](const cli::consoleParamList& paramList) {
            std::map<int, stream::DetectChannelPtr> channels;
            if (paramList.empty())
            {
                channels = stream::getDetectChannels();
                for (const auto& [channelId, channelPtr] : channels)
                {
                    std::cout << "channel[" << channelId << "] " << channelPtr->getStreamStatus() << std::endl;
                }
                auto tempChannels = stream::getTempChannels();
                for (const auto& [eType, channelPtr] : tempChannels)
                {
                    if ( channelPtr->getInputVideoId() > 0 )
                        std::cout << "channel[" << (eType== stream::ChannelType::TempRequest ? "Temp" : "Set") << "] " 
                            << channelPtr->getStreamStatus() << std::endl;
                }
            }
            else
            {
                int channelBegId = GET_PARAM_INT(paramList[0]);
                int channelEndId = GET_PARAM_INT(paramList[0]);
                if (paramList.size() > 1) {
                    channelEndId = GET_PARAM_INT(paramList[1]);
                }
                int channelId = GET_PARAM_INT(paramList[0]);
                for (int channelId = channelBegId; channelId <= channelEndId; ++channelId) {
                    auto channelPtr = stream::getDetectChannel(channelId);
                    if (channelPtr)
                        std::cout << "channel[" << channelId << "] " << channelPtr->getStreamStatus(true) << std::endl;
                    else
                        std::cout << "channel[" << channelId << "]  error!\n" << std::endl;
                }
            }
            std::cout << "command execute successfully !\n" << std::endl;
            }, "print stream status info, Parameters: int [int] or none");

        //! 添加查看流编码信息命令
        cmdProcPtr->addCommand("codec", [](const cli::consoleParamList& paramList) {
            std::map<int, stream::DetectChannelPtr> channels;
            stream::DetectChannelPtr detectChannelPtr;
            if (paramList.empty())
            {
                channels = stream::getDetectChannels();
                for (const auto&[channelId, channelPtr] : channels)
                {
                    auto codecPtr = channelPtr->getCodecInfo();
                    std::cout << "channel[" << channelId << "] : ";
                    if (codecPtr)
                    {
                        std::cout << avcodec_get_name(codecPtr->getAVCodecParam()->codec_id);
                        std::cout << ", " << codecPtr->getWidth() << "x" << codecPtr->getHeight();
                        std::cout << ", " << av_get_pix_fmt_name((AVPixelFormat) codecPtr->getAVCodecParam()->format);
                        std::cout << std::endl;
                    }
                }
            }
            else
            {
                auto channelId = GET_PARAM_INT(paramList[0]);
                detectChannelPtr = stream::getDetectChannel(channelId);
                if (detectChannelPtr)
                {
                    auto codecPtr = detectChannelPtr->getCodecInfo();
                    std::cout << "channel[" << channelId << "] : ";
                    if (codecPtr)
                    {
                        std::cout << avcodec_get_name(codecPtr->getAVCodecParam()->codec_id);
                        std::cout << ", " << codecPtr->getWidth() << "x" << codecPtr->getHeight();
                        std::cout << ", " << av_get_pix_fmt_name((AVPixelFormat) codecPtr->getAVCodecParam()->format);
                        std::cout << std::endl;
                    }
                }
            }

            std::cout << "command execute successfully !" << std::endl;
        }, "print stream codec info, Parameters: int or none");

        //! 添加ffmpeg 内部日志开关选项
        cmdProcPtr->addCommand("fflog", [](const cli::consoleParamList& paramList) 
            {
                int logid = GET_PARAM_INT(paramList[0]);
                setFFmpegLog(logid);
            }, "fflog 1, Parameters 0-QUIET 1-TRACE 2-DEBUG 3-INFO 4-WARNING 5-ERROR");

        //! 添加快速录像命令
        cmdProcPtr->addCommand("save", [](const cli::consoleParamList& paramList) {
            CHECK_PARAM_NUM(paramList, 2);
            int channelId = GET_PARAM_INT(paramList[0]);
            int second = GET_PARAM_INT(paramList[1]);
            //最高不能超过配置文件中的时间
            if (second > SETTINGS->cmdSaveLength())
                second = SETTINGS->cmdSaveLength();
            auto channelPtr = stream::getDetectChannel(channelId);
            if (channelPtr)
            {
                auto outputPtr = channelPtr->getOutput(stream::StreamOuputType::File);
                auto pRecord = std::dynamic_pointer_cast<stream::VideoRecorder>(outputPtr);
                if (pRecord->cmdSaveStart(second))
                    std::cout << "channel " << channelId << " start record " << second << " s video" << std::endl;
                else
                    std::cout << "channel " << channelId << " haved a record! " << std::endl;
            }
            else
                std::cout << "channel "<< channelId <<" error!" << std::endl;
        }, "save 1 30, Parameters: int int");

        //! 停止快速录像命令
        cmdProcPtr->addCommand("unsave", [](const cli::consoleParamList& paramList) {
            CHECK_PARAM_NUM(paramList, 1);
            int channelId = GET_PARAM_INT(paramList[0]);
            auto channelPtr = stream::getDetectChannel(channelId);
            if (channelPtr)
            {
                auto outputPtr = channelPtr->getOutput(stream::StreamOuputType::File);
                auto pRecord = std::dynamic_pointer_cast<stream::VideoRecorder>(outputPtr);
                if (pRecord->cmdSaveStop())
                    std::cout << "channel " << channelId << " stop record!" << std::endl;
                else
                    std::cout << "channel " << channelId << " have no record task!" << std::endl;
            }
            else
                std::cout << "channel " << channelId << " error!" << std::endl;
            }, "unsave 1, Parameters: int");
        
        //! 停止通道取流
        cmdProcPtr->addCommand("stop", [](const cli::consoleParamList& paramList) {
            CHECK_PARAM_NUM(paramList, 1);
            int channelId = GET_PARAM_INT(paramList[0]);
            auto channelPtr = stream::getDetectChannel(channelId);
            if (channelPtr)
            {
                auto inputPtr = channelPtr->getInput();
                if (inputPtr) {
                    inputPtr->stopPlay(false);
                    std::cout << "command execute successfully !" << std::endl;
                }
            }
            else
                std::cout << "channel " << channelId << " error!" << std::endl;
            }, "stop 1, Parameters: int");

        //! 通道取流
        cmdProcPtr->addCommand("start", [](const cli::consoleParamList& paramList) {
            CHECK_PARAM_NUM(paramList, 1);
            int channelId = GET_PARAM_INT(paramList[0]);
            auto channelPtr = stream::getDetectChannel(channelId);
            if (channelPtr)
            {
                auto inputPtr = channelPtr->getInput();
                if (inputPtr) {
                    inputPtr->startPlay();
                    std::cout << "command execute successfully !" << std::endl;
                }
            }
            else
                std::cout << "channel " << channelId << " error!" << std::endl;
            }, "start 1, Parameters: int");

        //! 重置通道取流
        cmdProcPtr->addCommand("restart", [](const cli::consoleParamList& paramList) {
            CHECK_PARAM_NUM(paramList, 1);
            int channelId = GET_PARAM_INT(paramList[0]);
            auto channelPtr = stream::getDetectChannel(channelId);
            if (channelPtr)
            {
                auto inputPtr = channelPtr->getInput();
                if (inputPtr) {
                    inputPtr->stopPlay(false);
                    inputPtr->waitForFinished();
                    inputPtr->startPlay();
                    std::cout << "command execute successfully !" << std::endl;
                }
            }
            else
                std::cout << "channel " << channelId << " error!" << std::endl;
            }, "restart 1, Parameters: int");

        //! 增加通道聚焦
        cmdProcPtr->addCommand("focus", [](const cli::consoleParamList& paramList) {
            CHECK_PARAM_NUM(paramList, 5);
            int channelId = GET_PARAM_INT(paramList[0]);
            int xTop = GET_PARAM_INT(paramList[1]);
            int yTop = GET_PARAM_INT(paramList[2]);
            int xBottom = GET_PARAM_INT(paramList[3]);
            int yBottom = GET_PARAM_INT(paramList[4]);
            auto channelPtr = stream::getDetectChannel(channelId);
            if (channelPtr)
            {
                auto inputPtr = channelPtr->getInput();
                auto pullerPtr = dynamic_pointer_cast<fvm::stream::PlatformPuller>(inputPtr);
                if (pullerPtr)
                {
                    pullerPtr->focusRect(xTop, yTop, xBottom, yBottom);
                    std::cout << "command execute successfully !" << std::endl;
                }
            }
            else
                std::cout << "channel focus" << channelId << " error!" << std::endl;
            }, "focus 5, Parameters: int");

        //! 添加帮助命令
        cmdProcPtr->addCommand("help", [&](const cli::consoleParamList& paramList) {
            auto commands = cmdProcPtr->getCommands();
            std::string title = "COMMANDS:                       DESCRIPTION:";
            std::cout << std::endl << title << std::endl;
            auto pos = title.find("DESCRIPTION");
            for (auto &command : commands)
            {
                std::string commandDes = "                                            ";
                commandDes.insert(0, command.name);
                commandDes.insert(pos, command.description);
                std::cout << commandDes << std::endl;
            }
            std::cout << "command execute successfully !" << std::endl;
        }, "show usage info");

        //! 添加查看软件版本命令
        cmdProcPtr->addCommand("version", [](const cli::consoleParamList& paramList) {
            showVersion();
        }, "show version info");

        //! 添加退出程序命令
        cmdProcPtr->addCommand("quit", [&](const cli::consoleParamList& paramList) {
            appRunning = false;
            }, "quit fvm");

        cmdProcPtr->start();

    }


    void stopCli()
    {
        cmdProcPtr->stop();
        cmdProcPtr.reset();
    }

}
