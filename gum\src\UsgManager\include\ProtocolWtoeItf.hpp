
#ifndef PROTOCOLWTOEITF_HPP_
#define PROTOCOLWTOEITF_HPP_

#if defined( WIN32 ) && defined( _MSC_VER )
#    ifdef PROTOCOLWTOEITF_IMPL
#	     define PROTOCOLWTOEITF_PUBLIC __declspec( dllexport )
#    else
#	     define PROTOCOLWTOEITF_PUBLIC __declspec( dllimport )
#    endif
#elif defined( __GNUC__ )
#    define PROTOCOLWTOEITF_PUBLIC //__attribute__((visibility("default")))
#endif

#include <string>
#include <stdint.h>

#include "WtoeStruct.hpp"
#include "UsgProtocolGbt/include/Gb28181Struct.hpp"

namespace usg
{

    enum ESESSTION_TYPE{
        SESSTION_TYPE_NONE,
        SESSTION_TYPE_NOTIFY,
        SESSTION_TYPE_DDCPDO,
        SESSTION_TYPE_REGIST,
        SESSTION_TYPE_INVITE,
    };


    struct IWtoeSipFunc;

    struct IProtocolWtoe
    {
        virtual bool startup( usg::IWtoeSipFunc* wsf, const std::string &relativePath ) = 0;
        virtual bool shutdown() = 0;

        // 上层调用此函数,获取SIP通信信息,并将得到的通信信息给SIP通信模块.
        virtual bool getAccess( std::string &rAddr, uint16_t &rPort, std::string &rCode,
                                std::string &lAddr, uint16_t &lPort, std::string &lCode ) = 0;

        virtual bool getUserNameAndPasswd( std::string &userName, std::string &password )
        {
            userName = "";
            password = "";

            return true;
        }

        //
        virtual bool receivePkg( const std::string &sid, const std::string &resId, const std::string &inString, std::string &outString ) = 0;

        //for gb28181
        virtual bool receivePkg( const std::string &sid, const std::string &resId, const std::string &inString, std::vector<std::string> &outString ){ return true; };
        virtual bool receiveSubscribePkg( const std::string &sid, const std::string &resId, const std::string &inString, std::string &outString ){ return true; }
        virtual bool getDeviceRebootString( std::string &outString ) {return true;}
        virtual bool getRecordContronlString( std::string &outString, bool flag ) {return true;}
        virtual bool getGuardContronlString( std::string &outString, bool flag ) {return true;}
        virtual bool getAlarmResetString( std::string &outString ) {return true;}
        virtual bool getQueryDeviceCatalogString( std::string &outString ) { return true; }
        virtual bool getQueryDeviceInfoString( std::string &outString, std::string &sn ) { return true; }
        virtual bool getQueryDeviceStatusString( std::string &outString, std::string &sn ) { return true; }
        virtual bool setSesstionType( const ESESSTION_TYPE type ) { return true; }
        virtual bool getSesstionType( ESESSTION_TYPE &type ) { return true; }
        //add by jch，用于获取格式信息（如视频尺寸、码率等）
        virtual bool getFormatInfo(std::string &format){ return true; }
        virtual bool getSrcAddr( std::string& addr ) { return true; }

        virtual bool receivePkg( const std::string &sid, const std::string &resId, const std::string &inString, std::string &outString,const bool answer)
        {
            return receivePkg(sid,resId,inString,outString);
        };

        virtual bool receiveBye( const std::string &sid ) = 0;
        virtual bool receiveCancel( const std::string &sid ) = 0;
        virtual bool receiveAck( const std::string &sid ) = 0;


        // 本软件主动发的包要求转换数据
        // 另:被动发的包,在执行过程作转换,所以没有作为接口函数.
        virtual bool getCatalogItemSize( size_t &size ) = 0;
        // from从1开始. // 如果to过大,则结果截取.
        virtual bool getCatalogString( const size_t from, const size_t to, std::string &outString ) = 0;

        virtual bool getKeepaliveString( std::string &outString ) = 0;

        virtual bool getRealMediaString( const uint8_t imageSize,const std::string& clientAddr, std::string &outString )  = 0;
        virtual bool getHistoryListString( const uint32_t startTime, const uint32_t endTime, std::string &outString, std::string &sn  ) = 0;
        virtual bool getHistoryMediaString( const std::string& fileName, const uint32_t startTime, const uint32_t endTime, std::string &outString ) = 0;
        virtual bool getHistoryMediaString( const std::string& fileName, const uint32_t startTime, const uint32_t endTime,const int rtpPort, std::string &outString ) { return true; };
        virtual bool getHistoryMediaString( const std::string& fileName, const uint32_t startTime, const uint32_t endTime,const ACE_INET_Addr &resAddr, std::string &outString ) { return true; }
        virtual bool getDownloadMediaString( const std::string& fileName, const uint32_t startTime, const uint32_t endTime,const int rtpPort, std::string &outString ) { return true; };
        virtual bool getDownloadMediaString( const std::string& fileName, const uint32_t startTime, const uint32_t endTime,const ACE_INET_Addr &resAddr, std::string &outString ) { return true; }
        virtual bool getPtzCommandString(  const usg::SPtzCommand& command, std::string &outString ) = 0;
        virtual bool getPresetListString( const uint8_t formIndex, const uint8_t toIndex, std::string &outString ) = 0;

        virtual bool getDeviceConfigResponseString( bool isOk, const std::string &sn, const std::string &sid, std::string &outString ) { return true; };
        virtual bool getQueryDeviceConfigResponseString( const std::string &sn, const std::string &sid, const gb28181::SDeviceConfigQueryResult &result, std::string &outString ) { return true; };
        virtual bool getAlarmNotifyString(  const std::string &sn, const boost::uuids::uuid &resId, const gb28181::SAlarmInfo &info, std::string &outString ) { return true; };
        virtual bool getCatalogNotifyString( const std::string &sn, const std::string &sid, std::vector<std::string> &outString ) { return true; };
        virtual bool getReplayEndNotifyString( const std::string &sn, const boost::uuids::uuid &resId, std::string &outString ) { return true; };

        virtual bool getPlayCommandString(const int cseq,std::string &outString){ return true; };
        virtual bool getPauseCommandString(const int cseq,std::string &outString){ return true; };
        virtual bool getSetSpeedCommandString(const int cseq,uint8_t n,uint8_t d,std::string &outString){ return true; };
        virtual bool getSetTimeSpanCommandString(const int cseq,uint32_t s,uint32_t e,std::string &outString){ return true; };
        virtual bool getTeardownCommandString(const int cseq,std::string &outString){ return true; };

        virtual bool getSubscribeCatalogString(const uint32_t startTime, const uint32_t endTime, std::string &outString){ return true; };
        virtual bool getSubscribeAlarmString(const gb28181::SAlarmSubscribeParam &param,std::string &outString){ return true; };

        virtual bool getAudioBroadcastString(const std::string &sourceId, const std::string &targetId, std::string &outString){ return true; };
        virtual bool getReceiveBroadcastResponse( const std::string &sn, const std::string deviceId, bool isOk, std::string &outString ) { return true; };

        virtual bool setCatalogItems( const std::vector< gb28181::SCatalog::SItem > &items ) { return true; };
        virtual bool getCatalogItemNotifyString( const std::string &sn, const gb28181::SCatalog::SItem &item, std::vector<std::string> &outString ) { return true; };

    protected:
        virtual ~IProtocolWtoe(){}
    };

}

extern "C"
{
PROTOCOLWTOEITF_PUBLIC usg::IProtocolWtoe *getProtocolLibInstance();
typedef usg::IProtocolWtoe * (*pfnGetProtocolLibInstance)();
};

#endif
