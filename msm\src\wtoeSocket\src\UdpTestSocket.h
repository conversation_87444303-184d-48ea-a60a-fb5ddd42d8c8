#ifndef _UDPTESTSOCKET_H
#define _UDPTESTSOCKET_H

#include "UdpSocket.h"
#include "SocketHandler.h"
#include "MessageHandle.h"
#include "MessageSNSave.h"

class UdpTestSocket : public UdpSocket
{
public:
	UdpTestSocket(ISocketHandler&);
	~UdpTestSocket();
	void setMessageHandle(MessageHandle *pMessageHandle);
	//void SetMessageSNSaveHandle(MessageSNSave *pMessageSnSaveHandle);

	void OnRawData(const char *,size_t,struct sockaddr *,socklen_t);

private:
	MessageHandle *m_pMessageHandle;
	//MessageSNSave *m_pMessageSNSave;

};

#endif // _UDPTESTSOCKET_H
