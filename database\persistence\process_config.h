/**
 * @addtogroup odbDatabaseGroup
 * @brief iva多进程配置信息
 * @{
 */
#ifndef _PROCESS_CONFIG_H
#define _PROCESS_CONFIG_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
/**
 * @brief  iva多进程配置信息 对应数据库aimonitorV3的表wn_process_config
 */
#pragma db object table("wn_process_config")
class ProcessConfig {
public:

    ProcessConfig(unsigned long processId,
        int processPort,
        int startChannel,
        int endChannel,
        const std::string& networkCard,
        int gpuCard,
        int fvmPort,
        int streamType,
        int fvmStatus,
        const std::string& fvmUpdateTime,
        int ivaStatus,
        const std::string& ivaUpdateTime
        )

        : processId(processId), processPort(processPort), startChannel(startChannel),
          endChannel(endChannel), networkCard(networkCard), gpuCard(gpuCard),
          fvmPort(fvmPort), streamType(streamType), fvmStatus(fvmStatus),
          fvmUpdateTime(fvmUpdateTime), ivaStatus(ivaStatus), ivaUpdateTime(ivaUpdateTime)
    {
    }

    unsigned long getId() const {
        return id;
    }

    unsigned long getProcessId() const {
        return processId;
    }

    void setProcessId(unsigned long id) {
        this->processId = id;
    }

    int getProcessPort() const {
        return processPort;
    }

    void setProcessPort(int port) {
        this->processPort = port;
    }

    int getStartChannel() const {
        return startChannel;
    }

    void setStartChannel(int ch) {
        this->startChannel = ch;
    }

    int getEndChannel( )const {
        return endChannel;
    }

    void setEndChannel(int ch) {
        this->endChannel = ch;
    }

    const std::string& getNetworkCard() const {
        return networkCard;
    }

    void setNetworkCard(const std::string& card) {
        this->networkCard = card;
    }

    int getGpuCard() const {
        return gpuCard;
    }

    void setGpuCard(int card) {
        this->gpuCard = card;
    }

    int getFvmPort() const {
        return fvmPort;
    }

    void setFvmPort(int port) {
        this->fvmPort = port;
    }

    int getStreamType() const {
        return streamType;
    }

    void setStreamType(int type) {
        this->streamType = type;
    }

    int getFvmStatus() const {
        return fvmStatus;
    }

    void setFvmStatus(int status) {
        this->fvmStatus = status;
    }

    const std::string& getFvmUpdateTime() const {
        return fvmUpdateTime;
    }

    void setFvmUpdateTime(const std::string& time) {
        this->fvmUpdateTime = time;
    }

    int getIvaStatus() const {
        return ivaStatus;
    }

    void setIvaStatus(int status) {
        this->ivaStatus = status;
    }

    const std::string& getIvaUpdateTime() const {
        return ivaUpdateTime;
    }

    void setIvaUpdateTime(const std::string& time) {
        this->ivaUpdateTime = time;
    }


private:

    friend class odb::access;
    ProcessConfig() {}


private:

#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("process_id")
    unsigned long processId;            //!< iva进程id

#pragma db column("process_port")
	int processPort;                    //!< iva进程对应的fvm 消息端口号

#pragma db column("start_channel")
	int startChannel;                   //!< iva进程对应检测起始通道

#pragma db column("end_channel")
	int endChannel;                     //!< iva进程对应检测结束通道

#pragma db column("network_card")  type("VARCHAR(255)")
	std::string networkCard;            //!< iva进程对应网卡

#pragma db column("gpu_card")
	int gpuCard;                        //!< iva进程对应显卡id

#pragma db column("fvm_port")
	int fvmPort;                        //!< iva进程对应的fvm流端口号

#pragma db column("streamType")
	int streamType;                     //!< iva进程取流类型

#pragma db column("fvm_status")
	int fvmStatus;                      //!< fvm状态

#pragma db column("fvm_update_time")  type("VARCHAR(255)")
    std::string fvmUpdateTime;          //!< fvm更新时间

#pragma db column("iva_status")
	int ivaStatus;                      //!< iva状态

#pragma db column("iva_update_time")  type("VARCHAR(255)")
    std::string ivaUpdateTime;         //!< iva更新时间

};
}
#endif //_PROCESS_CONFIG_H

/**
 * @}
 */