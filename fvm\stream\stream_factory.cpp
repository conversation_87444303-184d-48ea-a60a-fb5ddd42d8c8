/**
 * Project FVM
 */
#include "stream_factory.h"
#include "input/file_puller.h"
#include "input/stream_puller.h"
#include "input/gb28181_puller.h"
#include "input/sdk_mpc_puller.h"
#include "input/platform_puller.h"
#include "output/stream_pusher.h"
#include "output/video_recorder.h"
#include "platform/gb28181_platform.h"
#include "platform/sdk_mpc_platform.h"
#ifdef SDK_HK_SUPPORTED
#include "input/sdk_hk_puller.h"
#include "platform/sdk_hk_platform.h"
#endif
#ifdef SDK_DH_SUPPORTED
#include "input/sdk_dh_puller.h"
#include "platform/sdk_dh_platform.h"
#endif

#include "ailog.h"
/**
 *  @brief: 视频流创建接口
 */
namespace fvm::stream
{
    using namespace data;
    using namespace platform;

    // 前端接入平台列表 <frontID, FrontPlatformPtr>
    extern std::map<int, FrontPlatformPtr> frontPlatforms;

    /**
     * 基于视频资源信息创建取流器
     * @param VideoSourceInfo
     * @return StreamInputPtr
     */
    StreamInputPtr createStreamInput(VideoSourceInfoPtr videoInfo)
    {
        StreamInputPtr input = nullptr;
        FrontPlatformPtr platform = nullptr;
        switch (videoInfo->streamInputType)
        {
            case StreamInputType::File:
                input = std::make_shared<FilePuller>();
                break;
            case StreamInputType::RTSP:
                input = std::make_shared<StreamPuller>();
                break;
            case StreamInputType::GB28181:
            {
                platform = getFrontPlatform(videoInfo->videoSourcePtr->getVideoServerId());
                input = std::make_shared<GB28181Puller>(platform);
            }
                break;
            case StreamInputType::SDK:
            {
                platform = getFrontPlatform(videoInfo->videoSourcePtr->getVideoServerId());
                if ( platform->serverPtr->getFactory() == "MPC")
                    input = std::make_shared<SDKMPCPuller>(platform);
#ifdef SDK_HK_SUPPORTED
                else if (platform->serverPtr->getFactory() == "WTOE")
                    input = std::make_shared<SDKHKPuller>(platform);
#endif
#ifdef SDK_DH_SUPPORTED
                else if (platform->serverPtr->getFactory() == "DaHua")
                    input = std::make_shared<SDKDHPuller>(platform);
#endif
                else
                {
                    ai::LogWarn << "Stream input : (SDK : " << platform->serverPtr->getFactory() << ") not supported ! ";
                    input = std::make_shared<StreamInput>();
                }

            }
                break;
            default:
                ai::LogWarn << "Stream input : " << (int)videoInfo->streamInputType << " not supported ! ";
                input = std::make_shared<StreamInput>();
                break;
        }
        if (input != nullptr)
        {
            input->initSource(videoInfo);
        }

        // 枪球联动 绑定
        if (videoInfo->videoSourcePtr->getVideoType() == 1) // 枪球一体 枪机
        {
            std::string groupUUID = videoInfo->videoSourcePtr->getGroupUUID();
            int groupID = videoInfo->videoSourcePtr->getGroupId();
            if (!groupUUID.empty()) // 二代平台关系
            {
                auto domeVideoSource = DATA_MANAGER.queryDomeVideoSource(groupUUID);
                if (domeVideoSource)
                {
                    input->bindDome(platform);
                    input->bindDomeSource(domeVideoSource.value());
                }
            }
            else if(groupID > 0)
            {
                auto platformDome = getFrontPlatform(groupID);
                if (platformDome)
                {
                    input->bindDome(platformDome);
                }
            }
        }

        return input;
    }

    /**
     * 创建发流器
     * @param outputType 输出类型
     * @param port 端口
     * @param baseUrl 地址
     * @return StreamOutputPtr
     */
    StreamOutputPtr createStreamOutput(StreamOuputType outputType, int port, std::string baseUrl)
    {
        StreamOutputPtr output = nullptr;
        std::string address;
        switch (outputType)
        {
            case StreamOuputType::RTP:
                output = std::make_shared<StreamPusher>(outputType);
                address = str(boost::format("rtp://%s:%d") % baseUrl % (port));
                break;
            case StreamOuputType::UDP:
                output = std::make_shared<StreamPusher>(outputType);
                address = str(boost::format("udp://%s:%d") % baseUrl % (port));
                break;
            case StreamOuputType::RTMP:
                output = std::make_shared<StreamPusher>(outputType);
                address = str(boost::format("rtmp://%s/live/%d") % baseUrl % (port));
                break;
            case StreamOuputType::File:
                output = std::make_shared<VideoRecorder>(outputType);
                address = std::string(baseUrl);
                break;
            default:
                ai::LogWarn << "Stream output not supported !";
                break;
        }
        if (output != nullptr)
        {
            output->initAddress(address);
            output->setPort(port);
        }
        return output;
    }

    /**
     * 获取资源的视频平台
     * @param VideoSourceInfo 视频资源信息
     * @return FrontPlatformPtr
     */
    FrontPlatformPtr getFrontPlatform(int platformID)
    {
        VideoServerPtr serverPtr = DATA_MANAGER.getVideoServer( platformID);
        if ( !serverPtr ){
            ai::LogWarn << "Platform " << platformID << " is not exist !";
            return nullptr;
        }
        if(frontPlatforms.find(platformID) != frontPlatforms.end())
        {
             //类型改变后重新初始化
            auto platform = frontPlatforms[platformID];
            if(serverPtr->getAccessType() == platform->serverPtr->getAccessType() && serverPtr->getFactory() == platform->serverPtr->getFactory())
            {
                frontPlatforms[platformID]->init( serverPtr );
                return frontPlatforms[platformID];
            }
            
            ai::LogInfo<<"getFrontPlatform type diffrent "<<platformID;
            frontPlatforms.erase( platformID );
            platform->onChanged();
            platform.reset();
        }

        FrontPlatformPtr platform = nullptr;
        switch ((StreamInputType)serverPtr->getAccessType())
        {
            case StreamInputType::GB28181:
                platform = std::make_shared<GB28181Platform>();
                break;
            case StreamInputType::SDK:
                if ( serverPtr->getFactory() == "MPC" )
                    platform = std::make_shared<SDKMPCPlatform>();
#ifdef SDK_HK_SUPPORTED
                else if (serverPtr->getFactory() == "WTOE")
                    platform = std::make_shared<SDKHKPlatform>();
#endif
#ifdef SDK_DH_SUPPORTED
                else if (serverPtr->getFactory() == "DaHua")
                    platform = std::make_shared<SDKDHPlatform>();
#endif
                else
                    platform = std::make_shared<FrontPlatform>();
                break;
            case StreamInputType::File:
            case StreamInputType::RTSP:
                break;
            default:
                ai::LogWarn << "Front platform not supported !";
                break;
        }

        if(platform != nullptr)
        {
            frontPlatforms[platformID] = platform;
            platform->init( serverPtr );
        }
        return platform;
    }

    /**
     * 更新远端信息
     */
    void updatePlatform( int platformID )
    {
        VideoServerPtr serverPtr = DATA_MANAGER.getVideoServer( platformID);
        FrontPlatformPtr platform = nullptr;
        if ( !serverPtr )  //数据库中已经没有这个前端，通知远端，并且从map中删除
        {
            if(frontPlatforms.find(platformID) != frontPlatforms.end())
            {
                platform = frontPlatforms[platformID];
                frontPlatforms.erase( platformID );
                platform->onChanged();
                platform.reset();
                return;
            }
            else  //系统中没有这个地址，还是向msm和gum都发送一次删除的消息
            {
                RequestRemoteChanged msg;
                msg.iRemoteId = platformID;
                PROTOCOL_MANAGER.sendTo(DATA_MANAGER.getMsmIP(), DATA_MANAGER.getMsmPort(), network::ProtocolType::UDP_REMOTECHANGED, msg );
                PROTOCOL_MANAGER.sendTo(DATA_MANAGER.getGumIP(), DATA_MANAGER.getGumPort(), network::ProtocolType::UDP_REMOTECHANGED, msg );
            }
        }
        else //数据库中有这个
        {
            platform = getFrontPlatform( platformID );
            if (platform)
                platform->onChanged();
        }        
    }
}