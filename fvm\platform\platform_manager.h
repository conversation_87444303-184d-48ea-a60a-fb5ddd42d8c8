//
// Project FVM
//
#pragma once

#include "stream/input/platform_puller.h"
#include "network.h"
/**
 * @brief 平台流管理器
 */
namespace fvm::platform {

    using namespace network;
    using namespace stream;

    using StreamPacketHandler = std::function<void(StreamPacketDataPtr)>;
    using RequestRetHandler = std::function<void(const std::string& )>;

    /**
     * 初始化tcp和udp服务器，方便后期gb28181取流
     * @return
     */
    bool initPlatformManager();

    bool disposePlatformManager();
    /** 
    * 获得当前未使用的端口
    */
    unsigned short getAvailPort(bool isUdp);

    /**
     * 注册和反注册接收请求流消息结果的对象
     */
     bool registerMsgPuller( int videoId, RequestRetHandler handler );
     void unregisterMsgPuller( int videoId );
     //处理请求视频的回应
     void dealRequestRet( RequestVideoRet msg );

     /** 
     * 向gum和msm发送fvm修改的通知
     */     
     void notifyGumMsm();

     /**
     * 判断端口是否可用
     */
     bool isPortUsable(unsigned short port, bool isUdp);

     /** 
     * 获得系统可用端口范围
     */
     void getPortRange();

    /** 
     * 通知FVM变化
     */
    void notifyChanged();
}



