/**
 * @addtogroup odbDatabaseGroup
 * @brief 灵敏度参数定义信息
 * @{
 */
#ifndef _ALGORITHMPARAM_H
#define _ALGORITHMPARAM_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>


namespace db {
/**
 * @brief  灵敏度参数定义信息: 对应数据库aimonitorV3的表wn_algorithm_param
 */
#pragma db object table("wn_algorithm_param")
class AlgorithmParam {
public:

    AlgorithmParam(const std::string &paramKey)
            : paramKey(paramKey) {
    }

    unsigned long getId() const {
        return id;
    }

    const std::string &getParamKey() const {
        return paramKey;
    }

    void setStartTime(const std::string &key) {
        this->paramKey = key;
    }

private:

    friend class odb::access;

    AlgorithmParam() {}

private:
#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("key")  type("VARCHAR(255)")
    std::string paramKey;               //!< 灵敏度参数Key
};
}

#endif //_ALGORITHMPARAM_H

/**
 * @}
 */