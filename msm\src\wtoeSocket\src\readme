

compile and install
=========================
1) install curl from source code which can be downloaded from offical website
2) modify lib-path and include-path variables of curl in Makefile
3) run 'make all'
4) run 'make install', this command will install the libwtoeSocket.so to /usr/local/lib

5) add include path and lib reference in other Makefile to use this .so library
=========================


http usage
=========================
#include "wtoe_http_client.h"
#include "wtoeSocketComm.h"

WtoeAIHttpClient http_client;
http_client.saveDetectInfo(...);
http_client.eventRecord(...);


udp usage
=========================
#include "wtoe_udp_lib.h"
#include "wtoeSocketComm.h"

WtoeUDPLib udp_client;
udp_client.SetDestPort(10000);
udp_client.StartUDPServer("0.0.0.0", 10001);
udp_client.requestInit();


