/**
 * Project FVM
 */

#include "worker_pool.h"
#include <boost/asio.hpp>

/**
 * @brief: 任务池管理
 *          主要定义各类模块任务线程
 */
namespace fvm::worker{

    std::shared_ptr<boost::asio::thread_pool> threadPool = nullptr;
    std::map<WorkerType, std::shared_ptr<fiberpool::WorkerPool>> workerPools;

    /*
     * 初始化 任务池
     */
    void init()
    {
        auto poolThreadCount = std::min(std::thread::hardware_concurrency(), 64u) - 2u;
        if(!threadPool)
            threadPool = std::make_shared<boost::asio::thread_pool>(poolThreadCount);

        workerPools.clear();
        workerPools[WorkerType::Channel] = std::make_shared<fiberpool::WorkerPool>(4u, 32u);
        workerPools[WorkerType::Message] = std::make_shared<fiberpool::WorkerPool>(2u, 32u);
        workerPools[WorkerType::StreamInput] = std::make_shared<fiberpool::WorkerPool>(64u, 64u);
        workerPools[WorkerType::StreamOutput] = std::make_shared<fiberpool::WorkerPool>(64u, 64u);
        workerPools[WorkerType::Record] = std::make_shared<fiberpool::WorkerPool>(8u, 32u);
        workerPools[WorkerType::Device] = std::make_shared<fiberpool::WorkerPool>(8u, 32u);
    }

    /*
     * 上报新任务
     * @param type 任务类型
     * @param work 具体任务
     */
    FiberFuture post(WorkerType type, std::function<void(void)> work)
    {
        if(workerPools.find(type) != workerPools.end())
            return workerPools[type]->submit(work);
        else
            return std::nullopt;
    }

    /*
     * 上报新任务 (额外线程池)
     * @param work 具体任务
     */
    void post(std::function<void(void)> work)
    {
        boost::asio::post(*threadPool, work);
    }

    /*
     * 关闭任务池队列
     */
    void close()
    {
        for(auto& [key, pool] : workerPools)
        {
            pool->close_queue();
        }
        if(threadPool)
            threadPool->join();
    }
}