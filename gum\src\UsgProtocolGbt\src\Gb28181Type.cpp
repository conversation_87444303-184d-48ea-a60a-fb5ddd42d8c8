#ifndef GB28181TYPE_CPP_
#define GB28181TYPE_CPP_

#include "../include/Gb28181Type.hpp"

namespace gb28181
{

boost::bimap< std::string, int > s_videoType = boost::bimap< std::string, int >();
boost::bimap< std::string, int > s_audioType = boost::bimap< std::string, int >();
boost::bimap< std::string, int > s_formatType = boost::bimap< std::string, int >();
boost::bimap< std::string, int > s_operateType = boost::bimap< std::string, int >();
boost::bimap< std::string, int > s_alarmType = boost::bimap< std::string, int >();
boost::bimap< std::string, int > s_socketType = boost::bimap< std::string, int >();

class CTypeInit
{
public:
    CTypeInit()
    {
        if( s_videoType.left.empty() )
        {
			s_videoType.insert( boost::bimap< std::string, int >::value_type( "MPEG-2", EVIDEOTYPE_MPEG_2 ) );
            s_videoType.insert( boost::bimap< std::string, int >::value_type( "MPEG-4", EVIDEOTYPE_MPEG_4 ) );
            s_videoType.insert( boost::bimap< std::string, int >::value_type( "H.264", EVIDEOTYPE_H_264 ) );
			s_videoType.insert( boost::bimap< std::string, int >::value_type( "SVAC", EVIDEOTYPE_SAVC ) );
        }

        if( s_audioType.left.empty() )
        {
            s_audioType.insert( boost::bimap< std::string, int >::value_type( "G.711", EAUDIOTYPE_G_711 ) );
            s_audioType.insert( boost::bimap< std::string, int >::value_type( "G.722", EAUDIOTYPE_G_722 ) );
            s_audioType.insert( boost::bimap< std::string, int >::value_type( "G.723.1", EAUDIOTYPE_G_723_1 ) );
            s_audioType.insert( boost::bimap< std::string, int >::value_type( "G.729", EAUDIOTYPE_G_729 ) );
        }

        if( s_formatType.left.empty() )
        {
            s_formatType.insert( boost::bimap< std::string, int >::value_type( "QCIF", EFORMATTYPE_QCIF ) );
            s_formatType.insert( boost::bimap< std::string, int >::value_type( "CIF", EFORMATTYPE_CIF ) );
            s_formatType.insert( boost::bimap< std::string, int >::value_type( "2CIF", EFORMATTYPE_2CIF ) );
            s_formatType.insert( boost::bimap< std::string, int >::value_type( "DCIF", EFORMATTYPE_DCIF ) );
            s_formatType.insert( boost::bimap< std::string, int >::value_type( "D1", EFORMATTYPE_D1 ) );
            s_formatType.insert( boost::bimap< std::string, int >::value_type( "4CIF", EFORMATTYPE_4CIF ) );
            s_formatType.insert( boost::bimap< std::string, int >::value_type( "16CIF", EFORMATTYPE_16CIF ) );
		}

        if( s_operateType.left.empty() )
        {
            s_operateType.insert( boost::bimap< std::string, int >::value_type( "ADD", EOPERATETYPE_ADD ) );
            s_operateType.insert( boost::bimap< std::string, int >::value_type( "DEL", EOPERATETYPE_DEL ) );
            s_operateType.insert( boost::bimap< std::string, int >::value_type( "MOD", EOPERATETYPE_MOD ) );
            s_operateType.insert( boost::bimap< std::string, int >::value_type( "OTH", EOPERATETYPE_OTH ) );
        }

        if( s_alarmType.left.empty() )
        {
            s_alarmType.insert( boost::bimap< std::string, int >::value_type( "Detect", EALARMTYPE_DETECT ) );
            s_alarmType.insert( boost::bimap< std::string, int >::value_type( "VDetect", EALARMTYPE_VDETECT ) );
            s_alarmType.insert( boost::bimap< std::string, int >::value_type( "VLost", EALARMTYPE_VLOST ) );
            s_alarmType.insert( boost::bimap< std::string, int >::value_type( "VHide", EALARMTYPE_VHIDE ) );
            s_alarmType.insert( boost::bimap< std::string, int >::value_type( "EventOnVideo", EALARMTYPE_EVENTONVIDEO ) );
            s_alarmType.insert( boost::bimap< std::string, int >::value_type( "OTH", EALARMTYPE_OTH ) );
        }

        if( s_socketType.left.empty() )
        {
            s_socketType.insert( boost::bimap< std::string, int >::value_type( "UDP", ESOCKETTYPE_UDP ) );
            s_socketType.insert( boost::bimap< std::string, int >::value_type( "TCPPASSIVE", ESOCKETTYPE_TCP_PASSIVE ) );
            s_socketType.insert( boost::bimap< std::string, int >::value_type("TCPACTIVE", ESOCKETTYPE_TCP_ACTIVE ) );
        }
    }
};

const static CTypeInit g_tmp;

}

#endif
