#include <wtoe/TestSupport/TestSupportExp.hpp>
#include <boost/date_time/posix_time/ptime.hpp>
#include <boost/lexical_cast.hpp>

#include "UsgManager/include/UsgManagerExp.hpp"

#include "Gb28181Parser.hpp"
#include "Gb28181XmlTag.hpp"
#include "Gb28181Xml.hpp"
#include "ConfigFile.hpp"
#include "UsgManager/include/WtoeSipFuncItf.hpp"
#include "UsgManager/include/UsgServiceTypeDefine.hpp"
#include "UsgManager/include/WtoeStruct.hpp"

namespace gb28181
{

    CGb28181Parser::CGb28181Parser(
            usg::IWtoeSipFunc* wsf,
            CConfigFile *configFile,
            const std::string &sid,
            const std::string &resAddr,
            const std::string &gbtXmlStr,
            const usg::ESESSTION_TYPE type)
            : m_wsf( wsf )
            , m_sid( sid )
            , m_resAddr( resAddr )
            , m_snContent("")
            , m_pool( 0 )
            , m_root( 0 )
            , m_isOk( false )
            , m_isInitCachingPool( false )
            , m_type(type)
            , m_gbtXml( 0 )
            , m_configFile( configFile )
    {
        if( m_wsf == 0 )
            return;

        if( m_configFile == 0 )
            return;
        if( gbtXmlStr.empty() )
            return;

        m_msg.ptr = 0;
        m_msg.slen = 0;

        boost::shared_ptr<usg::IUsgManager> sg;
        if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_UsgManager_UsgManager, sg ) ) return;

        long *ptrdes = 0;
        if( PJ_TRUE != pj_thread_is_registered() )
        {
            ptrdes = new long[PJ_THREAD_DESC_SIZE];
            pj_thread_desc *tmp = ( pj_thread_desc* )ptrdes;
            pj_thread_t *this_thread = 0;
            ::memset( tmp, 0, sizeof( pj_thread_desc ) );
            if( PJ_SUCCESS != pj_thread_register( 0, *tmp, &this_thread ) )
            {
                std::cerr << "CGb28181Xml::CGb28181Xml pj_thread_register false" << std::endl;
                return;
            }

            sg->addPJThreadDes( ptrdes );
        }

        pj_caching_pool_init( &m_caching_pool, &pj_pool_factory_default_policy, 0 );
        pj_pool_factory *mem = 0;
        mem = &m_caching_pool.factory;
        m_isInitCachingPool = true;
        m_pool = pj_pool_create( mem, "CGb28181Parser", 4096, 4096 , 0 );
        if( m_pool == 0 )
            return;

        pj_strdup2( m_pool, &m_msg, gbtXmlStr.c_str() );
        if( m_msg.ptr == 0 )
            return;
        m_root = pj_xml_parse( m_pool, m_msg.ptr, m_msg.slen );
        if( m_root == 0 )
            return;

        m_gbtXml = new_o( CGb28181Xml, m_pool );
        if( !m_gbtXml->isOk() )
            return;

        m_isOk = true;
    }

    CGb28181Parser::~CGb28181Parser()
    {
        if( m_gbtXml )
        {
            delete_o( m_gbtXml );
            m_gbtXml = 0;
        }
        if( m_pool )
        {
            pj_pool_release( m_pool );
            m_pool = 0;
        }
        if ( m_isInitCachingPool )
            pj_caching_pool_destroy( &m_caching_pool );
    }

    bool CGb28181Parser::isOk()
    {
        return m_isOk;
    }

    std::string CGb28181Parser::getName( pj_xml_node *p )
    {
        if( p == 0 ) return std::string();
        return std::string( p->name.ptr, p->name.slen );
    }
    std::string CGb28181Parser::getContent( pj_xml_node *p )
    {
        if( p == 0 ) return std::string();
        return std::string( p->content.ptr, p->content.slen );
    }
    pj_str_t* CGb28181Parser::doConv( pj_str_t *dst, const std::string &str )
    {
        if( !m_isOk ) return 0;
        if( m_pool == 0 ) return 0;
        return pj_strdup2( m_pool, dst, str.c_str() );
    }
    std::string CGb28181Parser::getResultStr()
    {
        // 返回的串有可能为空. 如分析的本身就是一个回应包,则结果为空.
        return m_result;
    }

    void CGb28181Parser::getResultVec( std::vector<std::string> &vec )
    {
        vec.clear();
        vec = m_resultVec;
    }

//目录通知返回的response
    bool CGb28181Parser::response_func_catalog()
    {
        if( !m_isOk ) return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        pj_xml_node *subList = 0;
        usg::SCatalog resInfo;
        if( !( subList = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICELIST ) ) ) )
        {
            return false;
        }

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_SN ) ) ) )
        {
            return false;
        }

        std::string resSn = getContent( next );

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_SUMNUM ) ) ) )
        {
            return false;
        }

        std::string sumnum = getContent( next );

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID) ) ) )
        {
            return false;
        }

        m_result = getContent( next );
        resInfo.deviceId = m_result;

        boost::regex expression("^[0-9]{0,20}");
        if( !boost::regex_match( m_result.c_str(), expression ) )
        {
            std::cerr << "CGb28181Parser::response_func_catalog devid sipcode false : " << m_result << std::endl;
            return false;
        }

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_ITEM ) ) ) )
        {
            return false;
        }

        int devlist = 0;
        while( next )
        {
            pj_xml_node *item = next;
            usg::SCatalog::SItem resItem;

            pj_xml_node *subnode = 0;
            if( !( subnode = pj_xml_find_node( item, doConv( &tarName, gb28181::TAG_DEVICEID ) ) ) )
            {
                return false;
            }

            m_result = getContent( subnode );

            if( !boost::regex_match( m_result.c_str(), expression ) )
            {
                std::cerr << "CGb28181Parser::response_func_catalog itemid sipcode : " << m_result << std::endl;
                return false;
            }

            resItem.sipResCode = getContent( subnode );

            if( !( subnode = pj_xml_find_node( item, doConv( &tarName, gb28181::TAG_ITEM_NAME ) ) ) )
            {
                return false;
            }

            resItem.name = getContent( subnode );
            resItem.operatorType = usg::CATALOG_OPER_ADD;
            resItem.resStatus = true;

            if( ( subnode = pj_xml_find_node( item, doConv( &tarName, "Event" ) ) ) )
            {
                std::string event = getContent( subnode );

                if (event == "ADD")
                {
                    resItem.operatorType = usg::CATALOG_OPER_ADD;
                }
                else if (event == "DEL")
                {
                    resItem.operatorType = usg::CATALOG_OPER_DEL;
                }
                else if (event == "UPDATE")
                {
                    resItem.operatorType = usg::CATALOG_OPER_MOD;
                }

                if (event == "ON")
                {
                    resItem.resStatus = true;
                }
                else if (event == "OFF")
                {
                    resItem.resStatus = false;
                }
            }

            if( ( subnode = pj_xml_find_node( item, doConv( &tarName, gb28181::TAG_ITEM_IPADDRESS ) ) ) )
            {
                resItem.ip = getContent( subnode );
            }

            if( ( subnode = pj_xml_find_node( item, doConv( &tarName, gb28181::TAG_ITEM_PARENTAL ) ) ) )
            {
                resItem.parental = getContent( subnode );
            }

            if( ( subnode = pj_xml_find_node( item, doConv( &tarName, "Status" ) ) ) )
            {
                std::string status = getContent( subnode );
                if (status == "ON")
                {
                    resItem.resStatus = true;
                }
                else
                {
                    resItem.resStatus = false;
                }
            }

            resItem.hasPtz = true;
            resItem.isHD = true;

            resInfo.subItems.push_back(resItem);

            if( !( next = pj_xml_find_next_node( subList, item, doConv( &tarName, gb28181::TAG_ITEM ) ) ) )
            {
                break;
            }

            ++devlist;
        }

        usg::SCatalogResponse response;
        if( !m_wsf->onReceiveCatalog( m_sid, resInfo, response ) )
            return false;

        gb28181::SCatalogResponse gbtInfo;
        gbtInfo.result = response.isOk;
        gbtInfo.sn = resSn;

        gb28181::CGb28181Xml xml( 0 );
        if( !xml.isOk() )
            return false;
        if( !xml.makeString( gbtInfo, m_result ) )
            return false;

        return true;
    }

    bool CGb28181Parser::request_query_func_devicecatalog()
    {
        if( !m_isOk )
            return false;

        /*
         * 取出从from到to个item,然后形成发送串.
         */
        size_t allSize = m_configFile->getResSize();

        gb28181::SCatalog gbtInfo;
        if( !m_configFile->getResMulti( 1, allSize, gbtInfo.subList ) )
            return false;
        gbtInfo.subNum = gbtInfo.subList.size();
        gbtInfo.coding = m_resAddr;
        gbtInfo.sn = m_snContent;

        gb28181::CGb28181Xml xml( 0 );
        if( !xml.isOk() )
            return false;
        if( !xml.getString( gbtInfo, m_resultVec ) )
            return false;

        return true;
    }

//目录查询返回的response
    bool CGb28181Parser::response_query_func_devicecatalog()
    {
        if( !m_isOk ) return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        pj_xml_node *subList = 0;
        usg::SCatalog resInfo;
        if( !( subList = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICELIST ) ) ) )
        {
            return false;
        }

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_SN ) ) ) )
        {
            return false;
        }

        std::string resSn = getContent( next );

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_SUMNUM ) ) ) )
        {
            return false;
        }

        std::string sumnum = getContent( next );
        resInfo.num = boost::lexical_cast< int >( sumnum );

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID) ) ) )
        {
            return false;
        }

        m_result = getContent( next );
        resInfo.deviceId = m_result;

        boost::regex expression("^[0-9]{0,20}");
        if( !boost::regex_match( m_result.c_str(), expression ) )
        {
            std::cerr << "CGb28181Parser::response_query_func_devicecatalog devid sipcode false : " << m_result << std::endl;
            return false;
        }

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_ITEM ) ) ) )
        {
            return false;
        }

        int devlist = 0;
        while( next )
        {
            pj_xml_node *item = next;
            usg::SCatalog::SItem resItem;

            pj_xml_node *subnode = 0;
            if( !( subnode = pj_xml_find_node( item, doConv( &tarName, gb28181::TAG_DEVICEID ) ) ) )
            {
                return false;
            }

            m_result = getContent( subnode );

            boost::regex expression("^[0-9]{0,20}");
            if( !boost::regex_match( m_result.c_str(), expression ) )
            {
                std::cerr << "CGb28181Parser::response_query_func_devicecatalog itemid sipcode false : " << m_result << std::endl;
                return false;
            }

            resItem.sipResCode = getContent( subnode );

            if( !( subnode = pj_xml_find_node( item, doConv( &tarName, gb28181::TAG_ITEM_NAME ) ) ) )
            {
                return false;
            }

            resItem.name = getContent( subnode );
            resItem.operatorType = usg::CATALOG_OPER_ADD;
            resItem.resStatus = true;

            if( ( subnode = pj_xml_find_node( item, doConv( &tarName, "Event" ) ) ) )
            {
                std::string event = getContent( subnode );

                if (event == "ADD")
                {
                    resItem.operatorType = usg::CATALOG_OPER_ADD;
                }
                else if (event == "DEL")
                {
                    resItem.operatorType = usg::CATALOG_OPER_DEL;
                }
                else if (event == "UPDATE")
                {
                    resItem.operatorType = usg::CATALOG_OPER_MOD;
                }

                if (event == "ON")
                {
                    resItem.resStatus = true;
                }
                else if (event == "OFF")
                {
                    resItem.resStatus = false;
                }
            }

            if( ( subnode = pj_xml_find_node( item, doConv( &tarName, gb28181::TAG_ITEM_IPADDRESS ) ) ) )
            {
                resItem.ip = getContent( subnode );
            }
            
            if( ( subnode = pj_xml_find_node( item, doConv( &tarName, gb28181::TAG_ITEM_PARENTAL ) ) ) )
            {
                resItem.parental = getContent( subnode );
            }

            if( ( subnode = pj_xml_find_node( item, doConv( &tarName, "Status" ) ) ) )
            {
                std::string status = getContent( subnode );
                if (status == "ON")
                {
                    resItem.resStatus = true;
                }
                else
                {
                    resItem.resStatus = false;
                }
            }

            resItem.hasPtz = true;
            resItem.isHD = true;

            resInfo.subItems.push_back(resItem);

            if( !( next = pj_xml_find_next_node( subList, item, doConv( &tarName, gb28181::TAG_ITEM ) ) ) )
            {
                break;
            }

            ++devlist;
        }

        if( !m_wsf->onReceiveQueryCatalog( /*m_sid*/resSn, resInfo ) )
            return false;

        return true;
    }

    bool CGb28181Parser::request_notify_func_keepalive()
    {
        if( !m_isOk ) return false;

        if ( !m_wsf->onReceiveKeeplive( m_sid ) )
        {
            return false;
        }

        // 虽然已经收到了别人给自己保活请求,但自己不处理,仅返回成功,以辅助测试.
        m_result =
                "<Response>"
                "<Variable>KeepAlive</Variable>"
                "<Result>";
        std::string boolTmp;
        strFromBoolResult( false, boolTmp );
        m_result = m_result+boolTmp;
        m_result = m_result+
                   "</Result>"
                   "</Response>";
        m_result = gb28181::XML_PROLOG_HEAD+m_result;
        return true;
    }
    bool CGb28181Parser::response_func_keepalive()
    {
        if( !m_isOk ) return false;

        bool result = false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID ) ) ) )
        {
            return false;
        }

        std::string resAddr = getContent( next );
        if( !m_wsf->onReceiveKeepaliveResponse( resAddr, !result ) )
            return false;

        return true;
    }

    bool CGb28181Parser::request_func_realmedia()
    {
        if( !m_isOk ) return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;
        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_PRIVILEGE ) ) ) )
        {
            return false;
        }
        gb28181::SRealMedia gbt_request;
        gbt_request.resAddr = m_resAddr;
        gbt_request.privilege = getContent( next );
        if( !isValidPrivilegeType( gbt_request.privilege ) )
            return false;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_FORMAT ) ) ) )
        {
            return false;
        }
        if( !strToFromatType_s( getContent( next ), gbt_request.supportFormatTypes ) )
            return false;
        if( gbt_request.supportFormatTypes.empty() )
            return false;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_VIDEO ) ) ) )
        {
            return false;
        }
        if( !strToVideoType_s( getContent( next ), gbt_request.supportVideoTypes ) )
            return false;
        if( gbt_request.supportVideoTypes.empty() )
            return false;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_AUDIO ) ) ) )
        {
            return false;
        }
        if( !strToAudioType_s( getContent( next ), gbt_request.supportAudioTypes ) )
            return false;
        if( gbt_request.supportAudioTypes.empty() )
            return false;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_MAXBITRATE ) ) ) )
        {
            return false;
        }
        if( !valueToLexical< std::string, int >( getContent( next ), gbt_request.maxBitrate ) )
            return false;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_SOCKET ) ) ) ) // 210.98.45.200 UDP 2360
        {
            return false;
        }
        if( !strToSocketUrl( getContent( next ), gbt_request.socketType, gbt_request.sockAddr, gbt_request.sockPort ) )
            return false;

        // 转换成msg的
        usg::SRealMedia msg_request;
        if( !m_structExchange.realMediaGbtToWtoe( gbt_request, msg_request ) )
            return false;
        // 得到UUID
        boost::uuids::uuid resUuid;
        if( !m_configFile->getResUuid( gbt_request.resAddr, resUuid ) )
            return false;

        uint8_t subImageSize = 0;
        if( !m_configFile->getSubImageSize( gbt_request.resAddr, subImageSize ) )
            return false;
        uint8_t masterImageSize = 0;
        if( !m_configFile->getMasterImageSize( gbt_request.resAddr, masterImageSize ) )
            return false;
        // 执行命令
        usg::SRealMediaResponse msg_response;
        if( !m_wsf->onReceiveRealMedia( m_sid, resUuid, subImageSize, masterImageSize, msg_request, msg_response ) )
            return false;
        // 转换结果类型
        gb28181::SRealMediaResponse gbt_response;
        if( !m_structExchange.realMediaResponseWtoeToGbt( msg_response, gbt_response ) )
            return false;
        // 传回AddrCode.
        gbt_response.resAddr = gbt_request.resAddr;

        // 用结果获取发送字符串.
        if( !m_gbtXml->getString( gbt_response, m_result ) )
            return false;

        return true;
    }
    bool CGb28181Parser::response_func_realmedia()
    {
        if( !m_isOk ) return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;
        gb28181::SRealMediaResponse gbt_response;
        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_FORMAT ) ) ) )
        {
            return false;
        }
        if( !strToFormatType( getContent( next ), gbt_response.formatType ) )
            return false;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_VIDEO ) ) ) )
        {
            return false;
        }
        if ( !strToVideoType( getContent( next), gbt_response.videoType ))
        {
            return false;
        }

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_AUDIO ) ) ) )
        {
            return false;
        }
        if ( !strToAudioType( getContent(next) ,gbt_response.audioType) )
        {
            return false;
        }

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_BITRATE ) ) ) )
        {
            return false;
        }

        if( !valueToLexical< std::string, int >( getContent( next ), gbt_response.bitrate ) )
            return false;

        usg::SRealMediaResponse msg_response;
        if ( !	m_structExchange.realMediaResponseGbtToWtoe(gbt_response,msg_response) )
        {
            return false;
        }
        if( !m_wsf->onReceiveRealMediaResponse( m_sid, msg_response ) )
            return false;

        return true;
    }

    bool CGb28181Parser::response_query_func_filelist()
    {
        if( !m_isOk ) return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;
        gb28181::SFileListResponse gbt_response;

        //sn
        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_CONTROL_SN ) ) ) )
            return false;

        std::string snstr = getContent( next );
        //SumNum
        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_SUMNUM ) ) ) )
        {
            return false;
        }

        if( !valueToLexical< std::string, int >( getContent( next ), gbt_response.realFileNum ) )
            return false;

        if (gbt_response.realFileNum != 0)
        {
            if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_RECORDLIST ) ) ) )
            {
                return false;
            }

            pj_xml_node * subList = next;
            if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_ITEM ) ) ) )
            {
                return false;
            }

            while( next )
            {
                pj_xml_node *item = next;
                //std::cout << " find Item is ====== " << std::endl;
                gb28181::SFileListResponse::SItem tmpItem;

                if( !( next = pj_xml_find_node( item, doConv( &tarName, gb28181::TAG_DEVICEID ) ) ) )
                {
                    return false;
                }
                tmpItem.itemName = getContent(next);

                if( !( next = pj_xml_find_node( item, doConv( &tarName, gb28181::TAG_STARTTIME ) ) ) )
                {
                    return false;
                }
                tmpItem.itemBeginTime = getContent(next);
                if( !( next = pj_xml_find_node( item, doConv( &tarName, gb28181::TAG_ENDTIME ) ) ) )
                {
                    return false;
                }
                tmpItem.itemEndTime = getContent(next);

                gbt_response.fileInfolist.push_back( tmpItem );

                if( !( next = pj_xml_find_next_node( subList, item, doConv( &tarName, gb28181::TAG_ITEM ) ) ) )
                {
                    break;
                }
            }

            gbt_response.sendFileNum = gbt_response.fileInfolist.size();
        }

        usg::SHistoryListResponse msg_response;
        m_structExchange.historyListResponseGbtToWtoe(gbt_response,msg_response);

        if( !m_wsf->onReceiveHistoryListResponse( snstr, msg_response ) )
        {
            std::cerr << "!m_wsf->onReceiveHistoryListResponse( snstr, msg_response ) : " << snstr << std::endl;
            return false;
        }

        return true;
    }

    bool CGb28181Parser::request_func_vodbyrstp()
    {
        if( !m_isOk ) return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;
        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_ACTION_QUERY ) ) ) )
        {
            return false;
        }
        pj_xml_node *subList = next; // 先要向下多走一级.
        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_PRIVILEGE ) ) ) )
        {
            return false;
        }
        gb28181::SHistoryMedia gbt_request;
        gbt_request.resAddr = m_resAddr;
        gbt_request.privilege = getContent( next );
        if( !isValidPrivilegeType( gbt_request.privilege ) )
            return false;

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_FILETYPE ) ) ) )
        {
            return false;
        }
        if( !valueToLexical< std::string, int >( getContent( next ), gbt_request.fileType ) )
            return false;

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_NAME ) ) ) )
        {
            return false;
        }
        gbt_request.name = getContent( next );

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_STARTTIME ) ) ) )
        {
            return false;
        }
        gbt_request.beginTime = getContent( next ); // 为了防止time有更高级的格式,所以在此没有转换.

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_ENDTIME ) ) ) )
        {
            return false;
        }
        gbt_request.endTime = getContent( next ); // 为了防止time有更高级的格式,所以在此没有转换.

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_MAXBITRATE ) ) ) )
        {
            return false;
        }
        if( !valueToLexical< std::string, int >( getContent( next), gbt_request.maxBitrate ) )
            return false;

        // 转换成msg的
        usg::SHistoryMedia msg_request;
        if( !m_structExchange.historyMediaGbtToWtoe( gbt_request, msg_request ) )
            return false;
        // 得到UUID
        boost::uuids::uuid resUuid;
        if( !m_configFile->getResUuid( gbt_request.resAddr, resUuid ) )
            return false;
        // 执行命令
        usg::SHistoryMediaResponse msg_response;
        if( !m_wsf->onReceiveHistoryMedia( m_sid, resUuid, msg_request, msg_response ) )
            return false;
        // 转换结果类型
        gb28181::SHistoryMediaResponse gbt_response;
        if( !m_structExchange.historyMediaResponseWtoeToGbt( msg_response, gbt_response ) )
            return false;
        // 传回AddrCode.
        gbt_response.resAddr = gbt_request.resAddr;
        // 用结果获取发送字符串.
        if( !m_gbtXml->getString( gbt_response, m_result ) )
            return false;

        return true;
    }
    bool CGb28181Parser::response_func_vodbyrstp()
    {
        if( !m_isOk ) return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;
        gb28181::SHistoryMediaResponse gbt_response;
        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_RESPONSE_QUERY ) ) ) )
        {
            return false;
        }
        pj_xml_node *sub = next;
        if( !( next = pj_xml_find_node( sub, doConv( &tarName, gb28181::TAG_RESULT ) ) ) )
        {
            return false;
        }
        if( !strToBoolResult( getContent( next ), gbt_response.result ) )
            return false;

        if( !( next = pj_xml_find_node( sub, doConv( &tarName, gb28181::TAG_BITRATE ) ) ) )
        {
            return false;
        }
        if( !valueToLexical< std::string, int >( getContent( next ), gbt_response.bitrate ) )
            return false;

        if( !( next = pj_xml_find_node( sub, doConv( &tarName, gb28181::TAG_PLAYURL ) ) ) )
        {
            return false;
        }
        gbt_response.playUrl = getContent( next );
        usg::SHistoryMediaResponse msg_response;
        if ( !m_structExchange.historyMediaResponseGbtToWtoe(gbt_response,msg_response) )
        {
            return true;
        }
        if ( !m_wsf->onReceiveHistoryMediaResponse(m_sid, msg_response ))
        {
            return false;
        }
        return true;
    }

    bool CGb28181Parser::request_control_func_ptzcommand()
    {
        if( !m_isOk )
            return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        //获取PtzCmd节点
        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_CONTROL_PTZCMD ) ) ) )
        {
            return false;
        }

        gb28181::SPtzCommand request;
        request.sn = m_snContent;

        uint32_t cmd = 0;
        if ( ! strToControlCode( getContent( next ), cmd,
                                 request.commandParam1, request.commandParam2 ) )
        {
            return false;
        }

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID ) ) ) )
        {
            return false;
        }

        request.resAddr = getContent( next );

        request.command = cmd;
        // 转换成msg的
        std::vector<usg::SPtzCommand> msg_request;
        if( !m_structExchange.ptzCommandGb28181ToWtoe( request, msg_request ) )
            return false;
        // 得到UUID`
        boost::uuids::uuid resUuid;
        if( !m_configFile->getResUuid( request.resAddr, resUuid ) )
            return false;
        // 执行命令
        usg::SPtzCommandResponse msg_response;

        for( uint8_t i = 0; i < msg_request.size(); ++i)
        {
            m_wsf->onReceivePtzCommand( m_sid, resUuid, msg_request[i], msg_response );
        }

        return true;
    }
    bool CGb28181Parser::response_control_func_ptzcommand()
    {
        if( !m_isOk )
            return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;
        gb28181::SPtzCommandResponse gbt_response;
        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_RESPONSE_CONTROL ) ) ) )
        {
            return false;
        }
        pj_xml_node *subList = next; // 先要向下多走一级.

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_RESULT ) ) ) )
        {
            return false;
        }
        if( !strToBoolResult( getContent( next ), gbt_response.result ) )
            return false;

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_COMMAND ) ) ) )
        {
            return false;
        }

        uint32_t cmd = 0;
        if ( strToControlCode( getContent( next ), cmd, gbt_response.commandParam1, gbt_response.commandParam2 ) )
        {
            gbt_response.command = (EPtzCommand)cmd;
        }
        usg::SPtzCommandResponse msg_response;

        if ( !m_structExchange.ptzCommandResponseGbtToWtoe(gbt_response,msg_response))
        {
            return false;
        }
        return true;
    }

    bool CGb28181Parser::request_query_func_presetlist()
    {
        if( !m_isOk ) return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;
        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_ACTION_QUERY ) ) ) )
        {
            return false;
        }
        pj_xml_node *subList = next; // 先要向下多走一级.

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_PRIVILEGE ) ) ) )
        {
            return false;
        }
        gb28181::SPresetList gbt_request;
        gbt_request.resAddr = m_resAddr;
        gbt_request.privilege = getContent( next );
        if( !isValidPrivilegeType( gbt_request.privilege ) )
            return false;

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_FROMINDEX ) ) ) )
        {
            return false;
        }
        if( !valueToLexical< std::string, int >( getContent( next ), gbt_request.fromIndex ) )
            return false;

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_TOINDEX ) ) ) )
        {
            return false;
        }
        if( !valueToLexical< std::string, int >( getContent( next ), gbt_request.toIndex ) )
            return false;

        // 转换成msg的
        usg::SPresetList msg_request;
        if( !m_structExchange.presetListGbtToWtoe( gbt_request, msg_request ) )
            return false;
        // 得到UUID
        boost::uuids::uuid resUuid;
        if( !m_configFile->getResUuid( gbt_request.resAddr, resUuid ) )
            return false;
        // 执行命令
        usg::SPresetListResponse msg_response;
        if( !m_wsf->onReceivePresetList( m_sid, resUuid, msg_request, msg_response ) )
            return false;
        // 转换结果类型
        gb28181::SPresetListResponse gbt_response;
        if( !m_structExchange.presetListResponseWtoeToGbt( msg_response, gbt_response ) )
            return false;
        // 传回AddrCode.
        gbt_response.resAddr = gbt_request.resAddr;
        // 用结果获取发送字符串.
        if( !m_gbtXml->getString( gbt_response, m_result ) )
            return false;

        return true;
    }
    bool CGb28181Parser::response_query_func_presetlist()
    {
        if( !m_isOk ) return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;
        gb28181::SPresetListResponse gbt_response;
        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_RESPONSE_QUERY ) ) ) )
        {
            return false;
        }
        pj_xml_node *subList = next; // 先要向下多走一级.

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_RESULT ) ) ) )
        {
            return false;
        }
        if( !strToBoolResult( getContent( next ), gbt_response.result ) )
            return false;
        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_REALNUM ) ) ) )
        {
            return false;
        }
        if( !valueToLexical< std::string, int >( getContent( next ), gbt_response.realNum ) )
            return false;

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_FROMINDEX ) ) ) )
        {
            return false;
        }
        if( !valueToLexical< std::string, int >( getContent( next ), gbt_response.fromIndex ) )
            return false;

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_TOINDEX ) ) ) )
        {
            return false;
        }
        if( !valueToLexical< std::string, int >( getContent( next ), gbt_response.toIndex ) )
            return false;

        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_PRESETINFOLIST ) ) ) )
        {
            return false;
        }
        subList = next; // 先要向下多走一级.
        if( !( next = pj_xml_find_node( subList, doConv( &tarName, gb28181::TAG_ITEM ) ) ) )
        {
            return false;
        }
        while( next )
        {
            pj_xml_node *item = next;
            //std::cout << " find Item is ====== " << std::endl;
            gb28181::SPresetListResponse::SItem tmpItem;

            if( !( next = pj_xml_find_node( item, doConv( &tarName, gb28181::TAG_ITEM_VALUE ) ) ) )
            {
                return false;
            }
            if( !valueToLexical< std::string, int >( getContent( next ), tmpItem.itemValue ) )
                return false;
            if( !( next = pj_xml_find_node( item, doConv( &tarName, gb28181::TAG_ITEM_DESCRIPTION ) ) ) )
            {
                return false;
            }
            tmpItem.itemDescription = getContent( next );
            gbt_response.presetInfoList.push_back( tmpItem);

            // 下一个Item
            if( !( next = pj_xml_find_next_node( subList, item, doConv( &tarName, gb28181::TAG_ITEM ) ) ) )
            {
                break;
            }
        }
        usg::SPresetListResponse msg_response;
        m_structExchange.presetListResponseGbtToWtoe( gbt_response, msg_response);

        if ( !m_wsf->onReceivePresetListResponse( m_sid,msg_response ) )
        {
            return false;
        }
        return true;
    }

    bool CGb28181Parser::request_func_alarmsubcribe()
    {
        return true;
    }

    bool CGb28181Parser::response_func_alarmsubcribe()
    {
        if( !m_isOk ) return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_RESULT ) ) ) )
            return false;

        bool result = true;
        if ( getContent( next ) != "OK")
        {
            result = false;
        }

        if( !m_wsf->onReceiveSubscribeAlarmResponse( m_sid, result ) )
            return false;

        return result;
    }

    bool CGb28181Parser::request_func_alarmnotify()
    {
        return true;
    }

    bool CGb28181Parser::request_func_reboot()
    {
        if( !m_isOk )
            return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID ) ) ) )
        {
            return false;
        }

        std::string resAddr = getContent( next );

        // 得到UUID`
        boost::uuids::uuid resUuid;
        if( !m_configFile->getResUuid( resAddr, resUuid ) )
            return false;
        // 执行命令
        usg::SRebootCommandResponse msg_response;
        if( !m_wsf->onReceiveReboot( m_sid, resUuid, msg_response ) ) ///< 上级不用实现，接口默认为true
            return false;

        return true;
    }

    bool CGb28181Parser::parserReceivePkg()
    {
        if( m_isOk == false )
            return false;

        std::string rootName = getName( m_root );
        pj_xml_node *secondLevelNode = 0;
        pj_str_t tarName;

        if( gb28181::TAG_CONTROL == rootName )
        {
            //获取SN值
            pj_xml_node *snNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_CONTROL_SN ) );
            m_snContent = getContent( snNode );

            //查找PTZCmd节点是否存在，如果存在，开始PTZ控制
            if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_CONTROL_PTZCMD ) ) ) )
            {
                bool b = request_control_func_ptzcommand();
                return b;
            }

            if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_CONTROL_RECORDCMD ) ) ) )
            {
                std::string content = getContent( secondLevelNode );
                if( content == gb28181::TAG_CONTROL_RECORD )
                {
                    bool b = request_control_func_startRecord();
                    return b;
                }

                if( content == gb28181::TAG_CONTROL_STOPRECORD )
                {
                    bool b = request_control_func_stopRecord();
                    return b;
                }
                return true;
            }

            if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_CONTROL_GUARDCMD ) ) ) )
            {
                std::string content = getContent( secondLevelNode );
                if( content == gb28181::TAG_CONTROL_SETGUARD )
                {
                    bool b = request_control_func_setGuard();
                    return b;
                }

                if( content == gb28181::TAG_CONTROL_RESETGUARD )
                {
                    bool b = request_control_func_resetGuard();
                    return b;
                }
                return true;
            }

            if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_CONTROL_ALARMCMD ) ) ) )
            {
                std::string content = getContent( secondLevelNode );
                if( content == gb28181::TAG_CONTROL_RESETALARM )
                {
                    bool b = request_control_func_resetAlarm();
                    return b;
                }
                return true;
            }

            //查询TeleBoot节点是否存在，如果存在，开始远程启动
            if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_CONTROL_TELEBOOT ) ) ) )
            {
                bool b = request_func_reboot();
                return b;
            }
        }
        else if ( gb28181::TAG_RESPONSE == rootName )
        {
            //查找CmdType节点是否存在，如果存在，根据CmdType的值来判断要查询的内容
            if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_CONTROL_CMDTYPE ) ) ) )
            {
                std::string cmdTypeContent = getContent( secondLevelNode );

                if( cmdTypeContent == FUNC_CONTROL_CMDTYPE_CATALOG )
                {
                    //设备目录通知
                    if ( usg::SESSTION_TYPE_NOTIFY == m_type)
                        return response_func_catalog();

                        //设备目录查询
                    else if ( usg::SESSTION_TYPE_DDCPDO == m_type)
                        return response_query_func_devicecatalog();
                    else
                        return false;

                }
                else if( cmdTypeContent == FUNC_CONTROL_CMDTYPE_DEVICEINFO )
                {
                    //响应设备信息查询
                    return response_query_func_deviceInfo();
                }
                else if( cmdTypeContent == FUNC_CONTROL_CMDTYPE_DEVICESTATUS )
                {
                    //响应设备状态查询
                    return response_query_func_deviceStatus();
                }
                else if (cmdTypeContent == FUNC_CONTROL_CMDTYPE_RECORDINFO)
                {
                    return response_query_func_filelist();
                }
                else if (cmdTypeContent == TAG_CONTROL_ALARM)
                {
                    //要根据call-id号确定当下的响应是对应什么请求的。然后再决定用什么方式处理
                    return response_func_alarmsubcribe();
                }
                else if (cmdTypeContent == TAG_BROADCAST)
                {
                    return response_func_broadcast();
                }
            }
        }
        else if (gb28181::TAG_NOTIFY == rootName)
        {
            if (( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_CONTROL_CMDTYPE ) ) ))
            {
                std::string cmdTypeContent = getContent( secondLevelNode );
                if (cmdTypeContent == TAG_BROADCAST)
                {
                    return response_notify_broadcast();
                }
                else if (cmdTypeContent == TAG_CONTROL_MEDIASTAUTS)
                {
                    return request_func_filetoend();
                }
                else if (cmdTypeContent == TAG_CONTROL_ALARM)
                {
                    return response_func_alarmnotify();
                }
                else if (cmdTypeContent == FUNC_CONTROL_CMDTYPE_KEEPALIVE)///< 处理保活
                {
                    return response_func_keepalive();
                }
            }
        }

        return true;
    }

    bool CGb28181Parser::parserAction()
    {
        if( !m_isOk ) return false;
        pj_str_t tarName;
        pj_xml_node *secondLevelNode = 0;
        pj_xml_node *thirdLevelNode = 0;
        if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_ACTION_CONTROL ) ) ) )
        {
            if( ( thirdLevelNode = pj_xml_find_node( secondLevelNode, doConv( &tarName, gb28181::TAG_VARIABLE ) ) ) )
            {
                if( gb28181::FUNC_VARIABLE_PTZCOMMAND == getContent( thirdLevelNode ) )
                {
                    bool b = request_control_func_ptzcommand();
                    return b;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
        else if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_ACTION_QUERY ) ) ) )
        {
            if( ( thirdLevelNode = pj_xml_find_node( secondLevelNode, doConv( &tarName, gb28181::TAG_VARIABLE ) ) ) )
            {
                if( gb28181::FUNC_VARIABLE_VODBYRTSP == getContent( thirdLevelNode ) )
                {
                    bool b = request_func_vodbyrstp();
                    return b;
                }
                else if( gb28181::FUNC_VARIABLE_PRESETLIST == getContent( thirdLevelNode ) )
                {
                    bool b = request_query_func_presetlist();
                    return b;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
        else if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_ACTION_NOTIFY ) ) ) )
        {
            if( ( thirdLevelNode = pj_xml_find_node( secondLevelNode, doConv( &tarName, gb28181::TAG_VARIABLE ) ) ) )
            {
                if( gb28181::FUNC_VARIABLE_KEEPALIVE == getContent( thirdLevelNode ) )
                {
                    bool b = request_notify_func_keepalive();
                    return b;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
        else if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_VARIABLE ) ) ) )
        {
            if( gb28181::FUNC_VARIABLE_REALMEDIA == getContent( secondLevelNode ) )
            {
                bool b = request_func_realmedia();
                return b;
            }

            else if( gb28181::FUNC_VARIABLE_ALARMSUBSCRIBE == getContent( secondLevelNode ) )
            {
                bool b = request_func_alarmsubcribe();
                return b;
            }
            else if( gb28181::FUNC_VARIABLE_ALARMNOTIFY == getContent( secondLevelNode ) )
            {
                bool b = request_func_alarmnotify();
                return b;
            }
            else
            {
                return false;
            }
        }
        else
        {
            return false;
        }
    }

    bool CGb28181Parser::parserResponse()
    {
        if( !m_isOk ) return false;
        pj_str_t tarName;
        pj_xml_node *secondLevelNode = 0;
        pj_xml_node *thirdLevelNode = 0;

        // response下,全部都有Variable,否则失败.
        if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_RESPONSE_CONTROL ) ) ) )
        {
            if( ( thirdLevelNode = pj_xml_find_node( secondLevelNode, doConv( &tarName, gb28181::TAG_VARIABLE ) ) ) )
            {
                if( gb28181::FUNC_VARIABLE_PTZCOMMAND == getContent( thirdLevelNode ) )
                {
                    bool b = response_control_func_ptzcommand();
                    return b;

                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
        else if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_RESPONSE_QUERY ) ) ) )
        {
            if( ( thirdLevelNode = pj_xml_find_node( secondLevelNode, doConv( &tarName, gb28181::TAG_VARIABLE ) ) ) )
            {
                if( gb28181::FUNC_VARIABLE_FILELIST == getContent( thirdLevelNode ) )
                {
                    bool b = response_query_func_filelist();
                    return b;

                }
                else if( gb28181::FUNC_VARIABLE_VODBYRTSP == getContent( thirdLevelNode ) )
                {
                    bool b = response_func_vodbyrstp();
                    return b;

                }
                else if( gb28181::FUNC_VARIABLE_PRESETLIST == getContent( thirdLevelNode ) )
                {
                    bool b = response_query_func_presetlist();
                    return b;

                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
        else if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_VARIABLE ) ) ) )
        {
            if( gb28181::FUNC_VARIABLE_CATALOG == getContent( secondLevelNode ) )
            {
                bool b = response_func_catalog();
                return b;
            }
            else if( gb28181::FUNC_VARIABLE_KEEPALIVE == getContent( secondLevelNode ) )
            {
                bool b = response_func_keepalive();
                return b;
            }
            else if( gb28181::FUNC_VARIABLE_REALMEDIA == getContent( secondLevelNode ) )
            {
                bool b = response_func_realmedia();
                return b;

            }
            else if( gb28181::FUNC_VARIABLE_ALARMSUBSCRIBE == getContent( secondLevelNode ) )
            {
                bool b = response_func_alarmsubcribe();
                return b;
            }
            else if( gb28181::FUNC_VARIABLE_ALARMNOTIFY == getContent( secondLevelNode ) )
            {
                bool b = response_func_alarmnotify();
                return b;
            }
            else
            {
                return false;
            }
        }
        else
        {
            return false;
        }
    }

    bool CGb28181Parser::response_query_func_deviceInfo()
    {
        if( !m_isOk ) return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        SDeviceInfoResponse deviceInfo;
        deviceInfo.maxAlarm = 0;
        deviceInfo.maxCamera = 0;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_CONTROL_SN ) ) ) )
        {
            return false;
        }
        deviceInfo.sn = getContent( next );


        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID ) ) ) )
        {
            return false;
        }
        deviceInfo.resAddr = getContent( next );

        if ( ( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_MANUFACTURER ) ) ) )
        {
            deviceInfo.manufacturer = getContent( next );
        }

        if( (next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_MODEL ) ) ))
        {
            deviceInfo.model = getContent( next );
        }

        if( (next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_FIRMWARE ) )  ))
        {
            deviceInfo.firmware = getContent( next );
        }

        if( (next = pj_xml_find_node( m_root, doConv( &tarName, "Channel" ) ) ))
        {
            deviceInfo.maxAlarm = boost::lexical_cast< int >(getContent( next ));
        }

        usg::SDeviceInfoResponse msg_response;
        if ( !m_structExchange.deviceInfoResponseGb28181ToWtoe(deviceInfo,msg_response) )
        {
            return false;
        }

        boost::uuids::uuid resUuid;
        if( !m_wsf->onReceiveDeviceInfo( deviceInfo.sn, resUuid, msg_response ) )
            return false;

        return true;
    }

    bool CGb28181Parser::response_query_func_deviceStatus()
    {
        if( !m_isOk ) return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        SDeviceStatusResponse deviceStatus;
        deviceStatus.isEncoder = false;
        deviceStatus.isRecord = false;
        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_CONTROL_SN ) ) ) )
        {
            return false;
        }
        deviceStatus.sn = getContent( next );

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID ) ) ) )
        {
            return false;
        }
        deviceStatus.resAddr = getContent( next );


        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_RESULT ) ) ) )
        {
            return false;
        }
        deviceStatus.result = ("OK" == getContent( next ));

        if( ( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_ENCODE ) ) ) )
        {
            deviceStatus.isEncoder = ("ON" == getContent( next ));
        }


        if( ( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_RECORD ) ) ) )
        {
            deviceStatus.isRecord = ("ON" == getContent( next ));
        }


        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_ONLINE ) ) ) )
        {
            return false;
        }
        deviceStatus.isOnline = ("ONLINE" == getContent( next ));

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_STATUS ) ) ) )
        {
            return false;
        }
        deviceStatus.isNormal = ("OK" == getContent( next ));

        if( ( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_REASON ) ) ) )
        {
            deviceStatus.strFaultReason = getContent( next );
        }

        usg::SDeviceStatusResponse msg_response;
        if ( !m_structExchange.deviceStatusResponseGb28181ToWtoe(deviceStatus, msg_response) )
        {
            return false;
        }

        boost::uuids::uuid resUuid;
        if( !m_wsf->onReceiveDeviceStatus( deviceStatus.sn, resUuid, msg_response ) )
            return false;

        return true;
    }

    bool CGb28181Parser::request_control_func_startRecord()
    {
        if( !m_isOk )
            return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID ) ) ) )
        {
            return false;
        }

        std::string resAddr = getContent( next );

        // 得到UUID`
        boost::uuids::uuid resUuid;
        if( !m_configFile->getResUuid( resAddr, resUuid ) )
            return false;

        gb28181::SRecordContronlResponse gb28181_response;
        gb28181_response.devid = resAddr;
        gb28181_response.sn = m_snContent;
        gb28181_response.flag = true;
        gb28181_response.result = m_wsf->onReceiveStartRecord( resUuid );///< 上级不用实现，接口默认为true

        m_gbtXml->getString( gb28181_response, m_result );
        m_resultVec.push_back(m_result);

        return true;
    }

    bool CGb28181Parser::request_control_func_stopRecord()
    {
        if( !m_isOk )
            return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID ) ) ) )
        {
            return false;
        }

        std::string resAddr = getContent( next );

        // 得到UUID`
        boost::uuids::uuid resUuid;
        if( !m_configFile->getResUuid( resAddr, resUuid ) )
            return false;

        gb28181::SRecordContronlResponse gb28181_response;
        gb28181_response.devid = resAddr;
        gb28181_response.sn = m_snContent;
        gb28181_response.flag = false;
        gb28181_response.result = m_wsf->onReceiveStopRecord( resUuid ); ///< 上级不用实现，接口默认为true

        m_gbtXml->getString( gb28181_response, m_result );
        m_resultVec.push_back(m_result);

        return true;
    }

    bool CGb28181Parser::request_control_func_setGuard()
    {
        if( !m_isOk )
            return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID ) ) ) )
        {
            return false;
        }

        std::string resAddr = getContent( next );

        // 得到UUID`
        boost::uuids::uuid resUuid;
        if( !m_configFile->getResUuid( resAddr, resUuid ) )
            return false;

        gb28181::SGuardContronlResponse gb28181_response;
        gb28181_response.devid = resAddr;
        gb28181_response.sn = m_snContent;
        gb28181_response.flag = true;
        gb28181_response.result = m_wsf->onReceiveSetGuard( resUuid ); ///< 上级不用实现，接口默认为true

        m_gbtXml->getString( gb28181_response, m_result );
        m_resultVec.push_back(m_result);
        return true;
    }

    bool CGb28181Parser::request_control_func_resetGuard()
    {
        if( !m_isOk )
            return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID ) ) ) )
        {
            return false;
        }

        std::string resAddr = getContent( next );

        // 得到UUID`
        boost::uuids::uuid resUuid;
        if( !m_configFile->getResUuid( resAddr, resUuid ) )
            return false;

        gb28181::SGuardContronlResponse gb28181_response;
        gb28181_response.devid = resAddr;
        gb28181_response.sn = m_snContent;
        gb28181_response.flag = false;
        gb28181_response.result = m_wsf->onReceiveResetGuard( resUuid ); ///< 上级不用实现，接口默认为true

        m_gbtXml->getString( gb28181_response, m_result );
        m_resultVec.push_back(m_result);
        return true;
    }

    bool CGb28181Parser::request_control_func_resetAlarm()
    {
        if( !m_isOk )
            return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID ) ) ) )
        {
            return false;
        }

        std::string resAddr = getContent( next );

        // 得到UUID`
        boost::uuids::uuid resUuid;
        if( !m_configFile->getResUuid( resAddr, resUuid ) )
            return false;

        gb28181::SAlarmResetResponse gb28181_response;
        gb28181_response.devid = resAddr;
        gb28181_response.sn = m_snContent;
        gb28181_response.result = m_wsf->onReceiveResetAlarm( resUuid );  ///< 上级不用实现，接口默认为true

        m_gbtXml->getString( gb28181_response, m_result );
        m_resultVec.push_back(m_result);
        return true;
    }

    bool CGb28181Parser::parserSubscribeReceivePkg()
    {
        if( m_isOk == false )
            return false;

        std::string rootName = getName( m_root );
        pj_xml_node *secondLevelNode = 0;
        pj_str_t tarName;

        if( gb28181::TAG_CONTROL == rootName )
        {

        }
        else if( gb28181::TAG_QUERY == rootName )
        {
            //查找CmdType节点是否存在，如果存在，根据CmdType的值来判断要查询的内容
            if( ( secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_CONTROL_CMDTYPE ) ) ) )
            {
                std::string cmdTypeContent = getContent( secondLevelNode );

                if( cmdTypeContent == FUNC_CONTROL_CMDTYPE_CATALOG )
                {
                    //目录订阅
                    return request_func_subscribe_catalog();
                }
                else if( cmdTypeContent == TAG_CONTROL_ALARM )
                {
                    //告警订阅
                    return request_func_subscribe_alarm();
                }
                else
                {
                    return true;
                }
            }
        }
        else if ( gb28181::TAG_RESPONSE == rootName )
        {

        }

        return true;
    }

    bool CGb28181Parser::request_func_subscribe_catalog()
    {
        pj_str_t tarName;
        pj_xml_node *secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_STARTTIME ) );

        if( secondLevelNode == 0 ) return false;

        std::string cmdTypeContent = getContent( secondLevelNode );

        int year, month, day, hour, minute, second;
        sscanf( cmdTypeContent.c_str(), "%d-%d-%dT%d:%d:%d", &year, &month, &day, &hour, &minute, &second );
        boost::posix_time::ptime time1( boost::gregorian::date( year, month, day ), boost::posix_time::hours( hour ) + boost::posix_time::minutes( minute ) + boost::posix_time::seconds( second ) );

        secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_ENDTIME ) );
        if( secondLevelNode == 0 )
        {
            std::cerr << "pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_ENDTIME ) ) false" << std::endl;
            return false;
        }

        cmdTypeContent = getContent( secondLevelNode );

        sscanf( cmdTypeContent.c_str(), "%d-%d-%dT%d:%d:%d", &year, &month, &day, &hour, &minute, &second );
        boost::posix_time::ptime time2( boost::gregorian::date( year, month, day ), boost::posix_time::hours( hour ) + boost::posix_time::minutes( minute ) + boost::posix_time::seconds( second ) );

        if( time1 > time2 )
        {
            std::cerr << "if( time1 > time2 )  false" << std::endl;
            return false;
        }

        boost::posix_time::ptime curtime = boost::posix_time::second_clock::local_time();

        if( curtime > time2 ) return false;

        secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID ) );

        if( !secondLevelNode ) return false;

        std::string resId = getContent( secondLevelNode );

        secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_SN ) );

        if( !secondLevelNode )
        {
            std::cerr << "pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_SN ) ) false" << std::endl;
            return false;
        }

        std::string sn = getContent( secondLevelNode );

        if( !m_wsf->onReceiveSubscribeCatalog( sn, resId, time1, time2 ) )
        {
            m_result =
                    "<Response>\r\n"
                    "<CmdType>Catalog</CmdType>\r\n"
                    "<SN>";
            m_result = m_result + sn
                       + "</SN>\r\n"
                       + "<DeviceID>";
            m_result += resId;
            m_result = m_result + "</DeviceID>\r\n"
                       + "<Result>ERROR</Result>\r\n"
                       + "</Response>\r\n";
            m_result = gb28181::XML_PROLOG_HEAD+m_result;

            return false;
        }

        m_result =
                "<Response>\r\n"
                "<CmdType>Catalog</CmdType>\r\n"
                "<SN>";
        m_result = m_result + sn
                   + "</SN>\r\n"
                   + "<DeviceID>";
        m_result += resId;
        m_result = m_result + "</DeviceID>\r\n"
                   + "<Result>OK</Result>\r\n"
                   + "</Response>\r\n";
        m_result = gb28181::XML_PROLOG_HEAD+m_result;
        return true;
    }

    bool CGb28181Parser::request_func_subscribe_alarm()
    {
        pj_str_t tarName;
        pj_xml_node *secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_STARTTIME ) );

        if( secondLevelNode == 0 ) return false;

        std::string cmdTypeContent = getContent( secondLevelNode );

        int year, month, day, hour, minute, second;
        sscanf( cmdTypeContent.c_str(), "%d-%d-%dT%d:%d:%d", &year, &month, &day, &hour, &minute, &second );
        boost::posix_time::ptime time1( boost::gregorian::date( year, month, day ), boost::posix_time::hours( hour ) + boost::posix_time::minutes( minute ) + boost::posix_time::seconds( second ) );

        secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_ENDTIME ) );
        if( secondLevelNode == 0 ) return false;

        cmdTypeContent = getContent( secondLevelNode );

        sscanf( cmdTypeContent.c_str(), "%d-%d-%dT%d:%d:%d", &year, &month, &day, &hour, &minute, &second );
        boost::posix_time::ptime time2( boost::gregorian::date( year, month, day ), boost::posix_time::hours( hour ) + boost::posix_time::minutes( minute ) + boost::posix_time::seconds( second ) );

        if( time1 > time2 ) return false;

        boost::posix_time::ptime curtime = boost::posix_time::second_clock::local_time();

        if( curtime < time1 ) return false;
        if( curtime > time2 ) return false;

        secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID ) );

        if( !secondLevelNode ) return false;

        std::string resId = getContent( secondLevelNode );

        secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_SN ) );

        if( !secondLevelNode ) return false;

        std::string sn = getContent( secondLevelNode );

        boost::uuids::uuid did;
        m_configFile->getResUuid( resId, did );
        if( !m_wsf->onReceiveSubscribeAlarm( did ) )
        {
            m_result =
                    "<Response>\r\n"
                    "<CmdType>Alarm</CmdType>\r\n"
                    "<SN>";
            m_result = m_result + sn
                       + "</SN>\r\n"
                       + "<DeviceID>";
            m_result += resId;
            m_result = m_result + "</DeviceID>\r\n"
                       + "<Result>ERROR</Result>\r\n"
                       + "</Response>\r\n";
            m_result = gb28181::XML_PROLOG_HEAD+m_result;
            return false;
        }

        m_result =
                "<Response>\r\n"
                "<CmdType>Alarm</CmdType>\r\n"
                "<SN>";
        m_result = m_result + sn
                   + "</SN>\r\n"
                   + "<DeviceID>";
        m_result += resId;
        m_result = m_result + "</DeviceID>\r\n"
                   + "<Result>OK</Result>\r\n"
                   + "</Response>\r\n";
        m_result = gb28181::XML_PROLOG_HEAD+m_result;
        return true;
    }

    bool CGb28181Parser::response_notify_broadcast()
    {
        pj_str_t tarName;
        pj_xml_node *secondLevelNode = 0;

        secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_SN ) );
        if( !secondLevelNode ) return false;
        std::string sn = getContent( secondLevelNode );

        secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_SOURCEID ) );
        if( !secondLevelNode ) return false;
        std::string sourceId = getContent( secondLevelNode );

        secondLevelNode = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_TARGETID ) );
        if( !secondLevelNode ) return false;
        std::string targetid = getContent( secondLevelNode );

// 	// 得到UUID`
        boost::uuids::uuid resUuid;
        return m_wsf->onReceiveBroadcast( resUuid, m_sid, sn, sourceId, targetid );
    }

    bool CGb28181Parser::response_func_broadcast()
    {
        if( !m_isOk ) return false;

        pj_xml_node *next = 0;
        pj_str_t tarName;

        if( !( next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_RESULT ) ) ) )
            return false;

        if ( getContent( next ) != "OK")
            return false;

        if( !m_wsf->onReceiveBroadcastResponse( m_sid, true ) )
            return false;

        return true;
    }

    bool CGb28181Parser::request_func_filetoend()
    {
        if( !m_isOk ) return false;

        if( !m_wsf->onReceiveFiletoEnd( m_sid ) )
            return false;

        return true;
    }

    bool CGb28181Parser::response_func_alarmnotify()
    {
        pj_str_t tarName;
        pj_xml_node *next = 0;

        //处理报警通知的响应
        next = pj_xml_find_node( m_root, doConv( &tarName, "Result" ) );
        if( next ) return true;

        //响应报警通知消息
        SAlarmParam gbtInfo;

        next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_SN ) );
        if( !next ) return false;
        std::string sn = getContent( next );

        next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_DEVICEID ) );
        if( !next ) return false;
        gbtInfo.resId = getContent( next );

        next = pj_xml_find_node( m_root, doConv( &tarName, "AlarmPriority" ) );
        if( !next ) return false;
        if( !valueToLexical< std::string, int >( getContent( next ), gbtInfo.priority ) )
            return false;

        next = pj_xml_find_node( m_root, doConv( &tarName, gb28181::TAG_ALARM_METHOD ) );
        if( !next ) return false;
        if( !valueToLexical< std::string, int >( getContent( next ), gbtInfo.method ) )
            return false;

        next = pj_xml_find_node( m_root, doConv( &tarName, "AlarmTime" ) );
        if( !next ) return false;
        if (!strTime2uint32Time( getContent( next ), gbtInfo.time))
            return false;

        next = pj_xml_find_node( m_root, doConv( &tarName, "AlarmDescription" ) );
        if( next )
        {
            gbtInfo.description = getContent( next );
        }

        next = pj_xml_find_node( m_root, doConv( &tarName, "Longitude" ) );
        if( next )
        {
            if( !valueToLexical< std::string, float >( getContent( next ), gbtInfo.longitude ) )
                return false;
        }

        next = pj_xml_find_node( m_root, doConv( &tarName, "Latitude" ) );
        if( next )
        {
            if( !valueToLexical< std::string, float >( getContent( next ), gbtInfo.latitude) )
                return false;
        }

        usg::SAlarmParam alarmParam;
        m_structExchange.alarmInfoGb28181ToWtoe(gbtInfo,alarmParam);
        bool flag = m_wsf->onReceiveAlarmNotify( alarmParam );

        std::string result =
                "<Response>\r\n"
                "<CmdType>Alarm</CmdType>\r\n"
                "<SN>";
        result = result + sn
                 + "</SN>\r\n"
                 + "<DeviceID>";
        result += gbtInfo.resId;
        if (flag)
        {
            result = result + "</DeviceID>\r\n" + "<Result>OK</Result>\r\n" + "</Response>\r\n";
        }
        else
        {
            result = result + "</DeviceID>\r\n" + "<Result>ERROR</Result>\r\n" + "</Response>\r\n";
        }
        result = gb28181::XML_PROLOG_HEAD + result;

        m_resultVec.push_back(result);
        return true;
    }

}
