# FVM

* [目录说明](#目录说明)
* [自定义视频输入、输出](#自定义流元件)
* [编译步骤](#编译步骤)

## 目录说明
.<br>
├── [data](data)    `数据定义、管理`<br>
├── [platform](platform)    `前端接入平台`<br>
├── [protocol](protocol)    `消息协议注册、收发`<br>
├── [stream](stream)    `视频分发管理`<br>
│   ├── [channel](stream/channel)    `通道`<br>
│   └── [input](stream/input)    `视频流输入`<br>
│   └── [output](stream/output)    `视频流输出`<br>
│   └── [onvif](onvif)<br>
└── [util](util)    `工具相关`<br>

## 自定义流元件

&emsp;参照[FilePuller](stream/input/file_puller.h)自定义一个[StreamInput](stream/input/stream_input.h)或[StreamOutput](stream/output/stream_output.h)的派生类

1. mpc_sdk_puller.h 示例

```c++
namespace fvm{
    namespace stream{
        class MpcSdkPuller : public StreamInput{
        private:
            /**
            * 核心取流逻辑
            */
            void process() override;
        }; 
    }
}

```
2. mpc_sdk_puller.cpp 示例

```c++

namespace fvm{
    namespace stream{
        /**
        * 核心取流逻辑
        */
        void MpcSdkPuller::process()
        {
            // 1 收流初始化
            
            // 2 获取视频信息
            auto codecInfo = std::make_shared<CodecInfo>();
            this->onStreamCodecInfoRetrieved(codecInfo); // 信号通知

            // 3 持续获取视频流
            while (flagRequestExitJob)
            {
                AVPacket* pkt =  av_packet_alloc();
                auto ret = av_read_frame(ifmt_ctx, pkt);
                
                auto packet = std::make_shared<PacketData>();
                packet->avPacket = pkt;
                this->onStreamDataReceived(packet); // 信号通知
                
                // 处理相应的 协程等待
                boost::this_fiber::sleep_for(std::chrono::milliseconds (sleepTime));
                //boost::this_fiber::yield();
            }
            
            end:
            // 4 收流结束 释放资源
                closeContext();
        }
    }
}
```

## 编译步骤

### 1.依赖包: <br/>
&emsp;[boost 1.77.0 +](https://www.boost.org/)  <br>
&emsp;[ffmpeg 4.4.1 +](https://ffmpeg.org/)

1. /opt/boost <br>
2. /opt/ffmpeg