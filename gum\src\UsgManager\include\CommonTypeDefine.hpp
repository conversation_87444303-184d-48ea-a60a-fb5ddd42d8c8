#ifndef COMMONTYPEDEFINE_HPP_
#define COMMONTYPEDEFINE_HPP_

#include <boost/uuid/uuid.hpp>

namespace usg
{


    typedef std::pair< uint32_t, uint32_t > TimePeriodUnit;


/**
*@enum EHistoryStreamPosType
*@brief 历史视频点类型
*/
    enum EHistoryStreamPosType
    {
        EHISTORYSTREAMPOSTYPE_MSS  = 0,         ///< 平台播放
        EHISTORYSTREAMPOSTYPE_PRS  = 1          ///< 前端播放
    };


/**
*@enum EDualStreamType
*@brief 码流类型
*/
    enum EDualStreamType
    {
        EDUALSTREAMTYPE_MASTER  = 0,         ///< 主码流
        EDUALSTREAMTYPE_SUB     = 1          ///< 子码流
    };

/**
* 视频封包模式
*/
    enum EPackerType
    {
        EPACKERTYPE_PS          = 0,        ///< ps流
        EPACKERTYPE_ES          = 1,        ///< ES流
        EPACKERTYPE_TS          = 2,        ///< ts流
    };

/**
* @brief 视频尺寸
* 
*/
    enum EMediaImageSize
    {
        EMEDIAIMAGESIZE_QVGA    = 0, ///< qvga  320*240
        EMEDIAIMAGESIZE_CIF     = 1, ///< cif   352*288
        EMEDIAIMAGESIZE_VGA     = 2, ///< vga   640*480
        EMEDIAIMAGESIZE_4CIF    = 3, ///< 4cif  704*576
        EMEDIAIMAGESIZE_SVGA    = 4, ///< svga  800*600
        EMEDIAIMAGESIZE_XGA     = 5, ///< xga   1024*768
        EMEDIAIMAGESIZE_720P    = 6, ///< 720p  1280*720
        EMEDIAIMAGESIZE_UVGA    = 7, ///< uvga  1600*1200
        EMEDIAIMAGESIZE_1080P   = 8, ///< 1080p 1920*1080
    };

} // usg

#endif // COMMONTYPEDEFINE_HPP_
