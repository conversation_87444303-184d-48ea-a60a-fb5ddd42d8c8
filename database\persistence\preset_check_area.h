/**
 * @addtogroup odbDatabaseGroup
 * @brief 预置位对应的检测区信息
 * @{
 */
#ifndef _PRESETCHECKAREA_H
#define _PRESETCHECKAREA_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {
/**
 * @brief: 预置位对应的检测区信息 对应数据库aimonitorV3的表wn_preset_check_area
 */
#pragma db object table("wn_preset_check_area")
class PresetCheckArea {
public:

    PresetCheckArea(unsigned long roiId,
        const std::string& checkArea,
        const std::string& eventProperty,
        const std::string& labelProperty,
        const std::string& param,
        int areaType,
        unsigned long paramPlanId
    )
        : roiId(roiId), checkArea(checkArea), eventProperty(eventProperty),
        labelProperty(labelProperty), param(param), areaType(areaType), paramPlanId(paramPlanId)
    {
    }

    unsigned long getId() const {
        return id;
    }

    unsigned long getRoiId()const {
        return roiId;
    }

    void setRoiId(unsigned long id) {
        this->roiId = id;
    }

    const std::string& getCheckArea() const {
        return checkArea;
    }

    void setCheckArea(const std::string& area) {
        this->checkArea = area;
    }

    const std::string& getEventProperty() const {
        return eventProperty;
    }

    void setEventProperty(const std::string& property) {
        this->eventProperty = property;
    }

    const std::string& getLabelProperty() const {
        return labelProperty;
    }

    void setLabelProperty(const std::string& property) {
        this->labelProperty = property;
    }

    const std::string& getParam() const {
        return param;
    }

    void setParam(const std::string& param) {
        this->param = param;
    }

    int getAreaType()const {
        return areaType;
    }

    void setAreaType(int type) {
        this->areaType = type;
    }

    unsigned long getParamPlanId()const {
        return paramPlanId;
    }

    void setParamPlanId(unsigned long id) {
        this->paramPlanId = id;
    }

private:

    friend class odb::access;
    PresetCheckArea() {}

private:

#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("roi_id")
    unsigned long roiId;                //!< roi id 对应表wn_video_source(VideoSource) id

#pragma db column("check_area")  type("VARCHAR(255)")
    std::string checkArea;              //!< 检测区信息

#pragma db column("event_property") type("VARCHAR(255)")
    std::string eventProperty;          //!< 事件属性值

#pragma db column("label_property") type("VARCHAR(255)")
    std::string labelProperty;          //!< 目标标签属性值

#pragma db column("param")
        std::string param;              //!< 个性化参数 不配置则使用iva默认灵敏度参数

#pragma db column("area_type")
    int areaType;                       //!< 检测区类型

#pragma db column("param_plan_id")
    unsigned long paramPlanId;          //!< 灵敏度预案 对应表wn_alg_param_plan(AlgParamPlan) id
};
}
#endif //_PRESETCHECKAREA_H
/**
 * @}
 */