/**
 * Project FVM
 */
#pragma once

#include "param_prog.h"
#include "param_prog-odb.hxx"
#include "event_type.h"
#include "event_type-odb.hxx"
#include "check_area_type.h"
#include "check_area_type-odb.hxx"
#include "nv_aimonitor31_view.h"
#include "nv_aimonitor31_view-odb.hxx"
#include "process_config.h"
#include "process_config-odb.hxx"
#include "video_server.h"
#include "video_server-odb.hxx"
#include "boost/asio/time_traits.hpp"
#include "boost/date_time/posix_time/posix_time.hpp"

/**
 * @brief: 数据结构及配置相关定义
 */
namespace fvm::data 
{
    typedef std::vector<db::EventType>      EventTypeVec;
    typedef std::vector<db::CheckAreaType>  CheckAreaTypeVec;
    typedef std::vector<db::AlgoParamsData> AlgoParamsVec;
    typedef std::vector<db::ProcessConfig>  ProcessConfigsVec;
    typedef std::vector<db::ParamProgData>  ParamProgDataVec;
    typedef std::vector<db::VideoServer>    RemotesVec;
    typedef std::vector<db::VideoServer>    RemotesVec;

    /**
     * @brief     时间切换方案定义
     */
    struct ProgramTime{
        boost::gregorian::date startDate;
        uint32_t morningTime = 0;
        uint32_t eveningTime = 0;
    };

    typedef std::vector<ProgramTime>        ProgramTimeVec;


    /**
     * @brief     全局配置参数
     */
    struct ConfigParamInfo{
        RemotesVec         remotes;                        //!< 前端接入信息
        ProcessConfigsVec  processConfigs;                 //!< 进程配置
        AlgoParamsVec      algoParams;                     //!< 灵敏度配置
        EventTypeVec       eventTypes;                     //!< 事件类型
        CheckAreaTypeVec   checkAreaTypes;                 //!< 检测区类型
        ParamProgDataVec   paramProgData;                  //!< 程序运行参数
        ProgramTimeVec     programTimeVec;                 //!< 时间方案参数
    };

    /**
     * 事件录像方式
     */
    enum class EvtRecordType
    {
        FVM = 0,
        IVA
    };


    /**
     * @brief     全局配置中KEY名称对应宏
     */
    constexpr auto FVM_PRE_RECORD = "pre_record";
    constexpr auto FVM_RECORD_TIME = "record_time";
    constexpr auto FVM_RTMP_PORT = "FVM_RtmpPort";
    constexpr auto FVM_SHOW_TRACKID = "ShowTrackId";
    constexpr auto FVM_WAIT_DEVIATION = "WaitDeviation";
    constexpr auto FVM_ROI_LEVEL = "roi_alg_level";
    constexpr auto FVM_LANE_LEVEL = "lane_alg_level";
    constexpr auto FVM_CHECKAREA_LEVEL = "checkarea_alg_level";
    constexpr auto FVM_PRESET_PAUSE_TIME = "PresetPauseTime";
    constexpr auto FVM_EVENT_POST_LOCAL = "eventPostLocal";
    constexpr auto FVM_OFFSET_WAIT_TIME = "OffsetWaitTime";     //!< FVM判断偏移的间隔时间，单位为秒，0表示不判断
    constexpr auto GB_STREAM_TYPE = "GBStreamType";
    constexpr auto MPC_STREAM_TYPE = "MPCStreamType";
    constexpr auto DH_STREAM_TYPE =  "DHStreamType";
    constexpr auto AUTO_PTZ_TIME =  "AutoPtzTime";              //!< FVM相机自动归位间隔时间，单位为分钟，0表示不使用自动归位
    constexpr auto WAIT_DEVIATION =  "WaitDeviation";           //!< FVM偏移自动归位间隔时间，单位为分钟，0表示不使用自动归位
    constexpr auto IVA_PRE_RECORD =  "iva_pre_record";          //!< IVA预录像时长
    constexpr auto IVA_RECORD_TIME =  "iva_record_time";        //!< IVA录像总时长
    constexpr auto EVT_RECORD_TYPE =  "event_record_type";      //!< 事件录像方式  0-FVM录像，1-IVA录像

    // 枪球联动
    constexpr auto DOME_EVENT_TYPE = "dome_event_type";         //!< 枪球联动事件类型集
    constexpr auto DOME_PTZ_TIME = "dome_ptz_time";             //!< 枪球联动球机转动时间
    constexpr auto DOME_EVENT_INTERVAL = "dome_event_interval"; //!< 枪球联动事件排队间隔
}