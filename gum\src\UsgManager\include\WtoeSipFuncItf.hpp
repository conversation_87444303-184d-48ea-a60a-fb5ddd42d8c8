
#ifndef WTOESIPFUNCITF_HPP_
#define WTOESIPFUNCITF_HPP_

#include "WtoeStruct.hpp"
#include "UsgProtocolGbt/include/Gb28181Struct.hpp"
#include "UsgManager/include/UsgServiceTypeDefine.hpp"

namespace usg
{
    const static uint8_t MediaImageSize_CIF  = 0;
    const static uint8_t MediaImageSize_4CIF = 1;
    const static uint8_t MediaImageSize_720P = 2;
    const static uint8_t MediaImageSize_1080P= 3;

    enum EAlarmStatus { ONDUTY, OFFDUTY, ONALARM };

/**
 * 给协议模块来调用的接口, 其中包括了WTOE对于SIP的功能支持.
 * 协议模块收到某类型的包之后,调用这些接口完成包所要求的动作.
 */
    struct IWtoeSipFunc
    {
        virtual bool onReceiveCatalogResponse( const std::string &sid, const bool isOk ) = 0;
        virtual bool onReceiveKeepaliveResponse( const std::string &sid, const bool isOk ) = 0;

        /**
         * imageSize: CIF=0, 4CIF=1, 720P=2, 1080P=3, 其它值直接返回失败.
         */
        virtual bool onReceiveRealMedia(
                const std::string &sid,
                const boost::uuids::uuid &resId,
                const uint8_t subImageSize,
                const uint8_t masterImageSize,
                const SRealMedia &r,
                SRealMediaResponse &out ) = 0;
        virtual bool onReceiveRealMediaCancel( const std::string &sid ) = 0;
        virtual bool onReceiveRealMediaAck( const std::string &sid ) = 0;
        virtual bool onReceiveRealMediaBye( const std::string &sid ) = 0;

        virtual bool onReceiveHistoryList( const std::string &sid, const boost::uuids::uuid &resId, const SHistoryList &r, SHistoryListResponse &out ) = 0;
        virtual bool onReceiveHistoryMedia( const std::string &sid, const boost::uuids::uuid &resId, const SHistoryMedia &r, SHistoryMediaResponse &out ) = 0;
        virtual bool onReceivePresetList( const std::string &sid, const boost::uuids::uuid &resId, const SPresetList &r, SPresetListResponse &out ) = 0;
        virtual bool onReceivePtzCommand( const std::string &sid, const boost::uuids::uuid &resId, const SPtzCommand &r, SPtzCommandResponse &out ) = 0;

        virtual bool onReceiveRealMediaResponse( const std::string &sid, SRealMediaResponse& out ) = 0;
        virtual bool onReceiveHistoryListResponse( const std::string& sid, SHistoryListResponse& out ) = 0;
        virtual bool onReceiveHistoryMediaResponse( const std::string& sid, SHistoryMediaResponse& out ) = 0;
        virtual bool onReceivePresetListResponse( const std::string& sid,SPresetListResponse& out ) = 0;

        virtual bool onReceiveCatalog( const std::string &sid, const SCatalog& info, SCatalogResponse& out ) = 0;
        virtual bool onReceiveQueryCatalog( const std::string &sid, const SCatalog& info ) { return true; }
        virtual bool onReceiveKeeplive( const std::string &sid ) = 0;
        virtual bool onReceiveError( const std::string& sid ) = 0;
        virtual bool onReceive200Ok( const std::string& sid ) { return true; }

        virtual bool onReceiveStartRecord( const boost::uuids::uuid &resId ) { return true; }
        virtual bool onReceiveStopRecord( const boost::uuids::uuid &resId ) { return true; }
        virtual bool onReceiveSetGuard( const boost::uuids::uuid &resId ) { return true; }
        virtual bool onReceiveAlarmStatusQuery( const boost::uuids::uuid &resId, EAlarmStatus &status ) { return true; }
        virtual bool onReceiveResetGuard( const boost::uuids::uuid &resId ) { return true; }
        virtual bool onReceiveResetAlarm( const boost::uuids::uuid &resId ) { return true; }

        //江西预置位通知
        virtual bool onReceivePresetListNotify( const std::string& sipCode,SPresetListResponse& out ) = 0;

        //gb28181
        virtual bool onReceiveDeviceCatalog(const std::string &sid, SCatalog &out) { return true; }
        virtual bool onReceiveDeviceInfo( const std::string &sid, const boost::uuids::uuid &resId, SDeviceInfoResponse &out ) { return true; }
        virtual bool onReceiveDeviceStatus( const std::string &sid, const boost::uuids::uuid &resId, SDeviceStatusResponse &out ) { return true; }
        virtual bool onReceiveReboot( const std::string &sid, const boost::uuids::uuid &resId, SRebootCommandResponse &out ){ return true; }
        //查询设备配置
        virtual bool onReceiveQueryDeviceConfig( const boost::uuids::uuid &resId, const std::string &sid, const std::string &sn, const std::vector< std::string > &paramType ) { return true; }
        //设备配置
        virtual bool onRecviveDeviceConfig( const std::string &sn, const std::string &sid, const boost::uuids::uuid &resId, const boost::shared_ptr< SDeviceConfigBasicParam > &bp, const std::vector< SDeviceConfigVideoParam > &vps, const std::vector< SDeviceConfigAudioParam > &aps ) { return true; }
        //订阅告警事件
        virtual bool onReceiveSubscribeAlarm( const boost::uuids::uuid &resId ) { return true; }
        virtual bool onReceiveSubscribeAlarmResponse( const std::string& sid, bool result) { return true; }
        //订阅目录
        virtual bool onReceiveSubscribeCatalog( const std::string &sn, const std::string &sid, boost::posix_time::ptime startTime, boost::posix_time::ptime stopTime ) { return true; }

        //语音广播
        virtual bool onReceiveBroadcast( const boost::uuids::uuid &resId, const std::string &cid, const std::string &sn, const std::string &srcdeviceId, const std::string &deviceId ) { return true; };
        virtual bool onReceiveBroadcastResponse(const std::string& sid ,const bool result) { return true; };

        //录像播放到结束
        virtual bool onReceiveFiletoEnd(const std::string &sid) { return true; };

        virtual bool onReceiveAlarmNotify(const SAlarmParam &alarm) {return true;};

//	virtual bool getCatalog( std::vector< gb28181::SCatalog::SItem > &items ) { return true; };

    protected:
        virtual ~IWtoeSipFunc(){}

    };

}

#endif
