#include "MainApp.hpp"
#include <stdio.h>
#include <stdlib.h>
#include <signal.h>
#include <string.h>
#include <iostream>
#include <cstdio>
#include "include/file_info.hpp"
#include "include/debug_info.hpp"
#include "ailog.h"

bool isSingleProcess( const std::string& pidTxtPath )
{
    std::string szOldValue[1];
    int ret = readFile( (char*)(pidTxtPath.c_str()), szOldValue );
    if ( ret > 0 )  //原来存在该文件
    {
        std::string pidPath = "/proc/" + szOldValue[0];
        if ( access( pidPath.c_str(), F_OK ) == 0 )
        {
            return true;
        }
        ai::LogError << pidPath << "  IS NOT EXIST, REMOVE FILE " << pidTxtPath;
        std::remove( pidTxtPath.c_str() );
    }
    pid_t pid = getpid();  //得到当前程序的pid
    std::string szPid = int2string( pid );
    writeFile((char*)(pidTxtPath.c_str()), szPid);
    ai::LogInfo << " WRITE NEW PID " << szPid << " TO " << pidTxtPath;
    return false;
}

bool checkJsFile( const std::string& chFile )
{
    FILE* fp = fopen(chFile.c_str(), "r");
    if (fp == NULL)
    {
        printf("File %s is not exist!\n", chFile.c_str());
        return false;
    }
    else
        fclose(fp);
    return true;
}

int main( int argc, char* argv[] )
{
    char* argv1[3];
    argv1[0] = argv[0];
    argv1[1] = (char*)"--load=./config/load.js";
    argv1[2] = (char*)"--init=./config/init.js";

    if (argc == 1)
    {
        argc = 3;
        argv = argv1;
    }
    std::string szProgram = "gum";
    ai::InitLog(szProgram, "logs");
    std::string pidTxtPath = "/var/run/" + szProgram + ".pid";

    if ( isSingleProcess( pidTxtPath ))
    {
        ai::LogError << szProgram << " is running in another place, this program exit";
        return 0;
    }

    wtoe::CCmndLineOptionsDescription desc( "Application Options" );
    desc.add_options()
            ( "help", "help message" )
            ( "port", boost::program_options::value<uint16_t>(), "port" )
            ( "load", boost::program_options::value<std::string>(), "load.js" )
            ( "init", boost::program_options::value<std::string>(), "init.js" );

    /*
    * 创建应用程序对象.
    */
    gum::CMainApp app( argc, argv, desc );
    if ( app.getCmndLineParser().hasCmndLineOption( "help" ) )
    {
        app.usage();
        std::remove( pidTxtPath.c_str() );
        ai::LogInfo << "REMOVE PIDFILE "  << pidTxtPath;
        return 0;
    }
    std::string init, load;
    app.getCmndLineParser().getCmndLineOption("init", init);
    app.getCmndLineParser().getCmndLineOption("load", load);
    if (!checkJsFile(init) || !checkJsFile(load))
    {
        std::remove( pidTxtPath.c_str() );
        ai::LogInfo << "REMOVE PIDFILE "  << pidTxtPath;
        return 0;
    }
    /*
    * 启动应用程序对象.
    */
    if ( !app.startup() )
    {
        std::remove( pidTxtPath.c_str() );
        ai::LogInfo << "REMOVE PIDFILE "  << pidTxtPath;
        return -1;
    }
    /*
    * 处理应用程序消息循环.
    */
    int ret = app.run();
    /*
    * 关闭应用程序对象.
    */
    if ( !app.shutdown() )
    {
        std::remove( pidTxtPath.c_str() );
        ai::LogInfo << "REMOVE PIDFILE "  << pidTxtPath;
        return -1;
    }
    std::remove( pidTxtPath.c_str() );
    ai::LogInfo << "REMOVE PIDFILE "  << pidTxtPath;
    ai::FiniLog();
    return ret;
};