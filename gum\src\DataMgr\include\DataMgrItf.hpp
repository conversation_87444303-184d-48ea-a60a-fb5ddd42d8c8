#ifndef DATAMGRITF_HPP_
#define DATAMGRITF_HPP_

#include <functional>
#include <stdint.h>
#include <boost/shared_ptr.hpp>
#include "DataMgr/include/DataMgrCfg.hpp"
#include "wtoe/PackageManager/PackageManagerExp.hpp"

namespace gum
{

interface IDataMgr;

typedef boost::shared_ptr<IDataMgr> CSpIDataMgr;

interface IDataMgr
{
	virtual ~IDataMgr() {}

    virtual bool init(const std::string& ip, const std::string& db) = 0;
    virtual void showVersion() = 0;
    virtual void showStatus( int remoteId = 0, const std::string& szKey = "" ) = 0;
};

} //namespace gum


#endif // DATAMGRITF_HPP_
