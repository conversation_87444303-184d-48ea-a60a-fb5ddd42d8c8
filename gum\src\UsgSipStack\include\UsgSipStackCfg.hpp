#ifndef USGSIPSTACKCFG_HPP_
#define USGSIPSTACKCFG_HPP_

#if defined( WIN32 ) && defined( _MSC_VER )
#    ifdef USGSIPSTACK_PRJ
#        define USGSIPSTACK_PUBLIC __declspec(dllexport)
#    else
#        define USGSIPSTACK_PUBLIC __declspec(dllimport)
#    endif
#elif defined( __GNUC__ )
#    define USGSIPSTACK_PUBLIC //__attribute__((visibility("default")))
#endif

#if defined( TEST_SUPPORT )
#    define USGSIPSTACK_PRIVATE USGSIPSTACK_PUBLIC
#else
#    if defined( WIN32 ) && defined( _MSC_VER )
#        define USGSIPSTACK_PRIVATE
#    elif defined( __GNUC__ )
#        define USGSIPSTACK_PRIVATE __attribute__((visibility("hidden")))
#    endif
#endif

#if defined( COMPILE_BY_SOURCE ) && defined( WIN32 ) && defined( _MSC_VER )
#    undef  USGSIPSTACK_PUBLIC
#    undef  USGSIPSTACK_PRIVATE
#    define USGSIPSTACK_PUBLIC
#    define USGSIPSTACK_PRIVATE
#endif

#ifndef interface
#   define interface struct
#endif

#endif
