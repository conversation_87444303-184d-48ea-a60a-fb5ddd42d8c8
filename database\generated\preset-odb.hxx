// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#ifndef PRESET_ODB_HXX
#define PRESET_ODB_HXX

#include <odb/version.hxx>


#include <odb/pre.hxx>

#include "preset.h"

#include <memory>
#include <cstddef>
#include <utility>

#include <odb/core.hxx>
#include <odb/traits.hxx>
#include <odb/callback.hxx>
#include <odb/wrapper-traits.hxx>
#include <odb/pointer-traits.hxx>
#include <odb/container-traits.hxx>
#include <odb/no-op-cache-traits.hxx>
#include <odb/result.hxx>
#include <odb/simple-object-result.hxx>

#include <odb/details/unused.hxx>
#include <odb/details/shared-ptr.hxx>

namespace odb
{
  // Preset
  //
  template <>
  struct class_traits< ::db::Preset >
  {
    static const class_kind kind = class_object;
  };

  template <>
  class access::object_traits< ::db::Preset >
  {
    public:
    typedef ::db::Preset object_type;
    typedef ::db::Preset* pointer_type;
    typedef odb::pointer_traits<pointer_type> pointer_traits;

    static const bool polymorphic = false;

    typedef long unsigned int id_type;

    static const bool auto_id = true;

    static const bool abstract = false;

    static id_type
    id (const object_type&);

    typedef
    no_op_pointer_cache_traits<pointer_type>
    pointer_cache_traits;

    typedef
    no_op_reference_cache_traits<object_type>
    reference_cache_traits;

    static void
    callback (database&, object_type&, callback_event);

    static void
    callback (database&, const object_type&, callback_event);
  };
}

#include <odb/details/buffer.hxx>

#include <odb/mysql/version.hxx>
#include <odb/mysql/forward.hxx>
#include <odb/mysql/binding.hxx>
#include <odb/mysql/mysql-types.hxx>
#include <odb/mysql/query.hxx>

namespace odb
{
  // Preset
  //
  template <typename A>
  struct query_columns< ::db::Preset, id_mysql, A >
  {
    // id
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    id_type_;

    static const id_type_ id;

    // videoId
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    videoId_type_;

    static const videoId_type_ videoId;

    // actPreset
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    actPreset_type_;

    static const actPreset_type_ actPreset;

    // detectType
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        int,
        mysql::id_long >::query_type,
      mysql::id_long >
    detectType_type_;

    static const detectType_type_ detectType;

    // checkArea
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    checkArea_type_;

    static const checkArea_type_ checkArea;

    // paramPlanId
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        long unsigned int,
        mysql::id_ulonglong >::query_type,
      mysql::id_ulonglong >
    paramPlanId_type_;

    static const paramPlanId_type_ paramPlanId;

    // param
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    param_type_;

    static const param_type_ param;

    // isDel
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        bool,
        mysql::id_long >::query_type,
      mysql::id_long >
    isDel_type_;

    static const isDel_type_ isDel;

    // isEnable
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        bool,
        mysql::id_long >::query_type,
      mysql::id_long >
    isEnable_type_;

    static const isEnable_type_ isEnable;

    // position
    //
    typedef
    mysql::query_column<
      mysql::value_traits<
        ::std::string,
        mysql::id_string >::query_type,
      mysql::id_string >
    position_type_;

    static const position_type_ position;
  };

  template <typename A>
  const typename query_columns< ::db::Preset, id_mysql, A >::id_type_
  query_columns< ::db::Preset, id_mysql, A >::
  id (A::table_name, "`id`", 0);

  template <typename A>
  const typename query_columns< ::db::Preset, id_mysql, A >::videoId_type_
  query_columns< ::db::Preset, id_mysql, A >::
  videoId (A::table_name, "`video_id`", 0);

  template <typename A>
  const typename query_columns< ::db::Preset, id_mysql, A >::actPreset_type_
  query_columns< ::db::Preset, id_mysql, A >::
  actPreset (A::table_name, "`act_preset`", 0);

  template <typename A>
  const typename query_columns< ::db::Preset, id_mysql, A >::detectType_type_
  query_columns< ::db::Preset, id_mysql, A >::
  detectType (A::table_name, "`detect_type`", 0);

  template <typename A>
  const typename query_columns< ::db::Preset, id_mysql, A >::checkArea_type_
  query_columns< ::db::Preset, id_mysql, A >::
  checkArea (A::table_name, "`check_area`", 0);

  template <typename A>
  const typename query_columns< ::db::Preset, id_mysql, A >::paramPlanId_type_
  query_columns< ::db::Preset, id_mysql, A >::
  paramPlanId (A::table_name, "`param_plan_id`", 0);

  template <typename A>
  const typename query_columns< ::db::Preset, id_mysql, A >::param_type_
  query_columns< ::db::Preset, id_mysql, A >::
  param (A::table_name, "`param`", 0);

  template <typename A>
  const typename query_columns< ::db::Preset, id_mysql, A >::isDel_type_
  query_columns< ::db::Preset, id_mysql, A >::
  isDel (A::table_name, "`is_del`", 0);

  template <typename A>
  const typename query_columns< ::db::Preset, id_mysql, A >::isEnable_type_
  query_columns< ::db::Preset, id_mysql, A >::
  isEnable (A::table_name, "`is_enable`", 0);

  template <typename A>
  const typename query_columns< ::db::Preset, id_mysql, A >::position_type_
  query_columns< ::db::Preset, id_mysql, A >::
  position (A::table_name, "`position`", 0);

  template <typename A>
  struct pointer_query_columns< ::db::Preset, id_mysql, A >:
    query_columns< ::db::Preset, id_mysql, A >
  {
  };

  template <>
  class access::object_traits_impl< ::db::Preset, id_mysql >:
    public access::object_traits< ::db::Preset >
  {
    public:
    struct id_image_type
    {
      unsigned long long id_value;
      my_bool id_null;

      std::size_t version;
    };

    struct image_type
    {
      // id
      //
      unsigned long long id_value;
      my_bool id_null;

      // videoId
      //
      unsigned long long videoId_value;
      my_bool videoId_null;

      // actPreset
      //
      unsigned long long actPreset_value;
      my_bool actPreset_null;

      // detectType
      //
      int detectType_value;
      my_bool detectType_null;

      // checkArea
      //
      details::buffer checkArea_value;
      unsigned long checkArea_size;
      my_bool checkArea_null;

      // paramPlanId
      //
      unsigned long long paramPlanId_value;
      my_bool paramPlanId_null;

      // param
      //
      details::buffer param_value;
      unsigned long param_size;
      my_bool param_null;

      // isDel
      //
      int isDel_value;
      my_bool isDel_null;

      // isEnable
      //
      int isEnable_value;
      my_bool isEnable_null;

      // position
      //
      details::buffer position_value;
      unsigned long position_size;
      my_bool position_null;

      std::size_t version;
    };

    struct extra_statement_cache_type;

    using object_traits<object_type>::id;

    static id_type
    id (const id_image_type&);

    static id_type
    id (const image_type&);

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&,
          mysql::statement_kind);

    static void
    bind (MYSQL_BIND*, id_image_type&);

    static bool
    init (image_type&,
          const object_type&,
          mysql::statement_kind);

    static void
    init (object_type&,
          const image_type&,
          database*);

    static void
    init (id_image_type&, const id_type&);

    typedef mysql::object_statements<object_type> statements_type;

    typedef mysql::query_base query_base_type;

    static const std::size_t column_count = 10UL;
    static const std::size_t id_column_count = 1UL;
    static const std::size_t inverse_column_count = 0UL;
    static const std::size_t readonly_column_count = 0UL;
    static const std::size_t managed_optimistic_column_count = 0UL;

    static const std::size_t separate_load_column_count = 0UL;
    static const std::size_t separate_update_column_count = 0UL;

    static const bool versioned = false;

    static const char persist_statement[];
    static const char find_statement[];
    static const char update_statement[];
    static const char erase_statement[];
    static const char query_statement[];
    static const char erase_query_statement[];

    static const char table_name[];

    static void
    persist (database&, object_type&);

    static pointer_type
    find (database&, const id_type&);

    static bool
    find (database&, const id_type&, object_type&);

    static bool
    reload (database&, object_type&);

    static void
    update (database&, const object_type&);

    static void
    erase (database&, const id_type&);

    static void
    erase (database&, const object_type&);

    static result<object_type>
    query (database&, const query_base_type&);

    static unsigned long long
    erase_query (database&, const query_base_type&);

    public:
    static bool
    find_ (statements_type&,
           const id_type*);

    static void
    load_ (statements_type&,
           object_type&,
           bool reload);
  };

  template <>
  class access::object_traits_impl< ::db::Preset, id_common >:
    public access::object_traits_impl< ::db::Preset, id_mysql >
  {
  };

  // Preset
  //
}

#include "preset-odb.ixx"

#include <odb/post.hxx>

#endif // PRESET_ODB_HXX
