
#pragma once
#include "protocol_manager.h"
#include "stream/channel/detect_channel.h"

namespace fvm::protocol{

    /**
      * @brief 添加事件到事件缓存中
      */
    void addEvent(const network::EventOccurInfo &eventOccurInfo);

    /**
     * @brief 从事件缓存中移除已经过滤的事件
     */
    void removeEvent(const network::EventOccurInfo& eventOccurInfo);

    /**
     * @brief 清空对应通道的事件
     */
    void clearChannelEvents(int channelId);

    /**
     * @brief 获取缓存的事件
     */
    std::vector<network::EventOccurInfo> getEvents(int channelId);

    /**
     * @brief     根据ptz预置位判断结果来过滤事件
     * @param[in] presetChangeTime: 预置位变化的时间点
     * @param[in] isPresetChange: 预置位是否发生变化
     */
    void filterEvent(boost::posix_time::ptime presetChangeTime, bool isPresetChange, int channelId);

    /**
     * @brief fvm上报事件，找到对应通道的VideoRecoder对象，放到事件录像线程池中进行录像。
     */
    void postEvent(network::EventOccurInfo &eventOccurInfo, stream::DetectChannelPtr& detectChannelPtr);

    /**
     * @brief iva上报事件到fvm, fvm根据预置位状态处理事件缓存和过滤
     */
    void eventOccurred(network::EventOccurInfo& eventOccurInfo);

    /**
     * @brief iva上报 事件移除 消息, 球机预置位归位
     */
    void eventRemoved(network::EventRemoveInfo &eventRemoveInfo);
    /**
     * @brief 过滤处理iva的videoQuaAlarm消息
     */
    void postVideoQuaAlarm(network::VideoQuaAlarmConf& videoQuaAlarmConf);
    /**
     * @brief 过滤处理iva的videoQuaRecovery消息
     */
    void postVideoQuaRecovery(network::VideoQuaRecovery& videoQuaRecovery);

    /**
     * @brief 事件聚焦
    * @param[in] channelID: 通道ID
    * @param[in] rect: 事件检测区域
     */
    void focusEvent(int channelID, std::vector<Point>& rect);
    void focusEvent(stream::DetectChannelPtr& detectChannelPtr, std::vector<Point>& rect, bool ignoreIfRecording = false);

    /**
     * @brief 初始化枪球联动关联事件列表
     */
    void initDomeEventTypes();

}
