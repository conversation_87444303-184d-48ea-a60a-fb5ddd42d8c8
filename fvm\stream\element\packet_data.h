/**
 * Project FVM
 */
#pragma once

#include <cstdint>
#include <memory>

#ifdef __cplusplus
extern "C"
{
#endif
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#ifdef __cplusplus
}
#endif

namespace fvm {
    namespace data {

        /**
        * 视频流数据包
        */
        class PacketData {
            //std::shared_ptr<std::uint8_t> data;
            //int length;
            //std::int64_t timestamp;
        public:
            PacketData(AVPacket *packet)
            {
                this->avPacket = packet;
            }

            ~PacketData()
            {
                if (avPacket != NULL)
                {
                    av_packet_unref(avPacket);
                    av_packet_free(&avPacket);
                    avPacket = NULL;
                }
            }

            /**
            * 获得码流数据(ffmpeg)
            */
            AVPacket* getData(bool clone= false) const {return clone ? av_packet_clone (this->avPacket) : this->avPacket;}

        private:
            AVPacket *avPacket = NULL;
        };


        /**
        * 码流相关信息等
        */
        class CodecInfo {
        public:
            CodecInfo();
            CodecInfo(const AVRational& timebase, const AVRational& frameRate, const AVCodecParameters* srcCodecpar= NULL)
            {
                this->timebase = timebase;
                this->frameRate = frameRate;
                this->setAVCodecParam(srcCodecpar);
            }

            ~CodecInfo()
            {
                if (codecpar != NULL) {
                    avcodec_parameters_free(&codecpar);
                }
            }

            /**
             * 设置视频详细码流参数
             */
            void setAVCodecParam(const AVCodecParameters* srcCodecpar)
            {
                if(srcCodecpar == NULL)
                    return;

                if (this->codecpar != NULL) {
                    avcodec_parameters_free(&this->codecpar);
                }
                this->codecpar = avcodec_parameters_alloc();
                avcodec_parameters_copy(this->codecpar, srcCodecpar);
            }

            /**
             * 获取视频详细码流参数
             */
            AVCodecParameters* getAVCodecParam() const {return codecpar;}

            /**
            * 获取视频宽
            */
            int getWidth() {return codecpar != NULL ? codecpar->width : 0;};

            /**
            * 获取视频高
            */
            int getHeight() {return codecpar != NULL ? codecpar->height : 0;};

            /**
            * 获取编码类型
            */
            auto getCodecID(){return codecpar != NULL ? codecpar->codec_id: AV_CODEC_ID_NONE;};

            /**
            * 获取时间基准
            */
            AVRational getTimebase() const {return this->timebase;};

            /**
            * 获取视频帧率
            */
            AVRational getFrameRate() const{return this->frameRate;};

        private:
            AVRational timebase;
            AVRational frameRate;
            AVCodecParameters *codecpar = NULL;
        };
    }

    typedef std::shared_ptr<data::PacketData> PacketDataPtr;
    typedef std::shared_ptr<data::CodecInfo> CodecInfoPtr;
}