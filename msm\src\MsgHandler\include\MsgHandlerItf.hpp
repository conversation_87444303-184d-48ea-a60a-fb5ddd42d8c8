#ifndef MSGHANDLERITF_HPP_
#define MSGHANDLERITF_HPP_

#include "wtoe/BasicHelper/BasicHelperExp.hpp"
#include "wtoeSocket/src/wtoe_udp_lib.h"
#include "wtoeSocket/src/wtoe_http_client.h"

namespace msm 
{

interface IMsgHandler;
typedef boost::shared_ptr<IMsgHandler> CSpIMsgHandler;

interface IMsgHandler 
{
	virtual ~IMsgHandler() {}
	virtual bool init( uint16_t port ) = 0;
	virtual bool fini() = 0;
	virtual void getLocalIps( std::vector<std::string>& localIps ) = 0;

	virtual bool sendRemoteChange( uint32_t remoteId, const std::string& ip, uint16_t port ) = 0;
	virtual bool postRemoteStatus( uint32_t remoteId, bool status, const std::string& ip, uint16_t port ) = 0;
};

}  //namespace msm 

#endif // DATAMGRITF_HPP_

