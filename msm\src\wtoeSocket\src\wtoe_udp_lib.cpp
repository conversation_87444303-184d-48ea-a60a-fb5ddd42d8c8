﻿#include "wtoe_udp_lib.h"
#include "StdoutLog.h"
#include "SocketHandler.h"
#include "UdpSocket.h"
#include "UdpTestSocket.h"
#include "Thread.h"
#include "Utility.h"
#include "EventTime.h"
#include "SelectThread.h"
#include "MessageHandle.h"
#include "MessageSNSave.h"

#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include <iostream>
#ifdef linux
#include <cstdio>
#endif
using namespace rapidjson;
using namespace std;

#ifndef _WIN32
  #include <sys/types.h>
  #include <ifaddrs.h>
#endif

static void trans2Point( Value& p1, WTOEPoint& pt, Document& d )
{
	p1.AddMember("x", pt.x, d.GetAllocator() );
	p1.AddMember("y", pt.y, d.GetAllocator() );
}

static void trans2Area( Value& area, std::vector<WTOEPoint>& vtPoint, Document& d )
{
	for (size_t m = 0; m < vtPoint.size(); m++)
	{
		Value point(rapidjson::kObjectType);
		trans2Point( point, vtPoint[m], d );
		area.PushBack( point, d.GetAllocator());
	}
}

static void trans2Line( Value& direction, Direction& dirtObj, Document& d )
{
	Value pointBegin(rapidjson::kObjectType), pointEnd(rapidjson::kObjectType);
	trans2Point( pointBegin, dirtObj.pointBegin, d );
	trans2Point( pointEnd, dirtObj.pointEnd, d );
	direction.PushBack(pointBegin, d.GetAllocator());
	direction.PushBack(pointEnd, d.GetAllocator());
}

static void trans2Param( Value& param, std::vector<Param>& vecParam, Document& d )
{
	for (size_t k = 0; k < vecParam.size(); k++)
	{
		Param& pmObj = vecParam[k];
		Value paramNode(rapidjson::kObjectType);
		paramNode.AddMember(rapidjson::StringRef(pmObj.key.c_str()), rapidjson::StringRef(pmObj.value.c_str()), d.GetAllocator());
		param.PushBack(paramNode, d.GetAllocator());
	}
}

static void trans2Rect( Value& area, WTOERect& rtObj, Document& d )
{
	area.AddMember("height", rtObj.height, d.GetAllocator());
	area.AddMember("width", rtObj.width, d.GetAllocator());
	area.AddMember("x", rtObj.point.x, d.GetAllocator());
	area.AddMember("y", rtObj.point.y, d.GetAllocator());
}

static void trans2Rect( Value& area, KeyRect& rtObj, Document& d )
{
	WTOEPoint pbObj[] = { rtObj.p1, rtObj.p2, rtObj.p3, rtObj.p4 };
	int sz = sizeof( pbObj ) / sizeof( pbObj[0] );
	for ( int i = 0; i < sz; i++ )
	{
		Value pt(rapidjson::kObjectType);
		trans2Point( pt, pbObj[i], d );
		area.PushBack( pt, d.GetAllocator());
	}
}

static void addProperty( Value& node, int iEventProperty, int iLabelProperty, int iParamPlanId, std::vector<Param>& vtParam, Document& d )
{
	if ( iEventProperty != -1 )
		node.AddMember("EventProperty", iEventProperty, d.GetAllocator());
	if ( iLabelProperty != -1 )
		node.AddMember("LabelProperty", iLabelProperty, d.GetAllocator());
	node.AddMember("ParamPlanId", iParamPlanId, d.GetAllocator());
	Value param(rapidjson::kArrayType);
	trans2Param( param, vtParam, d );
	node.AddMember("Param", param, d.GetAllocator());
}

WtoeUDPLib::WtoeUDPLib():pSelectThread(NULL),pMessageHandle(NULL)//,pMessageSNSave(NULL)
{
	port =5000;
	ip = "127.0.0.1";

	//PrintLog = true;
    //ChannelIndex = -1;

	if(!pSelectThread)
	{
		pSelectThread = new SelectThread();
		pSelectThread->Start();
	}

#if 0
	if(!pMessageSNSave)
	{
		pMessageSNSave = new MessageSNSave();
		//pMessageSNSave->Start();
	}
#endif

	if (!pMessageHandle)
	{
		pMessageHandle = new MessageHandle();
		pMessageHandle->Start();
	}
}

WtoeUDPLib::~WtoeUDPLib()
{
	while(pSelectThread->IsRunning())
	{
		pSelectThread->SetRunning(false);
		Utility::Sleep(2000);
	}
	delete pSelectThread;
	pSelectThread = NULL;

	while (pMessageHandle->IsRunning())
	{
		pMessageHandle->SetRunning(false);
		Utility::Sleep(2000);
	}
	delete pMessageHandle;
	pMessageHandle = NULL;
#if 0
	while (pMessageSNSave->IsRunning())
	{
		pMessageSNSave->Stop();
		Utility::Sleep(1000);
	}
	delete pMessageSNSave;
	pMessageSNSave = NULL;
#endif

}

void WtoeUDPLib::SetDestPort(int iPort)
{
	port = iPort;
}

void WtoeUDPLib::SetDestIP(std::string strIp)
{
	ip = strIp;
}

int WtoeUDPLib::StartUDPServer( std::string Ip,unsigned short Port)
{

	UdpTestSocket *s = new UdpTestSocket(*(pSelectThread->GetSocketHandler()));
	s->SetDeleteByHandler(true);
	s->setMessageHandle(pMessageHandle);
	//s->SetMessageSNSaveHandle(pMessageSNSave);

	port_t port = Port;
	
	try{
		if (s->Bind(Ip, port, 10) == -1)
		{
			delete s;
			s = NULL;
			return WTOESOCKET_FALSE;
		}
		pSelectThread->GetSocketHandler()->Add(s);
	}
	catch (...)
	{
		delete s;
		s = NULL;
		return WTOESOCKET_FALSE;
	}
	return WTOESOCKET_TRUE;
}

int WtoeUDPLib::udpSendTo(std::string ip, unsigned short port, std::string strData)
{
	//UdpSocket s(*(pSelectThread)->GetSocketHandler());
	SocketHandler h;
	UdpSocket s(h);

	if(pMessageHandle->cbhandleMsgReceived)
	{
		char chTmp[100];
		sprintf(chTmp, "SEND TO %s:%d ---- ", ip.c_str(), port );
		pMessageHandle->cbhandleMsgReceived(std::string(chTmp) + strData,NULL);
	}
	else
		cout << "send to " << port << " .... " << strData.c_str() << endl;

	//for (int i = 0; i< 3; i++)
	for (int i = 0; i< 1; i++)
	{	
		s.SendTo(ip, port, strData);
		//Utility::Sleep(100);
	}
	return WTOESOCKET_TRUE;
}


int WtoeUDPLib::GetPointList(const std::string& strPoint, std::vector<WTOEPoint> &vtPoint)
{
	Document doc;
	doc.Parse(strPoint.c_str());
	if (doc.HasParseError())
	{
		return WTOESOCKET_FALSE;
	}
	if (!doc.IsArray())
	{
		return WTOESOCKET_FALSE;
	}
	for (unsigned long i = 0; i < doc.Size(); i++)
	{
		WTOEPoint pt;
		const Value& point = doc[i];
		if (point.HasMember("x"))
		{
			pt.x = point["x"].GetDouble();
		}
		if (point.HasMember("y"))
		{
			pt.y = point["y"].GetDouble();
		}
		vtPoint.push_back(pt);
	}
	return WTOESOCKET_TRUE;
}


int WtoeUDPLib::GetKeyValue( const std::string& szSrc, std::vector<Param>& vtValue )
{
	Document doc;
	doc.Parse(szSrc.c_str());
	if (doc.HasParseError())
	{
		return WTOESOCKET_FALSE;
	}
	if (!doc.IsArray())
	{
		return WTOESOCKET_FALSE;
	}
	for (unsigned long i = 0; i < doc.Size(); i++)
	{
		Param param;
		const Value& tmpValue = doc[i];
		int count = tmpValue.MemberCount();
		if ( count == 1 )  //直接是[{"StopCheckTime":"2"},{"StopCheckIou":"0.8"},{"StopCheckRate":"0.8"},{"StopRemoveTime":"50"}]
		{
			for (rapidjson::Value::ConstMemberIterator iter=tmpValue.MemberBegin();
				iter!=tmpValue.MemberEnd(); ++iter)
			{
				const rapidjson::Value& name_json = iter->name; // 这个必是字符串
				const rapidjson::Value& value_json = iter->value; // 这个可以为对象、数组等
				param.key = name_json.GetString();
				param.value = value_json.GetString();
			}
		}
		else
		{
			if (tmpValue.HasMember("key"))
			{
				param.key = tmpValue["key"].GetString();
			}
			if (tmpValue.HasMember("value"))
			{
				if ( tmpValue["value"].IsString() )
				{
					param.value = tmpValue["value"].GetString();
				}
				else if ( tmpValue["value"].IsDouble() )
				{
					char tmp[100] = {0};
					sprintf( tmp, "%.3f", tmpValue["value"].GetDouble() );
					param.value = std::string( tmp );
				}
			}
		}
		vtValue.push_back(param );
	}
	return WTOESOCKET_TRUE;
}

int WtoeUDPLib::GetIPAddress(std::vector<std::string> &vtIpList)
{
#ifdef _WIN32
	char name[256];
	gethostname(name, sizeof(name));
	HOSTENT* host = gethostbyname(name);
	for (int i = 0; host->h_addr_list[i]; i++) {
		std::string addr;
		Utility::l2ip(((struct in_addr*)host->h_addr_list[i])->s_addr, addr);
		vtIpList.push_back(addr);
	}
#else
	struct sockaddr_in *sin = NULL;
	struct ifaddrs *ifa = NULL, *ifList;
	if (getifaddrs(&ifList) < 0)
	{
		return WTOESOCKET_FALSE;
	}
	
	for (ifa = ifList; ifa != NULL; ifa = ifa->ifa_next)
	{
		if (ifa->ifa_addr->sa_family == AF_INET)
		{
			sin = (struct sockaddr_in *)ifa->ifa_addr;
			//printf("ipAddress: %s\n", inet_ntoa(sin->sin_addr));
			vtIpList.push_back(inet_ntoa(sin->sin_addr));
		}
	}
	freeifaddrs(ifList);
#endif
	return WTOESOCKET_TRUE;
}

int WtoeUDPLib::resetChannel(int iChannelID, std::string strIp, unsigned short uPort)
{
	StringBuffer s1;
    Writer<StringBuffer> writer(s1);
    
    writer.StartObject();               // Between StartObject()/EndObject(), 
    writer.Key("sn");                // output a key,
    writer.Uint64(EventTime::Tick());             // follow by a value.

    writer.Key("type");
	writer.String("ChannelReset");
    writer.Key("from");
	writer.String("FVM");
	writer.Key("data");

	writer.StartObject();
	writer.Key("ChannelID");
	writer.Int(iChannelID);

	writer.EndObject();

    writer.EndObject();

	if (strIp == "" && uPort == 0)
	{
		return udpSendTo(ip.c_str(), port, s1.GetString());
	}
	else
	{
		return udpSendTo(strIp.c_str(), uPort, s1.GetString());
	}
}


int WtoeUDPLib::configSystem(SystemConfig &sysConfig, std::string strIp, unsigned short uPort)
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "SystemConfig", d.GetAllocator());
	d.AddMember("from", "FVM", d.GetAllocator());


	Value data(rapidjson::kObjectType);

	Value checkAreaTypeArray(rapidjson::kArrayType);
	for (size_t i = 0; i< sysConfig.vtCheckAreaType.size(); i++)
	{
		Value checkAreaType(rapidjson::kObjectType);

		checkAreaType.AddMember("id",sysConfig.vtCheckAreaType[i].id, d.GetAllocator());
		checkAreaType.AddMember("remark",rapidjson::StringRef(sysConfig.vtCheckAreaType[i].strRemark.c_str()), d.GetAllocator());
		checkAreaTypeArray.PushBack(checkAreaType, d.GetAllocator());
	}
	data.AddMember("CheckAreaType", checkAreaTypeArray, d.GetAllocator());


	Value eventTypeArray(rapidjson::kArrayType);
	for (size_t i = 0; i< sysConfig.vtEventType.size(); i++)
	{
		Value eventTypeValue(rapidjson::kObjectType);

		eventTypeValue.AddMember("id",sysConfig.vtEventType[i].eventType, d.GetAllocator());
		eventTypeValue.AddMember("remark",rapidjson::StringRef(sysConfig.vtEventType[i].strRemark.c_str()), d.GetAllocator());
		eventTypeArray.PushBack(eventTypeValue, d.GetAllocator());
	}
	data.AddMember("EventType", eventTypeArray, d.GetAllocator());



	data.AddMember("platIP",  rapidjson::StringRef(sysConfig.strPlatIP.c_str()), d.GetAllocator());
	data.AddMember("localIP", rapidjson::StringRef(sysConfig.strLocalIP.c_str()), d.GetAllocator());
	data.AddMember("isShowID",sysConfig.isShowID, d.GetAllocator());
	data.AddMember("offsetRetunTime", sysConfig.iOffsetRetunTime, d.GetAllocator());
	data.AddMember("RoiAlgLevel",sysConfig.iRoiAlgLevel, d.GetAllocator());
	data.AddMember("LaneAlgLevel",sysConfig.iLaneAlgLevel, d.GetAllocator());
	data.AddMember("CheckAreaAlgLevel",sysConfig.iCheckAreaAlgLevel, d.GetAllocator());

	
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	if (strIp == "" && uPort == 0)
	{
		return udpSendTo(ip.c_str(), port, buffer.GetString());
	}
	else
	{
		return udpSendTo(strIp.c_str(), uPort, buffer.GetString());
	}
}

int WtoeUDPLib::algorithmParam(AlgorithmParam& algorithmParam, std::string strIp, unsigned short uPort)
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "AlgorithmParam", d.GetAllocator());
	d.AddMember("from", "FVM", d.GetAllocator());

	Value data(rapidjson::kArrayType);
	for (size_t i = 0; i < algorithmParam.vtParam.size(); i++)
	{
		Value paramItem(rapidjson::kObjectType);
		paramItem.AddMember("PlanId", algorithmParam.vtParam[i].iPlanId, d.GetAllocator());

		Value param(rapidjson::kArrayType);
		trans2Param( param, algorithmParam.vtParam[i].vtParamItem, d );
		paramItem.AddMember("Param", param, d.GetAllocator() );
		data.PushBack(paramItem, d.GetAllocator());
	}
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	if (strIp == "" && uPort == 0)
	{
		return udpSendTo(ip.c_str(), port, buffer.GetString());
	}
	else
	{
		return udpSendTo(strIp.c_str(), uPort, buffer.GetString());
	}
}



#if 0
int WtoeUDPLib::configSystem(SystemConfig &sysConfig)
{
	StringBuffer s1;
    Writer<StringBuffer> writer(s1);
    
    writer.StartObject();						 // Between StartObject()/EndObject(), 
    writer.Key("sn");							// output a key,
    writer.Uint64(EventTime::Tick());           // follow by a value.

    writer.Key("type");
	writer.String("SystemConfig");
    writer.Key("from");
	writer.String("FVM");
	writer.Key("data");

	writer.StartObject();
	writer.Key("CheckAreaType");
	writer.StartArray();

	for(size_t i =0; i< sysConfig.vtCheckAreaType.size(); i++)
	{
		writer.StartObject();
		writer.Key("id");
		writer.Int(sysConfig.vtCheckAreaType[i].id);

		writer.Key("remark");
		writer.String(sysConfig.vtCheckAreaType[i].strRemark.c_str());
		writer.EndObject();
	}
	writer.EndArray();

	writer.Key("EventType");
	writer.StartArray();

	for(size_t i =0; i< sysConfig.vtEventType.size(); i++)
	{
		writer.StartObject();
		writer.Key("id");
		writer.Int(sysConfig.vtEventType[i].eventType);
		
		writer.Key("remark");
		writer.String(sysConfig.vtEventType[i].strRemark.c_str());
		writer.EndObject();
	}
	writer.EndArray();

	writer.Key("platIP");
	writer.String(sysConfig.strPlatIP.c_str());

	writer.Key("localIP");
	writer.String(sysConfig.strLocalIP.c_str());

	writer.Key("isShowID");
	writer.Int(sysConfig.isShowID);

	writer.Key("offsetRetunTime");
	writer.Uint(sysConfig.offsetRetunTime);


	writer.Key("preRecordDuration");
	writer.Uint(sysConfig.preRecordDuration);

	writer.Key("recordDuration");
	writer.Uint(sysConfig.recordDuration);

	writer.EndObject();

	return udpSendTo(ip.c_str(), port, s1.GetString());
}
#endif

int WtoeUDPLib::configBasicChannel(std::vector<ChannelBasicConf> &vtChannelBasicConf, std::string strIp, unsigned short uPort)
{
	StringBuffer s1;
    Writer<StringBuffer> writer(s1);
    
    writer.StartObject();						 // Between StartObject()/EndObject(), 
    writer.Key("sn");							// output a key,
    writer.Uint64(EventTime::Tick());           // follow by a value.

    writer.Key("type");
	writer.String("ChannelBasicConf");
    writer.Key("from");
	writer.String("FVM");
	writer.Key("data");

	writer.StartArray();

	for(size_t i =0; i< vtChannelBasicConf.size(); i++)
	{
		writer.StartObject();
		writer.Key("ChannelID");
		writer.Int(vtChannelBasicConf[i].iChannelID);
	
		writer.Key("Status");
		writer.Int(vtChannelBasicConf[i].status);


		writer.Key("VideoInfo");
		writer.StartArray();
		for(size_t j = 0 ; j< vtChannelBasicConf[i].vtRollInfo.size(); j++)	
		{
			writer.StartObject();

			writer.Key("VideoID");
			writer.Int(vtChannelBasicConf[i].vtRollInfo[j].iVideoID);

			writer.Key("PresetID");
			writer.StartArray();
			for(size_t k = 0; k < vtChannelBasicConf[i].vtRollInfo[j].vtPresetID.size();k++)
			{
				writer.Int(vtChannelBasicConf[i].vtRollInfo[j].vtPresetID[k]);
			}
			writer.EndArray();

			writer.Key("HasPtz");
			writer.Int(vtChannelBasicConf[i].vtRollInfo[j].iHasPtz);

			writer.EndObject();

		}
		writer.EndArray();
		writer.EndObject();
		
	}
	writer.EndArray();

	writer.EndObject();

	if (strIp == "" && uPort == 0)
	{
		return udpSendTo(ip.c_str(), port, s1.GetString());
	}
	else
	{
		return udpSendTo(strIp.c_str(), uPort, s1.GetString());
	}
}

int WtoeUDPLib::pauseChannel( ChannelPauseConf& channelPauseConf, std::string strIp, unsigned short uPort)
{
    Document d;
	d.SetObject();
	d.AddMember("sn",EventTime::Tick(), d.GetAllocator());
	d.AddMember("type","ChannelPause", d.GetAllocator());
	d.AddMember("from","FVM", d.GetAllocator());

	Value data(rapidjson::kObjectType);
	data.AddMember("ChannelID", channelPauseConf.iChannelID,d.GetAllocator());
	data.AddMember("VideoID", channelPauseConf.iVideoID,d.GetAllocator());
	data.AddMember("Reason", channelPauseConf.iReason, d.GetAllocator());
	data.AddMember("Width", channelPauseConf.iWidth, d.GetAllocator());
	data.AddMember("Height", channelPauseConf.iHeight, d.GetAllocator());
	d.AddMember("data",data, d.GetAllocator());

    StringBuffer buffer;
    Writer<StringBuffer> writer(buffer);
    d.Accept(writer);

	if (strIp == "" && uPort == 0)
	{
		return udpSendTo(ip.c_str(), port, buffer.GetString());
	}
	else
	{
		return udpSendTo(strIp.c_str(), uPort, buffer.GetString());
	}
}

int WtoeUDPLib::channelDetectParam(ChannelDetectParam& channelDetectParam, std::string strIp, unsigned short uPort)
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "ChannelDetectParam", d.GetAllocator());
	d.AddMember("from", "FVM", d.GetAllocator());

	Value data(rapidjson::kObjectType);
	data.AddMember("ChannelID", channelDetectParam.ChannelID, d.GetAllocator());
	data.AddMember("VideoID", channelDetectParam.VideoID, d.GetAllocator());

	Value presets(rapidjson::kArrayType);
	for (size_t i = 0; i < channelDetectParam.Presets.size(); i++)
	{//preset begin
		Preset& presetObj = channelDetectParam.Presets[i];
		Value preset(rapidjson::kObjectType);
		preset.AddMember("PresetID", presetObj.PresetID, d.GetAllocator());
		
		{//detectParam begin
			Value detectParam(rapidjson::kObjectType);
			DetectParam& dpObj = presetObj.detectParam;
			{//area begin
				Value area(rapidjson::kObjectType);
				trans2Rect( area, dpObj.DetectArea, d );
				detectParam.AddMember("Area", area, d.GetAllocator());
			}//area end

			{//deviation begin
// 				Value deviation(rapidjson::kObjectType);
// 				trans2Rect( deviation, dpObj.Deviation, d );
				Value deviation(rapidjson::kArrayType);
				trans2Area( deviation, dpObj.Deviation.vtPoint, d );
				detectParam.AddMember("Deviation", deviation, d.GetAllocator());
			}//deviation end
			detectParam.AddMember("DetectType", dpObj.DetectType, d.GetAllocator());
			addProperty( detectParam, -1, -1, dpObj.ParamPlanId, dpObj.param, d );

			{//ROI begin

				Value ROINodeArray(rapidjson::kArrayType);
				for (size_t j = 0; j < dpObj.ROI.size(); j++)
				{
					ROINode& roiObj = dpObj.ROI[j];
					Value ROINode(rapidjson::kObjectType);
					addProperty( ROINode, roiObj.EventProperty, roiObj.LabelProperty, roiObj.ParamPlanId, roiObj.param, d );

					{//area begin
						Value area(rapidjson::kArrayType);
						trans2Area( area, roiObj.area.vtPoint, d );
						ROINode.AddMember("Area", area, d.GetAllocator());
					}//area end

					{//direction begin
						Value direction(rapidjson::kArrayType);
						trans2Line( direction, roiObj.diection, d );
						ROINode.AddMember("Direction", direction, d.GetAllocator());
					}//direction end

					{//countline begin
						Value countline(rapidjson::kArrayType), pointEnd(rapidjson::kObjectType);
						trans2Line( countline, roiObj.countLine, d );
						ROINode.AddMember("CountLine", countline, d.GetAllocator());
					}//countline end

					ROINode.AddMember("KeyLength", roiObj.KeyLength, d.GetAllocator());

					{//KeyPoint begin
						Value keyPoint(rapidjson::kArrayType);
						trans2Area( keyPoint, roiObj.keyPoint.vtKeyPoint, d );
						ROINode.AddMember("KeyPoint", keyPoint, d.GetAllocator());
					}//KeyPoint end

					{//KeyRect begin
						Value keyRect(rapidjson::kArrayType);
						trans2Rect( keyRect, roiObj.keyRect, d );
						ROINode.AddMember("KeyRect", keyRect, d.GetAllocator());
					}//KeyRect end

					{//Lane begin
						Value laneArray(rapidjson::kArrayType);
						for (size_t k = 0; k < roiObj.lane.size(); k++)
						{
							Lane& laneObj = roiObj.lane[k];
							Value lane(rapidjson::kObjectType);
							{//area begin
								Value area(rapidjson::kArrayType);
								trans2Area( area, laneObj.area.vtPoint, d );
								lane.AddMember("Area", area, d.GetAllocator());
							}//area end

							{//direction begin
								Value direction(rapidjson::kArrayType);
								trans2Line( direction, laneObj.diection, d );
								lane.AddMember("Direction", direction, d.GetAllocator());
							}//direction end

							lane.AddMember("LaneType", laneObj.LaneType , d.GetAllocator());							
							addProperty( lane, laneObj.EventProperty, laneObj.LabelProperty, laneObj.ParamPlanId, laneObj.param, d );

							lane.AddMember("LaneID", laneObj.LaneID, d.GetAllocator());
							lane.AddMember("MaxSpeed", laneObj.MaxSpeed, d.GetAllocator());
							lane.AddMember("MinSpeed", laneObj.MinSpeed, d.GetAllocator());
							laneArray.PushBack(lane, d.GetAllocator());
						}
						ROINode.AddMember("Lane", laneArray, d.GetAllocator());

					}//Lane end

					{//checkArea begin
						Value checkArea(rapidjson::kArrayType);
						for (size_t k = 0; k < roiObj.vtCheckArea.size(); k++)
						{
							CheckAreaItemNode& areaObj = roiObj.vtCheckArea[k];
							Value checkAreaItemNode(rapidjson::kObjectType);

							{//area begin
								Value area(rapidjson::kArrayType);
								trans2Area( area, areaObj.area.vtPoint, d );
								checkAreaItemNode.AddMember("Area", area, d.GetAllocator());
							}//area end

							checkAreaItemNode.AddMember("AreaTypeId", areaObj.AreaTypeId, d.GetAllocator());
							checkAreaItemNode.AddMember("AreaId", areaObj.AreaId, d.GetAllocator());
							addProperty( checkAreaItemNode, areaObj.EventProperty, areaObj.LabelProperty, areaObj.ParamPlanId, areaObj.param, d );							
							checkArea.PushBack(checkAreaItemNode, d.GetAllocator());
						}
						ROINode.AddMember("CheckArea", checkArea, d.GetAllocator());
					}//checkArea end

					ROINode.AddMember("LogicID", roiObj.LogicID, d.GetAllocator());
					ROINode.AddMember("ROIID", roiObj.ROIID, d.GetAllocator());
					ROINodeArray.PushBack(ROINode, d.GetAllocator());
				}
				detectParam.AddMember("ROI", ROINodeArray, d.GetAllocator());
			}//ROI end
			preset.AddMember("DetectParam", detectParam, d.GetAllocator());
		}//detectParam end
		presets.PushBack(preset, d.GetAllocator());
		//preset end
	  }
	data.AddMember("Presets", presets, d.GetAllocator());
	d.AddMember("data", data, d.GetAllocator());
	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	writer.SetMaxDecimalPlaces(3);
	d.Accept(writer);
	if (strIp == "" && uPort == 0)
	{
		return udpSendTo(ip.c_str(), port, buffer.GetString());
	}
	else
	{
		return udpSendTo(strIp.c_str(), uPort, buffer.GetString());
	}
}

int WtoeUDPLib::restoreChannel(ChannelRestoreConf &channelRestoreConf, std::string strIp, unsigned short uPort)
{
	Document d;
	d.SetObject();
	d.AddMember("sn",EventTime::Tick(), d.GetAllocator());
	d.AddMember("type","ChannelRestore", d.GetAllocator());
	d.AddMember("from","FVM", d.GetAllocator());

	Value data(rapidjson::kObjectType);
	data.AddMember("ChannelID",channelRestoreConf.iChannelID,d.GetAllocator());
	data.AddMember("VideoID", channelRestoreConf.iVideoID, d.GetAllocator());
	data.AddMember("PresetID", channelRestoreConf.iPresetID, d.GetAllocator());
	data.AddMember("Width", channelRestoreConf.iWidth, d.GetAllocator());
	data.AddMember("Height", channelRestoreConf.iHeight, d.GetAllocator());
	data.AddMember("Reason", channelRestoreConf.iReason, d.GetAllocator());

#if 0


	Value curConfig(rapidjson::kObjectType);
	{
		curConfig.AddMember("PresetID",channelRestoreConf.currentConfig.iPresetID,d.GetAllocator());
		curConfig.AddMember("VideoID",channelRestoreConf.currentConfig.iVideoID,d.GetAllocator());

	}
	data.AddMember("CurrentConfig",curConfig,d.GetAllocator());

	Value analysis(rapidjson::kObjectType);

	{//分析配置开始
		//Value analysis(rapidjson::kObjectType);
		
		{//Area begin
			Value area(rapidjson::kObjectType);
			area.AddMember("height",channelRestoreConf.analysisConfig.area.height, d.GetAllocator());
			area.AddMember("width",channelRestoreConf.analysisConfig.area.width, d.GetAllocator());
			area.AddMember("x",channelRestoreConf.analysisConfig.area.point.x, d.GetAllocator());
			area.AddMember("y",channelRestoreConf.analysisConfig.area.point.y, d.GetAllocator());	
			analysis.AddMember("Area",area, d.GetAllocator());
		}//Area end
		
		{//Deviation begin
			Value deviation(rapidjson::kObjectType);
			deviation.AddMember("height",channelRestoreConf.analysisConfig.deviation.height, d.GetAllocator());
			deviation.AddMember("width",channelRestoreConf.analysisConfig.deviation.width, d.GetAllocator());
			deviation.AddMember("x",channelRestoreConf.analysisConfig.deviation.point.x, d.GetAllocator());
			deviation.AddMember("y",channelRestoreConf.analysisConfig.deviation.point.y, d.GetAllocator());
			analysis.AddMember("Deviation",deviation, d.GetAllocator());

		}//Deviation end
		
		{//DayNightSensitive begin
			analysis.AddMember("DayNightSensitive",channelRestoreConf.analysisConfig.dayNightSensitive, d.GetAllocator());
		}//DayNightSensitive end

		{//DetectSensitive begin
			analysis.AddMember("DetectSensitive",channelRestoreConf.analysisConfig.detectSensitive, d.GetAllocator());
		}//DetectSensitive end
		{//DetectType begin
			analysis.AddMember("DetectType",channelRestoreConf.analysisConfig.detectType, d.GetAllocator());
		}//DetectType end

		{//ROI begin

			Value ROINodeArray(rapidjson::kArrayType);
			for(size_t i=0; i<channelRestoreConf.analysisConfig.vtROI.size();i++)
			{
				Value ROINode(rapidjson::kObjectType);
				{//area begin
					Value area(rapidjson::kArrayType);
					for(size_t j=0; j<channelRestoreConf.analysisConfig.vtROI[i].area.vtPoint.size();j++)
					{
						Value point(rapidjson::kObjectType);
						point.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].area.vtPoint[j].x,d.GetAllocator());
						point.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].area.vtPoint[j].y,d.GetAllocator());
						area.PushBack(point,d.GetAllocator());
					}
					ROINode.AddMember("Area",area,d.GetAllocator());

				}//area end
				
				{//direction begin
					Value direction(rapidjson::kArrayType);
					{
						Value pointBegin(rapidjson::kObjectType);
						pointBegin.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].diection.pointBegin.x,d.GetAllocator());
						pointBegin.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].diection.pointBegin.y,d.GetAllocator());

						direction.PushBack(pointBegin,d.GetAllocator());

						Value pointEnd(rapidjson::kObjectType);
						pointEnd.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].diection.pointEnd.x,d.GetAllocator());
						pointEnd.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].diection.pointEnd.y,d.GetAllocator());
						direction.PushBack(pointEnd,d.GetAllocator());
					}

					ROINode.AddMember("Direction",direction,d.GetAllocator());

				}//direction end
				
				ROINode.AddMember("KeyLength",channelRestoreConf.analysisConfig.vtROI[i].keyLength,d.GetAllocator());

				{//KeyPoint begin
					Value keyPoint(rapidjson::kArrayType);
					for(size_t k = 0; k<channelRestoreConf.analysisConfig.vtROI[i].keyPoint.vtKeyPoint.size();k++)
					{
						Value point(rapidjson::kObjectType);
						point.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].keyPoint.vtKeyPoint[k].x,d.GetAllocator());
						point.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].keyPoint.vtKeyPoint[k].y,d.GetAllocator());
						keyPoint.PushBack(point,d.GetAllocator());
					}
					
				ROINode.AddMember("KeyPoint",keyPoint,d.GetAllocator());

				}//KeyPoint end

				{//KeyRect begin
					Value keyRect(rapidjson::kArrayType);
					
					Value p1(rapidjson::kObjectType);
					p1.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].keyRect.p1.x,d.GetAllocator());
					p1.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].keyRect.p1.y,d.GetAllocator());
					keyRect.PushBack(p1,d.GetAllocator());

					Value p2(rapidjson::kObjectType);
					p2.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].keyRect.p2.x,d.GetAllocator());
					p2.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].keyRect.p2.y,d.GetAllocator());
					keyRect.PushBack(p2,d.GetAllocator());

					Value p3(rapidjson::kObjectType);
					p3.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].keyRect.p3.x,d.GetAllocator());
					p3.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].keyRect.p3.y,d.GetAllocator());
					keyRect.PushBack(p3,d.GetAllocator());

					Value p4(rapidjson::kObjectType);
					p4.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].keyRect.p4.x,d.GetAllocator());
					p4.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].keyRect.p4.y,d.GetAllocator());
					keyRect.PushBack(p4,d.GetAllocator());

					ROINode.AddMember("KeyRect",keyRect,d.GetAllocator());

				}//KeyRect end

				{//Lane begin

					Value laneArray(rapidjson::kArrayType);
					for(size_t n=0; n<channelRestoreConf.analysisConfig.vtROI[i].vtLane.size();n++)
					{
						Value lane(rapidjson::kObjectType);

						{//area begin
							Value area(rapidjson::kArrayType);

							for(size_t m=0; m<channelRestoreConf.analysisConfig.vtROI[i].vtLane[n].area.vtPoint.size();m++)
							{
								Value point(rapidjson::kObjectType);
								point.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].vtLane[n].area.vtPoint[m].x,d.GetAllocator());
								point.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].vtLane[n].area.vtPoint[m].y,d.GetAllocator());
								area.PushBack(point,d.GetAllocator());
							}

							lane.AddMember("Area",area,d.GetAllocator());
						}//area end


						lane.AddMember("IsElane",channelRestoreConf.analysisConfig.vtROI[i].vtLane[n].isElane,d.GetAllocator());
						lane.AddMember("LaneID",channelRestoreConf.analysisConfig.vtROI[i].vtLane[n].iLaneID,d.GetAllocator());
						lane.AddMember("MaxSpeed",channelRestoreConf.analysisConfig.vtROI[i].vtLane[n].fMaxSpeed,d.GetAllocator());
						lane.AddMember("MinSpeed",channelRestoreConf.analysisConfig.vtROI[i].vtLane[n].fMinSpeed,d.GetAllocator());

						
						laneArray.PushBack(lane,d.GetAllocator());
					}

					ROINode.AddMember("Lane",laneArray,d.GetAllocator());

				}//Lane end

				{//countline begin
					Value countline(rapidjson::kArrayType);
					{
						Value pointBegin(rapidjson::kObjectType);
						pointBegin.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].countLine.pointBegin.x,d.GetAllocator());
						pointBegin.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].countLine.pointBegin.y,d.GetAllocator());

						countline.PushBack(pointBegin,d.GetAllocator());

						Value pointEnd(rapidjson::kObjectType);
						pointEnd.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].countLine.pointEnd.x,d.GetAllocator());
						pointEnd.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].countLine.pointEnd.y,d.GetAllocator());
						countline.PushBack(pointEnd,d.GetAllocator());
					}

					ROINode.AddMember("CountLine",countline,d.GetAllocator());

				}//countline end

				ROINode.AddMember("LogicID",channelRestoreConf.analysisConfig.vtROI[i].iLogicID,d.GetAllocator());
				ROINode.AddMember("ROIID",channelRestoreConf.analysisConfig.vtROI[i].iROIID,d.GetAllocator());
				
				{//SubRegion begin

					Value subRegionArray(rapidjson::kArrayType);
					for(size_t n=0; n<channelRestoreConf.analysisConfig.vtROI[i].vtSubRegin.size();n++)
					{
						Value subRegion(rapidjson::kObjectType);

						{//area begin
							Value area(rapidjson::kArrayType);

							for(size_t m=0; m<channelRestoreConf.analysisConfig.vtROI[i].vtSubRegin[n].area.vtPoint.size();m++)
							{
								Value point(rapidjson::kObjectType);
								point.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].vtSubRegin[n].area.vtPoint[m].x,d.GetAllocator());
								point.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].vtSubRegin[n].area.vtPoint[m].y,d.GetAllocator());
								area.PushBack(point,d.GetAllocator());
							}

							subRegion.AddMember("Area",area,d.GetAllocator());
						}//area end


						subRegion.AddMember("RegionID",channelRestoreConf.analysisConfig.vtROI[i].vtSubRegin[n].iRegionID,d.GetAllocator());
						subRegion.AddMember("RegionType",channelRestoreConf.analysisConfig.vtROI[i].vtSubRegin[n].regionType,d.GetAllocator());
						
						subRegionArray.PushBack(subRegion,d.GetAllocator());
					}

					ROINode.AddMember("SubRegion",subRegionArray,d.GetAllocator());



				}//SubRegion end


				{//ChangeLine begin

					Value changeLine(rapidjson::kArrayType);

					for(size_t n=0; n<channelRestoreConf.analysisConfig.vtROI[i].vtChangeLine.size();n++)
					{
						Value changeLineNode(rapidjson::kObjectType);

						changeLineNode.AddMember("LineID",channelRestoreConf.analysisConfig.vtROI[i].vtChangeLine[n].iLineID,d.GetAllocator());

						{//area begin
							Value area(rapidjson::kArrayType);

							for(size_t m=0; m<channelRestoreConf.analysisConfig.vtROI[i].vtChangeLine[n].vtLine.size();m++)
							{
								Value point(rapidjson::kObjectType);
								point.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].vtChangeLine[n].vtLine[m].x,d.GetAllocator());
								point.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].vtChangeLine[n].vtLine[m].y,d.GetAllocator());
								area.PushBack(point,d.GetAllocator());
							}

							changeLineNode.AddMember("Line",area,d.GetAllocator());
						}//area end


						changeLine.PushBack(changeLineNode,d.GetAllocator());
					}

					ROINode.AddMember("ChangeLine",changeLine,d.GetAllocator());

				}//ChangeLine end


				{//checkArea begin

					Value checkArea(rapidjson::kArrayType);
					
					for(size_t n=0; n<channelRestoreConf.analysisConfig.vtROI[i].vtCheckArea.size();n++)
					{
						Value checkAreaItemNode(rapidjson::kObjectType);

						{//area begin
							Value area(rapidjson::kArrayType);

							for(size_t m=0; m<channelRestoreConf.analysisConfig.vtROI[i].vtCheckArea[n].area.vtPoint.size();m++)
							{
								Value point(rapidjson::kObjectType);
								point.AddMember("x",channelRestoreConf.analysisConfig.vtROI[i].vtCheckArea[n].area.vtPoint[m].x,d.GetAllocator());
								point.AddMember("y",channelRestoreConf.analysisConfig.vtROI[i].vtCheckArea[n].area.vtPoint[m].y,d.GetAllocator());
								area.PushBack(point,d.GetAllocator());
							}

							checkAreaItemNode.AddMember("Area",area,d.GetAllocator());
						}//area end

						checkAreaItemNode.AddMember("EventProperty",channelRestoreConf.analysisConfig.vtROI[i].vtCheckArea[n].iEventProperty,d.GetAllocator());

						checkAreaItemNode.AddMember("AreaId",channelRestoreConf.analysisConfig.vtROI[i].vtCheckArea[n].iAreaId,d.GetAllocator());

						checkAreaItemNode.AddMember("AreaTypeId",channelRestoreConf.analysisConfig.vtROI[i].vtCheckArea[n].iAreaTypeId,d.GetAllocator());

						checkAreaItemNode.AddMember("AreaParam",rapidjson::StringRef(channelRestoreConf.analysisConfig.vtROI[i].vtCheckArea[n].strAreaParam.c_str()),d.GetAllocator());

						checkArea.PushBack(checkAreaItemNode,d.GetAllocator());
					}

					ROINode.AddMember("CheckArea",checkArea,d.GetAllocator());

				}//checkArea end


				ROINodeArray.PushBack(ROINode,d.GetAllocator());	
			}
			analysis.AddMember("ROI",ROINodeArray, d.GetAllocator());

		}//ROI end


		data.AddMember("Analysis",analysis, d.GetAllocator());

	}//分析配置结束
	
#endif

	d.AddMember("data",data, d.GetAllocator());

    StringBuffer buffer;
    Writer<StringBuffer> writer(buffer);
	writer.SetMaxDecimalPlaces(3);
    d.Accept(writer);

	if (strIp == "" && uPort == 0)
	{
		return udpSendTo(ip.c_str(), port, buffer.GetString());
	}
	else
	{
		return udpSendTo(strIp.c_str(), uPort, buffer.GetString());
	}
}

int WtoeUDPLib::requestInit(int index)
{
    Document d;
	d.SetObject();
	d.AddMember("sn",EventTime::Tick(), d.GetAllocator());
	d.AddMember("type","RequestInit", d.GetAllocator());
	d.AddMember("index", index, d.GetAllocator());
	d.AddMember("from","IVA", d.GetAllocator());

    StringBuffer buffer;
    Writer<StringBuffer> writer(buffer);
    d.Accept(writer);

	return udpSendTo(ip.c_str(), port, buffer.GetString());
}

int WtoeUDPLib::eventOccured(EventOccurInfo & eventOccurInfo)
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "EventOccur", d.GetAllocator());
	d.AddMember("from", "IVA", d.GetAllocator());

	Value data(rapidjson::kObjectType);
	data.AddMember("EventId", rapidjson::StringRef(eventOccurInfo.eventInfo.strEventId.c_str()), d.GetAllocator());
	data.AddMember("ChannelId", eventOccurInfo.iChannelID, d.GetAllocator());
	data.AddMember("RoiId",eventOccurInfo.eventInfo.iRoiId, d.GetAllocator());
	data.AddMember("LaneId", eventOccurInfo.eventInfo.iLaneId, d.GetAllocator());
	data.AddMember("CheckAreaId", eventOccurInfo.eventInfo.iCheckAreaId, d.GetAllocator());
	data.AddMember("EventTypeId",eventOccurInfo.eventInfo.iEventTypeId, d.GetAllocator());
	data.AddMember("OccurTime",rapidjson::StringRef(eventOccurInfo.eventInfo.occurTime.c_str()), d.GetAllocator());
	data.AddMember("EventVideo",rapidjson::StringRef(eventOccurInfo.eventInfo.eventVideo.c_str()), d.GetAllocator());
	data.AddMember("EventImg",rapidjson::StringRef(eventOccurInfo.eventInfo.eventImg.c_str()), d.GetAllocator());
	data.AddMember("IsAlarm",eventOccurInfo.eventInfo.isAlarm, d.GetAllocator());
	data.AddMember("AreaTypeId",eventOccurInfo.eventInfo.iAreaTypeId, d.GetAllocator());
	
	Value objectArea(rapidjson::kArrayType);
	for (size_t i = 0; i< eventOccurInfo.eventInfo.objectArea.vtPoint.size(); i++)
	{
		Value point(rapidjson::kObjectType);
		point.AddMember("x",eventOccurInfo.eventInfo.objectArea.vtPoint[i].x, d.GetAllocator());
		point.AddMember("y",eventOccurInfo.eventInfo.objectArea.vtPoint[i].y, d.GetAllocator());
		objectArea.PushBack(point, d.GetAllocator());
	}
	data.AddMember("objectArea", objectArea, d.GetAllocator());
	
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return udpSendTo(ip.c_str(), port, buffer.GetString());
}

int WtoeUDPLib::requestDayNight(RequestDayNightInfo &requestDayNightInfo)
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "RequestDayNight", d.GetAllocator());
	d.AddMember("from", "IVA", d.GetAllocator());

	Value data(rapidjson::kObjectType);
	data.AddMember("ChannelId", requestDayNightInfo.iChannelID, d.GetAllocator());
	data.AddMember("VideoId",requestDayNightInfo.iVideo, d.GetAllocator());
	data.AddMember("IsDay",requestDayNightInfo.bIsDay, d.GetAllocator());
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return udpSendTo(ip.c_str(), port, buffer.GetString());
}


int WtoeUDPLib::request28181Video(int iVideoID, std::string strDestAddr, std::string strPlatIP, int iPort, int iReturnPort )
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "Request28181Video", d.GetAllocator());
	d.AddMember("from", "FVM", d.GetAllocator());

	Value data(rapidjson::kObjectType);
	data.AddMember("VideoID", iVideoID, d.GetAllocator());
	data.AddMember("DestAddr", rapidjson::StringRef(strDestAddr.c_str()), d.GetAllocator());
	data.AddMember("ReturnPort", iReturnPort, d.GetAllocator());
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	//std::cout <<"send data:"<< buffer.GetString() << std::endl;
	return udpSendTo(strPlatIP, iPort, buffer.GetString());
}

int WtoeUDPLib::request28181VideoResult(int iVideoID, std::string strDestAddr, int iResult, std::string strPlatIP, int iPort)
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "Request28181Video", d.GetAllocator());
	d.AddMember("from", "FVM", d.GetAllocator());

	Value data(rapidjson::kObjectType);
	data.AddMember("VideoID", iVideoID, d.GetAllocator());
	data.AddMember("DestAddr", rapidjson::StringRef(strDestAddr.c_str()), d.GetAllocator());
	data.AddMember("Result", iResult, d.GetAllocator());
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return udpSendTo(strPlatIP, iPort, buffer.GetString());
}

int WtoeUDPLib::stop28181Video(int iVideoID, std::string strDestAddr, std::string strPlatIP, int iPort, int iReturnPort)
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "Stop28181Video", d.GetAllocator());
	d.AddMember("from", "FVM", d.GetAllocator());

	Value data(rapidjson::kObjectType);
	data.AddMember("VideoID", iVideoID, d.GetAllocator());
	data.AddMember("DestAddr", rapidjson::StringRef(strDestAddr.c_str()), d.GetAllocator());
	data.AddMember("ReturnPort", iReturnPort, d.GetAllocator());
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return udpSendTo(strPlatIP, iPort, buffer.GetString());
}

int WtoeUDPLib::stop28181VideoResult(int iVideoID, std::string strDestAddr, int iResult, std::string strPlatIP, int iPort)
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "Stop28181Video", d.GetAllocator());
	d.AddMember("from", "FVM", d.GetAllocator());

	Value data(rapidjson::kObjectType);
	data.AddMember("VideoID", iVideoID, d.GetAllocator());
	data.AddMember("DestAddr", rapidjson::StringRef(strDestAddr.c_str()), d.GetAllocator());
	data.AddMember("Result", iResult, d.GetAllocator());
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return udpSendTo(strPlatIP, iPort, buffer.GetString());
}

int WtoeUDPLib::ptz28181Oper(int iVideoID, int iPresetID, std::string strPlatIP, int iPort)
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "28181PtzOper", d.GetAllocator());
	d.AddMember("from", "FVM", d.GetAllocator());

	Value data(rapidjson::kObjectType);
	data.AddMember("VideoID", iVideoID, d.GetAllocator());
	data.AddMember("PresetID", iPresetID, d.GetAllocator());
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return udpSendTo(strPlatIP, iPort, buffer.GetString());
}


int WtoeUDPLib::retRequestVideo( RequestVideoRet ret, std::string strIp /*= ""*/, unsigned short uPort /*= 0*/ )
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "RequestVideoRet", d.GetAllocator());
	d.AddMember("from", "MSM", d.GetAllocator());

	Value data(rapidjson::kObjectType);

	data.AddMember("iRemoteId", ret.iRemoteId, d.GetAllocator());
	data.AddMember("iVideoId", ret.iVideoId, d.GetAllocator());
	data.AddMember("iResult", ret.iResult, d.GetAllocator());
	data.AddMember("destAddr", rapidjson::StringRef(ret.destAddr.c_str()), d.GetAllocator());
	data.AddMember("transferType", rapidjson::StringRef(ret.transferType.c_str()), d.GetAllocator());
	data.AddMember("srcAddr", rapidjson::StringRef(ret.srcAddr.c_str()), d.GetAllocator());
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return udpSendTo(strIp, uPort, buffer.GetString());
}


int WtoeUDPLib::retRequestStopVideo( RequestStopVideoRet ret, std::string strIp /*= ""*/, unsigned short uPort /*= 0*/ )
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "StopVideoRet", d.GetAllocator());
	d.AddMember("from", "MSM", d.GetAllocator());

	Value data(rapidjson::kObjectType);

	data.AddMember("iRemoteId", ret.iRemoteId, d.GetAllocator());
	data.AddMember("iVideoId", ret.iVideoId, d.GetAllocator());
	data.AddMember("iResult", ret.iResult, d.GetAllocator());
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return udpSendTo(strIp, uPort, buffer.GetString());
}


int WtoeUDPLib::retRequestPtzOper( RequestPtzOperRet ret, std::string strIp /*= ""*/, unsigned short uPort /*= 0*/ )
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "PtzOperRet", d.GetAllocator());
	d.AddMember("from", "MSM", d.GetAllocator());

	Value data(rapidjson::kObjectType);

	data.AddMember("iRemoteId", ret.iRemoteId, d.GetAllocator());
	data.AddMember("iVideoId", ret.iVideoId, d.GetAllocator());
	data.AddMember("iResult", ret.iResult, d.GetAllocator());
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return udpSendTo(strIp, uPort, buffer.GetString());
}


int WtoeUDPLib::sendRemoteChange( int remoteId, std::string strIp /*= ""*/, unsigned short uPort /*= 0 */ )
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "RemoteChanged", d.GetAllocator());
	d.AddMember("from", "MSM", d.GetAllocator());

	Value data(rapidjson::kObjectType);

	data.AddMember("iRemoteId", remoteId, d.GetAllocator());
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return udpSendTo(strIp, uPort, buffer.GetString());
}


int WtoeUDPLib::postRemoteStatus( int remoteId, int status, std::string strIp /*= ""*/, unsigned short uPort /*= 0 */ )
{
	Document d;
	d.SetObject();
	d.AddMember("sn", EventTime::Tick(), d.GetAllocator());
	d.AddMember("type", "RemoteStatus", d.GetAllocator());
	d.AddMember("from", "MSM", d.GetAllocator());

	Value data(rapidjson::kObjectType);

	data.AddMember("iRemoteId", remoteId, d.GetAllocator());
	data.AddMember("iStatus", status, d.GetAllocator());
	d.AddMember("data", data, d.GetAllocator());

	StringBuffer buffer;
	Writer<StringBuffer> writer(buffer);
	d.Accept(writer);

	return udpSendTo(strIp, uPort, buffer.GetString());
}

void WtoeUDPLib::RegisterRequestInit(CBHandleRequestInit cb, void*pUserData)
{
	pMessageHandle->RegisterRequestInit(cb,pUserData);
}

void WtoeUDPLib::RegisterEventOccured(CBHandleEventOccured cb,void *pUserData)
{
	pMessageHandle->RegisterEventOccured(cb,pUserData);
}
void WtoeUDPLib::RegisterRequestDayNight(CBHandleRequestDayNight cb,void *pUserData)
{
	pMessageHandle->RegisterRequestDayNight(cb,pUserData);
}

void WtoeUDPLib::RegisterPauseChannel(CBHandlePauseChannel cb, void*pUserData)
{
	pMessageHandle->RegisterPauseChannel(cb,pUserData);
}

void WtoeUDPLib::RegisterDetectParam(CBHandleDetectParam cb, void* pUserData)
{
	pMessageHandle->RegisterDetectParam(cb, pUserData);
}

void WtoeUDPLib::RegisterRestoreChannel(CBHandleChannelRestore cb, void*pUserData)
{
	pMessageHandle->RegisterRestoreChannel(cb,pUserData);
}

void WtoeUDPLib::RegisterBasicChannel(CBHandleconfigBasicChannel cb, void*pUserData)
{
	pMessageHandle->RegisterBasicChannel(cb,pUserData);
}

void WtoeUDPLib::RegisterResetChannel(CBHandleResetChannel cb, void*pUserData)
{
	pMessageHandle->RegisterResetChannel(cb,pUserData);
}

void WtoeUDPLib::RegisterConfigSystem(CBHandleConfigSystem cb, void*pUserData)
{
	pMessageHandle->RegisterConfigSystem(cb,pUserData);
}

void WtoeUDPLib::RegisterAlgorithmParam(CBHandleAlgorithmParam cb, void* pUserData)
{
	pMessageHandle->RegisterAlgorithmParam(cb, pUserData);
}

void WtoeUDPLib::RegisterHandleFVMChanged(CBHandleFVMChanged cb, void*pUserData)
{
	pMessageHandle->RegisterHandleFVMChanged(cb,pUserData);
}

void WtoeUDPLib::RegisterHandleChannelChanged(CBHandleChannelChanged cb, void*pUserData)
{
	pMessageHandle->RegisterHandleChannelChanged(cb,pUserData);
}
void WtoeUDPLib::RegisterHandleRTSPChanged(CBHandleRTSPChanged cb, void*pUserData)
{
	pMessageHandle->RegisterHandleRTSPChanged(cb,pUserData);
}

void WtoeUDPLib::RegisterHandleRequestTempVideo(CBHandleRequestTempVideo cb, void*pUserData)
{
	pMessageHandle->RegisterHandleRequestTempVideo(cb,pUserData);
}

void WtoeUDPLib::RegisterHandleRequestSetVideo(CBHandleRequestSetVideo cb, void *pUserData)
{
	pMessageHandle->RegisterHandleRequestSetVideo(cb,pUserData);
}

void WtoeUDPLib::RegisterHandleVideoResume(CBHandleVideoResume cb, void*pUserData)
{
	pMessageHandle->RegisterHandleVideoResume(cb,pUserData);
}

void WtoeUDPLib::RegisterHandleVideoPause(CBHandleVideoPause cb, void*pUserData)
{
	pMessageHandle->RegisterHandleVideoPause(cb,pUserData);
}

void WtoeUDPLib::RegisterHandleMonitorChanged(CBHandleMonitorChanged cb, void*pUserData)
{
	pMessageHandle->RegisterHandleMonitorChanged(cb,pUserData);
}

void WtoeUDPLib::RegisterHandleRequest28181Video(CBHandleRequest28181Video cb, void*pUserData)
{
	pMessageHandle->RegisterHandleRequest28181Video(cb,pUserData);
}

void WtoeUDPLib::RegisterHandleRequest28181VideoResult(CBHandleRequest28181VideoResult cb, void*pUserData)
{
	pMessageHandle->RegisterHandleRequest28181VideoResult(cb,pUserData);
}

void WtoeUDPLib::RegisterHandleStop28181Video(CBHandleStop28181Video cb, void*pUserData)
{
	pMessageHandle->RegisterHandleStop28181Video(cb,pUserData);
}

void WtoeUDPLib::RegisterHandleStop28181VideoResult(CBHandleStop28181VideoResult cb, void*pUserData)
{
	pMessageHandle->RegisterHandleStop28181VideoResult(cb,pUserData);
}

void WtoeUDPLib::RegisterHandlePtz28181Oper(CBHandlePtz28181Oper cb, void*pUserData)
{
	pMessageHandle->RegisterHandlePtz28181Oper(cb,pUserData);
}

void WtoeUDPLib::RegisterMsgReceived(CBHandleMsgReceived cb, void*pUserData)
{
	pMessageHandle->RegisterMsgReceived(cb,pUserData);
}

void WtoeUDPLib::RegisterRequestIvaVideo(CBHandleRequestIvaVideo cb,void*pUserData)
{
	pMessageHandle->RegisterRequestIvaVideo(cb,pUserData);
}
void WtoeUDPLib::RegisterStopIvaVideo(CBHandleStopIvaVideo cb, void* pUserData)
{
	pMessageHandle->RegisterStopIvaVideo(cb, pUserData);
}

void WtoeUDPLib::RegisterHandleRequestVideo( CBHandleRequestVideo cb, void* pUserData )
{
pMessageHandle->RegisterHandleRequestVideo(cb, pUserData);
}

void WtoeUDPLib::RegisterHandleRequestStopVideo( CBHandleRequestStopVideo cb, void* pUserData )
{
pMessageHandle->RegisterHandleRequestStopVideo(cb, pUserData);
}

void WtoeUDPLib::RegisterHandleRequestPtzOper( CBHandleRequestPtzOper cb, void* pUserData )
{
pMessageHandle->RegisterHandleRequestPtzOper(cb, pUserData);
}

void WtoeUDPLib::RegisterHandleRemoteChanged( CBHandleRemoteChanged cb, void* pUserData )
{
pMessageHandle->RegisterHandleRemoteChanged(cb, pUserData);
}
