#include <wtoe/BasicHelper/VcWarningOff.hpp>
#include <wtoe/PackageManager/PackageManagerExp.hpp>
#include <wtoe/JavaScriptEngine/JavaScriptEngineExp.hpp>
#include "DataMgr/include/DataMgrPkg.hpp"
#include "DataMgr/src/DataMgr.hpp"

#include "include/file_info.hpp"
#include "data_center.h"
#include "msg_center.h"
#include "ailog.h"

DEFINE_DEBUG_SUPPORT( "" );

namespace gum
{

bool DATAMGR_PRIVATE javaScriptRegisterDataMgr( JSContext *jsCtx, JSObject *jsObj );

CDataMgr::CDataMgr()
{    
}

CDataMgr::~CDataMgr()
{
}

bool CDataMgr::startupImpl()
{
	boost::shared_ptr<wtoe::IJavaScriptVirtualMachine> vm;
	if ( !wtoe::getMainApp()->getServiceInstance( WTOE_SNAME_JavaScriptEngine_JavaScriptVirtualMachine, vm ) )
		return false;
	if (!vm->registerJavaScriptObject(javaScriptRegisterDataMgr))
		return false;

	return true;
}

bool CDataMgr::shutdownImpl()
{
	fini();
    return true;
}

bool CDataMgr::init( const std::string& ip, const std::string& db )
{
	if (!DATA_CENTER.init( ip, db ))
		return false;
	if (!MSG_CENTER.init())
		return false;
	return true;
}

bool CDataMgr::fini()
{
	DATA_CENTER.fini();
	MSG_CENTER.fini();
	return true;
}

void CDataMgr::showVersion()
{
    int iYear = getCurTime().date().year();
    std::cout << "GUM version 1.0.0 Copyright (c) 2021 - " << iYear
        << " WELLTRANS O&E CO., CO., All Rights Reserved." << std::endl;
}

void CDataMgr::showStatus( int remoteId, const std::string& szKey ) {
    return DATA_CENTER.showStatus( remoteId, szKey );
}

} //namespace gum


