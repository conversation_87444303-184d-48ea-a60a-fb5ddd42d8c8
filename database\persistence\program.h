/**
 * @addtogroup odbDatabaseGroup
 * @brief 夏天、冬天外场时间信息
 * @{
 */
#ifndef _PROGRAM_H
#define _PROGRAM_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>

namespace db {

/**
 * @brief  夏天、冬天外场时间信息 对应数据库aimonitorV3的表wn_program_info
 */
#pragma db object table("wn_program")
class Program {
public:

    Program(const std::string& startDate, const std::string& morning ,
            const std::string& evening ,bool isDel)
        : startDate(startDate), morningTime(morning), eveningTime(evening), isDel(isDel)
    {
    }

    unsigned long getId() const {
        return id;
    }

    const std::string& getStartDate() const {
        return startDate;
    }

    void setStartDate(const std::string& data) {
        this->startDate = data;
    }

    const std::string& getMorningTime() const {
        return morningTime;
    }

    void setMorningTime(const std::string& time) {
        this->morningTime = time;
    }

    const std::string& getEveningTime() const {
        return eveningTime;
    }

    void setEveningTime(const std::string& time) {
        this->eveningTime = time;
    }

    bool getIsDel() const {
        return isDel;
    }

    void setIsDel(bool del) {
        this->isDel = del;
    }

private:

    friend class odb::access;
    Program() {}


private:

#pragma db id auto
    unsigned long id;                   //!< 表ID

#pragma db column("start_date")  type("VARCHAR(255)")
    std::string startDate;              //!< 定义夏天、冬天起始日期

#pragma db column("morning_time")  type("VARCHAR(255)")
        std::string morningTime;        //!< 定义夏天、冬天早上起始时间

#pragma db column("evening_time")  type("VARCHAR(255)")
        std::string eveningTime;         //!< 定义夏天、冬天夜晚起始时间

#pragma db column("is_del") type("INT")
    bool isDel;                         //!< 是否删除
};
}
#endif //_PROGRAM_H
/**
 * @}
 */