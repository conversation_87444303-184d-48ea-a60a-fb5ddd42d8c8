#ifndef JAVASCRIPTWRAPPER_HPP_
#define JAVASCRIPTWRAPPER_HPP_

#include "wtoe/BasicHelper/VcWarningOff.hpp"
#include "wtoe/JavaScriptEngine/JavaScriptEngineExp.hpp"
#include "DatabaseManager/include/DatabaseManagerCfg.hpp"

namespace wtoe 
{

class DATABASEMANAGER_PRIVATE CJsDatabaseManager :
	public CJavaScriptAdapter<CJsDatabaseManager>, 
                                                 public CAdapterJavaScriptize
{
	friend class CJavaScriptAdapter<CJsDatabaseManager>;
public:
	CJsDatabaseManager();
	virtual ~CJsDatabaseManager();
	static CJsDatabaseManager *createObject();

public:
	JSBool setParams( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
	JSBool execSql( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
	JSBool select( JSContext *ctx, JSO<PERSON> *obj, uintN argc, jsval *argv, jsval *rval );

protected:
	WTOE_USING_BASE_CONSTRUCTOR( CJavaScriptAdapter<CJsDatabaseManager> );
	WTOE_USING_BASE_FINALIZER( CJavaScriptAdapter<CJsDatabaseManager> );
};

}

namespace msm
{
bool javaScriptRegisterDatabaseManager( JSContext *jsCtx, JSObject *jsObj );

}

#include "wtoe/BasicHelper/VcWarningOn.hpp"

#endif // JAVASCRIPTWRAPPER_HPP_

