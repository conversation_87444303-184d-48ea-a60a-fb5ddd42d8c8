// -*- C++ -*-
//
// This file was generated by ODB, object-relational mapping (ORM)
// compiler for C++.
//

#ifndef NV_AIMONITOR31_VIEW_ODB_HXX
#define NV_AIMONITOR31_VIEW_ODB_HXX

#include <odb/version.hxx>


#include <odb/pre.hxx>

#include "nv_aimonitor31_view.h"

#include "alg_param_plan-odb.hxx"
#include "algorithm_param-odb.hxx"
#include "detection_point-odb.hxx"
#include "monitor-odb.hxx"
#include "preset-odb.hxx"
#include "preset_check_area-odb.hxx"
#include "preset_lane-odb.hxx"
#include "preset_lane_line-odb.hxx"
#include "preset_offset-odb.hxx"
#include "preset_roi-odb.hxx"
#include "program-odb.hxx"
#include "program_info-odb.hxx"
#include "video_source-odb.hxx"

#include <memory>
#include <cstddef>
#include <utility>

#include <odb/core.hxx>
#include <odb/traits.hxx>
#include <odb/callback.hxx>
#include <odb/wrapper-traits.hxx>
#include <odb/pointer-traits.hxx>
#include <odb/container-traits.hxx>
#include <odb/no-op-cache-traits.hxx>
#include <odb/result.hxx>
#include <odb/simple-object-result.hxx>
#include <odb/view-image.hxx>
#include <odb/view-result.hxx>

#include <odb/details/unused.hxx>
#include <odb/details/shared-ptr.hxx>

namespace odb
{
  // MonitorDetectPointData
  //
  template <>
  struct class_traits< ::db::MonitorDetectPointData >
  {
    static const class_kind kind = class_view;
  };

  template <>
  class access::view_traits< ::db::MonitorDetectPointData >
  {
    public:
    typedef ::db::MonitorDetectPointData view_type;
    typedef ::db::MonitorDetectPointData* pointer_type;

    static void
    callback (database&, view_type&, callback_event);
  };

  // PresetProgramData
  //
  template <>
  struct class_traits< ::db::PresetProgramData >
  {
    static const class_kind kind = class_view;
  };

  template <>
  class access::view_traits< ::db::PresetProgramData >
  {
    public:
    typedef ::db::PresetProgramData view_type;
    typedef ::db::PresetProgramData* pointer_type;

    static void
    callback (database&, view_type&, callback_event);
  };

  // PresetAreasData
  //
  template <>
  struct class_traits< ::db::PresetAreasData >
  {
    static const class_kind kind = class_view;
  };

  template <>
  class access::view_traits< ::db::PresetAreasData >
  {
    public:
    typedef ::db::PresetAreasData view_type;
    typedef ::db::PresetAreasData* pointer_type;

    static void
    callback (database&, view_type&, callback_event);
  };

  // PresetOffsetData
  //
  template <>
  struct class_traits< ::db::PresetOffsetData >
  {
    static const class_kind kind = class_view;
  };

  template <>
  class access::view_traits< ::db::PresetOffsetData >
  {
    public:
    typedef ::db::PresetOffsetData view_type;
    typedef ::db::PresetOffsetData* pointer_type;

    static void
    callback (database&, view_type&, callback_event);
  };

  // PresetRoiData
  //
  template <>
  struct class_traits< ::db::PresetRoiData >
  {
    static const class_kind kind = class_view;
  };

  template <>
  class access::view_traits< ::db::PresetRoiData >
  {
    public:
    typedef ::db::PresetRoiData view_type;
    typedef ::db::PresetRoiData* pointer_type;

    static void
    callback (database&, view_type&, callback_event);
  };

  // ROICheckAreaData
  //
  template <>
  struct class_traits< ::db::ROICheckAreaData >
  {
    static const class_kind kind = class_view;
  };

  template <>
  class access::view_traits< ::db::ROICheckAreaData >
  {
    public:
    typedef ::db::ROICheckAreaData view_type;
    typedef ::db::ROICheckAreaData* pointer_type;

    static void
    callback (database&, view_type&, callback_event);
  };

  // ROILaneData
  //
  template <>
  struct class_traits< ::db::ROILaneData >
  {
    static const class_kind kind = class_view;
  };

  template <>
  class access::view_traits< ::db::ROILaneData >
  {
    public:
    typedef ::db::ROILaneData view_type;
    typedef ::db::ROILaneData* pointer_type;

    static void
    callback (database&, view_type&, callback_event);
  };

  // LaneLineData
  //
  template <>
  struct class_traits< ::db::LaneLineData >
  {
    static const class_kind kind = class_view;
  };

  template <>
  class access::view_traits< ::db::LaneLineData >
  {
    public:
    typedef ::db::LaneLineData view_type;
    typedef ::db::LaneLineData* pointer_type;

    static void
    callback (database&, view_type&, callback_event);
  };

  // AlgoParamsData
  //
  template <>
  struct class_traits< ::db::AlgoParamsData >
  {
    static const class_kind kind = class_view;
  };

  template <>
  class access::view_traits< ::db::AlgoParamsData >
  {
    public:
    typedef ::db::AlgoParamsData view_type;
    typedef ::db::AlgoParamsData* pointer_type;

    static void
    callback (database&, view_type&, callback_event);
  };

  // DetectPointVideoSourceData
  //
  template <>
  struct class_traits< ::db::DetectPointVideoSourceData >
  {
    static const class_kind kind = class_view;
  };

  template <>
  class access::view_traits< ::db::DetectPointVideoSourceData >
  {
    public:
    typedef ::db::DetectPointVideoSourceData view_type;
    typedef ::db::DetectPointVideoSourceData* pointer_type;

    static void
    callback (database&, view_type&, callback_event);
  };
}

#include <odb/details/buffer.hxx>

#include <odb/mysql/version.hxx>
#include <odb/mysql/forward.hxx>
#include <odb/mysql/binding.hxx>
#include <odb/mysql/mysql-types.hxx>
#include <odb/mysql/query.hxx>

namespace odb
{
  // MonitorDetectPointData
  //
  template <>
  class access::view_traits_impl< ::db::MonitorDetectPointData, id_mysql >:
    public access::view_traits< ::db::MonitorDetectPointData >
  {
    public:
    struct image_type
    {
      // monitorPtr
      //
      object_traits_impl< ::db::Monitor, id_mysql >::image_type monitorPtr_value;

      // detectPointPtr
      //
      object_traits_impl< ::db::DetectionPoint, id_mysql >::image_type detectPointPtr_value;

      std::size_t version;
    };

    typedef mysql::view_statements<view_type> statements_type;

    typedef mysql::query_base query_base_type;
    struct query_columns;

    static const bool versioned = false;

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&);

    static void
    init (view_type&,
          const image_type&,
          database*);

    static const std::size_t column_count = 19UL;

    static query_base_type
    query_statement (const query_base_type&);

    static result<view_type>
    query (database&, const query_base_type&);
  };

  template <>
  class access::view_traits_impl< ::db::MonitorDetectPointData, id_common >:
    public access::view_traits_impl< ::db::MonitorDetectPointData, id_mysql >
  {
  };

  // PresetProgramData
  //
  template <>
  class access::view_traits_impl< ::db::PresetProgramData, id_mysql >:
    public access::view_traits< ::db::PresetProgramData >
  {
    public:
    struct image_type
    {
      // programInfoPtr
      //
      object_traits_impl< ::db::ProgramInfo, id_mysql >::image_type programInfoPtr_value;

      // programPtr
      //
      object_traits_impl< ::db::Program, id_mysql >::image_type programPtr_value;

      std::size_t version;
    };

    typedef mysql::view_statements<view_type> statements_type;

    typedef mysql::query_base query_base_type;
    struct query_columns;

    static const bool versioned = false;

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&);

    static void
    init (view_type&,
          const image_type&,
          database*);

    static const std::size_t column_count = 11UL;

    static query_base_type
    query_statement (const query_base_type&);

    static result<view_type>
    query (database&, const query_base_type&);
  };

  template <>
  class access::view_traits_impl< ::db::PresetProgramData, id_common >:
    public access::view_traits_impl< ::db::PresetProgramData, id_mysql >
  {
  };

  // PresetAreasData
  //
  template <>
  class access::view_traits_impl< ::db::PresetAreasData, id_mysql >:
    public access::view_traits< ::db::PresetAreasData >
  {
    public:
    struct image_type
    {
      // presetOffsetPtr
      //
      object_traits_impl< ::db::PresetOffset, id_mysql >::image_type presetOffsetPtr_value;

      // presetRoiPtr
      //
      object_traits_impl< ::db::PresetRoi, id_mysql >::image_type presetRoiPtr_value;

      std::size_t version;
    };

    typedef mysql::view_statements<view_type> statements_type;

    typedef mysql::query_base query_base_type;
    struct query_columns;

    static const bool versioned = false;

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&);

    static void
    init (view_type&,
          const image_type&,
          database*);

    static const std::size_t column_count = 21UL;

    static query_base_type
    query_statement (const query_base_type&);

    static result<view_type>
    query (database&, const query_base_type&);
  };

  template <>
  class access::view_traits_impl< ::db::PresetAreasData, id_common >:
    public access::view_traits_impl< ::db::PresetAreasData, id_mysql >
  {
  };

  // PresetOffsetData
  //
  template <>
  class access::view_traits_impl< ::db::PresetOffsetData, id_mysql >:
    public access::view_traits< ::db::PresetOffsetData >
  {
    public:
    struct image_type
    {
      // presetOffsetPtr
      //
      object_traits_impl< ::db::PresetOffset, id_mysql >::image_type presetOffsetPtr_value;

      std::size_t version;
    };

    typedef mysql::view_statements<view_type> statements_type;

    typedef mysql::query_base query_base_type;
    struct query_columns;

    static const bool versioned = false;

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&);

    static void
    init (view_type&,
          const image_type&,
          database*);

    static const std::size_t column_count = 5UL;

    static query_base_type
    query_statement (const query_base_type&);

    static result<view_type>
    query (database&, const query_base_type&);
  };

  template <>
  class access::view_traits_impl< ::db::PresetOffsetData, id_common >:
    public access::view_traits_impl< ::db::PresetOffsetData, id_mysql >
  {
  };

  // PresetRoiData
  //
  template <>
  class access::view_traits_impl< ::db::PresetRoiData, id_mysql >:
    public access::view_traits< ::db::PresetRoiData >
  {
    public:
    struct image_type
    {
      // presetRoiPtr
      //
      object_traits_impl< ::db::PresetRoi, id_mysql >::image_type presetRoiPtr_value;

      std::size_t version;
    };

    typedef mysql::view_statements<view_type> statements_type;

    typedef mysql::query_base query_base_type;
    struct query_columns;

    static const bool versioned = false;

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&);

    static void
    init (view_type&,
          const image_type&,
          database*);

    static const std::size_t column_count = 16UL;

    static query_base_type
    query_statement (const query_base_type&);

    static result<view_type>
    query (database&, const query_base_type&);
  };

  template <>
  class access::view_traits_impl< ::db::PresetRoiData, id_common >:
    public access::view_traits_impl< ::db::PresetRoiData, id_mysql >
  {
  };

  // ROICheckAreaData
  //
  template <>
  class access::view_traits_impl< ::db::ROICheckAreaData, id_mysql >:
    public access::view_traits< ::db::ROICheckAreaData >
  {
    public:
    struct image_type
    {
      // presetCheckAreaPtr
      //
      object_traits_impl< ::db::PresetCheckArea, id_mysql >::image_type presetCheckAreaPtr_value;

      std::size_t version;
    };

    typedef mysql::view_statements<view_type> statements_type;

    typedef mysql::query_base query_base_type;
    struct query_columns;

    static const bool versioned = false;

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&);

    static void
    init (view_type&,
          const image_type&,
          database*);

    static const std::size_t column_count = 8UL;

    static query_base_type
    query_statement (const query_base_type&);

    static result<view_type>
    query (database&, const query_base_type&);
  };

  template <>
  class access::view_traits_impl< ::db::ROICheckAreaData, id_common >:
    public access::view_traits_impl< ::db::ROICheckAreaData, id_mysql >
  {
  };

  // ROILaneData
  //
  template <>
  class access::view_traits_impl< ::db::ROILaneData, id_mysql >:
    public access::view_traits< ::db::ROILaneData >
  {
    public:
    struct image_type
    {
      // presetLanePtr
      //
      object_traits_impl< ::db::PresetLane, id_mysql >::image_type presetLanePtr_value;

      std::size_t version;
    };

    typedef mysql::view_statements<view_type> statements_type;

    typedef mysql::query_base query_base_type;
    struct query_columns;

    static const bool versioned = false;

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&);

    static void
    init (view_type&,
          const image_type&,
          database*);

    static const std::size_t column_count = 13UL;

    static query_base_type
    query_statement (const query_base_type&);

    static result<view_type>
    query (database&, const query_base_type&);
  };

  template <>
  class access::view_traits_impl< ::db::ROILaneData, id_common >:
    public access::view_traits_impl< ::db::ROILaneData, id_mysql >
  {
  };

  // LaneLineData
  //
  template <>
  class access::view_traits_impl< ::db::LaneLineData, id_mysql >:
    public access::view_traits< ::db::LaneLineData >
  {
    public:
    struct image_type
    {
      // presetLaneLinePtr
      //
      object_traits_impl< ::db::PresetLaneLine, id_mysql >::image_type presetLaneLinePtr_value;

      std::size_t version;
    };

    typedef mysql::view_statements<view_type> statements_type;

    typedef mysql::query_base query_base_type;
    struct query_columns;

    static const bool versioned = false;

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&);

    static void
    init (view_type&,
          const image_type&,
          database*);

    static const std::size_t column_count = 18UL;

    static query_base_type
    query_statement (const query_base_type&);

    static result<view_type>
    query (database&, const query_base_type&);
  };

  template <>
  class access::view_traits_impl< ::db::LaneLineData, id_common >:
    public access::view_traits_impl< ::db::LaneLineData, id_mysql >
  {
  };

  // AlgoParamsData
  //
  template <>
  class access::view_traits_impl< ::db::AlgoParamsData, id_mysql >:
    public access::view_traits< ::db::AlgoParamsData >
  {
    public:
    struct image_type
    {
      // planId
      //
      unsigned long long planId_value;
      my_bool planId_null;

      // paramKey
      //
      details::buffer paramKey_value;
      unsigned long paramKey_size;
      my_bool paramKey_null;

      // paramValue
      //
      details::buffer paramValue_value;
      unsigned long paramValue_size;
      my_bool paramValue_null;

      std::size_t version;
    };

    typedef mysql::view_statements<view_type> statements_type;

    typedef mysql::query_base query_base_type;
    struct query_columns;

    static const bool versioned = false;

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&);

    static void
    init (view_type&,
          const image_type&,
          database*);

    static const std::size_t column_count = 3UL;

    static query_base_type
    query_statement (const query_base_type&);

    static result<view_type>
    query (database&, const query_base_type&);
  };

  template <>
  class access::view_traits_impl< ::db::AlgoParamsData, id_common >:
    public access::view_traits_impl< ::db::AlgoParamsData, id_mysql >
  {
  };

  // DetectPointVideoSourceData
  //
  template <>
  class access::view_traits_impl< ::db::DetectPointVideoSourceData, id_mysql >:
    public access::view_traits< ::db::DetectPointVideoSourceData >
  {
    public:
    struct image_type
    {
      // videoSourcePtr
      //
      object_traits_impl< ::db::VideoSource, id_mysql >::image_type videoSourcePtr_value;

      // detectPointPtr
      //
      object_traits_impl< ::db::DetectionPoint, id_mysql >::image_type detectPointPtr_value;

      std::size_t version;
    };

    typedef mysql::view_statements<view_type> statements_type;

    typedef mysql::query_base query_base_type;
    struct query_columns;

    static const bool versioned = false;

    static bool
    grow (image_type&,
          my_bool*);

    static void
    bind (MYSQL_BIND*,
          image_type&);

    static void
    init (view_type&,
          const image_type&,
          database*);

    static const std::size_t column_count = 23UL;

    static query_base_type
    query_statement (const query_base_type&);

    static result<view_type>
    query (database&, const query_base_type&);
  };

  template <>
  class access::view_traits_impl< ::db::DetectPointVideoSourceData, id_common >:
    public access::view_traits_impl< ::db::DetectPointVideoSourceData, id_mysql >
  {
  };

  // MonitorDetectPointData
  //
  struct access::view_traits_impl< ::db::MonitorDetectPointData, id_mysql >::query_columns
  {
    // Monitor
    //
    typedef
    odb::pointer_query_columns<
      ::db::Monitor,
      id_mysql,
      odb::access::object_traits_impl< ::db::Monitor, id_mysql > >
    Monitor;

    // DetectionPoint
    //
    typedef
    odb::pointer_query_columns<
      ::db::DetectionPoint,
      id_mysql,
      odb::access::object_traits_impl< ::db::DetectionPoint, id_mysql > >
    DetectionPoint;
  };

  // PresetProgramData
  //
  struct access::view_traits_impl< ::db::PresetProgramData, id_mysql >::query_columns
  {
    // ProgramInfo
    //
    typedef
    odb::pointer_query_columns<
      ::db::ProgramInfo,
      id_mysql,
      odb::access::object_traits_impl< ::db::ProgramInfo, id_mysql > >
    ProgramInfo;

    // Program
    //
    typedef
    odb::pointer_query_columns<
      ::db::Program,
      id_mysql,
      odb::access::object_traits_impl< ::db::Program, id_mysql > >
    Program;

    // Preset
    //
    typedef
    odb::pointer_query_columns<
      ::db::Preset,
      id_mysql,
      odb::access::object_traits_impl< ::db::Preset, id_mysql > >
    Preset;
  };

  // PresetAreasData
  //
  struct access::view_traits_impl< ::db::PresetAreasData, id_mysql >::query_columns
  {
    // Preset
    //
    typedef
    odb::pointer_query_columns<
      ::db::Preset,
      id_mysql,
      odb::access::object_traits_impl< ::db::Preset, id_mysql > >
    Preset;

    // PresetOffset
    //
    typedef
    odb::pointer_query_columns<
      ::db::PresetOffset,
      id_mysql,
      odb::access::object_traits_impl< ::db::PresetOffset, id_mysql > >
    PresetOffset;

    // PresetRoi
    //
    typedef
    odb::pointer_query_columns<
      ::db::PresetRoi,
      id_mysql,
      odb::access::object_traits_impl< ::db::PresetRoi, id_mysql > >
    PresetRoi;
  };

  // PresetOffsetData
  //
  struct access::view_traits_impl< ::db::PresetOffsetData, id_mysql >::query_columns
  {
    // Preset
    //
    typedef
    odb::pointer_query_columns<
      ::db::Preset,
      id_mysql,
      odb::access::object_traits_impl< ::db::Preset, id_mysql > >
    Preset;

    // PresetOffset
    //
    typedef
    odb::pointer_query_columns<
      ::db::PresetOffset,
      id_mysql,
      odb::access::object_traits_impl< ::db::PresetOffset, id_mysql > >
    PresetOffset;
  };

  // PresetRoiData
  //
  struct access::view_traits_impl< ::db::PresetRoiData, id_mysql >::query_columns
  {
    // Preset
    //
    typedef
    odb::pointer_query_columns<
      ::db::Preset,
      id_mysql,
      odb::access::object_traits_impl< ::db::Preset, id_mysql > >
    Preset;

    // PresetRoi
    //
    typedef
    odb::pointer_query_columns<
      ::db::PresetRoi,
      id_mysql,
      odb::access::object_traits_impl< ::db::PresetRoi, id_mysql > >
    PresetRoi;
  };

  // ROICheckAreaData
  //
  struct access::view_traits_impl< ::db::ROICheckAreaData, id_mysql >::query_columns
  {
    // PresetRoi
    //
    typedef
    odb::pointer_query_columns<
      ::db::PresetRoi,
      id_mysql,
      odb::access::object_traits_impl< ::db::PresetRoi, id_mysql > >
    PresetRoi;

    // PresetCheckArea
    //
    typedef
    odb::pointer_query_columns<
      ::db::PresetCheckArea,
      id_mysql,
      odb::access::object_traits_impl< ::db::PresetCheckArea, id_mysql > >
    PresetCheckArea;
  };

  // ROILaneData
  //
  struct access::view_traits_impl< ::db::ROILaneData, id_mysql >::query_columns
  {
    // PresetRoi
    //
    typedef
    odb::pointer_query_columns<
      ::db::PresetRoi,
      id_mysql,
      odb::access::object_traits_impl< ::db::PresetRoi, id_mysql > >
    PresetRoi;

    // PresetLane
    //
    typedef
    odb::pointer_query_columns<
      ::db::PresetLane,
      id_mysql,
      odb::access::object_traits_impl< ::db::PresetLane, id_mysql > >
    PresetLane;
  };

  // LaneLineData
  //
  struct access::view_traits_impl< ::db::LaneLineData, id_mysql >::query_columns
  {
    // Preset
    //
    typedef
    odb::pointer_query_columns<
      ::db::Preset,
      id_mysql,
      odb::access::object_traits_impl< ::db::Preset, id_mysql > >
    Preset;

    // PresetLaneLine
    //
    typedef
    odb::pointer_query_columns<
      ::db::PresetLaneLine,
      id_mysql,
      odb::access::object_traits_impl< ::db::PresetLaneLine, id_mysql > >
    PresetLaneLine;
  };

  // AlgoParamsData
  //
  struct access::view_traits_impl< ::db::AlgoParamsData, id_mysql >::query_columns
  {
    // AlgorithmParam
    //
    typedef
    odb::pointer_query_columns<
      ::db::AlgorithmParam,
      id_mysql,
      odb::access::object_traits_impl< ::db::AlgorithmParam, id_mysql > >
    AlgorithmParam;

    // AlgParamPlan
    //
    typedef
    odb::pointer_query_columns<
      ::db::AlgParamPlan,
      id_mysql,
      odb::access::object_traits_impl< ::db::AlgParamPlan, id_mysql > >
    AlgParamPlan;
  };

  // DetectPointVideoSourceData
  //
  struct access::view_traits_impl< ::db::DetectPointVideoSourceData, id_mysql >::query_columns
  {
    // DetectionPoint
    //
    typedef
    odb::pointer_query_columns<
      ::db::DetectionPoint,
      id_mysql,
      odb::access::object_traits_impl< ::db::DetectionPoint, id_mysql > >
    DetectionPoint;

    // VideoSource
    //
    typedef
    odb::pointer_query_columns<
      ::db::VideoSource,
      id_mysql,
      odb::access::object_traits_impl< ::db::VideoSource, id_mysql > >
    VideoSource;
  };
}

#include "nv_aimonitor31_view-odb.ixx"

#include <odb/post.hxx>

#endif // NV_AIMONITOR31_VIEW_ODB_HXX
