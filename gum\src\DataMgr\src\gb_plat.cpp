#include "gb_plat.h"
#include "gb_resource.h"
#include "../../UsgManager/include/UsgManagerExp.hpp"

namespace gum
{

	GbPlat::GbPlat(int id, const std::string& sipid, const std::string& ip, int port)
		:m_id(id), m_sipid(sipid), m_ip(ip), m_port(port)
	{
		m_addr = ip + ":" + std::to_string(port);
		size_t pos = sipid.find('-');		
		if (pos != std::string::npos)
		{
			m_sipid = sipid.substr(0, pos);
			int tp = atoi(sipid.substr(pos + 1, sipid.length()).c_str());
			switch (tp)
			{
			case 2:  m_type = "TCP PASSIVE"; break;
			case 3:  m_type = "TCP ACTIVE"; break;
			case 4:  m_type = "RTMP"; break;
			default: m_type = "UDP"; break;
			}
		}
	}

	GbPlat::~GbPlat()
	{
		fini();
	}

	bool GbPlat::init()
	{
		usg::CSpIGb28181 um;
		if (!wtoe::getMainApp()->getServiceInstance(WTOE_SNAME_UsgManager_UsgManager, um ))
			return false;
		return um->addRemote( m_id, m_sipid, m_addr );
	}

	void GbPlat::fini()
	{
		std::lock_guard<std::mutex> lock(m_mutex);
		for (auto& r : m_mapRes)
		{
			r.second.reset();
		}
		m_mapRes.clear();
		usg::CSpIGb28181 um;
		if (wtoe::getMainApp()->getServiceInstance(WTOE_SNAME_UsgManager_UsgManager, um))
		{
			um->delRemote(m_sipid, m_addr);
		}
	}

	bool GbPlat::match(const std::string& sipid, const std::string& ip, int port)
	{
		std::string s = sipid;
		size_t pos = s.find('-');
		if (pos != std::string::npos)
			s = s.substr(0, pos);
		if (s != m_sipid)
			return false;
		if (ip != "" && port > 0)
		{
			std::string addr = ip + ":" + std::to_string(port);
			if (addr != m_addr)
				return false;
		}
		return true;
	}

	std::string GbPlat::showStatus(const std::string& resInfo)
	{
		std::stringstream ss;
		ss << m_id << ": " << m_addr << " " << m_sipid 
			<< " status: " << m_isRegist << std::endl;
		for ( auto& r : m_mapRes )
		{
			if (!resInfo.empty())
			{
				if (resInfo.length() >  10 )  //sipid
				{ 
					if ( r.second->getKey() != resInfo)
						continue;
				}
				else   // videoid
				{
					int resId = std::stoi(resInfo.c_str());
					if ( resId > 0 && r.first != resId )
						continue;
				}
			}
			ss << r.second->showStatus();
		}
			
		return ss.str();
	}

	void GbPlat::setStatus(bool status)
	{
		if (m_isRegist == status)
			return;
		m_isRegist = status;
	}

	bool GbPlat::addRes(int id, const std::string& szKey, const std::string& name)
	{
		usg::CSpIGb28181 um;
		if (!wtoe::getMainApp()->getServiceInstance(WTOE_SNAME_UsgManager_UsgManager, um))
			return false;
		GbResourcePtr pRes = getRes(id);
		if (pRes)
		{
			pRes->updateName(name);
			pRes->setKey(szKey);
			return true;
		}
		GbResource* res = new GbResource(this, id, szKey, name);
		if (!res->init())
		{
			delete res;
			return false;
		}
		pRes.reset( res );
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			m_mapRes[id] = pRes;
		}
		um->addVideo(m_sipid, szKey, name, id);
		return true;
	}

	bool GbPlat::delRes(int id)
	{
		GbResourcePtr res = nullptr;
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			if (m_mapRes.find(id) == m_mapRes.end())
				return true;
			res = m_mapRes[id];
			m_mapRes.erase(id);
		}
		res.reset();		
		return true;
	}

	GbResourcePtr GbPlat::getRes(int id)
	{
		std::lock_guard<std::mutex> lock(m_mutex);
		if (m_mapRes.find(id) != m_mapRes.end())
			return m_mapRes[id];
		return nullptr;
	}

	GbResourcePtr GbPlat::getRes(const std::string& szKey)
	{
		std::lock_guard<std::mutex> lock(m_mutex);
		for (auto& r : m_mapRes)
		{
			if (r.second->getKey() == szKey)
				return r.second;
		}
		return nullptr;
	}

	int GbPlat::startPlay(int id, const std::string& transferType, int streamType, const std::string& destAddr, std::string& srcAddr, std::string& errInfo)
	{
		if (!m_isRegist)
		{
			errInfo = "Remote " + std::to_string(m_id) + " IS NOT REGIST";
			return -1;
		}
		auto res = getRes(id);
		if (!res)
			return -2;
		return res->startPlay(transferType, streamType, destAddr, srcAddr, errInfo);
	}

	int GbPlat::stopPlay(int id, int streamType /*= -1*/)
	{
		auto res = getRes(id);
		if (!res)
			return -2;
		return res->stopPlay( streamType);
	}

	int GbPlat::ptzOper(int id, usg::EPtzCommand cmd, int arg1, int arg2)
	{
		auto res = getRes(id);
		if (!res)
			return -2;
		return res->ptzOper(cmd, arg1, arg2);
	}

}