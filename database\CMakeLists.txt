
cmake_minimum_required(VERSION 3.5.2)

## 编译选项
set(CMAKE_VERBOSE_MAKEFILE ON)  
add_compile_options(-g -std=c++17 -fPIC -fstack-protector-all -Wno-deprecated-declarations)

## 输出目录
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/out/lib)

## 目标生成
set(TARGET_LIBRARY "ai_database")
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR} LIB_API_SRC)

## 依赖路径
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/generated
    ${CMAKE_CURRENT_SOURCE_DIR}/persistence
    /opt/odb/include
)

link_directories(/opt/odb/lib/)

#aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/generated/  LIB_GENERATION_SRC)
FILE(GLOB LIB_GENERATION_SRC "generated/*.cxx")

add_library(${TARGET_LIBRARY} SHARED ${LIB_API_SRC} ${LIB_GENERATION_SRC})

## 目标依赖
target_link_libraries(${TARGET_LIBRARY} odb-mysql odb)

