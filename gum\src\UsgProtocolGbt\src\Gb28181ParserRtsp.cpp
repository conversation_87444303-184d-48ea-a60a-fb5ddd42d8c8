
#include "Gb28181ParserRtsp.hpp"

namespace gb28181
{
    CGb28181ParserRtsp::CGb28181ParserRtsp( const std::string & str )
            : m_InputStr( str )
    {
        memset( &m_historyRtspInfo, 0, sizeof(gb28181::SHistoryRtspInfo) );
    }

    CGb28181ParserRtsp::~CGb28181ParserRtsp()
    {

    }

    bool CGb28181ParserRtsp::getRtspInfo( gb28181::SHistoryRtspInfo &info )
    {
        if( !parserReceivePkg() )
            return false;

        if ( m_historyRtspInfo.itype != 0 && m_historyRtspInfo.itype != 1 )
            return false;

        if ( fabs(m_historyRtspInfo.dscale ) < 0.0001 )
        {
            m_historyRtspInfo.dscale = 1.0;
        }
        info = m_historyRtspInfo;

        return true;
    }

    bool CGb28181ParserRtsp::parserReceivePkg()
    {
        if ( m_InputStr.empty() )
        {
            return false;
        }

        //解析指令
        const char* sdpLine = m_InputStr.c_str();
        const char* nextSDPLine = sdpLine;
        while(1)
        {
            sdpLine = nextSDPLine;
            if ( sdpLine == NULL ) break;

            if( !parseRtspLine(sdpLine, nextSDPLine) )
                return false;

            if ( parseRtspLine_control( sdpLine, m_historyRtspInfo.itype ))
                continue;

            if ( parseRtspLine_scale( sdpLine, m_historyRtspInfo.dscale ))
                continue;

            if ( parseRtspLine_range( sdpLine, m_historyRtspInfo.dRangeStart, m_historyRtspInfo.dRangeEnd ) )
                continue;
        }
        return true;
    }

    bool CGb28181ParserRtsp::parseRtspLine( const char* inputLine, const char*& nextLine )
    {
        // Begin by finding the start of the next line (if any):
        nextLine = NULL;
        for ( const char* ptr = inputLine; *ptr != '\0'; ++ptr )
        {
            if ( *ptr == '\r' || *ptr == '\n' )
            {
                // We found the end of the line
                ++ptr;
                while ( *ptr == '\r' || *ptr == '\n' )
                    ++ptr;
                nextLine = ptr;
                if ( nextLine[0] == '\0' )
                    nextLine = NULL; // special case for end
                break;
            }
        }

        // Then, check that this line is a SDP line of the form <char>=<etc>
        // (However, we also accept blank lines in the input.)
        if ( inputLine[0] == '\r' || inputLine[0] == '\n' )
            return true;

        return true;
    }

    bool CGb28181ParserRtsp::parseRtspLine_control( const char* inputLine, uint16_t &type )
    {
        // Check for "%S RTSP/1.0" line
        bool parseSuccess = false;
        char* buffer = strDupSize( inputLine );

        if ( sscanf( inputLine, "%s%*s", buffer ) == 1 )
        {
            parseSuccess = true;
        }

        if( strcmp( buffer, "PLAY") == 0 )
        {
            type = 0;
        }
        else if( strcmp( buffer, "PAUSE") == 0 )
        {
            type = 1;
        }
        else
        {
            parseSuccess = false;
        }

        strDel( buffer );
        return parseSuccess;
    }

    bool CGb28181ParserRtsp::parseRtspLine_scale( const char* inputLine, double &scale )
    {
        // Check for "Scale: 0.5" line
        bool parseSuccess = false;
        char* buffer = strDupSize( inputLine );

        if ( sscanf( inputLine, "Scale: %s", buffer ) == 1 )
        {
            parseSuccess = true;
        }

        std::string str(buffer);
        if( !valueToLexical< std::string, double >( str, scale ) )
            parseSuccess = false;


        strDel( buffer );
        return parseSuccess;
    }

    bool CGb28181ParserRtsp::parseRtspLine_range( const char* inputLine, uint32_t &rangeS, uint32_t&rangeE )
    {
        // Check for "Range: npt=0-" Line
        bool parseSuccess = false;
        char* buffer = strDupSize( inputLine );

        if ( sscanf( inputLine, "Range: npt=%u-%u", &rangeS, &rangeE ) == 2 )
        {
            parseSuccess = true;
        }

        //if ( rangeE == 0 )
        //{
        //	rangeE = -1;
        //}

        strDel( buffer );
        return parseSuccess;
    }

    char* CGb28181ParserRtsp::strDupSize( const char* str )
    {
        if ( !str )
            return NULL;

        size_t len = strlen( str ) + 1;

        char* strCopy = new char[len];

        return strCopy;
    }

    void CGb28181ParserRtsp::strDel( char*& str )
    {
        if ( str )
        {
            delete[] str;
            str = 0;;
        }
    }

}


