/**
 * Project FVM
 */
#pragma once

#include <vector>
#include <map>
#include "data/data_manager.h"
#include "channel/stream_pipe.h"
#include "channel/detect_channel.h"
#include "data/video_source_info.h"
#include "platform/front_platform.h"
#include "stream/output/stream_output.h"
#include "protocol/all.h"
#include "util/timer/timer_manager.h"

/**
 * @brief: 视频流管理 通道创建更新，暂停恢复，事件录像等
 */
namespace fvm::stream {

    using std::optional;
    using std::nullopt;

    /**
     * 初始化 管理器 （注册信号等）
     */
    void initStreamManager();

    /**
     * @brief 启动通道，配置通道资源
     * @note 一般发生在FVM启动、下发配置、iva请求
     * @param[in] processId 进程ID
     * @param[in] restart 是否重新发送数据（iva请求）
     */
    void startStreams(const optional<int> &processId = nullopt, bool ivaRequest = false);

    /**
     * @brief      根据web业务操作逻辑、iva请求 判断是否需要重启FVM流通道
     */
    bool isNeedRestartDetectChannel(int channelId, const VideoSourceInfoPtr &videoSourceInfoPtr, bool ivaRequest);

    /**
     * @brief      重启检测通道
     * @note       如果检测通道还存在，则是web端停止通道检测，此时恢复检测即可。如果检测通道不存在，则重新创建检测通道
     * @param[in]  channelId：通道id videoId:视频id
     */
    void restartDetectChannel(int channelId, int videoId);

    /**
     * @brief      根据web业务操作逻辑、iva请求 判断是否仅直接恢复检测，不重启fvm管道
     */
    bool isOnlyRestoreDetectRequest(int channelId, const VideoSourceInfoPtr &videoSourceInfoPtr, bool ivaRequest);

    /**
     * @brief      开始恢复检测
     */
    void restoreDetect(int channelId);

    /**
     * @brief 创建检测通道
     * @param[in] channelId：通道id, videoId：视频id; channelSources:通道视频资源
     */
    void createDetectChannel(int channelId, int videoId, const VideoSourceInfoPtr &channelSources);

    /**
    * @brief 创建球机录像通道
    * @param[in] groupId：接入id, channelSource:通道视频资源
    */
    void createDomeChannel(std::string groupId, VideoSourceInfoPtr &channelSource);

    /**
     * @brief 设置检测通道检测状态
     * @param[in] videoId：视频id
     * @param[in] presetId:预置位id，没有值时表示是 暂停检查通道，否则是恢复检测通道
     * @param[in] reason:状态原因 (暂停原因、恢复原因)
     */
    void setDetectChannelStatus(int videoId, const std::optional<int>& presetId, int reason);
    /**
     * @brief 保存当前画面为该预置位的基准画面
     * @param[in] videoId：视频id
     * @param[in] presetId:预置位id
     */
    void savePresetPosition(int videoId, int presetId);
    /**
     * @brief      停止临时通道
     * @param[in]  checkInput:  true: 需要检查通道变化才销毁管道 false: 不用检测管道变化，一律全部销毁
     */
    void stopTempChannels(bool checkInput = true);

    /**
     * @brief      停止进程对应的所有检测通道
     * @param[in]  processId:   进程id,停止进程对应的管道。 nullopt，所有进程
     * @param[in]  checkInput:  true: 需要检查通道变化才销毁管道 false: 不用检测管道变化，一律全部销毁
     * @note       下发配置之前，数据库中进程对应的通道可能变化，故从保存的processDetectChannels获取进程对应的通道，而不是从数据库接口获取。以保证以前的通道都停止。
     */
    void stopDetectChannels(const optional<int> &processId, bool checkInput = true);

    /**
     * @brief      停止检测通道
     * @param[in]  channelId:   需要停止的通道id
     * @param[in]  checkInput:  true: 需要检查通道变化才销毁管道 false: 不用检测管道变化，直接销毁
     */
    void stopDetectChannel(int channelId, bool checkInput);

    /**
     * @brief      停止球机通道
     */
    void stopDomeChannels();

    /**
     * @brief      获取所有检测通道
     */
    std::map<int, DetectChannelPtr> getDetectChannels();

    /** 
     * @brief 获取所有临时通道
     */
    std::map<ChannelType, StreamPipePtr> getTempChannels();
    /**
     * @brief      获取检测通道
     */
    DetectChannelPtr getDetectChannel(int channelId);

    /**
     * @brief      获取球机通道
     * @param[in]  groupId:球机接入id
     */
    StreamPipePtr getDomeChannel(std::string groupId);
    StreamPipePtr getDomeChannelBySource(data::VideoSourcePtr videoSourcePtr);

    /**
     * @brief "视频查看"，"设置检测区" 视频推流播放处理
     * @param[in] videoId：视频id;
     * @param[in] presetId:预置位id; actId:当前视频的平台对应实际预置位id, 可选参数，有值表示是"视频查看"，否则是"设置检测区"
     * @param[in] streamId：rtmp推流id
     */
    void playTempChannels(int videoId, const std::optional<int>& presetId, const std::optional<int>& actId, int streamId);

    /**
     * @brief      停止所有管道输入输出，销毁资源
     */
    void disposeStreamManager();

    /**
     * @brief 查找并设置时间切换方案对应的定时时间。
     */
    void setTimeProgramsTimers();

    /**
     * @brief 时间切换方案定时器回调函数
     */
    void timeProgramTimerCallBack(const timer::TimerPtr& timerPtr);

    /**
     * @brief      云台控制
     * @param[in] videoId：视频id;
     * @param[in]  action: 云台动作  1：向左 2：向右 3：向上 4：向下 5：变倍短 6：变倍长 7：聚焦近 8：聚焦远 9：光圈小 10：光圈大 11：灯光关 12：灯光开
     * @param[in]  step:  true: 步长  主要用于控制云台转动方向的操作，1-8为步长值，1为最小步长，8为最大步长
     */
    void controlPtz(int videoId, int action, int step);


    /**
     * @brief      操作云台预置位
     * @param[in] videoId：视频id;
     * @param[in]  actPresetId: 相机预置位id;
     * @param[in]  action: 操作码 0：调用预置位，1 保存预置位
     */
    void controlPreset(int videoId, int actPresetId, int action);
}