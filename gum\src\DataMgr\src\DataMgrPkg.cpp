#include <wtoe/BasicHelper/VcWarningOff.hpp>
#include "DataMgr/src/DataMgr.hpp"

/*
 * 定义所有的需要提供给外部用户的服务的标识.
 */
using namespace gum;
WTOE_DEFINE_PACKAGE_SERVICE_ID( DataMgr, DataMgr );

/*
 * 定义所有的需要通过名字进行动态访问的服务.
 */
WTOE_PACKAGE_SERVICE_LIST_BEG()
                    WTOE_PACKAGE_SERVICE_LIST_ADD( DataMgr, DataMgr )
WTOE_PACKAGE_SERVICE_LIST_END()

/*
 * 定义本服务包的标识.
 */
WTOE_DEFINE_PACKAGE_ID( DataMgr );

