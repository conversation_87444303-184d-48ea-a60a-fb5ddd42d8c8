#if defined( WIN32 )
#   include <WinSock2.h>
#else
#   include <arpa/inet.h>
#endif
#if defined( Linux )
#   include <sys/socket.h>
#endif

#include "wtoe/BasicHelper/VcWarningOff.hpp"
#include "wtoe/BasicHelper/BasicHelperExp.hpp"
#include "wtoe/PackageManager/PackageManagerExp.hpp"
#include "wtoe/JavaScriptEngine/JavaScriptEngineExp.hpp"
#include "DataMgr/include/DataMgrExp.hpp"
#include "DataMgr/src/JsDataMgr.hpp"


namespace wtoe {

template<>
JSClass CJavaScriptAdapter<CJsDataMgr>::s_jsClass =
{
    "dm", 
    JSCLASS_HAS_PRIVATE,
    JS_PropertyStub, // add
    JS_PropertyStub, // del
    JS_PropertyStub, // get
    JS_PropertyStub, // set
    JS_EnumerateStub, 
    JS_ResolveStub, 
    JS_ConvertStub, 
    WTOE_JS_FINALIZER( CJsDataMgr , finalize ),
    JSCLASS_NO_OPTIONAL_MEMBERS
};

template<>
JSPropertySpec CJavaScriptAdapter<CJsDataMgr>::s_jsClassProperties[] =
{
    { 0, 0, 0, 0, 0 }
};

template<>
JSFunctionSpec CJavaScriptAdapter<CJsDataMgr>::s_jsClassMethords[] =
{
	{ "init", WTOE_JS_METHORD( CJsDataMgr, init ),     0, 0, 0 }, 	
	{ "ver", WTOE_JS_METHORD( CJsDataMgr, showVer ),     0, 0, 0 },
    { "status", WTOE_JS_METHORD( CJsDataMgr, showStatus ),     0, 0, 0 },
    { 0, 0, 0, 0, 0 }
};

template<>
JSPropertySpec CJavaScriptAdapter<CJsDataMgr>::s_jsObjectProperties[] =
{
    { 0, 0, 0, 0, 0 }
};

template<>
JSFunctionSpec CJavaScriptAdapter<CJsDataMgr>::s_jsObjectMethords[] =
{
    { 0, 0, 0, 0, 0 }
};

CJsDataMgr::CJsDataMgr()
{
}

CJsDataMgr::~CJsDataMgr()
{
}

CJsDataMgr *CJsDataMgr::createObject()
{
    return 0;
}

JSBool CJsDataMgr::init( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
{
	gum::CSpIDataMgr dm;
	if ( !getMainApp()->getServiceInstance( WTOE_SNAME_DataMgr_DataMgr, dm ) )
	{
		return JS_FALSE;
	}
	std::string szIp;
    std::string szDb;
	
	if ( argc == 2 && JS_FALSE == JType2CType( szIp, szDb, ctx, argv ) )
		return false;
	if ( dm->init( szIp, szDb ) )
		return JS_TRUE;
	return JS_FALSE;
}

JSBool CJsDataMgr::showVer( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
{
	gum::CSpIDataMgr dm;
	if ( !getMainApp()->getServiceInstance( WTOE_SNAME_DataMgr_DataMgr, dm ) )
	{
		return JS_FALSE;
	}
	dm->showVersion();
	return JS_TRUE;
}


JSBool CJsDataMgr::showStatus( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval )
{
    gum::CSpIDataMgr dm;
    if ( !getMainApp()->getServiceInstance( WTOE_SNAME_DataMgr_DataMgr, dm ) )
    {
        return JS_FALSE;
    }
    int remoteId = 0;
    std::string szKey;

    if ( argc == 2 && JS_FALSE == JType2CType( remoteId, szKey, ctx, argv ) )
        return false;
    else if ( argc == 1 && JS_FALSE == JType2CType( remoteId, ctx, argv ) )
        return false;
    dm->showStatus( remoteId, szKey );
    return JS_TRUE;
}

}

namespace gum {
/******************************************************************************/
/* javaScriptRegister                                                         */
/******************************************************************************/
bool DATAMGR_PRIVATE javaScriptRegisterDataMgr( JSContext *jsCtx, JSObject *jsObj )
{
    return wtoe::CJavaScriptAdapter<wtoe::CJsDataMgr>::registerJavaScriptClass( jsCtx, jsObj );
}

}

