#ifndef JSSGSERVICE_HPP_
#define JSSGSERVICE_HPP_

#include <wtoe/JavaScriptEngine/JavaScriptEngineExp.hpp>
#include "UsgManager/include/UsgManagerCfg.hpp"
#include "UsgManager/include/UsgManagerItf.hpp"

namespace wtoe {

    class USGMANAGER_PRIVATE CJsUsgManager: public CJavaScriptAdapter<CJsUsgManager>
            , public CAdapterJavaScriptize
    {
        friend class CJavaScriptAdapter<CJsUsgManager>;
    public:
        CJsUsgManager();
        virtual ~CJsUsgManager();

        static CJsUsgManager *createObject();

    public:
        JSBool setLoacalAddr( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
//     JSBool setLoadLibName( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
//    JSBool setXmlType( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool setPackerType( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );

        //提供测试用的
        JSBool start( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool stop( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool getMediaInfoByMedia( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool playHistoryPrepare( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool getAllResInfo( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool getPtzController( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool onReceivePtzCommand( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool onqueryPreposition( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool onReceiveCatalog( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool onReceiveDeviceReboot( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool onReceiveRecordStart( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool onReceiveRecordStop( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool onReceiveGuardSet( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool onReceiveGuardReset( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool onReceiveAlarmReset( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool onReceiveQueryDeviceCatalog( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool onReceiveQueryDeviceInfo( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );
        JSBool onReceiveQueryDeviceStatus( JSContext *ctx, JSObject *obj, uintN argc, jsval *argv, jsval *rval );

    protected:
    WTOE_USING_BASE_CONSTRUCTOR( CJavaScriptAdapter<CJsUsgManager> );
    WTOE_USING_BASE_FINALIZER( CJavaScriptAdapter<CJsUsgManager> );
    };
}
#endif