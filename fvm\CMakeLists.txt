﻿cmake_minimum_required (VERSION 3.5.2)

if( ${ARCHITECTURE} STREQUAL "x86_64" )
    option(SDK_HK_SUPPORTED "HAVE HK SDK" ON)
    if (SDK_HK_SUPPORTED)
        add_definitions(-DSDK_HK_SUPPORTED)
    endif()

    option(SDK_DH_SUPPORTED "HAVE DH SDK" ON)
    if (SDK_DH_SUPPORTED)
        add_definitions(-DSDK_DH_SUPPORTED)
    endif()

    option(SDK_MPC_SUPPORTED "HAVE MPC SDK" ON)
    if (SDK_MPC_SUPPORTED)
        add_definitions(-DSDK_MPC_SUPPORTED)
    endif()
elseif( ${ARCHITECTURE} STREQUAL "aarch64")
    add_definitions(-DARM)
endif()

#FFMPEG版本控制
if  (${PLATFORM} MATCHES "HUAWEI")
	add_definitions(-DFFMPEG_LOW)
endif()

set(APP_NAME "fvmD")
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY  "${PROJECT_SOURCE_DIR}/out")
set(LIB_FVM ${PROJECT_SOURCE_DIR}/out/lib)

add_compile_options(-g -std=c++17 -fPIC -fstack-protector-all -Wno-unknown-pragmas -Wno-unused-variable -Werror=return-type -Wall -DBOOST_LOG_DYN_LINK)

# 添加编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=address -fno-omit-frame-pointer")
# 添加链接选项
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fsanitize=address")

# boost
set(BOOST_HOME "/opt/boost")
set(BOOST_LIB
    boost_fiber
    boost_context
    boost_filesystem
    boost_log
    boost_log_setup
    boost_locale
    boost_thread
)

# ffmpeg
set(FFMPEG_HOME "/opt/ffmpeg")
set(FFMPEG_LIB
    avutil
    avcodec
    avformat
    swresample)

if( ${ARCHITECTURE} STREQUAL "aarch64")
   list(APPEND FFMPEG_LIB x264)
endif()

# odb
set(ODB_HOME "/opt/odb")
set(ODB_LIB
    odb
    odb-mysql
    mysqlclient
    z
    ssl
    crypto
)

# 海康SDK
if (SDK_HK_SUPPORTED)
    set(HKSDK_HOME "/opt/HKSDK")
    set(HKSDK_LIB hcnetsdk HCCore hpr)
endif()

# 大华SDK
if (SDK_DH_SUPPORTED)
    set(DHSDK_HOME "/opt/DHSDK")
    set(DHSDK_LIB dhnetsdk dhconfigsdk)
endif()

# 二代平台SDK
if (SDK_MPC_SUPPORTED)
    set(MPCSDK_HOME "/opt/MPCSDK")
    set(MPCSDK_LIB MpcNetSdk ACE-6.0.1 RimiMechanism RsioMechanism MtcpHandler)
endif()

# 头文件
include_directories(
    ${PROJECT_SOURCE_DIR}/fvm
    ${BOOST_HOME}/include/
    ${FFMPEG_HOME}/include/
    ${ODB_HOME}/include/
if (SDK_HK_SUPPORTED)
    ${HKSDK_HOME}/include/
endif()
if (SDK_DH_SUPPORTED)
    ${DHSDK_HOME}/include/
endif()
if (SDK_MPC_SUPPORTED)
    ${MPCSDK_HOME}/include/
endif()
    ${PROJECT_SOURCE_DIR}/database
    ${PROJECT_SOURCE_DIR}/database/generated
    ${PROJECT_SOURCE_DIR}/database/persistence
    ${PROJECT_SOURCE_DIR}/cli/include
    ${PROJECT_SOURCE_DIR}/log/include
    ${PROJECT_SOURCE_DIR}/network
    ${PROJECT_SOURCE_DIR}/network/include
    ${PROJECT_SOURCE_DIR}/onvif/include
    ${PROJECT_SOURCE_DIR}/fvm/util/timer
)

# 库路径
link_directories(
    ${LIB_FVM}
    ${BOOST_HOME}/lib/
    ${FFMPEG_HOME}/lib/
    ${ODB_HOME}/lib/
if (SDK_HK_SUPPORTED)
    ${HKSDK_HOME}/lib/
endif()
if (SDK_DH_SUPPORTED)
    ${DHSDK_HOME}/lib/
endif()
if (SDK_MPC_SUPPORTED)
    ${MPCSDK_HOME}/lib/
endif()
)

FILE(GLOB data "data/*.cpp")
FILE(GLOB platform "platform/*.cpp")
FILE(GLOB stream "stream/*.cpp")
FILE(GLOB channel "stream/channel/*.cpp")
FILE(GLOB element "stream/element/*.cpp")
FILE(GLOB input "stream/input/*.cpp")
FILE(GLOB output "stream/output/*.cpp")
FILE(GLOB util "util/*.cpp")
FILE(GLOB protocol "protocol/*.cpp")
FILE(GLOB license "util/license/*.cpp")
FILE(GLOB config "util/config/*.cpp")
FILE(GLOB config "util/timer/*.cpp")

SET(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)
add_executable(${APP_NAME} ${data} ${platform} ${stream} ${channel} ${input} ${element}
    ${output} ${protocol} ${util} ${license} ${config} main.cpp)


target_link_libraries(${APP_NAME} rt pthread ${BOOST_LIB} ${FFMPEG_LIB} ai_database ai_cli ai_log ai_network ai_onvif)
if (SDK_HK_SUPPORTED)
    target_link_libraries(${APP_NAME} ${HKSDK_LIB}) 
endif()

if (SDK_DH_SUPPORTED)
    target_link_libraries(${APP_NAME} ${DHSDK_LIB})
endif()

if (SDK_MPC_SUPPORTED)
    target_link_libraries(${APP_NAME} ${MPCSDK_LIB})
endif()

set_target_properties(${APP_NAME} PROPERTIES INSTALL_RPATH "./lib/ffmpeg/;./lib/boost/;./lib/odb/;./lib/HKSDK/;./lib/DHSDK/;./lib/MPCSDK/;./lib/")


# 拷贝依赖库
message("-- Copying lib files, shell file gennerated at ${CMAKE_CURRENT_BINARY_DIR}/post_build.sh")
file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/post_build.sh" 
    "echo 'start copying lib files'\n"
    "if [ ! -e ${PROJECT_SOURCE_DIR}/out/lib/ffmpeg/ ]; then\n"
    "   mkdir ${PROJECT_SOURCE_DIR}/out/lib/ffmpeg/ \n"
    "fi\n"
    "if [ ! -e ${PROJECT_SOURCE_DIR}/out/lib/boost/ ]; then\n"
    "   mkdir ${PROJECT_SOURCE_DIR}/out/lib/boost/ \n"
    "fi\n"
    "if [ ! -e ${PROJECT_SOURCE_DIR}/out/lib/odb/ ]; then\n"
    "   mkdir ${PROJECT_SOURCE_DIR}/out/lib/odb/ \n"
    "fi\n"
    "if [ ! -e ${PROJECT_SOURCE_DIR}/out/lib/HKSDK/ ]; then\n"
    "   mkdir ${PROJECT_SOURCE_DIR}/out/lib/HKSDK/ \n"
    "   mkdir ${PROJECT_SOURCE_DIR}/out/lib/HKSDK/HCNetSDKCom \n"
    "fi\n"
    "if [ ! -e ${PROJECT_SOURCE_DIR}/out/lib/DHSDK/ ]; then\n"
    "   mkdir ${PROJECT_SOURCE_DIR}/out/lib/DHSDK/ \n"
    "fi\n"
    "if [ ! -e ${PROJECT_SOURCE_DIR}/out/lib/MPCSDK/ ]; then\n"
    "   mkdir ${PROJECT_SOURCE_DIR}/out/lib/MPCSDK/ \n"
    "fi\n"
)

foreach(lib_file ${FFMPEG_LIB})
file(APPEND "${CMAKE_CURRENT_BINARY_DIR}/post_build.sh" 
    "ls -1 ${FFMPEG_HOME}/lib/ | grep ${lib_file} | egrep \.so\.[0-9]*$ | xargs -I{} cp ${FFMPEG_HOME}/lib/{} ${PROJECT_SOURCE_DIR}/out/lib/ffmpeg/ \n")
endforeach(lib_file ${FFMPEG_LIB})

foreach(lib_file ${BOOST_LIB})
file(APPEND "${CMAKE_CURRENT_BINARY_DIR}/post_build.sh" 
    "cp -r -a ${BOOST_HOME}/lib/lib${lib_file}.so.* ${PROJECT_SOURCE_DIR}/out/lib/boost/ \n")
endforeach(lib_file ${BOOST_LIB})

foreach(lib_file ${ODB_LIB})
file(APPEND "${CMAKE_CURRENT_BINARY_DIR}/post_build.sh" 
    "cp -r -a ${ODB_HOME}/lib/lib${lib_file}*.so* ${PROJECT_SOURCE_DIR}/out/lib/odb/ \n")
endforeach(lib_file ${ODB_LIB})

if (SDK_HK_SUPPORTED)
file(APPEND "${CMAKE_CURRENT_BINARY_DIR}/post_build.sh" 
    "cp -r -a ${HKSDK_HOME}/lib/libcrypto.so* ${PROJECT_SOURCE_DIR}/out/lib/HKSDK/ \n"
    "cp -r -a ${HKSDK_HOME}/lib/libHCCore.so ${PROJECT_SOURCE_DIR}/out/lib/HKSDK/ \n"
    "cp -r -a ${HKSDK_HOME}/lib/libhcnetsdk.so ${PROJECT_SOURCE_DIR}/out/lib/HKSDK/ \n"
    "cp -r -a ${HKSDK_HOME}/lib/libhpr.so ${PROJECT_SOURCE_DIR}/out/lib/HKSDK/ \n"
    "cp -r -a ${HKSDK_HOME}/lib/libssl.so ${PROJECT_SOURCE_DIR}/out/lib/HKSDK/ \n"
    "cp -r -a ${HKSDK_HOME}/lib/HCNetSDKCom/libHCCoreDevCfg.so ${PROJECT_SOURCE_DIR}/out/lib/HKSDK/HCNetSDKCom/ \n"
    "cp -r -a ${HKSDK_HOME}/lib/HCNetSDKCom/libHCPreview.so ${PROJECT_SOURCE_DIR}/out/lib/HKSDK/HCNetSDKCom/ \n"
    "cp -r -a ${HKSDK_HOME}/lib/HCNetSDKCom/libSystemTransform.so ${PROJECT_SOURCE_DIR}/out/lib/HKSDK/HCNetSDKCom/ \n")
endif()

if (SDK_DH_SUPPORTED)
    file(APPEND "${CMAKE_CURRENT_BINARY_DIR}/post_build.sh"
    "cp -r -a ${DHSDK_HOME}/lib/libavnetsdk.so ${PROJECT_SOURCE_DIR}/out/lib/DHSDK/ \n"
    "cp -r -a ${DHSDK_HOME}/lib/libdhconfigsdk.so ${PROJECT_SOURCE_DIR}/out/lib/DHSDK/ \n"
    "cp -r -a ${DHSDK_HOME}/lib/libdhnetsdk.so ${PROJECT_SOURCE_DIR}/out/lib/DHSDK/ \n"
    "cp -r -a ${DHSDK_HOME}/lib/libStreamConvertor.so ${PROJECT_SOURCE_DIR}/out/lib/DHSDK/ \n")
endif()

if (SDK_MPC_SUPPORTED)
    file(APPEND "${CMAKE_CURRENT_BINARY_DIR}/post_build.sh"
    "cp -r -a ${MPCSDK_HOME}/lib/*.so ${PROJECT_SOURCE_DIR}/out/lib/MPCSDK/ \n"
    "cp -r -a ${MPCSDK_HOME}/lib/libboost_thread.so.1.74.0 ${PROJECT_SOURCE_DIR}/out/lib/MPCSDK/ \n")
endif()

add_custom_command(TARGET ${APP_NAME}
    POST_BUILD
    COMMAND sh post_build.sh WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)