/**
 * @addtogroup odbDatabaseGroup
 * @brief 预置位对应的车道线
 * @{
 */

#ifndef FVM_PRESET_LANE_LINE_H
#define FVM_PRESET_LANE_LINE_H

#include <string>
#include <cstddef>       // std::size_t
#include <odb/core.hxx>
#include <iostream>
#include <utility>
#include <vector>

namespace db {
/**
 * @brief  预置位对应的车道线配置 对应数据库aimonitorV3的表wn_preset_lane_line
 */
#pragma db object table("wn_preset_lane_line")
class PresetLaneLine {
public:

    PresetLaneLine(unsigned long presetId,
                     std::string l0, std::string l1, std::string l2,std::string l3, std::string l4,
                     std::string l5, std::string l6, std::string l7, std::string l8, std::string l9,
                     std::string l10, std::string l11, std::string l12, std::string l13, std::string l14, std::string l15)

            :  presetId(presetId),
                line0(std::move(l0)), line1(std::move(l1)), line2(std::move(l2)), line3(std::move(l3)),
                line4(std::move(l4)),line5(std::move(l5)), line6(std::move(l6)), line7(std::move(l7)),
                line8(std::move(l8)), line9(std::move(l9)),line10(std::move(l10)), line11(std::move(l11)),
                line12(std::move(l12)), line13(std::move(l13)), line14(std::move(l14)),line15(std::move(l5))
            {}

    unsigned long getId() const {
        return id;
    }

    unsigned long getPresetId() const {
        return presetId;
    }

    void setPresetId(unsigned long id) {
        this->presetId = id;
    }

    std::vector<std::string> getLaneLines(){
        return {line0,line1,line2,line3,line4,line5,line6,line7,line8,line9,line10,line11,line12,line13,line14,line15};
    }

    std::string getLaneLine(int lineNum)
    {
#define GET(i) case i: return line##i;
        switch (lineNum)
        {
            GET(1) GET(2) GET(3) GET(4) GET(5) GET(6) GET(7) GET(8) GET(9) GET(10) GET(11) GET(12) GET(13) GET(14) GET(15)
            default:
                return "";
        }
#undef GET
    }

    void setLaneLine(int lineNum, const std::string& line)
    {
#define SET(i) case i: line##i = line; break;
        switch (lineNum)
        {
            SET(1) SET(2) SET(3) SET(4) SET(5) SET(6) SET(7) SET(8) SET(9) SET(10) SET(11) SET(12) SET(13) SET(14) SET(15)
            default:
                break;
        }
#undef SET
    }

private:

    friend class odb::access;
    PresetLaneLine() {}

private:
#pragma db id auto
    unsigned long id;               //!< 表ID

#pragma db column("preset_id")
    unsigned long presetId;         //!< 预置位id 对应表wn_preset(preset) id

#pragma db column("line0")  type("VARCHAR(255)")
        std::string line0;              //!< 车道线

#pragma db column("line1")  type("VARCHAR(255)")
        std::string line1;              //!< 车道线

#pragma db column("line2")  type("VARCHAR(255)")
        std::string line2;              //!< 车道线

#pragma db column("line3")  type("VARCHAR(255)")
        std::string line3;              //!< 车道线

#pragma db column("line4")  type("VARCHAR(255)")
        std::string line4;              //!< 车道线

#pragma db column("line5")  type("VARCHAR(255)")
        std::string line5;              //!< 车道线

#pragma db column("line6")  type("VARCHAR(255)")
        std::string line6;              //!< 车道线

#pragma db column("line7")  type("VARCHAR(255)")
        std::string line7;              //!< 车道线

#pragma db column("line8")  type("VARCHAR(255)")
        std::string line8;              //!< 车道线

#pragma db column("line9")  type("VARCHAR(255)")
        std::string line9;              //!< 车道线

#pragma db column("line10")  type("VARCHAR(255)")
        std::string line10;             //!< 车道线

#pragma db column("line11")  type("VARCHAR(255)")
        std::string line11;             //!< 车道线

#pragma db column("line12")  type("VARCHAR(255)")
        std::string line12;             //!< 车道线

#pragma db column("line13")  type("VARCHAR(255)")
        std::string line13;             //!< 车道线

#pragma db column("line14")  type("VARCHAR(255)")
        std::string line14;             //!< 车道线

#pragma db column("line15")  type("VARCHAR(255)")
        std::string line15;             //!< 车道线

    };
}


#endif //FVM_PRESET_LANE_LINE_H
/**
 * @}
 */