#include <iostream>
#include <thread>
#include <csignal>
#include <vector>
#include <termio.h>
#include <cstdio>
#include <unistd.h>
#include <termios.h>
#include <cstring>
#include <functional>

#include "command_process.h"

using namespace std;
bool appRunning = true;
void onSignalAction(int signum, siginfo_t* siginfo, void* ucontext);

int logoutThread_print();

using namespace cli;
void test_int(int a, int b)
{
    cout << "test_int a+b = " << a+b << endl;
}

void test_char(char a, char b)
{
    cout << "test_char a = " << a << " b = " << b << endl;
}

void test_string(const string& a, const string& b)
{
    cout << "test_string a = " << a << " b = " << b << endl;
}


void testcmd(int a, int b, int c)
{
    cout << "testcmd a+b = " << a+b+c << endl;
}

class classTest
{
public:
    int c = 1;
    void classTestInt(int a, int b) const
    {
        cout << "classTestInt a+b = " << a+b  << " c = " << c << endl;
    }
};

int main(int argc, char* argv[])
{
    struct sigaction signalAction = {};
    signalAction.sa_sigaction = onSignalAction;
    signalAction.sa_flags = SA_SIGINFO;
    sigaction(SIGINT, &signalAction, nullptr);
    sigaction(SIGTERM, &signalAction, nullptr);
    sigaction(SIGUSR1, &signalAction, nullptr);
    sigaction(SIGUSR2, &signalAction, nullptr);

    cout << "start ......" <<  endl;

    std::thread logoutThread(logoutThread_print);
    std::thread logoutThread2(logoutThread_print);
    cli::CommandProcess cmdProcess;

    cmdProcess.addCommand("testint", [](const cli::consoleParamList& paramList) {
                                            CHECK_PARAM_NUM(paramList,2);
                                            test_int(GET_PARAM_INT(paramList[0]), GET_PARAM_INT(paramList[1]));
                                        },
    "test a+b, Parameters: int int");

    cmdProcess.addCommand("testchar", [](const cli::consoleParamList& paramList) {
                              CHECK_PARAM_NUM(paramList,2);
                              test_char(paramList[0].at(0), paramList[1].at(0));
                          }
    ,"testchar, Parameters: char char");

    cmdProcess.addCommand("teststring",[](const cli::consoleParamList& paramList){
                              CHECK_PARAM_NUM(paramList,2);
                              test_string(paramList[0], paramList[1]);
                        }
    ,"teststring, Parameters: string string");

    cmdProcess.addCommand("testcmd", [](const cli::consoleParamList& paramList) {
                              CHECK_PARAM_NUM(paramList,3);
        int a = GET_PARAM_INT(paramList[0]);
        int b = GET_PARAM_INT(paramList[1]);
        int c = GET_PARAM_INT(paramList[2]);
                              testcmd(a, b,c);
                          }
            ,"testcmd, Parameters: int int int");
    classTest classtest;
    cmdProcess.addCommand("testclass", [&](const cli::consoleParamList& paramList) {
                                CHECK_PARAM_NUM(paramList,2);
                                int a = GET_PARAM_INT(paramList[0]);
                                int b = GET_PARAM_INT(paramList[1]);
                                classtest.classTestInt(a, b);
                          }
            ,"testclass, Parameters: int int int");

   cmdProcess.start();

    //std::thread consoleThread = std::thread(&cli::CommandProcess::consoleRun, &cmdProcess);

    while(appRunning)
    {
        std::this_thread::sleep_for(std::chrono::microseconds (1000));
    }
    cmdProcess.stop();
    logoutThread.join();
    logoutThread2.join();
    cmdProcess.stop();
    //consoleThread.join();

    cout << "end ......" << endl;

    return 0;
}

int logoutThread_print()
{
    int t = 0;
    while (appRunning)
    {
        std::cout << "log output...................................." << t++ << endl;
        fflush(stdout);
        std::this_thread::sleep_for(std::chrono::milliseconds (1000));
    }
}


void onSignalAction(int signum, siginfo_t* siginfo, void* ucontext) {
    switch (signum) {
        case SIGINT:
        case SIGTERM:
        case SIGUSR1:
        case SIGUSR2:
            appRunning = false;
            break;
        default:
            break;
    }
}

