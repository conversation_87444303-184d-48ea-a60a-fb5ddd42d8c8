/**
 * Project FVM
 */
#pragma once

#include <memory>
#include <list>
#include "stream/element/packet_data.h"
#include "stream/element/stream_element.h"
#include "stream/element/ffmpeg_element.h"
#include "stream/element/onvif_ptz.h"
#include "data/video_source_info.h"
#include "platform/front_platform.h"
#include "util/worker_pool.h"
#include "protocol/all.h"

/**
 * @brief: 视频输入器
 *          派生文件流输入、RTSP输入等
 */
namespace fvm {
    namespace stream {
        typedef boost::signals2::signal<void(PacketDataPtr)> OnStreamDataReceived;
        typedef boost::signals2::signal<void(CodecInfoPtr)> OnStreamCodecInfoRetrieved;
        typedef boost::signals2::signal<void()> OnCameraPresetCalled;
        typedef boost::signals2::signal<void()> OnStreamInputLost;
        typedef boost::signals2::signal<void(boost::posix_time::ptime time, bool isPresetChange)> OnPTZPresetChecked;

        /**
         * onvif获取的云台ptz状态
         */
        enum class PTZPresetStatus
        {
            NotInited,
            None,            //!<  没有偏移检测能力
            Normal,          //!<  正常状态，拥有偏移检测能力
            CallPtz,         //!<  云台被调用中
            Abnormal,        //!<  运行过程中，获取实时坐标异常
            Offset,          //!<  检测到偏移
            Restore,         //!<  检测到恢复
        };
        struct PTZPresetInfo
        {
            double x = 0;
            double y = 0;
            double z = 0;
            PTZPresetStatus ptzPresetStatus = PTZPresetStatus::NotInited;

            std::optional<PTZPresetStatus> checkPTZPresetChange(double currX, double currY, double currZ, double offsetTh, double restoreTh) const
            {
                if (ptzPresetStatus == PTZPresetStatus::Restore)
                {
                    if (std::fabs(currX - x) >= offsetTh
                        || std::fabs(currY - y) >= offsetTh
                        || std::fabs(currZ - z) >= offsetTh)
                    {
                        return PTZPresetStatus::Offset;
                    }
                }
                else if ((ptzPresetStatus == PTZPresetStatus::Offset) || (ptzPresetStatus== PTZPresetStatus::CallPtz))
                {
                    if (std::fabs(currX - x) <= restoreTh
                        && std::fabs(currY - y) <= restoreTh
                        && std::fabs(currZ - z) <= restoreTh)
                    {
                        return PTZPresetStatus::Restore;
                    }
                }
                return nullopt;
            }

        };

        class StreamInput : /*public StreamElement, */public FFMPEGElement {
        public:
            /**
             * 初始化视频资源信息
             */
            virtual void initSource(const VideoSourceInfoPtr source);

            inline VideoSourceInfoPtr getVideoSource(){return videoSourceInfo;};

            /**
             * 异步调用预置位
             * @param [in] needWait: 默认为true，即转动预置位后等待一定时间发送retstore，画检测区时不需要等待，直接返回
             */
            void asyncCallCameraPreset(int presetId, int actPresetId, bool needWait = true );

            /**
             * 输入流是否能调用预置位
             */
            inline virtual bool isPtzCapable(){return false;};

            /**
             * @brief  通过onvif获取输入流ptz的信息，包括坐标和预置位偏移状态
             */
            virtual PTZPresetInfo getPTZPresetInfo();

            /**
             * @brief  设置onvif ptz的信息，包括坐标和预置位偏移状态
             */
            inline virtual void setPTZPresetInfo(const PTZPresetInfo& info){ ptzPresetInfo = info;};

            /**
             * @brief  在调用预置位之前更新当前预置位的坐标，以及预置状态
             */
            virtual bool updatePTZPresetInfo(int actPresetId);

            /**
             * @brief                   获取云台坐标
             * @param[out] x,y,z        云台ptz坐标
             * @param[in] actPresetId:  云台真实预置位，nullopt时获取实时坐标，否则获取actPresetId对应的预置位坐标
             */
            inline virtual bool getPtzPosition(double &x, double &y, double &z, const std::optional<int>& actPresetId){return false;};

            /**
             * @brief  实时检测相机云台ptz的信息，判断相机的偏移状态
             */
            virtual void checkPTZPresetThread();

            /**
             * @brief 通知ptz云台检测线程检测预置位状态
             */
            inline void notifyCheckPTZPresetThread(){isNotify = true; checkPtzCondVal.notify_one();};
            /**
             * 获取当前流的视频索引，>=0 表示得到了视频
             * @return
             */
            inline int getVideoIndex() { return videoIndex; }

            /**
             * @brief 等待元件结束，相关线程退出
             */
            void waitForFinished() override;

            /**
             * @brief 切换时间方案
             */
            void switchTimeProgram();

            /**
             * @brief 设置当前正在使用的预置位Id
             */
            inline void setCurrentPresetId(int Id) {presetId = Id;};

            /**
             * @brief 获取用于偏移检测时间间隔，用于判断偏移检测功能归属是否发生变化。
             */
            inline int  getOffsetWaitTime(){return offsetWaitTime;};

            /**
             * @brief 控制云台 
             * @param[in] action:  云台动作 1：向左 2：向右 3：向上 4：向下 5：变倍短 6：变倍长 7：聚焦近 8：聚焦远 9：光圈小 10：光圈大 11：灯光关 12：灯光开
             * @param[in] step:  步长  主要用于控制云台转动方向的操作，1-8为步长值，1为最小步长，8为最大步长
             */
            inline virtual bool controlPtz(int action, int step){return false;};

            /**
             * 云台保存预置位
             */
            inline virtual bool saveCameraPreset(int presetId) { return false; };

            /**
             * 调用相机预置位,子类实现
             */
            inline virtual bool callCameraPreset(int presetId) { return false; };

            /**
             * 云台控制指令动作类型转换
             */
            inline virtual network::EPtzCommand convertPtzCmd(int action);

            /**
             * 绑定球机（枪球联动）
             */
            inline void bindDome(const FrontPlatformPtr platform) { this->domePlatform = platform; }

            /**
             * 绑定球机（枪球联动）
             */
            inline void bindDomeSource(const VideoSourceInfoPtr source) { this->domeVideoSource = source; }

            /**
             * 联动的球机平台 (枪球联动)
             */
            inline FrontPlatformPtr linkedDomePlatform() { return this->domePlatform; }
            inline VideoSourceInfoPtr linkedDomeSource() { return this->domeVideoSource; }

            /**
             * 联动的球机预置位 (枪球联动)
             */
            inline int linkedDomePreset() { return videoSourceInfo->videoSourcePtr->getDomePreset(); }
            
        public:// signals:
            // 得到数据包
            OnStreamDataReceived onStreamDataReceived;
            // 得到码流信息
            OnStreamCodecInfoRetrieved onStreamCodecInfoRetrieved;
            // 相机预置位调用完成
            OnCameraPresetCalled onCameraPresetCalled;
            // 输入流丢失
            OnStreamInputLost onStreamInputLost;
            // ptz偏移判断线程判断结果
            OnPTZPresetChecked onPTZPresetChecked;

        protected:
            /**
             * 核心处理逻辑
             */
   			void process() override{};

            /**
             * 打开输入流，获取输入流信息
             */
            virtual bool open();

            /**
             * 关闭输入流
             */
            virtual void close();

            /**
             *  判断当前是否需要立即进行ptz云台检测
             */
            bool waitingForCheckPreset(int interval);

            /*
             * 任务类别
             */
            worker::WorkerType workerType() override {return worker::WorkerType::StreamInput;}


            // 前端平台
            FrontPlatformPtr frontPlatform;
            // 视频信息配置
            VideoSourceInfoPtr videoSourceInfo;
			// onvif控云台的信息
            OnvifPtzPtr onvifPtzInfo;
            // ptz云台坐标信息
            PTZPresetInfo ptzPresetInfo;
            // ptz云台偏移判断线程条件变量
            boost::fibers::condition_variable checkPtzCondVal;
            // ptz云台偏移判断线程通知
            std::atomic_bool isNotify = false;
            // 每次事件来后，判断下当前时间与上次事件时间间隔，超过一定时间，则立即检测ptz状态
            boost::posix_time::ptime checkPtzTime;
            // 偏移判断间隔
            int offsetWaitTime = 0;
            // ptz云台偏移判断锁
            boost::fibers::mutex  checkPtzLock;
            // ptz云台偏移判断线程future
            worker::FiberFuture presetThreadFuture;
            // 时间切换方案线程future
            worker::FiberFuture timeProgramThreadFuture;
            // 视频帧 码流索引
            int videoIndex = -1;
            // 预置位Id
            int presetId = 0;
            // 视频帧时间间隔
            int64_t duration = 0;

			// 当前视频是否成功打开
            std::atomic_bool isOpened = false;

			// 当前（帧）序号
            std::atomic_int frameIndex = 0;
            //上一次云台命令
            network::EPtzCommand preCmd = network::EPTZCOMMAND_STOP;

            // 关联球机 （枪球联动）
            FrontPlatformPtr domePlatform = nullptr;
            VideoSourceInfoPtr domeVideoSource = nullptr;
        };

        typedef std::shared_ptr<StreamInput> StreamInputPtr;
        typedef std::list<StreamInputPtr> StreamInputList;
    }
}