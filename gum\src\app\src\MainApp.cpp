#include "MainApp.hpp"
#include "wtoe/JavaScriptEngine/JavaScriptEngineExp.hpp"
#include <boost/scoped_ptr.hpp>
#include <iostream>
#include <signal.h>
#include "ailog.h"

namespace gum
{

    static const char *MPC_MAIN_PROMPT_1 = "[jscript]# ";

    static const char *MPC_MAIN_GC    = "gc";
    static const char *MPC_MAIN_QUIT  = "quit";
    static const char *MPC_MAIN_RESET = "reset";

    bool g_killExit = false;

    CMainApp::CMainApp( int argc, char *argv[], const wtoe::CCmndLineOptionsDescription &desc)
            : wtoe::CMainApplication( argc, argv, desc )
    {
        m_argc = argc;
        m_argv = argv;
    }

    CMainApp::~CMainApp()
    {
    }

#if defined WIN32
    void registSignal()
{}
#else
    void signalHandle( int signalNO )
    {
        if ( signalNO == SIGTERM || signalNO == SIGKILL )
        {
            g_killExit = true;
            if ( signalNO == SIGTERM )
                ai::LogFatal << "Signal 'SIGTerm' recived, Program will exit!";
            else
                ai::LogFatal << "Signal 'SIGKILL' recived, Program will exit!";
        }
        else
            ai::LogInfo << "Signal " << signalNO << " is received";
    }

    void registSignal()
    {
//	signal( SIGINT, SIG_IGN );
        signal( SIGSTOP, SIG_IGN );
        signal( SIGKILL, SIG_IGN );

        signal(SIGPIPE, SIG_IGN);

        struct sigaction sa;
        sa.sa_handler = signalHandle;
        sa.sa_flags = 0;

        while(1)
        {
            try
            {
                if ( 0 != sigaction( SIGTERM, &sa, NULL)
                     //				|| 0 != sigaction( SIGKILL, &sa, NULL )
                     //				|| 0 != sigaction( SIGSTOP, &sa, NULL )
                     || 0 != sigaction( SIGINT, &sa, NULL ) )
                {
                    ai::LogError << "sigaction(SIGTERM) " << strerror(errno);
                }
                else
                    ai::LogInfo << "sigaction(SIGTERM ) ok";
                break;
            }
            catch (...)
            {
                ai::LogError << "sigaction error";
            }
        }
    }

#endif

    int CMainApp::run()
    {
        /*
         * 创建vscript脚本执行环境.
         */
        boost::scoped_ptr<wtoe::IJavaScriptRuntime> rt;
        boost::scoped_ptr<wtoe::IJavaScriptContext> ct;

        registSignal();

        run_beg:
        ct.reset();
        rt.reset( m_jsVM->createRuntime() );
        if ( !rt )
        {
            ai::LogError << "CAN NOT CREATE JavaScriptRuntime!";
            return -1;
        }
        ct.reset( rt->createContext( 2 * 1024 * 1024u ) );
        if ( !ct )
        {
            ai::LogError << "CAN NOT CREATE JavaScriptContext!";
            return -1;
        }
        // 执行初始化操作
        std::string init;
        if ( getCmndLineParser().getCmndLineOption( "init", init ) )
        {
            if ( !ct->execute( init ) ) return -1;
        }

        /*
        * 开始消息的循环.
        */
        static char str[1024 * 4];
        run_con:
        while ( std::cout << MPC_MAIN_PROMPT_1 && std::cin.get( str, sizeof( str ), '\n' )  )
        {
            /*
            * 获取从console上读取的字符数目.
            */
            size_t readed = std::cin.gcount();
            /*
            * 读取下一个字符，主要是为了将最后一个'\n'读取出来.
            */
            const char next = std::cin.rdbuf()->sbumpc();
            /*
            * 判断读取的字符串是否正确，如果next不是'\n'则表示命令行太长或发生了错误.
            */
            if ( next != '\n' )
            {
                std::cerr << "COMMAND LINE IS TOO LONG!\n";
                continue;
            }
            /*
            * 判断是否是需要退出的quit命令.
            */
            if ( readed == std::strlen( MPC_MAIN_GC ) &&
                 std::strncmp( str, MPC_MAIN_GC, readed ) == 0 )
            {
                ct->gc();
                continue;
            }
            if ( readed == std::strlen( MPC_MAIN_QUIT ) &&
                 std::strncmp( str, MPC_MAIN_QUIT, readed ) == 0 )
            {
                return 0;
            }
            if ( readed == std::strlen( MPC_MAIN_RESET ) &&
                 std::strncmp( str, MPC_MAIN_RESET, readed ) == 0 ) goto run_beg;
            /*
            * 执行vscript脚本串.
            */
            if ( !ct->execute( str, readed ) )
            {
                std::cerr << "JAVASCRIPT EXECUTE IS FAILED!\n";
            }
        }
        /*
        * 判断仅仅回车不输入任何字符的情况.
        */
        if ( g_killExit )
        {
            return 0;
        }
        if ( std::cin.gcount() == 0 && std::cin.rdbuf()->sbumpc() == '\n' )
        {
            std::cin.clear();
            goto run_con;
        }
        return 0;
    }

    bool CMainApp::setUp()
    {
        if( !CMainApplication::setUp() ) return false;
        return true;
    }

}  //namespace gum
