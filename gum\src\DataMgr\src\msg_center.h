//
// Created by yp on 2021/11/9.
//

#ifndef GUM_MSG_CENTER_H
#define GUM_MSG_CENTER_H

#include <boost/serialization/singleton.hpp>
#include <boost/signals2.hpp>
#include "protocol/all.h"
#include "protocol/gum.h"
#include "protocol/fvm.h"
#include "data_center.h"
#include "network_type.h"

namespace gum {

    using namespace network;

    #define PROTOCOL_SIGNAL(T) boost::signals2::signal<void(T)>

    class MsgCenter : public boost::noncopyable {
    public:
        MsgCenter() {}

    public:
        bool init();
        bool fini();

        /*
        * 发送数据 （默认UDP）
        * @param strAddr 目标地址和端口
        * @param msgType 消息类型
        * @param data 消息内容
        */
        template<typename T>
        bool sendTo(const std::string& strAddr, ProtocolType msgType, const T& data);

        bool retRequestVideo( const RequestVideo& msg, const std::string& szSrc, int ret );
        bool retRequestStopVideo(const RequestStopVideo& msg, int ret );
        bool retPtzOper(const RequestPtzOper& msg, int ret );
        bool postRemoteStatus(const std::string sFvmAddr, int iRemoteId, int iStatus);
    public: //SIGNALs
        PROTOCOL_SIGNAL(RequestVideo) onRequestVideoMessage;
        PROTOCOL_SIGNAL(RequestStopVideo) onRequestStopVideoMessage;
        PROTOCOL_SIGNAL(RequestPtzOper) onRequestPtzOperMessage;
        PROTOCOL_SIGNAL(RequestRemoteChanged) onRequestRemoteChangedMessage;
        PROTOCOL_SIGNAL(FVMChangedId) onRequestFvmChangedMessage;

    protected:

    private:

        /*
         * 消息数据处理
         * @param msgData 消息内容
         */
        void handleProtocolReceived(std::string& msgData);

    };

    typedef boost::serialization::singleton<MsgCenter> SingletonMsgCenter;
    #define MSG_CENTER SingletonMsgCenter::get_mutable_instance()
} //namespace gum

#endif //GUM_MSG_CENTER_H
