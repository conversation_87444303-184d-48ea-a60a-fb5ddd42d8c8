#!/bin/bash
PROGRAM=gum
CONFIG=$1
if [ -z $CONFIG ]
then
  CONFIG="Debug"
fi

INOS=""
SYSNAME=`uname -a | grep aarch64`
if [ -z "$SYSNAME" ]
then
  INOS=""
else
  INOS="-hw"
fi

echo "program is: "$PROGRAM
echo "config is: " $CONFIG
OUTPUT=output/
BINDIR=$OUTPUT$PROGRAM
THRIDLIBS="3rdlibs"
CONFIGDIR="config"
LIBDIR="lib"
FILENAME=$PROGRAM$INOS-1.0-"`date '+%Y%m%d'`.tar.bz2"

THRIDLIBS="3rdlib"
LIBSLIST="3rdlibsList"

#create $PROGRAM directory
if [ -d $BINDIR ]
then
    rm $BINDIR -fr
fi

mkdir $BINDIR/$CONFIGDIR -p
mkdir $BINDIR/$LIBDIR -p
mkdir $BINDIR/$THRIDLIBS -p

# copy 3rdlibs file
#/bin/cp $THRIDLIBS/* $BINDIR/$EXECUTEDIR
if [ -e $LIBSLIST ]
then
    cat $LIBSLIST | tr -d '\r'| xargs -i -t cp {} $BINDIR/$THRIDLIBS
else
    echo "Can't find LIBSLIST"
fi

# copy $PROGRAM bin file
/bin/cp gum $BINDIR/$PROGRAM"D"
/bin/cp ./lib/*.so* $BINDIR/lib -a
/bin/cp ../../out/lib/*.so $BINDIR/lib -a

# copy config file
/bin/cp $CONFIGDIR/org/*.* $BINDIR/$CONFIGDIR

# copy install script
/bin/cp install.sh $BINDIR
chmod +x $BINDIR/install.sh

# copy startup script
/bin/cp serv $BINDIR/$PROGRAM

cd $OUTPUT

# tar package
tar -cjvf $FILENAME $PROGRAM

/bin/mv $FILENAME ../

cd ..
/bin/rm $OUTPUT -fr

echo $FILENAME" is generated."

