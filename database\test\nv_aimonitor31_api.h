/**
 * @file    nv_aimonitor31_api.h
 * @brief   基于ODB的nvidia检测仪aimonitor v3.1的数据库联合操作api
 *          用户根据表的关联性自行添加接口
 * <AUTHOR>
 * @version 0.0.1
 * @date    18 Oct 2021
 */

/**
 * @defgroup group_aimonitor_odb aimonitor_odb
 * @brief aimonitor_odb group
 * @{
 */

#ifndef _NV_AIMONITOR_API_H
#define _NV_AIMONITOR_API_H

#include <iostream>
#include <string>
#include <odb/core.hxx>
#include <memory>     // std::auto_ptr
#include <vector>

#include "monitor.h"
#include "detection_point.h"
#include "program_info.h"
#include "preset.h"
#include "program.h"
#include "algorithm_param.h"
#include "alg_param_plan.h"
#include "nv_aimonitor31_view.h"
#include "nv_aimonitor31_view-odb.hxx"


/**
 * @brief: 基于ODB的nvidia检测仪aimonitor v3.1的数据库联合操作api
 */
namespace db{

	/**
	* @brief      查找检测仪关联的检测通道数据
	* @param[out] results: 返回的关联的数据对象集合
	* @param[in]  query: 查找条件
	*/
	bool queryMonitorDetectPoint(std::vector<MonitorDetectPointData>& results, odb::query<MonitorDetectPointData>& query);

	/**
	 * @brief      查找程序轮切运行关联的预置位，预置位时间数据
	 * @param[out] results: 返回的关联的数据对象集合
	 * @param[in]  query: 查找条件
	 */
	bool queryProgramPreset(std::vector<PresetProgramData>& results, odb::query<PresetProgramData>& query);


	/**
	 * @brief      查找灵敏度预案关联的灵敏度参数数据
	 * @param[out] results: 返回的关联的数据对象集合
	 * @param[in]  query: 查找条件
	 */
	bool queryAlgoPlanParams(std::vector<AlgoParamsData>& results,  odb::query<AlgoParamsData>& query);


	/**
	 * @brief      查找指定检测仪(wn_monitor)对应的检测通道(detect_point_id)的通道序号（stream_id）
	 * @param[out] channelInd: 检测通道序号 
	 * @param[in]  detectPointId 检测通道ID
	 * @param[in]  monitorIp 检测仪IP
	 */
	bool queryMonitorChannelId(unsigned long& channelInd, unsigned long detectPointId, const std::string& monitorIp);


}

/**
 * @}
 */

#endif //_NV_AIMONITOR_API_H